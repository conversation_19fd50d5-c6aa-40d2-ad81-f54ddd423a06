"use client"

import React from 'react'
import { LandingPageLoading } from '@/components/ui/global-loading'

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="p-8 text-center">
          <div className="text-gray-400 mb-4">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-800 rounded-lg flex items-center justify-center">
              <span className="text-2xl">⚠️</span>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Component Loading Error</h3>
            <p className="text-sm">This section is temporarily unavailable.</p>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// Simple wrapper for 3D components with enhanced error handling
export function Safe3DComponent({ children, fallback }: { children: React.ReactNode, fallback?: React.ReactNode }) {
  const [isClient, setIsClient] = React.useState(false)

  React.useEffect(() => {
    setIsClient(true)
  }, [])

  // Don't render 3D components on server
  if (!isClient) {
    return (
      <div className="min-h-[200px]">
        <LandingPageLoading
          isVisible={true}
          currentSection="3d-content"
          variant="section"
        />
      </div>
    )
  }

  return (
    <ErrorBoundary fallback={fallback || (
      <div className="p-8 text-center min-h-[200px] flex items-center justify-center">
        <div className="text-gray-400">
          <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg flex items-center justify-center">
            <span className="text-2xl">🎮</span>
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">3D Content Unavailable</h3>
          <p className="text-sm">This interactive section is temporarily disabled.</p>
        </div>
      </div>
    )}>
      <React.Suspense fallback={
        <div className="min-h-[200px]">
          <LandingPageLoading
            isVisible={true}
            currentSection="3d-content"
            variant="section"
          />
        </div>
      }>
        {children}
      </React.Suspense>
    </ErrorBoundary>
  )
}
