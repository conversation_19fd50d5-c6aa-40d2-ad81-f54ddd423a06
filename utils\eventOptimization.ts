/**
 * Event Handling Optimization Utilities for NanoHero Platform
 * Provides optimized event handling patterns and performance monitoring
 */

import { useRef, useEffect, useCallback } from 'react'

export type OptimizedEventType =
  | 'click'
  | 'scroll'
  | 'resize'
  | 'mousemove'
  | 'touchstart'
  | 'touchmove'
  | 'touchend'
  | 'keydown'
  | 'keyup'
  | 'pointerdown'
  | 'pointermove'
  | 'pointerup'

export interface EventOptimizationConfig {
  passive: boolean
  throttle?: number
  debounce?: number
  capture?: boolean
  once?: boolean
}

/**
 * Event delegation manager for optimized event handling
 */
export class EventDelegationManager {
  private static instance: EventDelegationManager
  private delegatedEvents = new Map<string, Map<Element, Function>>()
  private rootHandlers = new Map<string, Function>()

  static getInstance(): EventDelegationManager {
    if (!EventDelegationManager.instance) {
      EventDelegationManager.instance = new EventDelegationManager()
    }
    return EventDelegationManager.instance
  }

  /**
   * Add delegated event listener
   */
  addDelegatedListener(
    eventType: string,
    element: Element,
    handler: Function,
    selector?: string
  ) {
    if (!this.delegatedEvents.has(eventType)) {
      this.delegatedEvents.set(eventType, new Map())
      this.setupRootHandler(eventType, selector)
    }

    const eventMap = this.delegatedEvents.get(eventType)!
    eventMap.set(element, handler)
  }

  /**
   * Remove delegated event listener
   */
  removeDelegatedListener(eventType: string, element: Element) {
    const eventMap = this.delegatedEvents.get(eventType)
    if (eventMap) {
      eventMap.delete(element)
      
      if (eventMap.size === 0) {
        this.cleanupRootHandler(eventType)
        this.delegatedEvents.delete(eventType)
      }
    }
  }

  private setupRootHandler(eventType: string, selector?: string) {
    const rootHandler = (event: Event) => {
      const eventMap = this.delegatedEvents.get(eventType)
      if (!eventMap) return

      let target = event.target as Element
      
      // Bubble up to find matching elements
      while (target && target !== document.body) {
        if (selector) {
          if (target.matches(selector)) {
            const handler = eventMap.get(target)
            if (handler) {
              handler(event)
              break
            }
          }
        } else {
          const handler = eventMap.get(target)
          if (handler) {
            handler(event)
            break
          }
        }
        target = target.parentElement!
      }
    }

    this.rootHandlers.set(eventType, rootHandler)
    document.addEventListener(eventType, rootHandler, { passive: true })
  }

  private cleanupRootHandler(eventType: string) {
    const handler = this.rootHandlers.get(eventType)
    if (handler) {
      document.removeEventListener(eventType, handler as EventListener)
      this.rootHandlers.delete(eventType)
    }
  }

  /**
   * Clean up all delegated events
   */
  cleanup() {
    this.rootHandlers.forEach((handler, eventType) => {
      document.removeEventListener(eventType, handler as EventListener)
    })
    this.rootHandlers.clear()
    this.delegatedEvents.clear()
  }
}

export const eventDelegationManager = EventDelegationManager.getInstance()

/**
 * Optimized event listener hook
 */
export const useOptimizedEventListener = <T extends Event>(
  eventType: OptimizedEventType,
  handler: (event: T) => void,
  element?: Element | Window | null,
  config: Partial<EventOptimizationConfig> = {}
) => {
  const savedHandler = useRef<(event: T) => void | null>(null)
  const throttleRef = useRef<NodeJS.Timeout | null>(null)
  const debounceRef = useRef<NodeJS.Timeout | null>(null)

  // Remember the latest handler
  useEffect(() => {
    savedHandler.current = handler
  }, [handler])

  // Create optimized handler
  const optimizedHandler = useCallback((event: T) => {
    const { throttle, debounce } = config

    if (throttle) {
      if (throttleRef.current) return
      
      throttleRef.current = setTimeout(() => {
        throttleRef.current = null
      }, throttle)
      
      savedHandler.current?.(event)
    } else if (debounce) {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }
      
      debounceRef.current = setTimeout(() => {
        savedHandler.current?.(event)
      }, debounce)
    } else {
      savedHandler.current?.(event)
    }
  }, [config])

  useEffect(() => {
    const targetElement = element || window
    if (!targetElement?.addEventListener) return

    const eventOptions = {
      passive: config.passive ?? true,
      capture: config.capture ?? false,
      once: config.once ?? false
    }

    targetElement.addEventListener(eventType, optimizedHandler as EventListener, eventOptions)

    return () => {
      targetElement.removeEventListener(eventType, optimizedHandler as EventListener)
      
      // Clean up timers
      if (throttleRef.current) {
        clearTimeout(throttleRef.current)
      }
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }
    }
  }, [eventType, optimizedHandler, element, config])
}

/**
 * Optimized scroll handler hook
 */
export const useOptimizedScroll = (
  handler: (scrollData: { scrollY: number; direction: 'up' | 'down'; progress: number }) => void,
  throttleMs: number = 16 // ~60fps
) => {
  const lastScrollY = useRef(0)
  const ticking = useRef(false)

  const optimizedHandler = useCallback(() => {
    const scrollY = window.scrollY
    const direction = scrollY > lastScrollY.current ? 'down' : 'up'
    const progress = Math.min(scrollY / (document.body.scrollHeight - window.innerHeight), 1)

    handler({ scrollY, direction, progress })
    lastScrollY.current = scrollY
    ticking.current = false
  }, [handler])

  const throttledHandler = useCallback(() => {
    if (!ticking.current) {
      requestAnimationFrame(optimizedHandler)
      ticking.current = true
    }
  }, [optimizedHandler])

  useOptimizedEventListener('scroll', throttledHandler, window, {
    passive: true,
    throttle: throttleMs
  })
}

/**
 * Optimized resize handler hook
 */
export const useOptimizedResize = (
  handler: (dimensions: { width: number; height: number }) => void,
  debounceMs: number = 250
) => {
  const optimizedHandler = useCallback(() => {
    handler({
      width: window.innerWidth,
      height: window.innerHeight
    })
  }, [handler])

  useOptimizedEventListener('resize', optimizedHandler, window, {
    passive: true,
    debounce: debounceMs
  })
}

/**
 * Optimized mouse/touch handler hook
 */
export const useOptimizedPointer = (
  element: Element | null,
  handlers: {
    onPointerDown?: (event: PointerEvent) => void
    onPointerMove?: (event: PointerEvent) => void
    onPointerUp?: (event: PointerEvent) => void
  },
  throttleMs: number = 16
) => {
  useOptimizedEventListener('pointerdown', handlers.onPointerDown!, element, {
    passive: false
  })

  useOptimizedEventListener('pointermove', handlers.onPointerMove!, element, {
    passive: true,
    throttle: throttleMs
  })

  useOptimizedEventListener('pointerup', handlers.onPointerUp!, element, {
    passive: true
  })
}

/**
 * Event performance monitor
 */
export class EventPerformanceMonitor {
  private eventCounts = new Map<string, number>()
  private eventTimes = new Map<string, number[]>()
  private lastReset = Date.now()

  trackEvent(eventType: string, executionTime?: number) {
    // Track event count
    const count = this.eventCounts.get(eventType) || 0
    this.eventCounts.set(eventType, count + 1)

    // Track execution time if provided
    if (executionTime !== undefined) {
      const times = this.eventTimes.get(eventType) || []
      times.push(executionTime)
      
      // Keep only last 100 measurements
      if (times.length > 100) {
        times.shift()
      }
      
      this.eventTimes.set(eventType, times)
    }
  }

  getStats() {
    const stats: Record<string, any> = {}
    
    this.eventCounts.forEach((count, eventType) => {
      const times = this.eventTimes.get(eventType) || []
      const avgTime = times.length > 0 
        ? times.reduce((a, b) => a + b, 0) / times.length 
        : 0

      stats[eventType] = {
        count,
        averageExecutionTime: avgTime,
        eventsPerSecond: count / ((Date.now() - this.lastReset) / 1000)
      }
    })

    return stats
  }

  reset() {
    this.eventCounts.clear()
    this.eventTimes.clear()
    this.lastReset = Date.now()
  }
}

export const eventPerformanceMonitor = new EventPerformanceMonitor()

/**
 * DOM manipulation optimization utilities
 */
export const domOptimization = {
  /**
   * Batch DOM updates using DocumentFragment
   */
  batchDOMUpdates: (updates: Array<() => void>) => {
    const fragment = document.createDocumentFragment()
    
    // Perform all updates in memory
    updates.forEach(update => update())
    
    // Single DOM update
    return fragment
  },

  /**
   * Optimized class list manipulation
   */
  optimizedClassToggle: (element: Element, className: string, condition: boolean) => {
    if (condition && !element.classList.contains(className)) {
      element.classList.add(className)
    } else if (!condition && element.classList.contains(className)) {
      element.classList.remove(className)
    }
  },

  /**
   * Efficient style updates
   */
  batchStyleUpdates: (element: HTMLElement, styles: Record<string, string>) => {
    // Use cssText for better performance with multiple style changes
    const styleString = Object.entries(styles)
      .map(([property, value]) => `${property}: ${value}`)
      .join('; ')
    
    element.style.cssText += '; ' + styleString
  }
}

/**
 * React hook for optimized event delegation
 */
export const useEventDelegation = (
  eventType: string,
  selector: string,
  handler: (event: Event) => void
) => {
  const containerRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    eventDelegationManager.addDelegatedListener(eventType, container, handler, selector)

    return () => {
      eventDelegationManager.removeDelegatedListener(eventType, container)
    }
  }, [eventType, selector, handler])

  return containerRef
}

/**
 * Intersection Observer optimization hook
 */
export const useOptimizedIntersectionObserver = (
  callback: (entries: IntersectionObserverEntry[]) => void,
  options: IntersectionObserverInit = {}
) => {
  const elementRef = useRef<HTMLElement>(null)
  const observerRef = useRef<IntersectionObserver | null>(null)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const defaultOptions = {
      threshold: 0.1,
      rootMargin: '50px',
      ...options
    }

    observerRef.current = new IntersectionObserver(callback, defaultOptions)
    observerRef.current.observe(element)

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [callback, options])

  return elementRef
}

/**
 * Global event optimization configuration
 */
export const globalEventConfig = {
  // Default throttle/debounce values
  defaultScrollThrottle: 16, // ~60fps
  defaultResizeDebounce: 250,
  defaultMouseMoveThrottle: 16,
  
  // Performance monitoring
  enablePerformanceMonitoring: true,
  
  // Event delegation
  enableEventDelegation: true,
  
  // Passive event listeners
  usePassiveListeners: true
}
