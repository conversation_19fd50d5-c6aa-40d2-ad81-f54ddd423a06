'use client'

import { motion } from 'framer-motion'
import { Map, Globe, Eye, Focus, Users } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { NodeManagementProps, ViewMode } from './types'

interface TimelineMap3DProps extends NodeManagementProps {
  viewMode: ViewMode
  onViewModeChange: (mode: ViewMode) => void
}

export default function TimelineMap3D({ 
  nanoNodes, 
  selectedNode, 
  onNodeSelect,
  viewMode,
  onViewModeChange
}: TimelineMap3DProps) {
  return (
    <Card className="bg-black/40 border-blue-500/30 backdrop-blur-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-blue-400">
          <Map className="w-5 h-5" />
          3D Timeline World View
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative h-96 bg-gradient-to-br from-blue-900/20 to-purple-900/20 rounded-lg border border-blue-500/30 overflow-hidden">
          {/* 3D Map Placeholder - Would integrate Three.js here */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center space-y-4">
              <Globe className="w-16 h-16 text-blue-400 mx-auto animate-spin" style={{ animationDuration: '10s' }} />
              <div className="text-white/80">3D Timeline Map</div>
              <div className="text-sm text-white/60">Interactive world view with node positions</div>
            </div>
          </div>

          {/* Node Markers Overlay */}
          <div className="absolute inset-0">
            {nanoNodes.map((node, index) => (
              <motion.div
                key={node.id}
                className="absolute cursor-pointer"
                style={{
                  left: `${(node.position.x / 400) * 100}%`,
                  top: `${(node.position.y / 300) * 100}%`
                }}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: index * 0.2 }}
                whileHover={{ scale: 1.2 }}
                onClick={() => onNodeSelect(node)}
              >
                <div
                  className="w-4 h-4 rounded-full border-2 border-white/50"
                  style={{ backgroundColor: node.color }}
                />
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-xs text-white/80 whitespace-nowrap">
                  {node.title}
                </div>
              </motion.div>
            ))}
          </div>

          {/* Navigation Controls */}
          <div className="absolute bottom-4 right-4 flex gap-2">
            <Button size="sm" variant="outline" className="border-blue-500/30 text-blue-400">
              <Eye className="w-4 h-4" />
            </Button>
            <Button size="sm" variant="outline" className="border-green-500/30 text-green-400">
              <Focus className="w-4 h-4" />
            </Button>
            <Button size="sm" variant="outline" className="border-purple-500/30 text-purple-400">
              <Users className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* View Mode Controls */}
        <div className="flex gap-2 mt-4">
          {(['explore', 'focus', 'assist', 'syntropy'] as const).map((mode) => (
            <Button
              key={mode}
              size="sm"
              variant={viewMode === mode ? "default" : "outline"}
              onClick={() => onViewModeChange(mode)}
              className={`capitalize ${
                viewMode === mode
                  ? 'bg-blue-500/20 text-blue-400 border-blue-500/30'
                  : 'border-gray-600/30 text-gray-400 hover:border-blue-500/30 hover:text-blue-400'
              }`}
            >
              {mode}
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
