'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Compass,
  Dna,
  Map,
  Users,
  Target,
  BookOpen,
  BarChart3
} from 'lucide-react'

import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { useDashboardStore, usePlayerData, useSystemStatus } from '../../store/dashboardStore'

// Import all the extracted components
import DashboardHeader from './DashboardHeader'
import DNAFragmentDisplay from './DNAFragmentDisplay'
import TimelineEvolutionBar from './TimelineEvolutionBar'
import ConsciousnessMetrics from './ConsciousnessMetrics'
import AvatarEvolution from './AvatarEvolution'
import ActiveNodes from './ActiveNodes'
import DNAStructureAnalysis from './DNAStructureAnalysis'
import DNASequenceBreakdown from './DNASequenceBreakdown'
import EvolutionPrediction from './EvolutionPrediction'
import TimelineMap3D from './TimelineMap3D'
import AvatarIdentityPanel from './AvatarIdentityPanel'
import DailyMissionBoard from './DailyMissionBoard'

import { 
  DNACoreDashboardProps, 
  NanoNode, 
  EvolutionStage, 
  ViewMode, 
  EvolutionTheme 
} from './types'

export default function DNACoreDashboard({ className }: DNACoreDashboardProps) {
  const { setActivePanel: _setActivePanel, setViewMode: _setViewMode, setSelectedNode: _setSelectedNode } = useDashboardStore()
  const playerData = usePlayerData()
  const systemStatus = useSystemStatus()

  const [_animationTrigger, setAnimationTrigger] = useState(0)
  const [selectedNode, setSelectedNodeLocal] = useState<NanoNode | null>(null)
  const [viewMode, setViewModeLocal] = useState<ViewMode>('explore')
  const [dnaFragment, setDnaFragment] = useState('NANO-ARCH-EVOL-SYNC')
  const [timelineStage, setTimelineStage] = useState<EvolutionStage>('awakening')

  // Trigger animations periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setAnimationTrigger(prev => prev + 1)
    }, 3000)
    return () => clearInterval(interval)
  }, [])

  // Generate dynamic DNA fragment based on player data
  useEffect(() => {
    const generateDNAFragment = () => {
      const segments = [
        ['NANO', 'ARCH', 'EVOL', 'SYNC'],
        ['QNTM', 'FLUX', 'WAVE', 'CORE'],
        ['MIND', 'SOUL', 'BODY', 'SPRT'],
        ['LOVE', 'WISE', 'POWR', 'UNIT']
      ]
      const level = playerData?.level || 1
      const selectedSegments = segments.map((segment, index) =>
        segment[Math.min(Math.floor(level / 5) + index, segment.length - 1)]
      )
      setDnaFragment(selectedSegments.join('-'))
    }
    generateDNAFragment()
  }, [playerData?.level])

  // Evolution stage color theme based on player level
  const getEvolutionTheme = (): EvolutionTheme => {
    const stage = playerData?.level || 1
    if (stage <= 2) return {
      primary: '#22D3EE', // Neural cyan
      secondary: '#8B5CF6', // Quantum purple
      accent: '#F59E0B', // Amber
      glow: '#22D3EE40',
      background: 'from-cyan-500/10 to-purple-500/10',
      gradient: 'from-cyan-400 to-purple-400'
    }
    if (stage <= 5) return {
      primary: '#8B5CF6', // Quantum purple
      secondary: '#F59E0B', // Amber
      accent: '#EF4444', // Red
      glow: '#8B5CF640',
      background: 'from-purple-500/10 to-amber-500/10',
      gradient: 'from-purple-400 to-amber-400'
    }
    if (stage <= 10) return {
      primary: '#F59E0B', // Amber
      secondary: '#EF4444', // Red
      accent: '#10B981', // Emerald
      glow: '#F59E0B40',
      background: 'from-amber-500/10 to-red-500/10',
      gradient: 'from-amber-400 to-red-400'
    }
    if (stage <= 15) return {
      primary: '#EF4444', // Red
      secondary: '#10B981', // Emerald
      accent: '#3B82F6', // Blue
      glow: '#EF444440',
      background: 'from-red-500/10 to-emerald-500/10',
      gradient: 'from-red-400 to-emerald-400'
    }
    return {
      primary: '#FBBF24', // Golden
      secondary: '#FFFFFF', // White
      accent: '#22D3EE', // Cyan
      glow: '#FBBF2440',
      background: 'from-yellow-500/10 to-white/10',
      gradient: 'from-yellow-400 to-white'
    }
  }

  const evolutionTheme = getEvolutionTheme()

  // Mock nano nodes data for the enhanced system
  const nanoNodes: NanoNode[] = [
    {
      id: 'learning-1',
      type: 'learning',
      title: 'Neural Pathways',
      description: 'Enhance cognitive connections',
      status: 'active',
      position: { x: 100, y: 150, z: 0 },
      color: '#22D3EE',
      participants: 12,
      lastActivity: '2 min ago',
      completionRate: 85,
      rewards: { ce: 50, qs: 25 }
    },
    {
      id: 'echo-1',
      type: 'echo',
      title: 'Wisdom Echo',
      description: 'Share insights with community',
      status: 'active',
      position: { x: 250, y: 100, z: 0 },
      color: '#8B5CF6',
      participants: 8,
      lastActivity: '5 min ago',
      completionRate: 92,
      rewards: { ce: 30, qs: 40 }
    },
    {
      id: 'meditation-1',
      type: 'meditation',
      title: 'Quantum Balance',
      description: 'Stabilize consciousness energy',
      status: 'dormant',
      position: { x: 180, y: 220, z: 0 },
      color: '#F59E0B',
      participants: 15,
      lastActivity: '1 hour ago',
      completionRate: 67,
      rewards: { ce: 20, qs: 60 }
    },
    {
      id: 'timelinegen-1',
      type: 'timelinegen',
      title: 'Matter Synthesis',
      description: 'Create quantum constructs',
      status: 'active',
      position: { x: 320, y: 180, z: 0 },
      color: '#EF4444',
      participants: 6,
      lastActivity: '10 min ago',
      completionRate: 78,
      rewards: { ce: 75, qs: 35 }
    }
  ]

  return (
    <div className={`p-6 space-y-6 ${className}`}>
      {/* Dashboard Header */}
      <DashboardHeader 
        evolutionTheme={evolutionTheme}
        playerData={playerData}
        dnaFragment={dnaFragment}
      />

      <div className='flex flex-row gap-6'>
        <div className="col-span-1">
          {/* Daily Mission Board */}
          <DailyMissionBoard
            systemStatus={systemStatus}
            playerData={playerData}
            evolutionTheme={evolutionTheme}
          />

        </div>

        <div className='flex flex-col col-span-1 lg:col-span-1 space-y-6'>
          {/* Avatar & Identity Panel */}
          <AvatarIdentityPanel
            systemStatus={systemStatus}
            playerData={playerData}
            evolutionTheme={evolutionTheme}
          />

          {/* DNA Fragment Display */}
          <DNAFragmentDisplay
            dnaFragment={dnaFragment}
            playerData={playerData}
            systemStatus={systemStatus}
            evolutionTheme={evolutionTheme}
          />
        </div>
      </div>

      {/* Main NanoGenesis Interface */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-7 bg-black/40 border border-gray-800/50">
          <TabsTrigger value="overview" className="data-[state=active]:bg-cyan-500/20 data-[state=active]:text-cyan-400">
            <Compass className="w-4 h-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="dna-analysis" className="data-[state=active]:bg-cyan-500/20 data-[state=active]:text-cyan-400">
            <Dna className="w-4 h-4 mr-2" />
            DNA Analysis
          </TabsTrigger>
          <TabsTrigger value="timeline-map" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400">
            <Map className="w-4 h-4 mr-2" />
            Timeline Map
          </TabsTrigger>
          <TabsTrigger value="consciousness" className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-400">
            <Users className="w-4 h-4 mr-2" />
            Consciousness Feed
          </TabsTrigger>
          <TabsTrigger value="quantum-tasks" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400">
            <Target className="w-4 h-4 mr-2" />
            Quantum Tasks
          </TabsTrigger>
          <TabsTrigger value="library" className="data-[state=active]:bg-yellow-500/20 data-[state=active]:text-yellow-400">
            <BookOpen className="w-4 h-4 mr-2" />
            Library
          </TabsTrigger>
          <TabsTrigger value="metrics" className="data-[state=active]:bg-pink-500/20 data-[state=active]:text-pink-400">
            <BarChart3 className="w-4 h-4 mr-2" />
            Metrics
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Timeline Evolution Bar */}
            <TimelineEvolutionBar
              timelineStage={timelineStage}
              playerData={playerData}
              systemStatus={systemStatus}
            />



            {/* CE/QS Metrics */}
            <ConsciousnessMetrics
              systemStatus={systemStatus}
              playerData={playerData}
              evolutionTheme={evolutionTheme}
            />

            {/* Avatar Evolution Preview */}
            <AvatarEvolution
              systemStatus={systemStatus}
              playerData={playerData}
              evolutionTheme={evolutionTheme}
            />

            {/* Active Nodes Quick Access */}
            <ActiveNodes
              nanoNodes={nanoNodes}
              selectedNode={selectedNode}
              onNodeSelect={setSelectedNodeLocal}
            />
          </div>
        </TabsContent>

        {/* DNA Analysis Tab */}
        <TabsContent value="dna-analysis" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <DNAStructureAnalysis 
              dnaFragment={dnaFragment}
              playerData={playerData}
              systemStatus={systemStatus}
              evolutionTheme={evolutionTheme}
            />
            
            <DNASequenceBreakdown 
              dnaFragment={dnaFragment}
              playerData={playerData}
              systemStatus={systemStatus}
              evolutionTheme={evolutionTheme}
            />
          </div>

          <EvolutionPrediction 
            dnaFragment={dnaFragment}
            playerData={playerData}
            systemStatus={systemStatus}
            evolutionTheme={evolutionTheme}
          />
        </TabsContent>

        {/* Timeline Map Tab */}
        <TabsContent value="timeline-map" className="space-y-6">
          <TimelineMap3D 
            nanoNodes={nanoNodes}
            selectedNode={selectedNode}
            onNodeSelect={setSelectedNodeLocal}
            viewMode={viewMode}
            onViewModeChange={setViewModeLocal}
          />
        </TabsContent>

        {/* Other tabs would be implemented similarly */}
        <TabsContent value="consciousness" className="space-y-6">
          <div className="text-center text-white/60 py-12">
            Consciousness Feed - To be implemented
          </div>
        </TabsContent>

        <TabsContent value="quantum-tasks" className="space-y-6">
          <div className="text-center text-white/60 py-12">
            Quantum Tasks - To be implemented
          </div>
        </TabsContent>

        <TabsContent value="library" className="space-y-6">
          <div className="text-center text-white/60 py-12">
            Library - To be implemented
          </div>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-6">
          <div className="text-center text-white/60 py-12">
            Metrics Dashboard - To be implemented
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
