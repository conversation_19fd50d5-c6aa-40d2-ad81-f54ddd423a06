// IGNIS Safety Engine - Advanced AI-powered moderation system for QuantumLink
export interface ModerationResult {
  isApproved: boolean
  confidence: number
  flags: string[]
  emotionTags: string[]
  kindnessScore: number
  helpfulnessScore: number
  quantumResonance: number // New: measures positive energy contribution
  suggestedAction: 'approve' | 'review' | 'reject' | 'suggest_preset' | 'redirect_to_reflection'
  explanation?: string
  ignisSafetyLevel: 'green' | 'yellow' | 'orange' | 'red'
  recommendedPresetPhrases?: string[]
  emotionalIntelligenceBoost?: number
}

export interface PersonalInfoDetection {
  hasPersonalInfo: boolean
  detectedTypes: ('name' | 'email' | 'phone' | 'address' | 'school' | 'age')[]
  confidence: number
}

export interface IGNISAnalysis {
  emotionalState: 'positive' | 'neutral' | 'frustrated' | 'angry' | 'sad' | 'excited'
  communicationIntent: 'help' | 'learn' | 'share' | 'vent' | 'attack' | 'connect'
  maturityLevel: number // 1-10 scale
  empathyScore: number // 0-1 scale
  constructivenessScore: number // 0-1 scale
}

// IGNIS Safety Engine Core Analysis
function analyzeWithIGNIS(content: string): IGNISAnalysis {
  const lowerContent = content.toLowerCase()

  // Emotional state detection
  let emotionalState: IGNISAnalysis['emotionalState'] = 'neutral'

  const positiveWords = ['happy', 'excited', 'love', 'amazing', 'awesome', 'great', 'wonderful', 'fantastic']
  const frustrationWords = ['frustrated', 'annoyed', 'confused', 'stuck', 'difficult', 'hard']
  const angerWords = ['angry', 'mad', 'furious', 'hate', 'stupid', 'dumb', 'annoying']
  const sadWords = ['sad', 'depressed', 'down', 'upset', 'hurt', 'lonely', 'crying']
  const excitementWords = ['excited', 'pumped', 'thrilled', 'can\'t wait', 'amazing', 'incredible']

  if (positiveWords.some(word => lowerContent.includes(word))) {
    emotionalState = 'positive'
  } else if (excitementWords.some(word => lowerContent.includes(word))) {
    emotionalState = 'excited'
  } else if (angerWords.some(word => lowerContent.includes(word))) {
    emotionalState = 'angry'
  } else if (frustrationWords.some(word => lowerContent.includes(word))) {
    emotionalState = 'frustrated'
  } else if (sadWords.some(word => lowerContent.includes(word))) {
    emotionalState = 'sad'
  }

  // Communication intent detection
  let communicationIntent: IGNISAnalysis['communicationIntent'] = 'share'

  const helpWords = ['help', 'assist', 'support', 'guide', 'explain', 'show', 'teach']
  const learnWords = ['learn', 'understand', 'know', 'how', 'what', 'why', 'question']
  const _shareWords = ['share', 'tell', 'show', 'found', 'discovered', 'think']
  const ventWords = ['frustrated', 'annoyed', 'tired', 'sick of', 'can\'t stand']
  const attackWords = ['stupid', 'dumb', 'idiot', 'hate', 'shut up', 'loser']
  const connectWords = ['hi', 'hello', 'anyone', 'together', 'friend', 'chat']

  if (helpWords.some(word => lowerContent.includes(word))) {
    communicationIntent = 'help'
  } else if (learnWords.some(word => lowerContent.includes(word))) {
    communicationIntent = 'learn'
  } else if (attackWords.some(word => lowerContent.includes(word))) {
    communicationIntent = 'attack'
  } else if (ventWords.some(word => lowerContent.includes(word))) {
    communicationIntent = 'vent'
  } else if (connectWords.some(word => lowerContent.includes(word))) {
    communicationIntent = 'connect'
  }

  // Calculate scores
  const maturityLevel = calculateMaturityLevel(content)
  const empathyScore = calculateEmpathyScore(content)
  const constructivenessScore = calculateConstructivenessScore(content)

  return {
    emotionalState,
    communicationIntent,
    maturityLevel,
    empathyScore,
    constructivenessScore
  }
}

function calculateMaturityLevel(content: string): number {
  const lowerContent = content.toLowerCase()
  let score = 5 // Start at middle

  // Positive indicators
  if (lowerContent.includes('please') || lowerContent.includes('thank')) score += 1
  if (lowerContent.includes('sorry') || lowerContent.includes('apologize')) score += 1
  if (lowerContent.length > 50 && !lowerContent.includes('!!!')) score += 1

  // Negative indicators
  if (lowerContent.includes('!!!') || lowerContent.includes('???')) score -= 1
  if (lowerContent.toUpperCase() === lowerContent.toUpperCase() && lowerContent.length > 10) score -= 2
  if (['stupid', 'dumb', 'hate'].some(word => lowerContent.includes(word))) score -= 2

  return Math.max(1, Math.min(10, score))
}

function calculateEmpathyScore(content: string): number {
  const lowerContent = content.toLowerCase()
  let score = 0.5

  const empathyWords = ['understand', 'feel', 'sorry', 'help', 'support', 'care', 'hope']
  const empathyPhrases = ['i understand', 'i feel', 'that must be', 'i hope', 'i care']

  empathyWords.forEach(word => {
    if (lowerContent.includes(word)) score += 0.1
  })

  empathyPhrases.forEach(phrase => {
    if (lowerContent.includes(phrase)) score += 0.15
  })

  return Math.max(0, Math.min(1, score))
}

function calculateConstructivenessScore(content: string): number {
  const lowerContent = content.toLowerCase()
  let score = 0.5

  const constructiveWords = ['suggest', 'idea', 'maybe', 'could', 'might', 'try', 'consider']
  const destructiveWords = ['never', 'always', 'stupid', 'wrong', 'bad', 'terrible']

  constructiveWords.forEach(word => {
    if (lowerContent.includes(word)) score += 0.1
  })

  destructiveWords.forEach(word => {
    if (lowerContent.includes(word)) score -= 0.15
  })

  return Math.max(0, Math.min(1, score))
}

// IGNIS Safety Engine Preset Phrase Recommendations
function getKindnessPresetPhrases(): string[] {
  return [
    "I understand you might be feeling frustrated. Can we talk about this calmly?",
    "I'd like to help you with this. What specific part is challenging?",
    "Let's work together to find a solution that works for everyone.",
    "I appreciate you sharing your thoughts. Here's another way to look at it...",
    "Thank you for bringing this up. How can we make this better?"
  ]
}

function getConstructivePresetPhrases(): string[] {
  return [
    "I have a different perspective on this. Would you like to hear it?",
    "That's an interesting point. Have you considered this approach?",
    "I think there might be a misunderstanding. Let me clarify...",
    "I'd love to help you understand this better. Can you tell me more?",
    "Great question! Here's what I've learned about this topic..."
  ]
}

function _getEmotionalSupportPhrases(): string[] {
  return [
    "It sounds like you're going through something difficult. I'm here to listen.",
    "Thank you for sharing how you're feeling. That takes courage.",
    "I hear that you're struggling with this. You're not alone.",
    "It's okay to feel this way. Would you like to talk about it?",
    "I care about how you're doing. Is there anything I can do to help?"
  ]
}

// IGNIS Safety Engine - Advanced AI moderation with emotional intelligence
export async function moderateMessage(content: string, senderAge?: number): Promise<ModerationResult> {
  const flags: string[] = []
  const emotionTags: string[] = []
  let kindnessScore = 0.5
  let helpfulnessScore = 0.5
  let quantumResonance = 0.5

  // Convert to lowercase for analysis
  const lowerContent = content.toLowerCase()

  // IGNIS Analysis
  const ignis = analyzeWithIGNIS(content)

  // Enhanced inappropriate content detection
  const inappropriateWords = [
    'stupid', 'dumb', 'idiot', 'hate', 'shut up', 'loser', 'ugly', 'fat',
    'kill yourself', 'die', 'worthless', 'pathetic', 'freak', 'weirdo'
  ]

  const hasInappropriate = inappropriateWords.some(word => lowerContent.includes(word))
  if (hasInappropriate) {
    flags.push('inappropriate_language')
    quantumResonance -= 0.3
  }

  // Enhanced bullying pattern detection
  const bullyingPatterns = [
    'you are', 'you\'re so', 'nobody likes', 'everyone thinks', 'you can\'t', 'you suck',
    'you should', 'you never', 'you always', 'you\'re such a'
  ]

  const hasBullying = bullyingPatterns.some(pattern =>
    lowerContent.includes(pattern) && inappropriateWords.some(word => lowerContent.includes(word))
  )

  if (hasBullying) {
    flags.push('potential_bullying')
    quantumResonance -= 0.4
  }
  
  // Check for personal information
  const personalInfo = detectPersonalInformation(content)
  if (personalInfo.hasPersonalInfo) {
    flags.push('personal_information')
    quantumResonance -= 0.2
  }

  // Enhanced IGNIS-powered emotion and intent analysis
  emotionTags.push(ignis.emotionalState)
  emotionTags.push(ignis.communicationIntent)

  // Update scores based on IGNIS analysis
  kindnessScore = Math.max(0, Math.min(1, kindnessScore + (ignis.empathyScore - 0.5)))
  helpfulnessScore = Math.max(0, Math.min(1, helpfulnessScore + (ignis.constructivenessScore - 0.5)))
  quantumResonance = Math.max(0, Math.min(1, quantumResonance + (ignis.maturityLevel - 5) / 10))

  // Emotional state specific handling
  switch (ignis.emotionalState) {
    case 'positive':
    case 'excited':
      kindnessScore += 0.2
      quantumResonance += 0.1
      break
    case 'frustrated':
      flags.push('emotional_support_needed')
      break
    case 'angry':
      flags.push('emotional_distress')
      if (ignis.communicationIntent === 'attack') {
        flags.push('potential_aggression')
      }
      break
    case 'sad':
      flags.push('emotional_support_needed')
      emotionTags.push('needs_support')
      break
  }

  // Communication intent specific handling
  switch (ignis.communicationIntent) {
    case 'help':
      helpfulnessScore += 0.3
      kindnessScore += 0.2
      quantumResonance += 0.2
      break
    case 'learn':
      emotionTags.push('curious')
      quantumResonance += 0.1
      break
    case 'attack':
      flags.push('aggressive_intent')
      quantumResonance -= 0.5
      break
    case 'vent':
      flags.push('emotional_expression')
      break
  }

  // Calculate overall scores
  kindnessScore = Math.min(1, Math.max(0, kindnessScore))
  helpfulnessScore = Math.min(1, Math.max(0, helpfulnessScore))
  quantumResonance = Math.min(1, Math.max(0, quantumResonance))

  // Determine IGNIS Safety Level
  let ignisSafetyLevel: ModerationResult['ignisSafetyLevel'] = 'green'
  let suggestedAction: ModerationResult['suggestedAction'] = 'approve'
  let isApproved = true
  let recommendedPresetPhrases: string[] = []
  let emotionalIntelligenceBoost = 0

  // Safety level determination based on multiple factors
  const riskScore = flags.length + (1 - kindnessScore) + (1 - helpfulnessScore) + (1 - quantumResonance)

  if (riskScore >= 3 || flags.includes('potential_bullying') || flags.includes('aggressive_intent')) {
    ignisSafetyLevel = 'red'
    suggestedAction = 'reject'
    isApproved = false
    recommendedPresetPhrases = getKindnessPresetPhrases()
  } else if (riskScore >= 2 || flags.includes('inappropriate_language') || flags.includes('potential_aggression')) {
    ignisSafetyLevel = 'orange'
    suggestedAction = 'suggest_preset'
    isApproved = false
    recommendedPresetPhrases = getConstructivePresetPhrases()
  } else if (riskScore >= 1 || flags.includes('emotional_distress') || flags.includes('personal_information')) {
    ignisSafetyLevel = 'yellow'
    suggestedAction = 'review'
    isApproved = false
    if (flags.includes('emotional_support_needed')) {
      suggestedAction = 'redirect_to_reflection'
      emotionalIntelligenceBoost = 10
    }
  } else if (quantumResonance > 0.7 && kindnessScore > 0.7) {
    // High quality message - boost rewards
    emotionalIntelligenceBoost = 5
  }

  // Age-specific adjustments
  if (senderAge && senderAge < 13) {
    if (ignisSafetyLevel === 'yellow') {
      ignisSafetyLevel = 'orange'
      suggestedAction = 'suggest_preset'
      isApproved = false
    }
  }

  const confidence = Math.max(0.6, 1 - (flags.length * 0.1))

  return {
    isApproved,
    confidence,
    flags,
    emotionTags,
    kindnessScore,
    helpfulnessScore,
    quantumResonance,
    suggestedAction,
    ignisSafetyLevel,
    recommendedPresetPhrases,
    emotionalIntelligenceBoost,
    explanation: flags.length > 0 ? `IGNIS Analysis: ${flags.join(', ')}. Safety Level: ${ignisSafetyLevel.toUpperCase()}` : undefined
  }
}

export function detectPersonalInformation(content: string): PersonalInfoDetection {
  const detectedTypes: PersonalInfoDetection['detectedTypes'] = []
  let confidence = 0
  
  // Email pattern
  const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/
  if (emailPattern.test(content)) {
    detectedTypes.push('email')
    confidence += 0.9
  }
  
  // Phone pattern
  const phonePattern = /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/
  if (phonePattern.test(content)) {
    detectedTypes.push('phone')
    confidence += 0.8
  }
  
  // Age pattern
  const agePattern = /\b(i am|i'm|age|years old)\s*\d{1,2}\b/i
  if (agePattern.test(content)) {
    detectedTypes.push('age')
    confidence += 0.6
  }
  
  // School pattern
  const schoolPattern = /\b(school|elementary|middle|high school|university|college)\s+[A-Z][a-z]+/i
  if (schoolPattern.test(content)) {
    detectedTypes.push('school')
    confidence += 0.7
  }
  
  // Name pattern (common first names)
  const namePattern = /\b(my name is|i'm|call me)\s+[A-Z][a-z]+\b/i
  if (namePattern.test(content)) {
    detectedTypes.push('name')
    confidence += 0.5
  }
  
  // Address indicators
  const addressPattern = /\b(live at|address|street|avenue|road|drive)\b/i
  if (addressPattern.test(content)) {
    detectedTypes.push('address')
    confidence += 0.8
  }
  
  return {
    hasPersonalInfo: detectedTypes.length > 0,
    detectedTypes,
    confidence: Math.min(1, confidence)
  }
}

export function generateSafeAlternatives(originalMessage: string, flags: string[]): string[] {
  const alternatives: string[] = []
  
  if (flags.includes('inappropriate_language')) {
    alternatives.push(
      "I'm feeling frustrated. Can someone help me understand this better?",
      "This is challenging for me. Could we work on it together?",
      "I'm having trouble with this. Any suggestions?"
    )
  }
  
  if (flags.includes('potential_bullying')) {
    alternatives.push(
      "I see things differently. Can we discuss this respectfully?",
      "I'd like to understand your perspective better.",
      "Let's find a way to work together on this."
    )
  }
  
  if (flags.includes('personal_information')) {
    alternatives.push(
      "I'd like to share more, but let's keep personal details private.",
      "Thanks for asking! I prefer to keep some things private.",
      "Let's focus on our learning adventure instead!"
    )
  }
  
  if (flags.includes('emotional_distress')) {
    alternatives.push(
      "I'm feeling a bit overwhelmed. Could someone help me?",
      "This is making me feel stressed. Can we take a different approach?",
      "I need some encouragement. This is harder than I expected."
    )
  }
  
  return alternatives
}

export function calculateHarmonyImpact(moderationResult: ModerationResult): number {
  let impact = 0
  
  // Positive contributions
  if (moderationResult.kindnessScore > 0.7) impact += 5
  if (moderationResult.helpfulnessScore > 0.7) impact += 8
  if (moderationResult.emotionTags.includes('helpful')) impact += 10
  if (moderationResult.emotionTags.includes('positive')) impact += 3
  
  // Negative impacts
  if (moderationResult.flags.includes('inappropriate_language')) impact -= 15
  if (moderationResult.flags.includes('potential_bullying')) impact -= 25
  if (moderationResult.flags.includes('emotional_distress')) impact -= 5
  
  return Math.max(-50, Math.min(50, impact))
}

export function shouldTriggerParentNotification(
  moderationResult: ModerationResult,
  userAge: number,
  recentFlags: number
): boolean {
  // Trigger parent notification if:
  // 1. User is under 13 and has serious flags
  // 2. User has multiple flags in recent period
  // 3. Emotional distress is detected
  
  return (
    (userAge < 13 && (
      moderationResult.flags.includes('inappropriate_language') ||
      moderationResult.flags.includes('potential_bullying')
    )) ||
    recentFlags >= 3 ||
    moderationResult.flags.includes('emotional_distress')
  )
}

export function generateModerationReport(
  userId: string,
  timeframe: 'day' | 'week' | 'month',
  messages: any[]
): {
  totalMessages: number
  flaggedMessages: number
  kindnessAverage: number
  helpfulnessAverage: number
  commonFlags: string[]
  improvement: number
  recommendations: string[]
} {
  const flaggedMessages = messages.filter(m => m.moderationResult?.flags.length > 0)
  const kindnessScores = messages.map(m => m.moderationResult?.kindnessScore || 0.5)
  const helpfulnessScores = messages.map(m => m.moderationResult?.helpfulnessScore || 0.5)
  
  const kindnessAverage = kindnessScores.reduce((a, b) => a + b, 0) / kindnessScores.length
  const helpfulnessAverage = helpfulnessScores.reduce((a, b) => a + b, 0) / helpfulnessScores.length
  
  const allFlags = flaggedMessages.flatMap(m => m.moderationResult?.flags || [])
  const flagCounts = allFlags.reduce((acc, flag) => {
    acc[flag] = (acc[flag] || 0) + 1
    return acc
  }, {} as Record<string, number>)
  
  const commonFlags = Object.entries(flagCounts)
    .sort(([,a], [,b]) => (b as number) - (a as number))
    .slice(0, 3)
    .map(([flag]) => flag)
  
  const recommendations: string[] = []
  if (kindnessAverage < 0.6) {
    recommendations.push("Practice using more encouraging and supportive language")
  }
  if (helpfulnessAverage < 0.5) {
    recommendations.push("Look for more opportunities to help others with their questions")
  }
  if (commonFlags.includes('inappropriate_language')) {
    recommendations.push("Focus on expressing frustration in constructive ways")
  }
  
  return {
    totalMessages: messages.length,
    flaggedMessages: flaggedMessages.length,
    kindnessAverage,
    helpfulnessAverage,
    commonFlags,
    improvement: 0, // Calculate based on historical data
    recommendations
  }
}
