'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Target, 
  CheckCircle, 
  Clock, 
  Flame,
  Brain,
  Users,
  Heart,
  TrendingUp,
  Focus,
  Play,
  Pause,
  RotateCcw,
  Award,
  Crown
} from 'lucide-react'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Card } from '@/components/ui/card'

// Types
interface QuantumTask {
  id: string
  title: string
  description: string
  type: 'daily' | 'weekly' | 'challenge' | 'collaborative'
  difficulty: 'easy' | 'medium' | 'hard' | 'legendary'
  progress: number
  maxProgress: number
  rewards: {
    ce: number
    qs: number
    special?: string
  }
  timeLimit?: number
  completed: boolean
  icon: any
  color: string
}

interface QuantumTasksPanelProps {
  className?: string
}

// Task data
const generateTasks = (): QuantumTask[] => [
  {
    id: 'daily-1',
    title: 'Neural Synchronization',
    description: 'Complete 3 Learning Node sessions',
    type: 'daily',
    difficulty: 'easy',
    progress: 2,
    maxProgress: 3,
    rewards: { ce: 15, qs: 10 },
    completed: false,
    icon: Brain,
    color: 'text-neural-cyan'
  },
  {
    id: 'daily-2',
    title: 'Consciousness Assistance',
    description: 'Help 2 NanoArchitects with their tasks',
    type: 'daily',
    difficulty: 'medium',
    progress: 1,
    maxProgress: 2,
    rewards: { ce: 20, qs: 15 },
    completed: false,
    icon: Users,
    color: 'text-quantum-purple'
  },
  {
    id: 'daily-3',
    title: 'Quantum Meditation',
    description: 'Complete a 10-minute meditation session',
    type: 'daily',
    difficulty: 'easy',
    progress: 10,
    maxProgress: 10,
    rewards: { ce: 12, qs: 18 },
    completed: true,
    icon: Heart,
    color: 'text-green-500'
  },
  {
    id: 'weekly-1',
    title: 'Timeline Evolution',
    description: 'Contribute 100 CE to Timeline progress',
    type: 'weekly',
    difficulty: 'hard',
    progress: 67,
    maxProgress: 100,
    rewards: { ce: 50, qs: 40, special: 'DNA Fragment' },
    completed: false,
    icon: TrendingUp,
    color: 'text-flame-orange'
  },
  {
    id: 'challenge-1',
    title: 'Quantum Mastery',
    description: 'Achieve 95%+ stability for 24 hours',
    type: 'challenge',
    difficulty: 'legendary',
    progress: 18,
    maxProgress: 24,
    rewards: { ce: 100, qs: 100, special: 'Quantum Crown' },
    timeLimit: 6, // hours remaining
    completed: false,
    icon: Crown,
    color: 'text-yellow-400'
  }
]

// Task Item Component
function TaskItem({ task, onComplete }: { task: QuantumTask; onComplete: (taskId: string) => void }) {
  const [isHovered, setIsHovered] = useState(false)
  
  const getDifficultyColor = () => {
    switch (task.difficulty) {
      case 'easy': return 'text-green-500'
      case 'medium': return 'text-yellow-500'
      case 'hard': return 'text-orange-500'
      case 'legendary': return 'text-purple-500'
      default: return 'text-white/60'
    }
  }
  
  const getTypeIcon = () => {
    switch (task.type) {
      case 'daily': return <Clock className="w-3 h-3" />
      case 'weekly': return <Target className="w-3 h-3" />
      case 'challenge': return <Flame className="w-3 h-3" />
      case 'collaborative': return <Users className="w-3 h-3" />
      default: return <Target className="w-3 h-3" />
    }
  }
  
  const progressPercentage = (task.progress / task.maxProgress) * 100
  
  return (
    <motion.div
      className={`p-3 rounded-lg border transition-all cursor-pointer ${
        task.completed 
          ? 'border-green-500/30 bg-green-500/10' 
          : 'border-white/10 bg-white/5 hover:border-white/20'
      }`}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={() => !task.completed && onComplete(task.id)}
    >
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2">
          <task.icon className={`w-4 h-4 ${task.color}`} />
          <div className="flex items-center gap-1">
            {getTypeIcon()}
            <span className={`text-xs font-medium ${getDifficultyColor()}`}>
              {task.difficulty}
            </span>
          </div>
        </div>
        
        {task.completed ? (
          <CheckCircle className="w-4 h-4 text-green-500" />
        ) : (
          <div className="text-xs text-white/60">
            {task.progress}/{task.maxProgress}
          </div>
        )}
      </div>
      
      <h4 className="text-sm font-bold text-white/90 mb-1">{task.title}</h4>
      <p className="text-xs text-white/70 mb-3">{task.description}</p>
      
      <div className="space-y-2">
        <Progress 
          value={progressPercentage} 
          variant={task.completed ? "quantum" : "gaming"} 
          className="h-1.5"
          glow={isHovered}
        />
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 text-xs">
            <span className="text-neural-cyan">+{task.rewards.ce} CE</span>
            <span className="text-quantum-purple">+{task.rewards.qs} QS</span>
            {task.rewards.special && (
              <span className="text-yellow-400">+{task.rewards.special}</span>
            )}
          </div>
          
          {task.timeLimit && !task.completed && (
            <div className="text-xs text-flame-orange">
              {task.timeLimit}h left
            </div>
          )}
        </div>
      </div>
    </motion.div>
  )
}

// Focus Stream Component
function FocusStream({ isActive, onToggle }: { isActive: boolean; onToggle: () => void }) {
  const [timeElapsed, setTimeElapsed] = useState(0)
  const [sessionGoal] = useState(25 * 60) // 25 minutes in seconds
  
  useEffect(() => {
    if (!isActive) return
    
    const interval = setInterval(() => {
      setTimeElapsed(prev => prev + 1)
    }, 1000)
    
    return () => clearInterval(interval)
  }, [isActive])
  
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }
  
  const progress = (timeElapsed / sessionGoal) * 100
  
  return (
    <Card variant="quantum" className="p-4 gaming-panel-inner">
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-3">
          <Focus className="w-5 h-5 text-neural-cyan" />
          <h4 className="text-sm font-bold text-neural-cyan">Focus Stream</h4>
        </div>
        
        <div className="text-2xl font-mono font-bold text-white mb-2">
          {formatTime(timeElapsed)}
        </div>
        
        <Progress value={progress} variant="quantum" glow className="mb-4" />
        
        <div className="flex justify-center gap-2">
          <Button
            size="sm"
            variant={isActive ? "destructive" : "quantum"}
            onClick={onToggle}
          >
            {isActive ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
          </Button>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setTimeElapsed(0)}
          >
            <RotateCcw className="w-4 h-4" />
          </Button>
        </div>
        
        {isActive && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mt-3 text-xs text-white/60"
          >
            Deep focus mode active...
          </motion.div>
        )}
      </div>
    </Card>
  )
}

// Main Quantum Tasks Panel Component
export default function QuantumTasksPanel({ className }: QuantumTasksPanelProps) {
  const [tasks, setTasks] = useState<QuantumTask[]>(generateTasks())
  const [focusStreamActive, setFocusStreamActive] = useState(false)
  const [showCompleted, setShowCompleted] = useState(false)
  const [streak, setStreak] = useState(7)
  
  const completedTasks = tasks.filter(task => task.completed).length
  const totalTasks = tasks.length
  const dailyTasks = tasks.filter(task => task.type === 'daily')
  const completedDaily = dailyTasks.filter(task => task.completed).length
  
  const handleTaskComplete = (taskId: string) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { ...task, completed: true, progress: task.maxProgress }
        : task
    ))
  }
  
  const activeTasks = showCompleted ? tasks : tasks.filter(task => !task.completed)
  
  return (
    <div className={`h-full flex flex-col ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-flame-orange/20">
        <div className="flex items-center gap-2">
          <Target className="w-5 h-5 text-flame-orange" />
          <h3 className="text-lg font-orbitron font-bold text-flame-orange">Quantum Tasks</h3>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="text-xs text-white/60">
            {completedDaily}/{dailyTasks.length} daily
          </div>
          <div className="flex items-center gap-1">
            <Flame className="w-3 h-3 text-flame-orange" />
            <span className="text-xs font-bold text-flame-orange">{streak}</span>
          </div>
        </div>
      </div>
      
      {/* Progress Overview */}
      <div className="p-4 border-b border-white/10">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-white/80">Daily Progress</span>
          <span className="text-sm text-flame-orange font-bold">
            {completedTasks}/{totalTasks}
          </span>
        </div>
        <Progress 
          value={(completedTasks / totalTasks) * 100} 
          variant="gaming" 
          glow 
        />
      </div>
      
      {/* Focus Stream */}
      <div className="p-4 border-b border-white/10">
        <FocusStream 
          isActive={focusStreamActive}
          onToggle={() => setFocusStreamActive(!focusStreamActive)}
        />
      </div>
      
      {/* Task List */}
      <div className="flex-1 overflow-hidden">
        <div className="flex items-center justify-between p-4 pb-2">
          <h4 className="text-sm font-bold text-white/80">Active Tasks</h4>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setShowCompleted(!showCompleted)}
            className="text-xs"
          >
            {showCompleted ? 'Hide' : 'Show'} Completed
          </Button>
        </div>
        
        <div className="px-4 pb-4 space-y-3 overflow-y-auto max-h-64">
          <AnimatePresence mode="popLayout">
            {activeTasks.map((task) => (
              <TaskItem
                key={task.id}
                task={task}
                onComplete={handleTaskComplete}
              />
            ))}
          </AnimatePresence>
          
          {activeTasks.length === 0 && (
            <div className="text-center py-8 text-white/40">
              <Award className="w-8 h-8 mx-auto mb-2" />
              <p>All tasks completed!</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
