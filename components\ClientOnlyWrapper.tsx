"use client"

import { useState, useEffect, ReactNode } from 'react'

interface ClientOnlyWrapperProps {
  children: ReactNode
  fallback?: ReactNode
}

/**
 * A wrapper component that only renders its children on the client side.
 * This prevents hydration mismatches and localStorage/window access errors during SSR.
 */
export function ClientOnlyWrapper({ children, fallback = null }: ClientOnlyWrapperProps) {
  const [hasMounted, setHasMounted] = useState(false)

  useEffect(() => {
    setHasMounted(true)
  }, [])

  if (!hasMounted) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

/**
 * A loading fallback component for client-only content
 */
export function ClientOnlyLoadingFallback() {
  return (
    <div className="flex items-center justify-center p-8 min-h-[200px]">
      <div className="text-center">
        <div className="w-8 h-8 border-2 border-cyan-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-gray-400">Loading...</p>
      </div>
    </div>
  )
}
