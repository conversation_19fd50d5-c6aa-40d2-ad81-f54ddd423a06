import * as THREE from 'three'

/**
 * Utility functions for proper Three.js memory management and cleanup
 */

export class ThreeCleanupManager {
  private static disposedObjects = new WeakSet()

  /**
   * Dispose of a Three.js object and all its resources
   */
  static dispose(object: any): void {
    if (!object || this.disposedObjects.has(object)) return

    // Mark as disposed to prevent double disposal
    this.disposedObjects.add(object)

    // Dispose geometry
    if (object.geometry) {
      object.geometry.dispose()
    }

    // Dispose material(s)
    if (object.material) {
      if (Array.isArray(object.material)) {
        object.material.forEach((material: THREE.Material) => {
          this.disposeMaterial(material)
        })
      } else {
        this.disposeMaterial(object.material)
      }
    }

    // Dispose textures
    if (object.texture) {
      object.texture.dispose()
    }

    // Dispose children recursively
    if (object.children) {
      object.children.forEach((child: any) => {
        this.dispose(child)
      })
    }

    // Remove from parent
    if (object.parent) {
      object.parent.remove(object)
    }
  }

  /**
   * Dispose of a material and its textures
   */
  private static disposeMaterial(material: THREE.Material): void {
    if (this.disposedObjects.has(material)) return
    this.disposedObjects.add(material)

    // Dispose textures
    const materialWithTextures = material as any
    Object.keys(materialWithTextures).forEach(key => {
      const value = materialWithTextures[key]
      if (value && value.isTexture) {
        value.dispose()
      }
    })

    material.dispose()
  }

  /**
   * Dispose of a scene and all its contents
   */
  static disposeScene(scene: THREE.Scene): void {
    scene.traverse((object) => {
      this.dispose(object)
    })
    scene.clear()
  }

  /**
   * Dispose of a renderer
   */
  static disposeRenderer(renderer: THREE.WebGLRenderer): void {
    renderer.dispose()
    renderer.forceContextLoss()
  }

  /**
   * Clean up instanced mesh
   */
  static disposeInstancedMesh(mesh: THREE.InstancedMesh): void {
    if (this.disposedObjects.has(mesh)) return
    this.disposedObjects.add(mesh)

    mesh.dispose()
    this.dispose(mesh)
  }

  /**
   * Clean up buffer attributes
   */
  static disposeBufferAttribute(attribute: THREE.BufferAttribute): void {
    if (this.disposedObjects.has(attribute)) return
    this.disposedObjects.add(attribute)
    
    // Clear the array data
    if (attribute.array) {
      (attribute.array as any) = null
    }
  }

  /**
   * Memory usage monitoring
   */
  static getMemoryInfo(): any {
    const renderer = new THREE.WebGLRenderer()
    const info = renderer.info
    renderer.dispose()
    
    return {
      geometries: info.memory.geometries,
      textures: info.memory.textures,
      programs: info.programs?.length || 0
    }
  }
}

/**
 * React hook for Three.js cleanup
 */
export function useThreeCleanup() {
  const cleanupRefs = new Set<any>()

  const addCleanupRef = (ref: any) => {
    if (ref) {
      cleanupRefs.add(ref)
    }
  }

  const cleanup = () => {
    cleanupRefs.forEach(ref => {
      if (ref.current) {
        ThreeCleanupManager.dispose(ref.current)
      }
    })
    cleanupRefs.clear()
  }

  return { addCleanupRef, cleanup }
}

/**
 * Performance monitoring utilities
 */
export class PerformanceMonitor {
  private frameCount = 0
  private lastTime = performance.now()
  private fps = 60
  private memoryUsage = 0

  update(): { fps: number; memory: number } {
    this.frameCount++
    const currentTime = performance.now()

    if (currentTime - this.lastTime >= 1000) {
      this.fps = this.frameCount
      this.frameCount = 0
      this.lastTime = currentTime

      // Update memory usage if available
      if ((performance as any).memory) {
        this.memoryUsage = (performance as any).memory.usedJSHeapSize / 1024 / 1024
      }
    }

    return {
      fps: this.fps,
      memory: this.memoryUsage
    }
  }

  getFPS(): number {
    return this.fps
  }

  getMemoryUsage(): number {
    return this.memoryUsage
  }

  getQualityLevel(): 'low' | 'medium' | 'high' {
    if (this.fps < 25) return 'low'
    if (this.fps < 45) return 'medium'
    return 'high'
  }
}

/**
 * Object pooling for frequently created/destroyed objects
 */
export class ObjectPool<T> {
  private pool: T[] = []
  private createFn: () => T
  private resetFn: (obj: T) => void

  constructor(createFn: () => T, resetFn: (obj: T) => void, initialSize = 10) {
    this.createFn = createFn
    this.resetFn = resetFn

    // Pre-populate pool
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(this.createFn())
    }
  }

  get(): T {
    if (this.pool.length > 0) {
      const obj = this.pool.pop()!
      this.resetFn(obj)
      return obj
    }
    return this.createFn()
  }

  release(obj: T): void {
    this.resetFn(obj)
    this.pool.push(obj)
  }

  clear(): void {
    this.pool.forEach(obj => {
      if (obj && typeof (obj as any).dispose === 'function') {
        (obj as any).dispose()
      }
    })
    this.pool = []
  }
}

/**
 * Geometry pool for common shapes
 */
export const GeometryPool = {
  sphereGeometry: new ObjectPool(
    () => new THREE.SphereGeometry(1, 8, 8),
    (geo) => geo.scale(1, 1, 1),
    5
  ),
  
  boxGeometry: new ObjectPool(
    () => new THREE.BoxGeometry(1, 1, 1),
    (geo) => geo.scale(1, 1, 1),
    5
  ),

  cylinderGeometry: new ObjectPool(
    () => new THREE.CylinderGeometry(1, 1, 1, 8),
    (geo) => geo.scale(1, 1, 1),
    3
  )
}

/**
 * Material pool for common materials
 */
export const MaterialPool = {
  basicMaterial: new ObjectPool(
    () => new THREE.MeshBasicMaterial(),
    (mat) => {
      mat.color.setHex(0xffffff)
      mat.transparent = false
      mat.opacity = 1
    },
    5
  ),

  standardMaterial: new ObjectPool(
    () => new THREE.MeshStandardMaterial(),
    (mat) => {
      mat.color.setHex(0xffffff)
      mat.emissive.setHex(0x000000)
      mat.metalness = 0
      mat.roughness = 1
      mat.transparent = false
      mat.opacity = 1
    },
    5
  )
}
