"use client"

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  X, 
  Dna, 
  User,
  <PERSON><PERSON><PERSON>,
  ChevronRight,
  Trophy,
  Zap
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { VisuallyHidden } from '@/components/ui/visually-hidden'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface GamifiedSignUpModalProps {
  isOpen: boolean
  onClose: () => void
  onComplete: (userData: any) => void
}

interface SignUpData {
  username: string
  email: string
  age: string
  parentEmail: string
  selectedDNA: string
  interests: string[]
}

export function GamifiedSignUpModal({ isOpen, onClose, onComplete }: GamifiedSignUpModalProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [signUpData, setSignUpData] = useState<SignUpData>({
    username: '',
    email: '',
    age: '',
    parentEmail: '',
    selectedDNA: '',
    interests: []
  })

  const dnaFragments = [
    {
      id: 'coder',
      name: 'Code Weaver',
      description: 'Master of algorithms and logic',
      color: 'from-blue-500 to-cyan-500',
      icon: '💻',
      traits: ['Logical Thinking', 'Problem Solving', 'Pattern Recognition']
    },
    {
      id: 'hacker',
      name: 'Cyber Guardian',
      description: 'Protector of digital realms',
      color: 'from-red-500 to-orange-500',
      icon: '🛡️',
      traits: ['Security Mindset', 'Ethical Thinking', 'System Analysis']
    },
    {
      id: 'creator',
      name: 'Digital Artist',
      description: 'Builder of interactive experiences',
      color: 'from-purple-500 to-pink-500',
      icon: '🎨',
      traits: ['Creative Vision', 'User Experience', 'Innovation']
    },
    {
      id: 'explorer',
      name: 'Tech Explorer',
      description: 'Curious about all things digital',
      color: 'from-green-500 to-emerald-500',
      icon: '🚀',
      traits: ['Curiosity', 'Adaptability', 'Broad Knowledge']
    }
  ]

  const interests = [
    'Game Development', 'Web Design', 'Cybersecurity', 'AI & Machine Learning',
    'Mobile Apps', 'Robotics', 'Data Science', 'Digital Art', 'Music Tech', 'VR/AR'
  ]

  const steps = [
    { title: 'Choose Your DNA', description: 'Select your learning archetype' },
    { title: 'Create Identity', description: 'Set up your ByteHero profile' },
    { title: 'Select Interests', description: 'Pick your favorite tech domains' },
    { title: 'Final Setup', description: 'Complete your registration' }
  ]

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleComplete = () => {
    onComplete(signUpData)
  }

  const toggleInterest = (interest: string) => {
    setSignUpData(prev => ({
      ...prev,
      interests: prev.interests.includes(interest)
        ? prev.interests.filter(i => i !== interest)
        : [...prev.interests, interest]
    }))
  }

  const isStepValid = () => {
    switch (currentStep) {
      case 0: return signUpData.selectedDNA !== ''
      case 1: return signUpData.username !== '' && signUpData.email !== '' && signUpData.age !== ''
      case 2: return signUpData.interests.length > 0
      case 3: return true
      default: return false
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-black/95 border-gray-800 text-white max-w-4xl max-h-[90vh] overflow-y-auto">
        <VisuallyHidden>
          <DialogTitle>ByteHero Registration</DialogTitle>
          <DialogDescription>
            Complete your registration to join the ByteHero learning community
          </DialogDescription>
        </VisuallyHidden>
        <div className="relative">
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-gray-400 hover:text-white z-10"
          >
            <X className="w-6 h-6" />
          </button>

          {/* Header */}
          <div className="text-center mb-8 pt-8">
            <motion.h2
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-3xl font-bold bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent mb-2"
            >
              Welcome to ByteHero
            </motion.h2>
            <p className="text-gray-400">Your learning DNA is activating...</p>
          </div>

          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              {steps.map((step, index) => (
                <div key={index} className="flex items-center">
                  <motion.div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                      index <= currentStep
                        ? 'bg-gradient-to-r from-cyan-500 to-purple-500 text-white'
                        : 'bg-gray-700 text-gray-400'
                    }`}
                    animate={{ scale: index === currentStep ? 1.1 : 1 }}
                  >
                    {index + 1}
                  </motion.div>
                  {index < steps.length - 1 && (
                    <div className={`w-16 h-1 mx-2 ${
                      index < currentStep ? 'bg-gradient-to-r from-cyan-500 to-purple-500' : 'bg-gray-700'
                    }`} />
                  )}
                </div>
              ))}
            </div>
            <div className="text-center">
              <h3 className="text-lg font-semibold text-white">{steps[currentStep].title}</h3>
              <p className="text-sm text-gray-400">{steps[currentStep].description}</p>
            </div>
          </div>

          {/* Step Content */}
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="min-h-[400px]"
            >
              {/* Step 0: DNA Selection */}
              {currentStep === 0 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <Dna className="w-12 h-12 text-cyan-400 mx-auto mb-4" />
                    <h4 className="text-xl font-bold text-white mb-2">Choose Your Learning DNA</h4>
                    <p className="text-gray-400">
                      Your DNA fragment determines your starting traits and learning style. Don&apos;t worry - you can evolve and unlock all abilities!
                    </p>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {dnaFragments.map((dna) => (
                      <motion.div
                        key={dna.id}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className={`cursor-pointer ${
                          signUpData.selectedDNA === dna.id ? 'ring-2 ring-cyan-400' : ''
                        }`}
                        onClick={() => setSignUpData(prev => ({ ...prev, selectedDNA: dna.id }))}
                      >
                        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl hover:bg-black/60 transition-all duration-300">
                          <CardContent className="p-6">
                            <div className="text-center">
                              <div className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-r ${dna.color} rounded-full flex items-center justify-center text-2xl`}>
                                {dna.icon}
                              </div>
                              <h5 className="text-lg font-bold text-white mb-2">{dna.name}</h5>
                              <p className="text-gray-400 text-sm mb-4">{dna.description}</p>
                              <div className="space-y-1">
                                {dna.traits.map((trait, i) => (
                                  <Badge key={i} className={`bg-gradient-to-r ${dna.color} text-white text-xs`}>
                                    {trait}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}

              {/* Step 1: Identity Creation */}
              {currentStep === 1 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <User className="w-12 h-12 text-purple-400 mx-auto mb-4" />
                    <h4 className="text-xl font-bold text-white mb-2">Create Your ByteHero Identity</h4>
                    <p className="text-gray-400">
                      Choose a username that represents your digital persona in the ByteVerse
                    </p>
                  </div>
                  
                  <div className="max-w-md mx-auto space-y-4">
                    <div>
                      <Label htmlFor="username" className="text-white">Username</Label>
                      <Input
                        id="username"
                        value={signUpData.username}
                        onChange={(e) => setSignUpData(prev => ({ ...prev, username: e.target.value }))}
                        placeholder="Enter your ByteHero name"
                        className="bg-gray-900/50 border-gray-700 text-white"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="email" className="text-white">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={signUpData.email}
                        onChange={(e) => setSignUpData(prev => ({ ...prev, email: e.target.value }))}
                        placeholder="<EMAIL>"
                        className="bg-gray-900/50 border-gray-700 text-white"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="age" className="text-white">Age</Label>
                      <Input
                        id="age"
                        type="number"
                        value={signUpData.age}
                        onChange={(e) => setSignUpData(prev => ({ ...prev, age: e.target.value }))}
                        placeholder="Your age"
                        className="bg-gray-900/50 border-gray-700 text-white"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Step 2: Interest Selection */}
              {currentStep === 2 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <Sparkles className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
                    <h4 className="text-xl font-bold text-white mb-2">Select Your Interests</h4>
                    <p className="text-gray-400">
                      Choose the tech domains that excite you most. This helps us personalize your learning journey.
                    </p>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {interests.map((interest) => (
                      <motion.button
                        key={interest}
                        onClick={() => toggleInterest(interest)}
                        className={`p-3 rounded-lg text-sm font-medium transition-all duration-300 ${
                          signUpData.interests.includes(interest)
                            ? 'bg-gradient-to-r from-cyan-500 to-purple-500 text-white'
                            : 'bg-gray-800/50 text-gray-300 hover:bg-gray-700/50'
                        }`}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        {interest}
                      </motion.button>
                    ))}
                  </div>
                  
                  <div className="text-center text-sm text-gray-400">
                    Selected: {signUpData.interests.length} interests
                  </div>
                </div>
              )}

              {/* Step 3: Final Setup */}
              {currentStep === 3 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <Trophy className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
                    <h4 className="text-xl font-bold text-white mb-2">Almost Ready!</h4>
                    <p className="text-gray-400">
                      Final step: parent/guardian email for safety notifications (optional for 13+)
                    </p>
                  </div>
                  
                  <div className="max-w-md mx-auto space-y-4">
                    <div>
                      <Label htmlFor="parentEmail" className="text-white">Parent/Guardian Email (Optional)</Label>
                      <Input
                        id="parentEmail"
                        type="email"
                        value={signUpData.parentEmail}
                        onChange={(e) => setSignUpData(prev => ({ ...prev, parentEmail: e.target.value }))}
                        placeholder="<EMAIL>"
                        className="bg-gray-900/50 border-gray-700 text-white"
                      />
                    </div>
                    
                    {/* Summary */}
                    <div className="bg-gray-900/50 rounded-lg p-4 space-y-2">
                      <h5 className="font-semibold text-white">Your ByteHero Profile:</h5>
                      <div className="text-sm text-gray-300 space-y-1">
                        <p><strong>Username:</strong> {signUpData.username}</p>
                        <p><strong>DNA Type:</strong> {dnaFragments.find(d => d.id === signUpData.selectedDNA)?.name}</p>
                        <p><strong>Interests:</strong> {signUpData.interests.slice(0, 3).join(', ')}{signUpData.interests.length > 3 ? '...' : ''}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </motion.div>
          </AnimatePresence>

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8">
            <Button
              onClick={handleBack}
              variant="outline"
              disabled={currentStep === 0}
              className="border-gray-700 text-gray-300 hover:bg-gray-800/50"
            >
              Back
            </Button>
            
            <Button
              onClick={currentStep === steps.length - 1 ? handleComplete : handleNext}
              disabled={!isStepValid()}
              className="bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 text-white"
            >
              {currentStep === steps.length - 1 ? (
                <>
                  <Zap className="w-4 h-4 mr-2" />
                  Collapse Into ByteVerse
                </>
              ) : (
                <>
                  Next
                  <ChevronRight className="w-4 h-4 ml-2" />
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
