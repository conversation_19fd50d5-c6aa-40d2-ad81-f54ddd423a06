"use client"

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Globe, Users, Sparkles, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { EpochTownsNavigation } from './EpochTownsNavigation'

interface EpochTownsFloatingButtonProps {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
  showOnPages?: string[] // Array of page paths where button should be visible
  hideOnPages?: string[] // Array of page paths where button should be hidden
  className?: string
}

export function EpochTownsFloatingButton({
  position = 'bottom-right',
  showOnPages,
  hideOnPages,
  className = ""
}: EpochTownsFloatingButtonProps) {
  const [isNavigationOpen, setIsNavigationOpen] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)

  // Mock data - in real app this would come from context/state
  const townStats = {
    generation: new Date().toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
    citizens: 42,
    level: 8,
    dna: 'EWBCSH',
    hasNotifications: true,
    notificationCount: 3
  }

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-left':
        return 'bottom-6 left-6'
      case 'top-right':
        return 'top-6 right-6'
      case 'top-left':
        return 'top-6 left-6'
      default:
        return 'bottom-6 right-6'
    }
  }

  const handleNavigate = (path: string) => {
    // Handle navigation based on path
    if (path.startsWith('/dashboard')) {
      // Parse query parameters and navigate to dashboard section
      const url = new URL(path, window.location.origin)
      const section = url.searchParams.get('section')
      const view = url.searchParams.get('view')
      
      if (section === 'epochtowns') {
        window.location.href = '/dashboard'
        // In a real app, you'd use router and set the active section
        setTimeout(() => {
          // Trigger dashboard section change
          const event = new CustomEvent('dashboardSectionChange', { 
            detail: { section, view } 
          })
          window.dispatchEvent(event)
        }, 100)
      }
    } else {
      window.location.href = path
    }
  }

  // Check if button should be visible on current page
  const currentPath = typeof window !== 'undefined' ? window.location.pathname : ''
  
  if (hideOnPages && hideOnPages.includes(currentPath)) {
    return null
  }
  
  if (showOnPages && !showOnPages.includes(currentPath)) {
    return null
  }

  return (
    <>
      <motion.div
        className={`fixed ${getPositionClasses()} z-40 ${className}`}
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ delay: 1, duration: 0.3 }}
      >
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: 20 }}
              className="mb-4 p-4 bg-gray-900/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl"
            >
              <div className="space-y-3 min-w-[200px]">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-white text-sm">Your Town</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsExpanded(false)}
                    className="w-6 h-6 p-0"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
                
                <div className="space-y-2 text-xs text-gray-300">
                  <div className="flex justify-between">
                    <span>Generation:</span>
                    <span className="text-cyan-400">{townStats.generation}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Citizens:</span>
                    <span className="text-green-400">{townStats.citizens}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Town Level:</span>
                    <span className="text-yellow-400">{townStats.level}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>DNA:</span>
                    <span className="text-purple-400 font-mono">{townStats.dna}</span>
                  </div>
                </div>

                <div className="pt-2 border-t border-gray-700">
                  <Button
                    size="sm"
                    onClick={() => setIsNavigationOpen(true)}
                    className="w-full bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 text-xs"
                  >
                    <Globe className="w-3 h-3 mr-1" />
                    Enter Town
                  </Button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <motion.div
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="relative"
              >
                <Button
                  onClick={() => {
                    if (isExpanded) {
                      setIsNavigationOpen(true)
                    } else {
                      setIsExpanded(true)
                    }
                  }}
                  className="w-14 h-14 rounded-full bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden"
                >
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  >
                    <Globe className="w-6 h-6 text-white" />
                  </motion.div>
                  
                  {/* Notification indicator */}
                  {townStats.hasNotifications && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute -top-1 -right-1"
                    >
                      <Badge className="bg-red-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center p-0">
                        {townStats.notificationCount}
                      </Badge>
                    </motion.div>
                  )}

                  {/* Pulse effect */}
                  <motion.div
                    className="absolute inset-0 rounded-full bg-gradient-to-r from-cyan-500 to-purple-500"
                    animate={{ scale: [1, 1.2, 1], opacity: [0.5, 0, 0.5] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                </Button>
              </motion.div>
            </TooltipTrigger>
            <TooltipContent side="left" className="bg-gray-900 border-gray-700">
              <div className="text-center">
                <p className="font-semibold text-white">Epoch Towns</p>
                <p className="text-xs text-gray-400">Your collaborative world</p>
                {townStats.hasNotifications && (
                  <p className="text-xs text-cyan-400 mt-1">
                    {townStats.notificationCount} new activities
                  </p>
                )}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {/* Quick action buttons when expanded */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="absolute bottom-16 right-0 flex flex-col gap-2"
            >
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="sm"
                      onClick={() => handleNavigate('/social-demo')}
                      className="w-10 h-10 rounded-full bg-yellow-500 hover:bg-yellow-600"
                    >
                      <Users className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="left">
                    <p>Social Hub</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="sm"
                      onClick={() => handleNavigate('/dashboard?section=epochtowns&view=town')}
                      className="w-10 h-10 rounded-full bg-green-500 hover:bg-green-600"
                    >
                      <Sparkles className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="left">
                    <p>3D World</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Navigation Modal */}
      <EpochTownsNavigation
        isOpen={isNavigationOpen}
        onClose={() => {
          setIsNavigationOpen(false)
          setIsExpanded(false)
        }}
        onNavigate={handleNavigate}
      />
    </>
  )
}

export default EpochTownsFloatingButton
