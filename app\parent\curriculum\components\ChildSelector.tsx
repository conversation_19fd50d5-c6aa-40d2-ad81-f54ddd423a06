'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  ChevronDown, 
  Users, 
  User, 
  Star, 
  Clock, 
  Target,
  Copy,
  Plus
} from 'lucide-react'
import { useCurriculumPlanner } from './CurriculumPlannerProvider'

export function ChildSelector() {
  const { state, dispatch, getSelectedChild } = useCurriculumPlanner()
  const [isOpen, setIsOpen] = useState(false)
  const [showMultiSelect, setShowMultiSelect] = useState(false)
  
  const selectedChild = getSelectedChild()

  const handleChildSelect = (childId: string) => {
    dispatch({ type: 'SELECT_CHILD', payload: childId })
    setIsOpen(false)
  }

  const toggleMultiSelect = () => {
    setShowMultiSelect(!showMultiSelect)
    setIsOpen(false)
  }

  return (
    <div className="relative">
      {/* Main Selector Button */}
      <Button
        variant="outline"
        className="w-full justify-between bg-gray-800/50 border-gray-700/50 text-white hover:bg-gray-700/50"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center gap-3">
          {selectedChild ? (
            <>
              <span className="text-2xl">{selectedChild.avatar}</span>
              <div className="text-left">
                <div className="font-medium">{selectedChild.name}</div>
                <div className="text-xs text-gray-400">
                  Age {selectedChild.age} • Grade {selectedChild.gradeLevel}
                </div>
              </div>
            </>
          ) : (
            <>
              <User className="w-5 h-5 text-gray-400" />
              <span>Select a child</span>
            </>
          )}
        </div>
        <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </Button>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full left-0 right-0 z-50 mt-2"
          >
            <Card className="bg-gray-900/95 border-gray-700/50 backdrop-blur-xl">
              <CardContent className="p-2">
                {/* Individual Children */}
                <div className="space-y-1">
                  {state.children.map((child) => (
                    <motion.button
                      key={child.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className={`w-full p-3 rounded-lg text-left transition-all ${
                        selectedChild?.id === child.id
                          ? 'bg-cyan-500/20 border border-cyan-500/30'
                          : 'hover:bg-gray-800/50'
                      }`}
                      onClick={() => handleChildSelect(child.id)}
                    >
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{child.avatar}</span>
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-white">{child.name}</span>
                            {child.progress.currentStreak > 7 && (
                              <Badge className="bg-yellow-500/20 text-yellow-400 text-xs">
                                <Star className="w-3 h-3 mr-1" />
                                {child.progress.currentStreak} day streak
                              </Badge>
                            )}
                          </div>
                          <div className="text-xs text-gray-400 mt-1">
                            Age {child.age} • Grade {child.gradeLevel} • {child.progress.totalHours}h total
                          </div>
                          
                          {/* Quick Stats */}
                          <div className="flex items-center gap-4 mt-2">
                            <div className="flex items-center gap-1 text-xs text-gray-500">
                              <Target className="w-3 h-3" />
                              {child.progress.completedLessons.length} lessons
                            </div>
                            <div className="flex items-center gap-1 text-xs text-gray-500">
                              <Clock className="w-3 h-3" />
                              {child.preferences.dailyTimeLimit}min/day
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.button>
                  ))}
                </div>

                {/* Multi-Child Options */}
                <div className="border-t border-gray-700/50 mt-2 pt-2">
                  <button
                    className="w-full p-3 rounded-lg text-left hover:bg-gray-800/50 transition-all"
                    onClick={toggleMultiSelect}
                  >
                    <div className="flex items-center gap-3">
                      <Users className="w-5 h-5 text-purple-400" />
                      <div>
                        <div className="font-medium text-white">All Children</div>
                        <div className="text-xs text-gray-400">
                          Plan for multiple children together
                        </div>
                      </div>
                    </div>
                  </button>

                  <button
                    className="w-full p-3 rounded-lg text-left hover:bg-gray-800/50 transition-all"
                    onClick={() => {
                      // Handle copy plan functionality
                      setIsOpen(false)
                    }}
                  >
                    <div className="flex items-center gap-3">
                      <Copy className="w-5 h-5 text-green-400" />
                      <div>
                        <div className="font-medium text-white">Copy Plan</div>
                        <div className="text-xs text-gray-400">
                          Copy schedule between children
                        </div>
                      </div>
                    </div>
                  </button>

                  <button
                    className="w-full p-3 rounded-lg text-left hover:bg-gray-800/50 transition-all"
                    onClick={() => {
                      // Handle add child functionality
                      setIsOpen(false)
                    }}
                  >
                    <div className="flex items-center gap-3">
                      <Plus className="w-5 h-5 text-cyan-400" />
                      <div>
                        <div className="font-medium text-white">Add Child</div>
                        <div className="text-xs text-gray-400">
                          Add a new child profile
                        </div>
                      </div>
                    </div>
                  </button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Multi-Child Selection Modal */}
      <AnimatePresence>
        {showMultiSelect && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowMultiSelect(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-900 border border-gray-700 rounded-xl p-6 max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-xl font-bold text-white mb-4">Multi-Child Planning</h3>
              
              <div className="space-y-3 mb-6">
                {state.children.map((child) => (
                  <div
                    key={child.id}
                    className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg"
                  >
                    <input
                      type="checkbox"
                      id={`child-${child.id}`}
                      className="w-4 h-4 text-cyan-500 bg-gray-700 border-gray-600 rounded focus:ring-cyan-500"
                      defaultChecked
                    />
                    <label htmlFor={`child-${child.id}`} className="flex items-center gap-3 flex-1 cursor-pointer">
                      <span className="text-xl">{child.avatar}</span>
                      <div>
                        <div className="font-medium text-white">{child.name}</div>
                        <div className="text-xs text-gray-400">Age {child.age}</div>
                      </div>
                    </label>
                  </div>
                ))}
              </div>

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() => setShowMultiSelect(false)}
                >
                  Cancel
                </Button>
                <Button
                  className="flex-1 bg-gradient-to-r from-cyan-500 to-blue-500"
                  onClick={() => {
                    // Handle multi-child planning
                    setShowMultiSelect(false)
                  }}
                >
                  Plan Together
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
