'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Construction, Sparkles } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

// Placeholder component for components under development
function PlaceholderComponent({ title, description }: { title: string; description: string }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex items-center justify-center h-full min-h-[400px]"
    >
      <Card className="bg-gray-900/50 border-gray-700 max-w-md">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-cyan-400/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Construction className="w-8 h-8 text-cyan-400" />
          </div>
          <CardTitle className="text-cyan-400">{title}</CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-white/60 mb-4">{description}</p>
          <div className="flex items-center justify-center gap-2 text-yellow-400">
            <Sparkles className="w-4 h-4" />
            <span className="text-sm">Coming Soon</span>
            <Sparkles className="w-4 h-4" />
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

// DNA Core component placeholder
export default function DNACore() {
  return (
    <PlaceholderComponent
      title="DNA Core"
      description="Advanced genetic learning algorithm visualization and interaction interface."
    />
  )
}

// Timeline Map component placeholder
export function TimelineMap() {
  return (
    <PlaceholderComponent
      title="Timeline Map"
      description="Interactive 3D timeline visualization showing your learning journey through the quantum realm."
    />
  )
}

// Node Interaction Modal placeholder
export function NodeInteractionModal({ nodeId, onClose }: { nodeId: string | null; onClose: () => void }) {
  if (!nodeId) return null
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-gray-900 border border-gray-700 rounded-lg p-6 max-w-md"
        onClick={(e) => e.stopPropagation()}
      >
        <h3 className="text-xl font-bold text-white mb-4">Node Interaction</h3>
        <p className="text-white/60 mb-4">
          Advanced node interaction interface for timeline node: {nodeId}
        </p>
        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-cyan-400 text-black rounded hover:bg-cyan-300 transition-colors"
          >
            Close
          </button>
        </div>
      </motion.div>
    </motion.div>
  )
}

// Timeline Evolution Bar placeholder
export function TimelineEvolutionBar() {
  return (
    <PlaceholderComponent
      title="Timeline Evolution Bar"
      description="Dynamic progress visualization showing your evolution through different learning phases."
    />
  )
}

// Consciousness Feed placeholder
export function ConsciousnessFeed() {
  return (
    <PlaceholderComponent
      title="Consciousness Feed"
      description="Real-time feed of collective learning consciousness and community insights."
    />
  )
}

// Quantum Tasks Panel placeholder
export function QuantumTasksPanel() {
  return (
    <PlaceholderComponent
      title="Quantum Tasks Panel"
      description="AI-powered task management system with quantum-enhanced prioritization."
    />
  )
}

// Library of the Grid placeholder
export function LibraryOfTheGrid({ onClose }: { onClose: () => void }) {
  return (
    <div className="h-full p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-white">Library of the Grid</h2>
        <button
          onClick={onClose}
          className="text-white/60 hover:text-white"
        >
          ✕
        </button>
      </div>
      <PlaceholderComponent
        title="Knowledge Grid"
        description="Quantum-indexed library of all learning resources, tutorials, and community knowledge."
      />
    </div>
  )
}

// Error Boundary placeholder
export function ErrorBoundary({ children }: { children: React.ReactNode }) {
  return <>{children}</>
}

export function DNAErrorFallback() {
  return (
    <PlaceholderComponent
      title="DNA Core Error"
      description="The DNA Core encountered an error. Please try refreshing the page."
    />
  )
}

export function TimelineMapErrorFallback() {
  return (
    <PlaceholderComponent
      title="Timeline Map Error"
      description="The Timeline Map encountered an error. Please try refreshing the page."
    />
  )
}

export function FeedErrorFallback() {
  return (
    <PlaceholderComponent
      title="Feed Error"
      description="The Consciousness Feed encountered an error. Please try refreshing the page."
    />
  )
}

// Performance Monitor placeholder
export function PerformanceMonitor() {
  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-gray-900/90 border border-gray-700 rounded-lg p-2 text-xs text-white/60">
        <div>FPS: 60</div>
        <div>Memory: 45MB</div>
      </div>
    </div>
  )
}

// Welcome Message placeholder
export function WelcomeMessage({ onDismiss }: { onDismiss: () => void }) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center"
    >
      <Card className="bg-gray-900 border-cyan-400/50 max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-cyan-400">Welcome to NanoGenesis</CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-white/80 mb-6">
            Your quantum learning journey begins now. Explore the DNA Core, navigate the Timeline, 
            and unlock the mysteries of the digital realm.
          </p>
          <button
            onClick={onDismiss}
            className="px-6 py-2 bg-cyan-400 text-black rounded hover:bg-cyan-300 transition-colors"
          >
            Begin Journey
          </button>
        </CardContent>
      </Card>
    </motion.div>
  )
}

// User Profile placeholder
export function UserProfile() {
  return (
    <PlaceholderComponent
      title="User Profile"
      description="Comprehensive profile management with 3D avatar customization and achievement tracking."
    />
  )
}
