"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'

interface AuthContextType {
  user: User | null
  session: Session | null
  loading: boolean
  signInWithGoogle: (accountType?: 'student' | 'parent' | 'admin') => Promise<void>
  signInWithFacebook: (accountType?: 'student' | 'parent' | 'admin') => Promise<void>
  logout: () => Promise<void>
  userProfile: UserProfile | null
}

interface UserProfile {
  id: string
  email: string
  full_name: string
  avatar_url: string
  created_at: string
  last_sign_in_at: string
  provider: string
  is_new_user: boolean
  // Role-based access control
  account_type: 'student' | 'parent' | 'educator' | 'admin'
  // Parent-specific fields
  children_ids?: string[]
  parental_controls?: {
    content_filtering: 'strict' | 'moderate' | 'relaxed'
    time_limits: boolean
    chat_monitoring: boolean
    progress_reports: boolean
  }
  // Student-specific fields
  parent_id?: string
  birth_year?: number
  grade_level?: string
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
      setUser(session?.user ?? null)
      setLoading(false)
    })

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setSession(session)
      setUser(session?.user ?? null)

      if (session?.user) {
        // Get or create user profile
        const { data: profile, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)
          .single()

        if (error && error.code === 'PGRST116') {
          // Profile doesn't exist, create it
          const newProfile: Partial<UserProfile> = {
            id: session.user.id,
            email: session.user.email || '',
            full_name: session.user.user_metadata?.full_name || session.user.user_metadata?.name || '',
            avatar_url: session.user.user_metadata?.avatar_url || '',
            created_at: new Date().toISOString(),
            last_sign_in_at: new Date().toISOString(),
            provider: session.user.app_metadata?.provider || 'unknown',
            is_new_user: true
          }

          const { data: createdProfile } = await supabase
            .from('profiles')
            .insert([newProfile])
            .select()
            .single()

          setUserProfile(createdProfile)
        } else if (profile) {
          // Update last sign in
          const { data: updatedProfile } = await supabase
            .from('profiles')
            .update({ last_sign_in_at: new Date().toISOString() })
            .eq('id', session.user.id)
            .select()
            .single()

          setUserProfile(updatedProfile || profile)
        }
      } else {
        setUserProfile(null)
      }

      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  const signInWithGoogle = async (accountType?: 'student' | 'parent' | 'admin'): Promise<void> => {
    try {
      // Determine redirect URL based on account type
      const origin = typeof window !== 'undefined' ? window.location.origin : ''
      const redirectUrl = accountType
        ? `${origin}/auth/callback?type=${accountType}`
        : `${origin}/auth/callback`

      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl
        }
      })
      if (error) throw error
    } catch (error) {
      console.error('Google sign-in error:', error)
      throw error
    }
  }

  const signInWithFacebook = async (accountType?: 'student' | 'parent' | 'admin'): Promise<void> => {
    try {
      // Determine redirect URL based on account type
      const origin = typeof window !== 'undefined' ? window.location.origin : ''
      const redirectUrl = accountType
        ? `${origin}/auth/callback?type=${accountType}`
        : `${origin}/auth/callback`

      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'facebook',
        options: {
          redirectTo: redirectUrl
        }
      })
      if (error) throw error
    } catch (error) {
      console.error('Facebook sign-in error:', error)
      throw error
    }
  }

  const logout = async (): Promise<void> => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
    } catch (error) {
      console.error('Logout error:', error)
      throw error
    }
  }

  const value: AuthContextType = {
    user,
    session,
    loading,
    signInWithGoogle,
    signInWithFacebook,
    logout,
    userProfile
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
