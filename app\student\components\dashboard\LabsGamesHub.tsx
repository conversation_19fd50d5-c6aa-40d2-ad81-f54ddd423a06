'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  <PERSON>pad2, 
  Be<PERSON>, 
  <PERSON>, 
  Bot, 
  Puzzle, 
  Flag, 
  Trophy, 
  Clock, 
  Users, 
  Play, 
  Star,
  Zap,
  Target,
  Crown,
  Calendar,
  ChevronRight,
  ExternalLink,
  Sparkles,
  Flame
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { MetricsComponentProps, EvolutionTheme } from './types'

interface LabsGamesHubProps extends MetricsComponentProps {
  evolutionTheme: EvolutionTheme
}

interface Lab {
  id: string
  title: string
  description: string
  type: 'robotics' | 'cybersecurity' | 'chemistry' | 'physics' | 'ai'
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  duration: string
  participants: number
  maxParticipants: number
  status: 'available' | 'in-session' | 'full' | 'coming-soon'
  icon: React.ReactNode
  color: string
  features: string[]
  xpReward: number
}

interface MiniGame {
  id: string
  title: string
  description: string
  type: 'puzzle' | 'ctf' | 'minecraft' | 'coding' | 'logic'
  difficulty: number
  playTime: string
  highScore?: number
  personalBest?: number
  icon: string
  color: string
  isNew: boolean
  players: number
}

interface Event {
  id: string
  title: string
  description: string
  type: 'tournament' | 'competition' | 'workshop' | 'hackathon'
  startTime: string
  endTime: string
  participants: number
  maxParticipants: number
  prize: string
  status: 'upcoming' | 'live' | 'ended'
  icon: React.ReactNode
  color: string
}

export default function LabsGamesHub({ 
  systemStatus, 
  playerData,
  evolutionTheme 
}: LabsGamesHubProps) {
  const [selectedTab, setSelectedTab] = useState<'labs' | 'games' | 'events'>('labs')
  const [timeLeft, setTimeLeft] = useState<{ [key: string]: string }>({})

  // Mock labs data
  const labs: Lab[] = [
    {
      id: 'robotics-lab',
      title: 'Robotics Workshop',
      description: 'Build and program virtual robots in 3D space',
      type: 'robotics',
      difficulty: 'intermediate',
      duration: '45 min',
      participants: 12,
      maxParticipants: 20,
      status: 'available',
      icon: <Bot className="w-5 h-5" />,
      color: '#8B5CF6',
      features: ['3D Environment', 'Real Physics', 'Team Collaboration'],
      xpReward: 300
    },
    {
      id: 'cyber-lab',
      title: 'CyberSafe Lab',
      description: 'Hands-on cybersecurity challenges and simulations',
      type: 'cybersecurity',
      difficulty: 'advanced',
      duration: '60 min',
      participants: 8,
      maxParticipants: 15,
      status: 'in-session',
      icon: <Shield className="w-5 h-5" />,
      color: '#EF4444',
      features: ['Ethical Hacking', 'Network Security', 'Incident Response'],
      xpReward: 450
    },
    {
      id: 'chem-lab',
      title: 'Virtual Chemistry',
      description: 'Safe chemical experiments in virtual reality',
      type: 'chemistry',
      difficulty: 'beginner',
      duration: '30 min',
      participants: 15,
      maxParticipants: 25,
      status: 'available',
      icon: <Beaker className="w-5 h-5" />,
      color: '#10B981',
      features: ['VR Experience', 'Safe Experiments', 'Real Reactions'],
      xpReward: 200
    }
  ]

  // Mock mini games data
  const miniGames: MiniGame[] = [
    {
      id: 'logic-puzzle',
      title: 'Quantum Puzzles',
      description: 'Mind-bending logic challenges',
      type: 'puzzle',
      difficulty: 4,
      playTime: '10-15 min',
      highScore: 2450,
      personalBest: 1890,
      icon: '🧩',
      color: 'from-purple-500 to-pink-500',
      isNew: false,
      players: 234
    },
    {
      id: 'ctf-challenge',
      title: 'Capture The Flag',
      description: 'Cybersecurity challenges and competitions',
      type: 'ctf',
      difficulty: 5,
      playTime: '20-30 min',
      highScore: 5670,
      personalBest: 3240,
      icon: '🚩',
      color: 'from-red-500 to-orange-500',
      isNew: true,
      players: 156
    },
    {
      id: 'minecraft-world',
      title: 'NanoHero World',
      description: 'Educational Minecraft server with quests',
      type: 'minecraft',
      difficulty: 2,
      playTime: '30+ min',
      icon: '⛏️',
      color: 'from-green-500 to-emerald-500',
      isNew: false,
      players: 89
    },
    {
      id: 'code-battle',
      title: 'Code Warriors',
      description: 'Real-time coding competitions',
      type: 'coding',
      difficulty: 3,
      playTime: '15-20 min',
      highScore: 1890,
      personalBest: 1456,
      icon: '⚔️',
      color: 'from-blue-500 to-cyan-500',
      isNew: false,
      players: 178
    }
  ]

  // Mock events data
  const events: Event[] = [
    {
      id: 'ai-tournament',
      title: 'AI Challenge Tournament',
      description: 'Build the smartest AI in 2 hours',
      type: 'tournament',
      startTime: '2024-01-20T14:00:00Z',
      endTime: '2024-01-20T16:00:00Z',
      participants: 45,
      maxParticipants: 100,
      prize: '1000 XP + Exclusive Badge',
      status: 'upcoming',
      icon: <Bot className="w-4 h-4" />,
      color: 'from-purple-500 to-indigo-500'
    },
    {
      id: 'hackathon',
      title: 'CyberSafe Hackathon',
      description: 'Create security solutions for real problems',
      type: 'hackathon',
      startTime: '2024-01-22T09:00:00Z',
      endTime: '2024-01-22T18:00:00Z',
      participants: 23,
      maxParticipants: 50,
      prize: 'Mentorship + Internship Opportunity',
      status: 'upcoming',
      icon: <Shield className="w-4 h-4" />,
      color: 'from-red-500 to-pink-500'
    }
  ]

  // Countdown timer effect
  useEffect(() => {
    const updateCountdowns = () => {
      const newTimeLeft: { [key: string]: string } = {}
      
      events.forEach(event => {
        if (event.status === 'upcoming') {
          const now = new Date().getTime()
          const eventTime = new Date(event.startTime).getTime()
          const difference = eventTime - now

          if (difference > 0) {
            const days = Math.floor(difference / (1000 * 60 * 60 * 24))
            const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
            const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
            const seconds = Math.floor((difference % (1000 * 60)) / 1000)

            if (days > 0) {
              newTimeLeft[event.id] = `${days}d ${hours}h ${minutes}m`
            } else if (hours > 0) {
              newTimeLeft[event.id] = `${hours}h ${minutes}m ${seconds}s`
            } else {
              newTimeLeft[event.id] = `${minutes}m ${seconds}s`
            }
          } else {
            newTimeLeft[event.id] = 'Starting soon!'
          }
        }
      })
      
      setTimeLeft(newTimeLeft)
    }

    updateCountdowns()
    const interval = setInterval(updateCountdowns, 1000)
    return () => clearInterval(interval)
  }, [events])

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'available':
        return <Badge className="bg-green-500/20 text-green-400 border-green-500/30">Available</Badge>
      case 'in-session':
        return <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">In Session</Badge>
      case 'full':
        return <Badge className="bg-red-500/20 text-red-400 border-red-500/30">Full</Badge>
      case 'coming-soon':
        return <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">Coming Soon</Badge>
      default:
        return null
    }
  }

  return (
    <Card className="bg-black/40 border-purple-500/30 backdrop-blur-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-purple-400">
          <Gamepad2 className="w-5 h-5" />
          Labs & Games Hub
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Tab Navigation */}
          <div className="flex gap-2">
            {(['labs', 'games', 'events'] as const).map((tab) => (
              <Button
                key={tab}
                variant={selectedTab === tab ? "default" : "outline"}
                onClick={() => setSelectedTab(tab)}
                className={`capitalize ${
                  selectedTab === tab
                    ? 'bg-purple-500/20 text-purple-400 border-purple-500/30'
                    : 'border-gray-600/30 text-gray-400 hover:border-purple-500/30 hover:text-purple-400'
                }`}
              >
                {tab === 'labs' && <Beaker className="w-4 h-4 mr-2" />}
                {tab === 'games' && <Gamepad2 className="w-4 h-4 mr-2" />}
                {tab === 'events' && <Trophy className="w-4 h-4 mr-2" />}
                {tab}
              </Button>
            ))}
          </div>

          {/* Labs Tab */}
          {selectedTab === 'labs' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              <h3 className="text-lg font-bold text-white">Interactive 3D Labs</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {labs.map((lab, index) => (
                  <motion.div
                    key={lab.id}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-4 rounded-lg border bg-gray-800/30 border-gray-700/50 hover:border-purple-500/30 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <div className="p-2 rounded-lg" style={{ backgroundColor: `${lab.color}20`, border: `1px solid ${lab.color}50` }}>
                          <div style={{ color: lab.color }}>
                            {lab.icon}
                          </div>
                        </div>
                        <div>
                          <h4 className="font-bold text-white text-sm">{lab.title}</h4>
                          <p className="text-xs text-white/60">{lab.description}</p>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex justify-between items-center text-xs">
                        <span className="text-white/60">Participants</span>
                        <span className="text-cyan-400">{lab.participants}/{lab.maxParticipants}</span>
                      </div>
                      <Progress value={(lab.participants / lab.maxParticipants) * 100} className="h-1" />
                      
                      <div className="flex justify-between items-center text-xs">
                        <span className="text-white/60">Duration</span>
                        <span className="text-white">{lab.duration}</span>
                      </div>
                      
                      <div className="flex justify-between items-center text-xs">
                        <span className="text-white/60">XP Reward</span>
                        <span className="text-yellow-400">+{lab.xpReward}</span>
                      </div>

                      <div className="flex flex-wrap gap-1">
                        {lab.features.slice(0, 2).map((feature) => (
                          <Badge key={feature} className="text-xs bg-white/10 text-white/80 border-white/20">
                            {feature}
                          </Badge>
                        ))}
                      </div>

                      <div className="flex items-center justify-between">
                        {getStatusBadge(lab.status)}
                        <Button 
                          size="sm" 
                          className="bg-purple-500/20 text-purple-400 border border-purple-500/30 hover:bg-purple-500/30"
                          disabled={lab.status === 'full'}
                        >
                          <ExternalLink className="w-3 h-3 mr-1" />
                          {lab.status === 'in-session' ? 'Join' : 'Launch'}
                        </Button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Games Tab */}
          {selectedTab === 'games' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              <h3 className="text-lg font-bold text-white">Mini Games & Challenges</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {miniGames.map((game, index) => (
                  <motion.div
                    key={game.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`p-4 rounded-lg border bg-gradient-to-r ${game.color} bg-opacity-10 border-white/20 relative overflow-hidden`}
                  >
                    {game.isNew && (
                      <div className="absolute top-2 right-2">
                        <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30 text-xs">
                          New!
                        </Badge>
                      </div>
                    )}

                    <div className="flex items-center gap-3 mb-3">
                      <div className="text-3xl">{game.icon}</div>
                      <div className="flex-1">
                        <h4 className="font-bold text-white">{game.title}</h4>
                        <p className="text-sm text-white/60">{game.description}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-3 text-sm mb-4">
                      <div>
                        <span className="text-white/60">Difficulty:</span>
                        <div className="flex mt-1">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`w-3 h-3 ${
                                i < game.difficulty ? 'text-yellow-400 fill-current' : 'text-gray-600'
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                      <div>
                        <span className="text-white/60">Play Time:</span>
                        <div className="text-white font-medium">{game.playTime}</div>
                      </div>
                      {game.personalBest && (
                        <>
                          <div>
                            <span className="text-white/60">Your Best:</span>
                            <div className="text-cyan-400 font-bold">{game.personalBest.toLocaleString()}</div>
                          </div>
                          <div>
                            <span className="text-white/60">High Score:</span>
                            <div className="text-yellow-400 font-bold">{game.highScore?.toLocaleString()}</div>
                          </div>
                        </>
                      )}
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2 text-sm text-white/60">
                        <Users className="w-3 h-3" />
                        <span>{game.players} playing</span>
                      </div>
                      <Button 
                        size="sm" 
                        className="bg-purple-500/20 text-purple-400 border border-purple-500/30 hover:bg-purple-500/30"
                      >
                        <Play className="w-3 h-3 mr-1" />
                        Play Now
                      </Button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Events Tab */}
          {selectedTab === 'events' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              <h3 className="text-lg font-bold text-white">Events & Tournaments</h3>
              <div className="space-y-4">
                {events.map((event, index) => (
                  <motion.div
                    key={event.id}
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    className={`p-6 rounded-lg border bg-gradient-to-r ${event.color} bg-opacity-10 border-white/20`}
                  >
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className="p-3 rounded-lg bg-white/10 border border-white/20">
                          <div className="text-white">
                            {event.icon}
                          </div>
                        </div>
                        <div>
                          <h4 className="text-xl font-bold text-white">{event.title}</h4>
                          <p className="text-white/60">{event.description}</p>
                        </div>
                      </div>
                      <Badge className="bg-orange-500/20 text-orange-400 border-orange-500/30 capitalize">
                        {event.type}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="text-center p-3 bg-white/5 rounded-lg">
                        <Clock className="w-5 h-5 text-cyan-400 mx-auto mb-2" />
                        <div className="text-sm text-white/60">Starts In</div>
                        <div className="text-lg font-bold text-cyan-400">
                          {timeLeft[event.id] || 'Loading...'}
                        </div>
                      </div>
                      <div className="text-center p-3 bg-white/5 rounded-lg">
                        <Users className="w-5 h-5 text-green-400 mx-auto mb-2" />
                        <div className="text-sm text-white/60">Participants</div>
                        <div className="text-lg font-bold text-green-400">
                          {event.participants}/{event.maxParticipants}
                        </div>
                      </div>
                      <div className="text-center p-3 bg-white/5 rounded-lg">
                        <Trophy className="w-5 h-5 text-yellow-400 mx-auto mb-2" />
                        <div className="text-sm text-white/60">Prize</div>
                        <div className="text-sm font-bold text-yellow-400">
                          {event.prize}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <Progress 
                        value={(event.participants / event.maxParticipants) * 100} 
                        className="flex-1 mr-4 h-2"
                      />
                      <Button 
                        className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0 hover:from-purple-600 hover:to-pink-600"
                      >
                        <Calendar className="w-4 h-4 mr-2" />
                        Sign Up
                        <ChevronRight className="w-4 h-4 ml-2" />
                      </Button>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Leaderboard Preview */}
              <div className="mt-6 p-4 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-lg border border-yellow-500/30">
                <h4 className="text-lg font-bold text-white mb-3 flex items-center gap-2">
                  <Crown className="w-5 h-5 text-yellow-400" />
                  Current Leaderboard
                </h4>
                <div className="space-y-2">
                  {[
                    { rank: 1, name: 'QuantumMaster', score: 5670, badge: '🥇' },
                    { rank: 2, name: 'CyberNinja', score: 5240, badge: '🥈' },
                    { rank: 3, name: 'CodeWarrior', score: 4890, badge: '🥉' }
                  ].map((player) => (
                    <div key={player.rank} className="flex items-center justify-between p-2 bg-white/5 rounded">
                      <div className="flex items-center gap-3">
                        <span className="text-lg">{player.badge}</span>
                        <span className="text-white font-medium">{player.name}</span>
                      </div>
                      <span className="text-yellow-400 font-bold">{player.score.toLocaleString()}</span>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
