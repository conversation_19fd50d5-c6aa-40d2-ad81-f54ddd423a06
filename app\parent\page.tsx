'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Clock,
  BookOpen,
  BarChart3,
  Star,
  Activity,
  Brain,
  Target,
  TrendingUp,
  Users,
  ChevronDown,
  Heart,
  Shield,
  Calendar,
  Trophy,
  Gamepad2,
  Medal,
  Crown,
  Flame,
  Lock,
  CheckCircle,
  Plus,
  CalendarDays,
  Timer,
  PlayCircle,
  ArrowRight
} from 'lucide-react'

// GitHub-style Activity Heatmap Component
const ActivityHeatmap = ({ activityData, childName: _childName }: { activityData: any[], childName: string }) => {
  const getIntensityColor = (minutes: number) => {
    if (minutes === 0) return 'bg-gray-800/50'
    if (minutes < 30) return 'bg-green-900/50'
    if (minutes < 60) return 'bg-green-700/70'
    if (minutes < 120) return 'bg-green-500/80'
    return 'bg-green-400'
  }

  const getIntensityLevel = (minutes: number) => {
    if (minutes === 0) return 'No activity'
    if (minutes < 30) return 'Light activity'
    if (minutes < 60) return 'Moderate activity'
    if (minutes < 120) return 'High activity'
    return 'Very high activity'
  }

  // Generate last 12 weeks of data (84 days)
  const weeks = []
  const today = new Date()

  for (let week = 11; week >= 0; week--) {
    const weekData = []
    for (let day = 6; day >= 0; day--) {
      const date = new Date(today)
      date.setDate(date.getDate() - (week * 7 + day))

      // Find activity data for this date or use random data for demo
      const dayActivity = activityData.find(activity => {
        const activityDate = new Date(activity.date)
        return activityDate.toDateString() === date.toDateString()
      }) || {
        date: date.toISOString(),
        minutes: Math.floor(Math.random() * 180),
        activities: Math.floor(Math.random() * 8),
        lessons: Math.floor(Math.random() * 5)
      }

      weekData.push({
        ...dayActivity,
        date,
        dayName: date.toLocaleDateString('en', { weekday: 'short' })
      })
    }
    weeks.push(weekData)
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-white font-medium">Activity over the last 12 weeks</h3>
        <div className="flex items-center gap-2 text-xs text-gray-400">
          <span>Less</span>
          <div className="flex gap-1">
            <div className="w-3 h-3 bg-gray-800/50 rounded-sm"></div>
            <div className="w-3 h-3 bg-green-900/50 rounded-sm"></div>
            <div className="w-3 h-3 bg-green-700/70 rounded-sm"></div>
            <div className="w-3 h-3 bg-green-500/80 rounded-sm"></div>
            <div className="w-3 h-3 bg-green-400 rounded-sm"></div>
          </div>
          <span>More</span>
        </div>
      </div>

      <div className="overflow-x-auto">
        <div className="flex gap-1 min-w-max">
          {weeks.map((week, weekIndex) => (
            <div key={weekIndex} className="flex flex-col gap-1">
              {week.map((day, dayIndex) => (
                <div
                  key={dayIndex}
                  className={`w-3 h-3 rounded-sm ${getIntensityColor(day.minutes)} hover:ring-2 hover:ring-green-400/50 cursor-pointer transition-all`}
                  title={`${day.date.toLocaleDateString()}: ${day.minutes} minutes, ${day.activities} activities, ${day.lessons} lessons - ${getIntensityLevel(day.minutes)}`}
                />
              ))}
            </div>
          ))}
        </div>
      </div>

      <div className="flex justify-between text-xs text-gray-400">
        <span>{weeks[0][0].date.toLocaleDateString('en', { month: 'short' })}</span>
        <span>{weeks[weeks.length - 1][0].date.toLocaleDateString('en', { month: 'short' })}</span>
      </div>
    </div>
  )
}

// Radar Chart Component for Skills
const SkillRadarChart = ({ skills, childName: _childName }: { skills: any[], childName: string }) => {
  const size = 200
  const center = size / 2
  const radius = 70
  const angleStep = (2 * Math.PI) / skills.length

  const getPoint = (value: number, index: number) => {
    const angle = angleStep * index - Math.PI / 2
    const distance = (value / 100) * radius
    return {
      x: center + Math.cos(angle) * distance,
      y: center + Math.sin(angle) * distance
    }
  }

  const getLabelPoint = (index: number) => {
    const angle = angleStep * index - Math.PI / 2
    const distance = radius + 25
    return {
      x: center + Math.cos(angle) * distance,
      y: center + Math.sin(angle) * distance
    }
  }

  const pathData = skills.map((skill, index) => {
    const point = getPoint(skill.current, index)
    return `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
  }).join(' ') + ' Z'

  return (
    <div className="flex flex-col items-center">
      <svg width={size} height={size} className="mb-4">
        {/* Grid circles */}
        {[20, 40, 60, 80, 100].map(value => (
          <circle
            key={value}
            cx={center}
            cy={center}
            r={(value / 100) * radius}
            fill="none"
            stroke="rgba(255,255,255,0.1)"
            strokeWidth="1"
          />
        ))}

        {/* Grid lines */}
        {skills.map((_, index) => {
          const point = getPoint(100, index)
          return (
            <line
              key={index}
              x1={center}
              y1={center}
              x2={point.x}
              y2={point.y}
              stroke="rgba(255,255,255,0.1)"
              strokeWidth="1"
            />
          )
        })}

        {/* Data area */}
        <path
          d={pathData}
          fill="rgba(34, 197, 94, 0.2)"
          stroke="rgb(34, 197, 94)"
          strokeWidth="2"
        />

        {/* Data points */}
        {skills.map((skill, index) => {
          const point = getPoint(skill.current, index)
          return (
            <circle
              key={index}
              cx={point.x}
              cy={point.y}
              r="4"
              fill="rgb(34, 197, 94)"
            />
          )
        })}

        {/* Labels */}
        {skills.map((skill, index) => {
          const point = getLabelPoint(index)
          return (
            <text
              key={index}
              x={point.x}
              y={point.y}
              textAnchor="middle"
              dominantBaseline="middle"
              className="text-xs fill-white font-medium"
            >
              {skill.name}
            </text>
          )
        })}
      </svg>

      {/* Legend */}
      <div className="grid grid-cols-2 gap-2 text-sm">
        {skills.map((skill, index) => (
          <div key={index} className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="text-gray-300">{skill.name}: {skill.current}%</span>
          </div>
        ))}
      </div>
    </div>
  )
}

export default function ParentDashboardPage() {
  const [selectedChild, setSelectedChild] = useState('emma')
  const [activeTab, setActiveTab] = useState('overview')

  // Enhanced child data with comprehensive tracking
  const children = [
    {
      id: 'emma',
      name: 'Emma',
      age: 12,
      avatar: '👧',
      status: 'online' as const,
      currentActivity: 'Quantum Physics Module',
      lastActive: 'Now',
      learningProgress: 78,
      timeToday: 145, // minutes
      timeLimit: 180, // minutes
      achievementCount: 23,
      level: 8,
      totalHours: 156.5,
      modulesCompleted: 34,
      favoriteSubject: 'Quantum Physics',
      skills: [
        { name: 'Critical Thinking', current: 85, previous: 78, target: 90 },
        { name: 'Emotional Intelligence', current: 72, previous: 68, target: 80 },
        { name: 'Communication', current: 88, previous: 82, target: 90 },
        { name: 'Digital Literacy', current: 94, previous: 89, target: 95 },
        { name: 'Problem Solving', current: 81, previous: 75, target: 85 },
        { name: 'Creativity', current: 76, previous: 71, target: 80 }
      ],
      weeklyActivity: [
        { day: 'Mon', hours: 2.5, focus: 85, engagement: 92, mood: '🤩', minutes: 150, activities: 6, lessons: 3 },
        { day: 'Tue', hours: 3.2, focus: 78, engagement: 88, mood: '🎯', minutes: 192, activities: 8, lessons: 4 },
        { day: 'Wed', hours: 4.1, focus: 91, engagement: 95, mood: '🤔', minutes: 246, activities: 10, lessons: 5 },
        { day: 'Thu', hours: 2.8, focus: 82, engagement: 87, mood: '💪', minutes: 168, activities: 7, lessons: 3 },
        { day: 'Fri', hours: 3.6, focus: 89, engagement: 93, mood: '🏆', minutes: 216, activities: 9, lessons: 4 },
        { day: 'Sat', hours: 4.2, focus: 86, engagement: 90, mood: '😄', minutes: 252, activities: 11, lessons: 6 },
        { day: 'Sun', hours: 2.1, focus: 75, engagement: 82, mood: '😌', minutes: 126, activities: 5, lessons: 2 }
      ],
      activityHeatmap: [
        { date: '2024-01-15', minutes: 150, activities: 6, lessons: 3 },
        { date: '2024-01-14', minutes: 192, activities: 8, lessons: 4 },
        { date: '2024-01-13', minutes: 246, activities: 10, lessons: 5 },
        { date: '2024-01-12', minutes: 168, activities: 7, lessons: 3 },
        { date: '2024-01-11', minutes: 216, activities: 9, lessons: 4 },
        { date: '2024-01-10', minutes: 252, activities: 11, lessons: 6 },
        { date: '2024-01-09', minutes: 126, activities: 5, lessons: 2 },
        { date: '2024-01-08', minutes: 180, activities: 7, lessons: 4 },
        { date: '2024-01-07', minutes: 165, activities: 6, lessons: 3 },
        { date: '2024-01-06', minutes: 210, activities: 8, lessons: 5 }
      ],
      streakData: {
        currentStreak: 12,
        longestStreak: 18,
        weeklyConsistency: 85, // percentage
        monthlyConsistency: 78,
        averageDailyMinutes: 185,
        totalActiveDays: 28,
        streakHistory: [
          { startDate: '2024-01-04', endDate: '2024-01-15', length: 12, status: 'active' },
          { startDate: '2023-12-15', endDate: '2024-01-02', length: 18, status: 'completed' },
          { startDate: '2023-12-08', endDate: '2023-12-13', length: 6, status: 'completed' }
        ]
      },
      safetyScore: 95,
      socialInteractions: 24,
      helpGiven: 8,
      helpReceived: 3,
      streakDays: 12,
      monthlyGoals: {
        learningHours: { current: 48.7, target: 60 },
        modulesCompleted: { current: 6, target: 8 },
        socialHelp: { current: 8, target: 10 },
        skillGrowth: { current: 7.2, target: 10 }
      },
      curriculum: {
        lifeSkills: {
          name: 'Life Skills',
          totalModules: 24,
          completedModules: 18,
          percentage: 75,
          modules: [
            { name: 'Digital Citizenship', status: 'completed', progress: 100, topics: 8, completedTopics: 8 },
            { name: 'Time Management', status: 'completed', progress: 100, topics: 6, completedTopics: 6 },
            { name: 'Critical Thinking', status: 'completed', progress: 100, topics: 10, completedTopics: 10 },
            { name: 'Communication Skills', status: 'in-progress', progress: 80, topics: 8, completedTopics: 6 },
            { name: 'Financial Literacy', status: 'in-progress', progress: 60, topics: 12, completedTopics: 7 },
            { name: 'Emotional Intelligence', status: 'locked', progress: 0, topics: 9, completedTopics: 0 },
            { name: 'Leadership Basics', status: 'locked', progress: 0, topics: 7, completedTopics: 0 }
          ]
        },
        cybersecurity: {
          name: 'Cybersecurity',
          totalTutorials: 16,
          watchedTutorials: 12,
          percentage: 75,
          tutorials: [
            { name: 'Password Security', status: 'completed', progress: 100, duration: '15 min' },
            { name: 'Phishing Awareness', status: 'completed', progress: 100, duration: '20 min' },
            { name: 'Safe Browsing', status: 'completed', progress: 100, duration: '18 min' },
            { name: 'Social Media Privacy', status: 'in-progress', progress: 70, duration: '25 min' },
            { name: 'Data Protection', status: 'in-progress', progress: 40, duration: '22 min' },
            { name: 'Malware Prevention', status: 'locked', progress: 0, duration: '30 min' },
            { name: 'Advanced Encryption', status: 'locked', progress: 0, duration: '35 min' }
          ]
        },
        challenges: {
          name: 'Challenges & Labs',
          totalChallenges: 32,
          completedChallenges: 24,
          percentage: 75,
          categories: [
            {
              name: 'Logic Puzzles',
              total: 12,
              completed: 10,
              challenges: [
                { name: 'Pattern Recognition', status: 'completed', difficulty: 'Easy', points: 100 },
                { name: 'Sequence Solving', status: 'completed', difficulty: 'Medium', points: 200 },
                { name: 'Logic Gates', status: 'in-progress', difficulty: 'Hard', points: 300 }
              ]
            },
            {
              name: 'Hack Labs',
              total: 8,
              completed: 6,
              challenges: [
                { name: 'Basic Encryption', status: 'completed', difficulty: 'Easy', points: 150 },
                { name: 'Network Analysis', status: 'completed', difficulty: 'Medium', points: 250 },
                { name: 'Advanced Cryptography', status: 'locked', difficulty: 'Hard', points: 400 }
              ]
            },
            {
              name: 'Creative Challenges',
              total: 12,
              completed: 8,
              challenges: [
                { name: 'Design Thinking', status: 'completed', difficulty: 'Medium', points: 200 },
                { name: 'Innovation Lab', status: 'in-progress', difficulty: 'Hard', points: 350 },
                { name: 'Future Tech', status: 'locked', difficulty: 'Expert', points: 500 }
              ]
            }
          ]
        }
      },
      achievements: {
        totalBadges: 23,
        totalPoints: 4850,
        currentStreak: 12,
        earnedBadges: [
          {
            id: 'logic-hero',
            name: 'Logic Hero',
            description: 'Completed 15 logic puzzles with 90%+ accuracy',
            icon: '🧠',
            category: 'Problem Solving',
            rarity: 'epic',
            earnedDate: '2024-01-15',
            points: 300,
            progress: 100
          },
          {
            id: 'empathy-master',
            name: 'Empathy Master',
            description: 'Helped 10 peers with their learning challenges',
            icon: '💝',
            category: 'Social Skills',
            rarity: 'rare',
            earnedDate: '2024-01-10',
            points: 250,
            progress: 100
          },
          {
            id: 'quantum-explorer',
            name: 'Quantum Explorer',
            description: 'Mastered 5 quantum physics concepts',
            icon: '⚛️',
            category: 'Science',
            rarity: 'epic',
            earnedDate: '2024-01-08',
            points: 350,
            progress: 100
          },
          {
            id: 'cyber-guardian',
            name: 'Cyber Guardian',
            description: 'Completed all basic cybersecurity modules',
            icon: '🛡️',
            category: 'Digital Safety',
            rarity: 'rare',
            earnedDate: '2024-01-05',
            points: 200,
            progress: 100
          },
          {
            id: 'creative-genius',
            name: 'Creative Genius',
            description: 'Designed 3 innovative solutions in NanoLab',
            icon: '🎨',
            category: 'Creativity',
            rarity: 'legendary',
            earnedDate: '2024-01-12',
            points: 500,
            progress: 100
          },
          {
            id: 'team-player',
            name: 'Team Player',
            description: 'Successfully collaborated on 5 group projects',
            icon: '🤝',
            category: 'Collaboration',
            rarity: 'common',
            earnedDate: '2024-01-03',
            points: 150,
            progress: 100
          }
        ],
        inProgressBadges: [
          {
            id: 'nano-architect',
            name: 'Nano Architect',
            description: 'Design 10 nanotechnology solutions',
            icon: '🔬',
            category: 'Science',
            rarity: 'epic',
            points: 400,
            progress: 70,
            requirement: 10,
            current: 7
          },
          {
            id: 'mentor-supreme',
            name: 'Mentor Supreme',
            description: 'Help 20 peers achieve their goals',
            icon: '🌟',
            category: 'Leadership',
            rarity: 'legendary',
            points: 600,
            progress: 45,
            requirement: 20,
            current: 9
          }
        ],
        lockedBadges: [
          {
            id: 'ai-whisperer',
            name: 'AI Whisperer',
            description: 'Master advanced AI concepts and build 3 AI projects',
            icon: '🤖',
            category: 'Technology',
            rarity: 'legendary',
            points: 750,
            unlockRequirement: 'Complete Advanced AI Module'
          },
          {
            id: 'space-pioneer',
            name: 'Space Pioneer',
            description: 'Complete the entire Space Science curriculum',
            icon: '🚀',
            category: 'Science',
            rarity: 'epic',
            points: 450,
            unlockRequirement: 'Unlock Space Science Module'
          }
        ],
        milestones: [
          {
            id: 'first-week',
            name: 'First Week Complete',
            description: 'Completed your first week of learning',
            icon: '📅',
            achievedDate: '2023-12-08',
            points: 100
          },
          {
            id: 'puzzle-master',
            name: 'Puzzle Master',
            description: 'Solved 50 logic puzzles',
            icon: '🧩',
            achievedDate: '2024-01-15',
            points: 200
          },
          {
            id: 'social-butterfly',
            name: 'Social Butterfly',
            description: 'Made 10 new learning connections',
            icon: '🦋',
            achievedDate: '2024-01-10',
            points: 150
          },
          {
            id: 'streak-champion',
            name: 'Streak Champion',
            description: 'Maintained a 10-day learning streak',
            icon: '🔥',
            achievedDate: '2024-01-14',
            points: 250
          }
        ],
        nextGoals: [
          {
            id: 'nano-architect-goal',
            badgeId: 'nano-architect',
            name: 'Complete Nano Architect Badge',
            description: 'Design 3 more nanotechnology solutions',
            targetDate: '2024-02-15',
            priority: 'high'
          },
          {
            id: 'mentor-goal',
            badgeId: 'mentor-supreme',
            name: 'Become a Mentor Supreme',
            description: 'Help 11 more peers with their learning',
            targetDate: '2024-02-28',
            priority: 'medium'
          }
        ]
      }
    },
    {
      id: 'alex',
      name: 'Alex',
      age: 10,
      avatar: '👦',
      status: 'offline' as const,
      currentActivity: 'DNA Evolution Lab',
      lastActive: '2 hours ago',
      learningProgress: 65,
      timeToday: 90,
      timeLimit: 120,
      achievementCount: 15,
      level: 6,
      totalHours: 98.2,
      modulesCompleted: 22,
      favoriteSubject: 'Biology',
      skills: [
        { name: 'Critical Thinking', current: 72, previous: 68, target: 80 },
        { name: 'Emotional Intelligence', current: 85, previous: 80, target: 90 },
        { name: 'Communication', current: 78, previous: 74, target: 85 },
        { name: 'Digital Literacy', current: 68, previous: 62, target: 75 },
        { name: 'Problem Solving', current: 74, previous: 69, target: 80 },
        { name: 'Creativity', current: 89, previous: 84, target: 92 }
      ],
      weeklyActivity: [
        { day: 'Mon', hours: 1.8, focus: 78, engagement: 85, mood: '😊', minutes: 108, activities: 4, lessons: 2 },
        { day: 'Tue', hours: 2.1, focus: 82, engagement: 88, mood: '🎨', minutes: 126, activities: 5, lessons: 3 },
        { day: 'Wed', hours: 2.5, focus: 85, engagement: 91, mood: '🚀', minutes: 150, activities: 6, lessons: 3 },
        { day: 'Thu', hours: 1.9, focus: 79, engagement: 84, mood: '🤝', minutes: 114, activities: 4, lessons: 2 },
        { day: 'Fri', hours: 2.3, focus: 88, engagement: 92, mood: '🌟', minutes: 138, activities: 5, lessons: 3 },
        { day: 'Sat', hours: 2.8, focus: 83, engagement: 89, mood: '🎮', minutes: 168, activities: 7, lessons: 4 },
        { day: 'Sun', hours: 1.5, focus: 76, engagement: 81, mood: '📚', minutes: 90, activities: 3, lessons: 2 }
      ],
      activityHeatmap: [
        { date: '2024-01-15', minutes: 108, activities: 4, lessons: 2 },
        { date: '2024-01-14', minutes: 126, activities: 5, lessons: 3 },
        { date: '2024-01-13', minutes: 150, activities: 6, lessons: 3 },
        { date: '2024-01-12', minutes: 114, activities: 4, lessons: 2 },
        { date: '2024-01-11', minutes: 138, activities: 5, lessons: 3 },
        { date: '2024-01-10', minutes: 168, activities: 7, lessons: 4 },
        { date: '2024-01-09', minutes: 90, activities: 3, lessons: 2 },
        { date: '2024-01-08', minutes: 120, activities: 4, lessons: 3 },
        { date: '2024-01-07', minutes: 105, activities: 3, lessons: 2 },
        { date: '2024-01-06', minutes: 135, activities: 5, lessons: 3 }
      ],
      streakData: {
        currentStreak: 8,
        longestStreak: 12,
        weeklyConsistency: 72,
        monthlyConsistency: 68,
        averageDailyMinutes: 125,
        totalActiveDays: 22,
        streakHistory: [
          { startDate: '2024-01-08', endDate: '2024-01-15', length: 8, status: 'active' },
          { startDate: '2023-12-20', endDate: '2024-01-05', length: 12, status: 'completed' },
          { startDate: '2023-12-12', endDate: '2023-12-18', length: 7, status: 'completed' }
        ]
      },
      safetyScore: 92,
      socialInteractions: 18,
      helpGiven: 5,
      helpReceived: 7,
      streakDays: 8,
      monthlyGoals: {
        learningHours: { current: 32.4, target: 40 },
        modulesCompleted: { current: 4, target: 6 },
        socialHelp: { current: 5, target: 8 },
        skillGrowth: { current: 5.8, target: 8 }
      },
      curriculum: {
        lifeSkills: {
          name: 'Life Skills',
          totalModules: 24,
          completedModules: 12,
          percentage: 50,
          modules: [
            { name: 'Digital Citizenship', status: 'completed', progress: 100, topics: 8, completedTopics: 8 },
            { name: 'Time Management', status: 'in-progress', progress: 75, topics: 6, completedTopics: 4 },
            { name: 'Critical Thinking', status: 'in-progress', progress: 40, topics: 10, completedTopics: 4 },
            { name: 'Communication Skills', status: 'locked', progress: 0, topics: 8, completedTopics: 0 },
            { name: 'Financial Literacy', status: 'locked', progress: 0, topics: 12, completedTopics: 0 }
          ]
        },
        cybersecurity: {
          name: 'Cybersecurity',
          totalTutorials: 16,
          watchedTutorials: 8,
          percentage: 50,
          tutorials: [
            { name: 'Password Security', status: 'completed', progress: 100, duration: '15 min' },
            { name: 'Phishing Awareness', status: 'completed', progress: 100, duration: '20 min' },
            { name: 'Safe Browsing', status: 'in-progress', progress: 60, duration: '18 min' },
            { name: 'Social Media Privacy', status: 'locked', progress: 0, duration: '25 min' }
          ]
        },
        challenges: {
          name: 'Challenges & Labs',
          totalChallenges: 32,
          completedChallenges: 16,
          percentage: 50,
          categories: [
            {
              name: 'Logic Puzzles',
              total: 12,
              completed: 6,
              challenges: [
                { name: 'Pattern Recognition', status: 'completed', difficulty: 'Easy', points: 100 },
                { name: 'Sequence Solving', status: 'in-progress', difficulty: 'Medium', points: 200 }
              ]
            },
            {
              name: 'Creative Challenges',
              total: 12,
              completed: 8,
              challenges: [
                { name: 'Design Thinking', status: 'completed', difficulty: 'Medium', points: 200 },
                { name: 'Innovation Lab', status: 'in-progress', difficulty: 'Hard', points: 350 }
              ]
            }
          ]
        }
      }
    }
  ]

  const currentChild = children.find(child => child.id === selectedChild) || children[0]

  // Safety check - if no children data is available, show loading state
  if (!currentChild) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-400">Loading child data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-white font-space-grotesk">
            Child Stats
          </h1>
          <p className="text-gray-400 mt-1">
            Simple overview of your child&apos;s learning progress
          </p>
        </div>
      </motion.div>

      {/* Child Selector */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="flex gap-4"
      >
        {children.map((child) => (
          <Button
            key={child.id}
            variant={selectedChild === child.id ? "default" : "outline"}
            onClick={() => setSelectedChild(child.id)}
            className="flex items-center gap-2"
          >
            <span className="text-lg">{child.avatar}</span>
            {child.name}
            <ChevronDown className="w-4 h-4" />
          </Button>
        ))}
      </motion.div>

      {/* Main Dashboard Tabs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="bg-black/60 border border-gray-800/50 grid grid-cols-4 w-full max-w-2xl">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="curriculum" className="flex items-center gap-2">
              <BookOpen className="w-4 h-4" />
              Curriculum
            </TabsTrigger>
            <TabsTrigger value="achievements" className="flex items-center gap-2">
              <Trophy className="w-4 h-4" />
              Achievements
            </TabsTrigger>
            <TabsTrigger value="activity" className="flex items-center gap-2">
              <CalendarDays className="w-4 h-4" />
              Activity
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6 mt-6">
            {/* Key Metrics Overview */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
            >
        {[
          {
            label: 'Learning Hours',
            value: `${currentChild.totalHours}h`,
            change: '+12.5h this month',
            icon: Clock,
            color: 'text-blue-400',
            trend: 'up'
          },
          {
            label: 'Safety Score',
            value: `${currentChild.safetyScore}%`,
            change: '+3% this week',
            icon: Shield,
            color: 'text-green-400',
            trend: 'up'
          },
          {
            label: 'Social Interactions',
            value: currentChild.socialInteractions,
            change: '+5 this week',
            icon: Users,
            color: 'text-purple-400',
            trend: 'up'
          },
          {
            label: 'Achievements',
            value: currentChild.achievementCount,
            change: '+4 this month',
            icon: Trophy,
            color: 'text-yellow-400',
            trend: 'up'
          }
        ].map((stat, _index) => (
          <motion.div
            key={stat.label}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 + _index * 0.05 }}
          >
            <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <stat.icon className={`w-5 h-5 ${stat.color}`} />
                  <TrendingUp className={`w-4 h-4 ${stat.trend === 'up' ? 'text-green-400' : 'text-red-400'}`} />
                </div>
                <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
                <div className="text-xs text-gray-400 mb-1">{stat.label}</div>
                <div className="text-xs text-green-400">{stat.change}</div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      {/* Skills Progress Overview - Radar Chart */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="grid grid-cols-1 lg:grid-cols-2 gap-6"
      >
        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardHeader>
            <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
              <Brain className="w-5 h-5 text-purple-400" />
              Skill Development Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <SkillRadarChart skills={currentChild.skills} childName={currentChild.name} />
          </CardContent>
        </Card>

        {/* Weekly Activity & Engagement */}
        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardHeader>
            <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-blue-400" />
              Weekly Activity & Engagement
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {currentChild.weeklyActivity.map((day, _index) => (
                <div key={day.day} className="flex items-center gap-4">
                  <div className="w-8 text-gray-400 text-sm">{day.day}</div>
                  <div className="flex-1">
                    <div className="flex justify-between text-xs mb-1">
                      <span className="text-gray-400">{day.hours}h</span>
                      <span className="text-cyan-400">Focus: {day.focus}%</span>
                      <span className="text-green-400">Engagement: {day.engagement}%</span>
                      <span className="text-lg">{day.mood}</span>
                    </div>
                    <div className="h-2 bg-gray-800 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-gradient-to-r from-blue-500 to-cyan-500 transition-all duration-500"
                        style={{ width: `${(day.hours / 5) * 100}%` }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Monthly Goals & Current Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="grid grid-cols-1 lg:grid-cols-2 gap-6"
      >
        {/* Monthly Goals Progress */}
        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardHeader>
            <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
              <Target className="w-5 h-5 text-purple-400" />
              Monthly Goals Progress
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.entries(currentChild.monthlyGoals).map(([key, goal]) => (
              <div key={key} className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400 text-sm capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </span>
                  <span className="text-white text-sm font-medium">
                    {goal.current}/{goal.target}
                  </span>
                </div>
                <Progress value={(goal.current / goal.target) * 100} className="h-2" />
                <div className="flex justify-between text-xs">
                  <span className="text-gray-500">
                    {((goal.current / goal.target) * 100).toFixed(0)}% complete
                  </span>
                  <span className={`${
                    goal.current >= goal.target ? 'text-green-400' :
                    goal.current >= goal.target * 0.8 ? 'text-yellow-400' : 'text-red-400'
                  }`}>
                    {goal.current >= goal.target ? '✓ Goal Met' :
                     goal.current >= goal.target * 0.8 ? 'On Track' : 'Needs Focus'}
                  </span>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Current Activity & Social Stats */}
        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardHeader>
            <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
              <Activity className="w-5 h-5 text-green-400" />
              Current Activity & Social
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Current Activity */}
            <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-lg border border-cyan-500/20">
              <div className="text-2xl">{currentChild.avatar}</div>
              <div className="flex-1">
                <h4 className="text-white font-medium">{currentChild.currentActivity}</h4>
                <p className="text-gray-400 text-sm">Last active: {currentChild.lastActive}</p>
                <p className="text-cyan-400 text-sm">Favorite: {currentChild.favoriteSubject}</p>
              </div>
              <Badge variant={currentChild.status === 'online' ? 'default' : 'secondary'}>
                {currentChild.status}
              </Badge>
            </div>

            {/* Social & Help Stats */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-gray-800/30 rounded-lg">
                <div className="text-lg font-bold text-blue-400">{currentChild.helpGiven}</div>
                <div className="text-xs text-gray-400">Help Given</div>
              </div>
              <div className="text-center p-3 bg-gray-800/30 rounded-lg">
                <div className="text-lg font-bold text-green-400">{currentChild.socialInteractions}</div>
                <div className="text-xs text-gray-400">Social Interactions</div>
              </div>
            </div>

            {/* Screen Time Progress */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Daily Screen Time</span>
                <span className="text-white">
                  {Math.floor(currentChild.timeToday / 60)}h {currentChild.timeToday % 60}m / {Math.floor(currentChild.timeLimit / 60)}h {currentChild.timeLimit % 60}m
                </span>
              </div>
              <Progress value={(currentChild.timeToday / currentChild.timeLimit) * 100} className="h-2" />
            </div>
          </CardContent>
            </Card>
          </motion.div>
          </TabsContent>

          {/* Curriculum Tab */}
          <TabsContent value="curriculum" className="space-y-6 mt-6">
            {/* Quick Access to Flowchart */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-6"
            >
              <Card className="bg-gradient-to-r from-cyan-500/10 to-blue-500/10 border-cyan-500/20 backdrop-blur-xl">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="p-3 bg-cyan-500/20 rounded-lg">
                        <Calendar className="w-8 h-8 text-cyan-400" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-white font-space-grotesk">
                          Visual Curriculum Planner
                        </h3>
                        <p className="text-gray-400 mt-1">
                          Interactive flowchart-style curriculum planning with lesson hierarchy and progress tracking
                        </p>
                      </div>
                    </div>
                    <Button
                      className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600"
                      onClick={() => window.location.href = '/parent-dashboard/curriculum'}
                    >
                      <Calendar className="w-4 h-4 mr-2" />
                      Open Curriculum Flow
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Curriculum Overview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Life Skills */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Heart className="w-5 h-5 text-pink-400" />
                    Life Skills
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center mb-4">
                    <div className="text-3xl font-bold text-pink-400 mb-1">
                      {currentChild.curriculum.lifeSkills.percentage}%
                    </div>
                    <div className="text-sm text-gray-400">
                      {currentChild.curriculum.lifeSkills.completedModules}/{currentChild.curriculum.lifeSkills.totalModules} modules
                    </div>
                    <Progress value={currentChild.curriculum.lifeSkills.percentage} className="h-2 mt-2" />
                  </div>

                  <div className="space-y-2">
                    {currentChild.curriculum.lifeSkills.modules.slice(0, 4).map((module, _index) => (
                      <div key={_index} className="flex items-center justify-between p-2 bg-gray-800/30 rounded">
                        <div className="flex-1">
                          <div className="text-white text-sm font-medium">{module.name}</div>
                          <div className="text-xs text-gray-400">{module.completedTopics}/{module.topics} topics</div>
                        </div>
                        <Badge variant={
                          module.status === 'completed' ? 'default' :
                          module.status === 'in-progress' ? 'secondary' : 'outline'
                        } className="text-xs">
                          {module.status === 'completed' ? '✓' :
                           module.status === 'in-progress' ? `${module.progress}%` : '🔒'}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Cybersecurity */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Shield className="w-5 h-5 text-blue-400" />
                    Cybersecurity
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center mb-4">
                    <div className="text-3xl font-bold text-blue-400 mb-1">
                      {currentChild.curriculum.cybersecurity.percentage}%
                    </div>
                    <div className="text-sm text-gray-400">
                      {currentChild.curriculum.cybersecurity.watchedTutorials}/{currentChild.curriculum.cybersecurity.totalTutorials} tutorials
                    </div>
                    <Progress value={currentChild.curriculum.cybersecurity.percentage} className="h-2 mt-2" />
                  </div>

                  <div className="space-y-2">
                    {currentChild.curriculum.cybersecurity.tutorials.slice(0, 4).map((tutorial, _index) => (
                      <div key={_index} className="flex items-center justify-between p-2 bg-gray-800/30 rounded">
                        <div className="flex-1">
                          <div className="text-white text-sm font-medium">{tutorial.name}</div>
                          <div className="text-xs text-gray-400">{tutorial.duration}</div>
                        </div>
                        <Badge variant={
                          tutorial.status === 'completed' ? 'default' :
                          tutorial.status === 'in-progress' ? 'secondary' : 'outline'
                        } className="text-xs">
                          {tutorial.status === 'completed' ? '✓' :
                           tutorial.status === 'in-progress' ? `${tutorial.progress}%` : '🔒'}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Challenges */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Gamepad2 className="w-5 h-5 text-green-400" />
                    Challenges & Labs
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center mb-4">
                    <div className="text-3xl font-bold text-green-400 mb-1">
                      {currentChild.curriculum.challenges.percentage}%
                    </div>
                    <div className="text-sm text-gray-400">
                      {currentChild.curriculum.challenges.completedChallenges}/{currentChild.curriculum.challenges.totalChallenges} challenges
                    </div>
                    <Progress value={currentChild.curriculum.challenges.percentage} className="h-2 mt-2" />
                  </div>

                  <div className="space-y-3">
                    {currentChild.curriculum.challenges.categories.map((category, _index) => (
                      <div key={_index} className="p-2 bg-gray-800/30 rounded">
                        <div className="flex justify-between items-center mb-2">
                          <div className="text-white text-sm font-medium">{category.name}</div>
                          <div className="text-xs text-gray-400">{category.completed}/{category.total}</div>
                        </div>
                        <Progress value={(category.completed / category.total) * 100} className="h-1" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Interactive Curriculum Planner */}
            <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
              <CardHeader>
                <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                  <Calendar className="w-5 h-5 text-cyan-400" />
                  Interactive Curriculum Planner
                </CardTitle>
                <div className="flex items-center gap-2 mt-2">
                  <Button
                    size="sm"
                    className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600"
                    onClick={() => window.location.href = '/parent-dashboard/curriculum'}
                  >
                    <Calendar className="w-4 h-4 mr-2" />
                    Open Full Planner
                  </Button>
                  <Badge variant="outline" className="border-cyan-500/30 text-cyan-400">
                    Flowchart View Available
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Interactive Curriculum Flow Preview */}
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-white font-semibold flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-cyan-400" />
                        Learning Path Flow - {currentChild.name}
                      </h3>
                      <Badge variant="outline" className="border-cyan-500/30 text-cyan-400">
                        Interactive Preview
                      </Badge>
                    </div>

                    {/* Subject Flow Lanes */}
                    <div className="space-y-4">
                      {/* Quantum Physics Flow */}
                      <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-lg p-4">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="p-2 bg-blue-500/20 rounded-lg">
                            <span className="text-lg">⚛️</span>
                          </div>
                          <div className="flex-1">
                            <h4 className="text-white font-medium">Quantum Physics</h4>
                            <div className="flex items-center gap-2 text-xs text-gray-400">
                              <span>5 lessons</span>
                              <div className="w-16 bg-gray-700 rounded-full h-1">
                                <div className="bg-blue-400 h-1 rounded-full" style={{width: '60%'}}></div>
                              </div>
                              <span>60%</span>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2 overflow-x-auto">
                          <div className="flex items-center space-x-1 min-w-max">
                            <div className="w-8 h-8 rounded-full bg-green-500/20 border border-green-500 flex items-center justify-center">
                              <CheckCircle className="w-4 h-4 text-green-400" />
                            </div>
                            <ArrowRight className="w-3 h-3 text-gray-500" />
                            <div className="w-8 h-8 rounded-full bg-green-500/20 border border-green-500 flex items-center justify-center">
                              <CheckCircle className="w-4 h-4 text-green-400" />
                            </div>
                            <ArrowRight className="w-3 h-3 text-gray-500" />
                            <div className="w-8 h-8 rounded-full bg-yellow-500/20 border border-yellow-500 flex items-center justify-center">
                              <Clock className="w-4 h-4 text-yellow-400" />
                            </div>
                            <ArrowRight className="w-3 h-3 text-gray-500" />
                            <div className="w-8 h-8 rounded-full bg-gray-500/20 border border-gray-500 flex items-center justify-center">
                              <Lock className="w-4 h-4 text-gray-400" />
                            </div>
                            <ArrowRight className="w-3 h-3 text-gray-500" />
                            <div className="w-8 h-8 rounded-full bg-gray-500/20 border border-gray-500 flex items-center justify-center">
                              <Lock className="w-4 h-4 text-gray-400" />
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Critical Thinking Flow */}
                      <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-lg p-4">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="p-2 bg-purple-500/20 rounded-lg">
                            <span className="text-lg">🤔</span>
                          </div>
                          <div className="flex-1">
                            <h4 className="text-white font-medium">Critical Thinking</h4>
                            <div className="flex items-center gap-2 text-xs text-gray-400">
                              <span>4 lessons</span>
                              <div className="w-16 bg-gray-700 rounded-full h-1">
                                <div className="bg-purple-400 h-1 rounded-full" style={{width: '25%'}}></div>
                              </div>
                              <span>25%</span>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2 overflow-x-auto">
                          <div className="flex items-center space-x-1 min-w-max">
                            <div className="w-8 h-8 rounded-full bg-green-500/20 border border-green-500 flex items-center justify-center">
                              <CheckCircle className="w-4 h-4 text-green-400" />
                            </div>
                            <ArrowRight className="w-3 h-3 text-gray-500" />
                            <div className="w-8 h-8 rounded-full bg-yellow-500/20 border border-yellow-500 flex items-center justify-center">
                              <Clock className="w-4 h-4 text-yellow-400" />
                            </div>
                            <ArrowRight className="w-3 h-3 text-gray-500" />
                            <div className="w-8 h-8 rounded-full bg-gray-500/20 border border-gray-500 flex items-center justify-center">
                              <Lock className="w-4 h-4 text-gray-400" />
                            </div>
                            <ArrowRight className="w-3 h-3 text-gray-500" />
                            <div className="w-8 h-8 rounded-full bg-gray-500/20 border border-gray-500 flex items-center justify-center">
                              <Lock className="w-4 h-4 text-gray-400" />
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Cybersecurity Flow */}
                      <div className="bg-gradient-to-r from-red-500/10 to-orange-500/10 border border-red-500/20 rounded-lg p-4">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="p-2 bg-red-500/20 rounded-lg">
                            <span className="text-lg">🛡️</span>
                          </div>
                          <div className="flex-1">
                            <h4 className="text-white font-medium">Cybersecurity</h4>
                            <div className="flex items-center gap-2 text-xs text-gray-400">
                              <span>6 lessons</span>
                              <div className="w-16 bg-gray-700 rounded-full h-1">
                                <div className="bg-red-400 h-1 rounded-full" style={{width: '0%'}}></div>
                              </div>
                              <span>0%</span>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2 overflow-x-auto">
                          <div className="flex items-center space-x-1 min-w-max">
                            <div className="w-8 h-8 rounded-full bg-gray-500/20 border border-gray-500 flex items-center justify-center">
                              <Lock className="w-4 h-4 text-gray-400" />
                            </div>
                            <ArrowRight className="w-3 h-3 text-gray-500" />
                            <div className="w-8 h-8 rounded-full bg-gray-500/20 border border-gray-500 flex items-center justify-center">
                              <Lock className="w-4 h-4 text-gray-400" />
                            </div>
                            <ArrowRight className="w-3 h-3 text-gray-500" />
                            <div className="w-8 h-8 rounded-full bg-gray-500/20 border border-gray-500 flex items-center justify-center">
                              <Lock className="w-4 h-4 text-gray-400" />
                            </div>
                            <ArrowRight className="w-3 h-3 text-gray-500" />
                            <div className="w-8 h-8 rounded-full bg-gray-500/20 border border-gray-500 flex items-center justify-center">
                              <Lock className="w-4 h-4 text-gray-400" />
                            </div>
                            <ArrowRight className="w-3 h-3 text-gray-500" />
                            <div className="w-8 h-8 rounded-full bg-gray-500/20 border border-gray-500 flex items-center justify-center">
                              <Lock className="w-4 h-4 text-gray-400" />
                            </div>
                            <ArrowRight className="w-3 h-3 text-gray-500" />
                            <div className="w-8 h-8 rounded-full bg-gray-500/20 border border-gray-500 flex items-center justify-center">
                              <Lock className="w-4 h-4 text-gray-400" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="text-center mt-4 p-3 bg-gray-800/20 rounded-lg">
                      <p className="text-sm text-gray-400 mb-2">
                        🎯 Visual lesson progression • 🔗 Prerequisites tracking • 📅 Interactive scheduling
                      </p>
                      <Button
                        size="sm"
                        className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600"
                        onClick={() => window.location.href = '/parent-dashboard/curriculum'}
                      >
                        <Calendar className="w-4 h-4 mr-2" />
                        Open Full Flowchart Planner
                      </Button>
                    </div>
                  </div>

                  {/* Life Skills Detailed */}
                  <div>
                    <h3 className="text-white font-semibold mb-3 flex items-center gap-2">
                      <Heart className="w-4 h-4 text-pink-400" />
                      Life Skills Modules
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {currentChild.curriculum.lifeSkills.modules.map((module, index) => (
                        <div key={index} className="p-3 bg-gray-800/30 rounded-lg relative">
                          {/* Flow Connection Line */}
                          {index < currentChild.curriculum.lifeSkills.modules.length - 1 && (
                            <div className="absolute -right-1 top-1/2 w-2 h-0.5 bg-gradient-to-r from-cyan-500/50 to-transparent" />
                          )}

                          <div className="flex justify-between items-center mb-2">
                            <div className="flex items-center gap-2">
                              <div className={`w-3 h-3 rounded-full ${
                                module.status === 'completed' ? 'bg-green-500' :
                                module.status === 'in-progress' ? 'bg-yellow-500' : 'bg-gray-500'
                              }`} />
                              <span className="text-white font-medium">{module.name}</span>
                            </div>
                            <Badge variant={
                              module.status === 'completed' ? 'default' :
                              module.status === 'in-progress' ? 'secondary' : 'outline'
                            }>
                              {module.status === 'completed' ? 'Completed' :
                               module.status === 'in-progress' ? 'In Progress' : 'Locked'}
                            </Badge>
                          </div>
                          <div className="flex justify-between text-sm text-gray-400 mb-2">
                            <span>Topics: {module.completedTopics}/{module.topics}</span>
                            <span>{module.progress}%</span>
                          </div>
                          <Progress value={module.progress} className="h-2" />
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Challenge Categories Detailed */}
                  <div>
                    <h3 className="text-white font-semibold mb-3 flex items-center gap-2">
                      <Gamepad2 className="w-4 h-4 text-green-400" />
                      Challenge Flow Path
                    </h3>
                    <div className="space-y-4">
                      {currentChild.curriculum.challenges.categories.map((category, categoryIndex) => (
                        <div key={categoryIndex} className="relative">
                          {/* Category Flow Connection */}
                          {categoryIndex < currentChild.curriculum.challenges.categories.length - 1 && (
                            <div className="absolute left-6 top-16 w-0.5 h-8 bg-gradient-to-b from-green-500/50 to-cyan-500/50" />
                          )}

                          <div className="p-4 bg-gray-800/30 rounded-lg">
                            <div className="flex items-center gap-3 mb-3">
                              {/* Category Flow Node */}
                              <div className={`w-12 h-12 rounded-full border-2 flex items-center justify-center ${
                                category.completed === category.total
                                  ? 'bg-green-500/20 border-green-500/50'
                                  : category.completed > 0
                                    ? 'bg-yellow-500/20 border-yellow-500/50'
                                    : 'bg-gray-500/20 border-gray-500/50'
                              }`}>
                                {category.completed === category.total ? (
                                  <CheckCircle className="w-6 h-6 text-green-400" />
                                ) : category.completed > 0 ? (
                                  <Clock className="w-6 h-6 text-yellow-400" />
                                ) : (
                                  <Lock className="w-6 h-6 text-gray-400" />
                                )}
                              </div>

                              <div className="flex-1">
                                <h4 className="text-white font-medium">{category.name}</h4>
                                <div className="flex items-center gap-4 text-sm text-gray-400">
                                  <span>{category.completed}/{category.total} completed</span>
                                  <div className="flex-1 max-w-32">
                                    <Progress value={(category.completed / category.total) * 100} className="h-1" />
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Challenge Flow Grid */}
                            <div className="ml-15 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                              {category.challenges.map((challenge, challengeIndex) => (
                                <div key={challengeIndex} className="relative p-2 bg-gray-900/50 rounded">
                                  {/* Challenge Connection Line */}
                                  {challengeIndex < category.challenges.length - 1 && challengeIndex % 3 !== 2 && (
                                    <div className="absolute -right-1 top-1/2 w-2 h-0.5 bg-gradient-to-r from-cyan-500/30 to-transparent" />
                                  )}

                                  <div className="flex items-center gap-2 mb-1">
                                    <div className={`w-2 h-2 rounded-full ${
                                      challenge.status === 'completed' ? 'bg-green-500' :
                                      challenge.status === 'in-progress' ? 'bg-yellow-500' : 'bg-gray-500'
                                    }`} />
                                    <span className="text-white text-sm flex-1">{challenge.name}</span>
                                    <Badge variant="outline" className="text-xs">
                                      {challenge.difficulty}
                                    </Badge>
                                  </div>
                                  <div className="flex justify-between items-center">
                                    <span className="text-xs text-gray-400">{challenge.points} pts</span>
                                    <Badge variant={
                                      challenge.status === 'completed' ? 'default' :
                                      challenge.status === 'in-progress' ? 'secondary' : 'outline'
                                    } className="text-xs">
                                      {challenge.status === 'completed' ? '✓' :
                                       challenge.status === 'in-progress' ? '⏳' : '🔒'}
                                    </Badge>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Achievements Tab */}
          <TabsContent value="achievements" className="space-y-6 mt-6">
            {/* Achievement Stats Overview */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardContent className="p-4 text-center">
                  <Trophy className="w-8 h-8 mx-auto mb-2 text-yellow-400" />
                  <div className="text-2xl font-bold text-white">{currentChild.achievements?.totalBadges || 0}</div>
                  <div className="text-xs text-gray-400">Total Badges</div>
                </CardContent>
              </Card>

              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardContent className="p-4 text-center">
                  <Star className="w-8 h-8 mx-auto mb-2 text-purple-400" />
                  <div className="text-2xl font-bold text-white">{currentChild.achievements?.totalPoints || 0}</div>
                  <div className="text-xs text-gray-400">Total Points</div>
                </CardContent>
              </Card>

              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardContent className="p-4 text-center">
                  <Flame className="w-8 h-8 mx-auto mb-2 text-orange-400" />
                  <div className="text-2xl font-bold text-white">{currentChild.achievements?.currentStreak || 0}</div>
                  <div className="text-xs text-gray-400">Day Streak</div>
                </CardContent>
              </Card>

              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardContent className="p-4 text-center">
                  <Target className="w-8 h-8 mx-auto mb-2 text-green-400" />
                  <div className="text-2xl font-bold text-white">{currentChild.achievements?.inProgressBadges?.length || 0}</div>
                  <div className="text-xs text-gray-400">In Progress</div>
                </CardContent>
              </Card>
            </div>

            {/* Trophy Wall - Earned Badges */}
            <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
              <CardHeader>
                <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                  <Crown className="w-5 h-5 text-yellow-400" />
                  Trophy Wall - Earned Badges
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {(currentChild.achievements?.earnedBadges || []).map((badge, _index) => (
                    <motion.div
                      key={badge.id}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: _index * 0.1 }}
                      className="p-4 bg-gradient-to-br from-yellow-500/10 to-orange-500/10 rounded-lg border border-yellow-500/20"
                    >
                      <div className="text-center">
                        <div className="text-4xl mb-2">{badge.icon}</div>
                        <h3 className="text-white font-semibold text-lg">{badge.name}</h3>
                        <p className="text-gray-400 text-sm mb-2">{badge.description}</p>

                        <div className="flex justify-between items-center mb-2">
                          <Badge variant={
                            badge.rarity === 'legendary' ? 'default' :
                            badge.rarity === 'epic' ? 'secondary' :
                            badge.rarity === 'rare' ? 'outline' : 'outline'
                          } className={`text-xs ${
                            badge.rarity === 'legendary' ? 'bg-purple-500/20 text-purple-400 border-purple-500/30' :
                            badge.rarity === 'epic' ? 'bg-blue-500/20 text-blue-400 border-blue-500/30' :
                            badge.rarity === 'rare' ? 'bg-green-500/20 text-green-400 border-green-500/30' :
                            'bg-gray-500/20 text-gray-400 border-gray-500/30'
                          }`}>
                            {badge.rarity}
                          </Badge>
                          <span className="text-yellow-400 text-sm font-medium">{badge.points} pts</span>
                        </div>

                        <div className="text-xs text-gray-500">
                          Earned: {new Date(badge.earnedDate).toLocaleDateString()}
                        </div>
                        <div className="text-xs text-cyan-400 mt-1">{badge.category}</div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* In Progress & Goals */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* In Progress Badges */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Activity className="w-5 h-5 text-blue-400" />
                    In Progress Badges
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {(currentChild.achievements?.inProgressBadges || []).map((badge, _index) => (
                    <div key={badge.id} className="p-4 bg-gray-800/30 rounded-lg">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="text-2xl">{badge.icon}</div>
                        <div className="flex-1">
                          <h4 className="text-white font-medium">{badge.name}</h4>
                          <p className="text-gray-400 text-sm">{badge.description}</p>
                        </div>
                        <Badge variant="outline" className="text-blue-400 border-blue-500/30">
                          {badge.rarity}
                        </Badge>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Progress: {badge.current}/{badge.requirement}</span>
                          <span className="text-white">{badge.progress}%</span>
                        </div>
                        <Progress value={badge.progress} className="h-2" />
                        <div className="text-xs text-yellow-400">{badge.points} points when completed</div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Next Goals */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Target className="w-5 h-5 text-green-400" />
                    Next Goals
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {(currentChild.achievements?.nextGoals || []).map((goal, _index) => (
                    <div key={goal.id} className="p-4 bg-gray-800/30 rounded-lg">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="text-white font-medium">{goal.name}</h4>
                        <Badge variant={
                          goal.priority === 'high' ? 'default' :
                          goal.priority === 'medium' ? 'secondary' : 'outline'
                        } className={`text-xs ${
                          goal.priority === 'high' ? 'bg-red-500/20 text-red-400 border-red-500/30' :
                          goal.priority === 'medium' ? 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30' :
                          'bg-gray-500/20 text-gray-400 border-gray-500/30'
                        }`}>
                          {goal.priority} priority
                        </Badge>
                      </div>
                      <p className="text-gray-400 text-sm mb-2">{goal.description}</p>
                      <div className="text-xs text-cyan-400">
                        Target: {new Date(goal.targetDate).toLocaleDateString()}
                      </div>
                    </div>
                  ))}

                  <Button className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600">
                    <Plus className="w-4 h-4 mr-2" />
                    Set New Goal
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Milestones */}
            <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
              <CardHeader>
                <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                  <Medal className="w-5 h-5 text-orange-400" />
                  Recent Milestones
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {(currentChild.achievements?.milestones || []).map((milestone, index) => (
                    <motion.div
                      key={milestone.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="p-3 bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-lg border border-orange-500/20 text-center"
                    >
                      <div className="text-3xl mb-2">{milestone.icon}</div>
                      <h4 className="text-white font-medium text-sm">{milestone.name}</h4>
                      <p className="text-gray-400 text-xs mb-2">{milestone.description}</p>
                      <div className="text-xs text-orange-400">{milestone.points} pts</div>
                      <div className="text-xs text-gray-500 mt-1">
                        {new Date(milestone.achievedDate).toLocaleDateString()}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Locked Badges Preview */}
            <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
              <CardHeader>
                <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                  <Lock className="w-5 h-5 text-gray-400" />
                  Upcoming Badges
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {(currentChild.achievements?.lockedBadges || []).map((badge, _index) => (
                    <div key={badge.id} className="p-4 bg-gray-800/30 rounded-lg opacity-60">
                      <div className="flex items-center gap-3 mb-2">
                        <div className="text-2xl grayscale">{badge.icon}</div>
                        <div className="flex-1">
                          <h4 className="text-gray-300 font-medium">{badge.name}</h4>
                          <p className="text-gray-500 text-sm">{badge.description}</p>
                        </div>
                        <Lock className="w-4 h-4 text-gray-500" />
                      </div>
                      <div className="flex justify-between items-center">
                        <Badge variant="outline" className="text-xs text-gray-500 border-gray-600">
                          {badge.rarity}
                        </Badge>
                        <span className="text-gray-500 text-sm">{badge.points} pts</span>
                      </div>
                      <div className="text-xs text-gray-500 mt-2">
                        Unlock: {badge.unlockRequirement}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Weekly Activity Tab */}
          <TabsContent value="activity" className="space-y-6 mt-6">
            {/* Activity Overview Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardContent className="p-4 text-center">
                  <Flame className="w-8 h-8 mx-auto mb-2 text-orange-400" />
                  <div className="text-2xl font-bold text-white">{currentChild.streakData.currentStreak}</div>
                  <div className="text-xs text-gray-400">Current Streak</div>
                  <div className="text-xs text-orange-400 mt-1">days in a row</div>
                </CardContent>
              </Card>

              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardContent className="p-4 text-center">
                  <Timer className="w-8 h-8 mx-auto mb-2 text-blue-400" />
                  <div className="text-2xl font-bold text-white">{currentChild.streakData.averageDailyMinutes}</div>
                  <div className="text-xs text-gray-400">Avg Daily Minutes</div>
                  <div className="text-xs text-blue-400 mt-1">this month</div>
                </CardContent>
              </Card>

              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardContent className="p-4 text-center">
                  <PlayCircle className="w-8 h-8 mx-auto mb-2 text-green-400" />
                  <div className="text-2xl font-bold text-white">{currentChild.streakData.totalActiveDays}</div>
                  <div className="text-xs text-gray-400">Active Days</div>
                  <div className="text-xs text-green-400 mt-1">this month</div>
                </CardContent>
              </Card>

              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardContent className="p-4 text-center">
                  <Target className="w-8 h-8 mx-auto mb-2 text-purple-400" />
                  <div className="text-2xl font-bold text-white">{currentChild.streakData.weeklyConsistency}%</div>
                  <div className="text-xs text-gray-400">Weekly Consistency</div>
                  <div className="text-xs text-purple-400 mt-1">learning regularity</div>
                </CardContent>
              </Card>
            </div>

            {/* GitHub-style Activity Heatmap */}
            <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
              <CardHeader>
                <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                  <CalendarDays className="w-5 h-5 text-green-400" />
                  Learning Activity Heatmap
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ActivityHeatmap activityData={currentChild.activityHeatmap} childName={currentChild.name} />
              </CardContent>
            </Card>

            {/* Weekly Breakdown & Streak History */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* This Week's Activity */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <BarChart3 className="w-5 h-5 text-blue-400" />
                    This Week&apos;s Activity
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {currentChild.weeklyActivity.map((day, _index) => (
                      <div key={day.day} className="flex items-center gap-4">
                        <div className="w-12 text-gray-400 text-sm font-medium">{day.day}</div>
                        <div className="flex-1">
                          <div className="flex justify-between text-xs mb-1">
                            <span className="text-gray-400">{day.minutes} min</span>
                            <span className="text-cyan-400">{day.activities} activities</span>
                            <span className="text-green-400">{day.lessons} lessons</span>
                            <span className="text-lg">{day.mood}</span>
                          </div>
                          <div className="h-3 bg-gray-800 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-gradient-to-r from-blue-500 to-cyan-500 transition-all duration-500"
                              style={{ width: `${Math.min((day.minutes / 300) * 100, 100)}%` }}
                            />
                          </div>
                          <div className="flex justify-between text-xs mt-1">
                            <span className="text-gray-500">Focus: {day.focus}%</span>
                            <span className="text-gray-500">Engagement: {day.engagement}%</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Weekly Summary */}
                  <div className="mt-6 p-4 bg-gray-800/30 rounded-lg">
                    <h4 className="text-white font-medium mb-2">Weekly Summary</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-400">Total Time:</span>
                        <span className="text-white ml-2">
                          {Math.floor(currentChild.weeklyActivity.reduce((sum, day) => sum + day.minutes, 0) / 60)}h {currentChild.weeklyActivity.reduce((sum, day) => sum + day.minutes, 0) % 60}m
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-400">Total Activities:</span>
                        <span className="text-white ml-2">
                          {currentChild.weeklyActivity.reduce((sum, day) => sum + day.activities, 0)}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-400">Total Lessons:</span>
                        <span className="text-white ml-2">
                          {currentChild.weeklyActivity.reduce((sum, day) => sum + day.lessons, 0)}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-400">Avg Focus:</span>
                        <span className="text-white ml-2">
                          {Math.round(currentChild.weeklyActivity.reduce((sum, day) => sum + day.focus, 0) / currentChild.weeklyActivity.length)}%
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Streak History & Consistency */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Flame className="w-5 h-5 text-orange-400" />
                    Streak History & Consistency
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Current Streak Info */}
                  <div className="p-4 bg-gradient-to-r from-orange-500/10 to-red-500/10 rounded-lg border border-orange-500/20">
                    <div className="flex items-center gap-3 mb-2">
                      <Flame className="w-6 h-6 text-orange-400" />
                      <div>
                        <h4 className="text-white font-medium">Current Streak</h4>
                        <p className="text-gray-400 text-sm">Keep it going!</p>
                      </div>
                    </div>
                    <div className="text-2xl font-bold text-orange-400 mb-1">
                      {currentChild.streakData.currentStreak} days
                    </div>
                    <div className="text-sm text-gray-400">
                      Longest streak: {currentChild.streakData.longestStreak} days
                    </div>
                  </div>

                  {/* Consistency Metrics */}
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-400">Weekly Consistency</span>
                        <span className="text-white">{currentChild.streakData.weeklyConsistency}%</span>
                      </div>
                      <Progress value={currentChild.streakData.weeklyConsistency} className="h-2" />
                    </div>

                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-400">Monthly Consistency</span>
                        <span className="text-white">{currentChild.streakData.monthlyConsistency}%</span>
                      </div>
                      <Progress value={currentChild.streakData.monthlyConsistency} className="h-2" />
                    </div>
                  </div>

                  {/* Recent Streak History */}
                  <div>
                    <h4 className="text-white font-medium mb-3">Recent Streaks</h4>
                    <div className="space-y-2">
                      {currentChild.streakData.streakHistory.map((streak, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-gray-800/30 rounded">
                          <div>
                            <span className="text-white text-sm font-medium">{streak.length} days</span>
                            <div className="text-xs text-gray-400">
                              {new Date(streak.startDate).toLocaleDateString()} - {new Date(streak.endDate).toLocaleDateString()}
                            </div>
                          </div>
                          <Badge variant={streak.status === 'active' ? 'default' : 'outline'} className="text-xs">
                            {streak.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Daily Activity Patterns */}
            <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
              <CardHeader>
                <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                  <Clock className="w-5 h-5 text-purple-400" />
                  Daily Activity Patterns
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Peak Activity Times */}
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-400 mb-1">3:00 PM</div>
                    <div className="text-sm text-gray-400 mb-1">Peak Learning Time</div>
                    <div className="text-xs text-gray-500">Highest engagement period</div>
                  </div>

                  {/* Most Active Day */}
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-400 mb-1">Saturday</div>
                    <div className="text-sm text-gray-400 mb-1">Most Active Day</div>
                    <div className="text-xs text-gray-500">Average 4.2 hours</div>
                  </div>

                  {/* Preferred Session Length */}
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-400 mb-1">45 min</div>
                    <div className="text-sm text-gray-400 mb-1">Optimal Session</div>
                    <div className="text-xs text-gray-500">Best focus duration</div>
                  </div>
                </div>

                {/* Activity Distribution */}
                <div className="mt-6">
                  <h4 className="text-white font-medium mb-3">Activity Distribution</h4>
                  <div className="grid grid-cols-7 gap-2">
                    {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => {
                      const dayData = currentChild.weeklyActivity[index]
                      const intensity = Math.min((dayData.minutes / 300) * 100, 100)
                      return (
                        <div key={day} className="text-center">
                          <div className="text-xs text-gray-400 mb-1">{day}</div>
                          <div
                            className="h-16 bg-gradient-to-t from-purple-500 to-pink-400 rounded mx-auto"
                            style={{
                              width: '20px',
                              opacity: intensity / 100
                            }}
                          />
                          <div className="text-xs text-white mt-1">{dayData.minutes}m</div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  )
}