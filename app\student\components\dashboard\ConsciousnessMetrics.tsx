'use client'

import { motion } from 'framer-motion'
import { Activity } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { MetricsComponentProps, EvolutionTheme } from './types'

interface ConsciousnessMetricsProps extends MetricsComponentProps {
  evolutionTheme: EvolutionTheme
}

export default function ConsciousnessMetrics({ 
  systemStatus, 
  playerData,
  evolutionTheme 
}: ConsciousnessMetricsProps) {
  return (
    <Card className="bg-black/40 border-cyan-500/30 backdrop-blur-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-cyan-400">
          <Activity className="w-5 h-5" />
          Consciousness Metrics
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Consciousness Energy */}
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-white/80">Consciousness Energy</span>
              <span className="font-bold text-cyan-400">
                {Math.round((systemStatus?.consciousnessLevel || 0) * 100)}
              </span>
            </div>
            <div className="relative h-3 bg-space-dark/60 rounded-full overflow-hidden">
              <motion.div
                className="absolute inset-y-0 left-0 bg-gradient-to-r from-cyan-500 to-cyan-400 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${Math.round((systemStatus?.consciousnessLevel || 0) * 100)}%` }}
                transition={{ duration: 1.5, ease: 'easeOut' }}
              />
            </div>
          </div>

          {/* Quantum Stability */}
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-white/80">Quantum Stability</span>
              <span className="font-bold text-purple-400">
                {Math.round((systemStatus?.systemHealth || 0) * 100)}
              </span>
            </div>
            <div className="relative h-3 bg-space-dark/60 rounded-full overflow-hidden">
              <motion.div
                className="absolute inset-y-0 left-0 bg-gradient-to-r from-purple-500 to-purple-400 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${Math.round((systemStatus?.systemHealth || 0) * 100)}%` }}
                transition={{ duration: 1.5, ease: 'easeOut', delay: 0.3 }}
              />
            </div>
          </div>

          {/* Total Power */}
          <div className="pt-4 border-t border-white/10">
            <div className="flex justify-between items-center">
              <span className="text-white/80">Total Power Level</span>
              <span className="font-bold text-xl" style={{ color: evolutionTheme.primary }}>
                {Math.round(((systemStatus?.consciousnessLevel || 0) + (systemStatus?.systemHealth || 0)) * 100)}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
