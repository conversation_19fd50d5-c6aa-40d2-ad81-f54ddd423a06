import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-gradient-to-r from-neural-cyan to-quantum-purple text-white font-medium hover:from-neural-cyan/80 hover:to-quantum-purple/80 border border-neural-cyan/40",
        destructive:
          "bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 border border-red-500/40",
        outline:
          "border border-white/20 bg-black/40 backdrop-blur-sm text-white/80 hover:bg-white/10 hover:text-white hover:border-neural-cyan/40",
        secondary:
          "bg-black/40 backdrop-blur-sm text-white/80 hover:bg-black/60 border border-white/20",
        ghost: "text-white/70 hover:bg-white/10 hover:text-white",
        link: "text-neural-cyan underline-offset-4 hover:underline hover:text-neural-cyan/80",
        quantum: "bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold text-white font-bold shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 border border-neural-cyan/40",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        xl: "h-14 rounded-xl px-12 text-lg",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  glow?: boolean
  pulse?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, glow = false, pulse = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(
          buttonVariants({ variant, size }),
          glow && "shadow-lg shadow-cyan-400/50",
          pulse && "animate-pulse-glow",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
