/**
 * CSS Performance Optimization Utilities
 * Provides tools for optimizing CSS performance, especially will-change properties
 */

/**
 * Will-change property manager
 * Automatically manages will-change properties to optimize performance
 */
export class WillChangeManager {
  private static instance: WillChangeManager
  private activeElements = new Map<HTMLElement, Set<string>>()
  private cleanupTimeouts = new Map<HTMLElement, NodeJS.Timeout>()

  static getInstance(): WillChangeManager {
    if (!WillChangeManager.instance) {
      WillChangeManager.instance = new WillChangeManager()
    }
    return WillChangeManager.instance
  }

  /**
   * Add will-change property to element
   */
  addWillChange(element: HTMLElement, properties: string[], duration: number = 5000) {
    if (!element) return

    // Get existing properties or create new set
    const existingProps = this.activeElements.get(element) || new Set<string>()
    
    // Add new properties
    properties.forEach(prop => existingProps.add(prop))
    this.activeElements.set(element, existingProps)

    // Apply will-change
    element.style.willChange = Array.from(existingProps).join(', ')

    // Clear existing timeout
    const existingTimeout = this.cleanupTimeouts.get(element)
    if (existingTimeout) {
      clearTimeout(existingTimeout)
    }

    // Set cleanup timeout
    const timeout = setTimeout(() => {
      this.removeWillChange(element, properties)
    }, duration)
    
    this.cleanupTimeouts.set(element, timeout)
  }

  /**
   * Remove will-change properties from element
   */
  removeWillChange(element: HTMLElement, properties?: string[]) {
    if (!element) return

    const existingProps = this.activeElements.get(element)
    if (!existingProps) return

    if (properties) {
      // Remove specific properties
      properties.forEach(prop => existingProps.delete(prop))
      
      if (existingProps.size > 0) {
        element.style.willChange = Array.from(existingProps).join(', ')
      } else {
        element.style.willChange = 'auto'
        this.activeElements.delete(element)
      }
    } else {
      // Remove all properties
      element.style.willChange = 'auto'
      this.activeElements.delete(element)
    }

    // Clear timeout
    const timeout = this.cleanupTimeouts.get(element)
    if (timeout) {
      clearTimeout(timeout)
      this.cleanupTimeouts.delete(element)
    }
  }

  /**
   * Clean up all will-change properties
   */
  cleanup() {
    this.activeElements.forEach((_, element) => {
      element.style.willChange = 'auto'
    })
    
    this.cleanupTimeouts.forEach(timeout => clearTimeout(timeout))
    
    this.activeElements.clear()
    this.cleanupTimeouts.clear()
  }

  /**
   * Get stats about active will-change properties
   */
  getStats() {
    return {
      activeElements: this.activeElements.size,
      totalProperties: Array.from(this.activeElements.values())
        .reduce((total, props) => total + props.size, 0)
    }
  }
}

export const willChangeManager = WillChangeManager.getInstance()

/**
 * CSS animation performance utilities
 */
export const cssAnimationUtils = {
  /**
   * Create optimized CSS keyframes
   */
  createKeyframes(name: string, keyframes: Record<string, Record<string, string>>): string {
    const keyframeRules = Object.entries(keyframes)
      .map(([percentage, styles]) => {
        const styleRules = Object.entries(styles)
          .map(([property, value]) => `${property}: ${value}`)
          .join('; ')
        return `${percentage} { ${styleRules} }`
      })
      .join('\n  ')

    return `@keyframes ${name} {\n  ${keyframeRules}\n}`
  },

  /**
   * Apply optimized animation to element
   */
  applyAnimation(
    element: HTMLElement, 
    animationName: string, 
    duration: number = 1000,
    easing: string = 'ease-out',
    fillMode: string = 'both'
  ) {
    // Add will-change before animation
    willChangeManager.addWillChange(element, ['transform', 'opacity'], duration + 100)

    element.style.animation = `${animationName} ${duration}ms ${easing} ${fillMode}`

    // Clean up after animation
    setTimeout(() => {
      element.style.animation = ''
    }, duration)
  },

  /**
   * Create CSS transform string with hardware acceleration
   */
  createTransform(transforms: Record<string, string | number>): string {
    const transformParts: string[] = []

    // Always include translate3d for hardware acceleration
    if (!transforms.translateX && !transforms.translateY && !transforms.translateZ) {
      transformParts.push('translate3d(0, 0, 0)')
    }

    Object.entries(transforms).forEach(([property, value]) => {
      switch (property) {
        case 'translateX':
        case 'translateY':
        case 'translateZ':
          // Handle individual translate properties
          break
        case 'translate':
          transformParts.push(`translate(${value})`)
          break
        case 'translate3d':
          transformParts.push(`translate3d(${value})`)
          break
        case 'scale':
          transformParts.push(`scale(${value})`)
          break
        case 'scaleX':
        case 'scaleY':
        case 'scaleZ':
          transformParts.push(`${property}(${value})`)
          break
        case 'rotate':
          transformParts.push(`rotate(${value})`)
          break
        case 'rotateX':
        case 'rotateY':
        case 'rotateZ':
          transformParts.push(`${property}(${value})`)
          break
        case 'skew':
        case 'skewX':
        case 'skewY':
          transformParts.push(`${property}(${value})`)
          break
      }
    })

    // Handle individual translate properties
    const translateX = transforms.translateX || 0
    const translateY = transforms.translateY || 0
    const translateZ = transforms.translateZ || 0
    
    if (translateX || translateY || translateZ) {
      transformParts.unshift(`translate3d(${translateX}, ${translateY}, ${translateZ})`)
    }

    return transformParts.join(' ')
  }
}

/**
 * Performance-optimized CSS classes
 */
export const optimizedCSSClasses = {
  // Hardware acceleration
  hwAccelerated: 'transform-gpu will-change-transform',
  
  // Optimized animations
  fadeIn: 'animate-fade-in will-change-opacity',
  slideUp: 'animate-slide-up will-change-transform',
  scaleIn: 'animate-scale-in will-change-transform',
  
  // Performance-friendly transitions
  smoothTransition: 'transition-all duration-300 ease-out will-change-auto',
  fastTransition: 'transition-all duration-150 ease-out will-change-auto',
  
  // Layout optimization
  containLayout: 'contain-layout',
  containPaint: 'contain-paint',
  containStyle: 'contain-style',
  containStrict: 'contain-strict'
}

/**
 * CSS performance monitoring
 */
export class CSSPerformanceMonitor {
  private animationCount = 0
  private transitionCount = 0
  private willChangeCount = 0

  trackAnimation() {
    this.animationCount++
  }

  trackTransition() {
    this.transitionCount++
  }

  trackWillChange() {
    this.willChangeCount++
  }

  getStats() {
    return {
      animations: this.animationCount,
      transitions: this.transitionCount,
      willChangeProperties: this.willChangeCount
    }
  }

  reset() {
    this.animationCount = 0
    this.transitionCount = 0
    this.willChangeCount = 0
  }
}

export const cssPerformanceMonitor = new CSSPerformanceMonitor()

/**
 * Utility for creating performance-optimized CSS
 */
export const createOptimizedCSS = {
  /**
   * Create animation with automatic will-change management
   */
  animation: (
    name: string,
    duration: string = '0.3s',
    easing: string = 'ease-out',
    fillMode: string = 'both'
  ) => ({
    animation: `${name} ${duration} ${easing} ${fillMode}`,
    willChange: 'transform, opacity'
  }),

  /**
   * Create transition with automatic will-change
   */
  transition: (
    properties: string[] = ['all'],
    duration: string = '0.3s',
    easing: string = 'ease-out'
  ) => ({
    transition: `${properties.join(', ')} ${duration} ${easing}`,
    willChange: properties.includes('transform') || properties.includes('all') 
      ? 'transform, opacity' 
      : properties.join(', ')
  }),

  /**
   * Create transform with hardware acceleration
   */
  transform: (transforms: Record<string, string | number>) => ({
    transform: cssAnimationUtils.createTransform(transforms),
    willChange: 'transform'
  }),

  /**
   * Create optimized hover effect
   */
  hover: (scale: number = 1.05, duration: string = '0.2s') => ({
    transition: `transform ${duration} ease-out`,
    willChange: 'transform',
    ':hover': {
      transform: `scale(${scale})`
    }
  })
}

/**
 * React hook for managing will-change properties
 */
export const useWillChangeOptimization = () => {
  const addWillChange = (element: HTMLElement, properties: string[], duration?: number) => {
    willChangeManager.addWillChange(element, properties, duration)
  }

  const removeWillChange = (element: HTMLElement, properties?: string[]) => {
    willChangeManager.removeWillChange(element, properties)
  }

  return { addWillChange, removeWillChange }
}

/**
 * Global CSS optimization configuration
 */
export const globalCSSConfig = {
  // Enable/disable hardware acceleration
  enableHardwareAcceleration: true,
  
  // Default animation duration
  defaultAnimationDuration: 300,
  
  // Default easing function
  defaultEasing: 'ease-out',
  
  // Automatic will-change cleanup duration
  willChangeCleanupDelay: 5000,
  
  // Performance monitoring
  enablePerformanceMonitoring: true
}
