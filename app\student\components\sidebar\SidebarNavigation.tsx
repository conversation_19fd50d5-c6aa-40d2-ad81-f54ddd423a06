'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { MenuItem, EvolutionTheme } from './types'
import { getMenuItemStyle, getIconStyle } from './styles'

interface SidebarNavigationProps {
  menuItems: MenuItem[]
  activeItem: string
  isCollapsed: boolean
  evolutionTheme: EvolutionTheme
  onItemClick: (item: MenuItem) => void
}

export function SidebarNavigation({
  menuItems,
  activeItem,
  isCollapsed,
  evolutionTheme,
  onItemClick
}: SidebarNavigationProps) {
  return (
    <div className="flex-1 overflow-y-auto py-6">
      <nav className="space-y-3 px-4">
        {menuItems.map((item) => (
          <div key={item.id}>
            <motion.button
              style={getMenuItemStyle(
                activeItem === item.id || activeItem.startsWith(`${item.id}-`),
                false,
                evolutionTheme
              )}
              onClick={() => onItemClick(item)}
              whileHover={{
                scale: 1.02,
                boxShadow: activeItem === item.id || activeItem.startsWith(`${item.id}-`)
                  ? "0 8px 32px rgba(34, 211, 238, 0.3)"
                  : "0 4px 20px rgba(255, 255, 255, 0.1)"
              }}
              whileTap={{ scale: 0.98 }}
              onMouseEnter={(e) => {
                const isActive = activeItem === item.id || activeItem.startsWith(`${item.id}-`);
                Object.assign(e.currentTarget.style, getMenuItemStyle(isActive, true, evolutionTheme));
              }}
              onMouseLeave={(e) => {
                const isActive = activeItem === item.id || activeItem.startsWith(`${item.id}-`);
                Object.assign(e.currentTarget.style, getMenuItemStyle(isActive, false, evolutionTheme));
              }}
            >
              {/* Enhanced Quantum Effects */}
              <div className="absolute inset-0 pointer-events-none">
                {/* Quantum sweep effect */}
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                  <div
                    className="absolute inset-0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"
                    style={{
                      background: `linear-gradient(90deg, transparent, ${evolutionTheme.primary}30, transparent)`
                    }}
                  />
                </div>

                {/* Active quantum field */}
                {(activeItem === item.id || activeItem.startsWith(`${item.id}-`)) && (
                  <>
                    <motion.div
                      className="absolute inset-0"
                      style={{
                        background: `linear-gradient(45deg, ${evolutionTheme.primary}08, ${evolutionTheme.secondary}08, ${evolutionTheme.primary}08)`
                      }}
                      animate={{
                        opacity: [0.3, 0.6, 0.3]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    />

                    {/* Quantum particles */}
                    {Array.from({ length: 3 }).map((_, i) => (
                      <motion.div
                        key={i}
                        className="absolute w-1 h-1 rounded-full"
                        style={{
                          backgroundColor: evolutionTheme.primary,
                          left: `${20 + i * 30}%`,
                          top: '50%'
                        }}
                        animate={{
                          x: [0, 50, 0],
                          y: [0, -10, 0],
                          opacity: [0, 1, 0],
                          scale: [0.5, 1, 0.5]
                        }}
                        transition={{
                          duration: 2 + i * 0.5,
                          repeat: Infinity,
                          delay: i * 0.3,
                          ease: "easeInOut"
                        }}
                      />
                    ))}
                  </>
                )}
              </div>

              <item.icon
                style={getIconStyle(
                  activeItem === item.id || activeItem.startsWith(`${item.id}-`),
                  false,
                  false,
                  evolutionTheme
                )}
              />
              
              <AnimatePresence>
                {!isCollapsed && (
                  <motion.div
                    initial={{ opacity: 0, width: 0 }}
                    animate={{ opacity: 1, width: 'auto' }}
                    exit={{ opacity: 0, width: 0 }}
                    className="flex items-center justify-between flex-1 overflow-hidden"
                  >
                    <span className="text-sm font-medium truncate">{item.label}</span>
                    <div className="flex items-center gap-2">
                      {item.badge && (
                        <motion.span
                          className={`text-xs px-2 py-1 rounded-full font-medium ${
                            typeof item.badge === 'string' && item.badge === 'Live'
                              ? 'bg-green-500/20 text-green-400 border border-green-400/30'
                              : typeof item.badge === 'string' && item.badge === 'AI'
                              ? 'bg-purple-500/20 text-purple-400 border border-purple-400/30'
                              : typeof item.badge === 'string' && item.badge === 'Build'
                              ? 'bg-orange-500/20 text-orange-400 border border-orange-400/30'
                              : typeof item.badge === 'string' && item.badge === 'Sim'
                              ? 'bg-blue-500/20 text-blue-400 border border-blue-400/30'
                              : typeof item.badge === 'string' && item.badge === 'Active'
                              ? 'bg-cyan-500/20 text-cyan-400 border border-cyan-400/30'
                              : `bg-gradient-to-r from-${evolutionTheme.primary}20 to-${evolutionTheme.secondary}20 border border-${evolutionTheme.primary}30`
                          }`}
                          style={{
                            color: typeof item.badge === 'number' || (typeof item.badge === 'string' && /^\d+/.test(item.badge))
                              ? evolutionTheme.primary
                              : undefined
                          }}
                          animate={{
                            scale: typeof item.badge === 'string' && item.badge === 'Live' ? [1, 1.1, 1] : 1
                          }}
                          transition={{
                            duration: 2,
                            repeat: typeof item.badge === 'string' && item.badge === 'Live' ? Infinity : 0,
                            ease: 'easeInOut'
                          }}
                        >
                          {item.badge}
                        </motion.span>
                      )}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.button>
          </div>
        ))}
      </nav>
    </div>
  )
}
