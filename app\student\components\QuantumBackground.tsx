'use client'

import { useEffect, useRef, useState } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import * as THREE from 'three'

// Generate deterministic positions for quantum dots to avoid hydration issues
function generateQuantumDots(count: number) {
  return Array.from({ length: count }, (_, i) => ({
    id: i,
    left: (i * 37) % 100, // Deterministic positioning
    top: (i * 23) % 100,
    animationDelay: (i * 0.3) % 3,
    animationDuration: 2 + ((i * 0.5) % 2)
  }))
}

// Optimized Quantum Particle System
function QuantumParticles() {
  const meshRef = useRef<THREE.Points>(null)
  const particleCount = 150 // Reduced from 300
  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    if (!meshRef.current) return
    
    const positions = new Float32Array(particleCount * 3)
    const colors = new Float32Array(particleCount * 3)
    const sizes = new Float32Array(particleCount)
    const phases = new Float32Array(particleCount)
    
    for (let i = 0; i < particleCount; i++) {
      // Deterministic positions
      positions[i * 3] = ((i * 37) % 100 - 50) * 0.5
      positions[i * 3 + 1] = ((i * 23) % 100 - 50) * 0.5
      positions[i * 3 + 2] = ((i * 17) % 100 - 50) * 0.5

      // Deterministic colors (quantum palette)
      const colorChoice = (i * 0.618) % 1 // Golden ratio for distribution
      if (colorChoice < 0.4) {
        // Neural cyan
        colors[i * 3] = 0.13
        colors[i * 3 + 1] = 0.83
        colors[i * 3 + 2] = 0.93
      } else if (colorChoice < 0.7) {
        // Quantum purple
        colors[i * 3] = 0.55
        colors[i * 3 + 1] = 0.36
        colors[i * 3 + 2] = 0.96
      } else {
        // Flame orange
        colors[i * 3] = 0.96
        colors[i * 3 + 1] = 0.62
        colors[i * 3 + 2] = 0.04
      }

      sizes[i] = ((i * 0.5) % 2) + 0.5
      phases[i] = (i * 0.2) % (Math.PI * 2)
    }
    
    const geometry = meshRef.current.geometry
    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3))
    geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1))
    geometry.setAttribute('phase', new THREE.BufferAttribute(phases, 1))

    // Ensure attributes are properly initialized
    const positionAttr = geometry.attributes.position as THREE.BufferAttribute
    const phaseAttr = geometry.attributes.phase as THREE.BufferAttribute
    if (positionAttr.setUsage) positionAttr.setUsage(THREE.DynamicDrawUsage)
    if (phaseAttr.setUsage) phaseAttr.setUsage(THREE.StaticDrawUsage)

    setIsInitialized(true)
  }, [])
  
  useFrame((state) => {
    if (!isInitialized || !meshRef.current || !meshRef.current.geometry.attributes.position || !meshRef.current.geometry.attributes.phase) return

    const time = state.clock.elapsedTime
    const positionAttribute = meshRef.current.geometry.attributes.position
    const phaseAttribute = meshRef.current.geometry.attributes.phase

    if (!positionAttribute.array || !phaseAttribute.array) return

    const positions = positionAttribute.array as Float32Array
    const phases = phaseAttribute.array as Float32Array

    // Optimize by updating fewer particles per frame
    const updateStep = 3 // Update every 3rd particle
    const startIndex = Math.floor(time * 10) % updateStep

    for (let i = startIndex; i < particleCount; i += updateStep) {
      const i3 = i * 3

      // Simplified wave motion
      positions[i3 + 1] += Math.sin(time * 0.3 + phases[i]) * 0.008 // Reduced amplitude
      positions[i3] += Math.cos(time * 0.2 + phases[i]) * 0.004

      // Simplified wrap around
      if (positions[i3 + 1] > 20) positions[i3 + 1] = -20 // Reduced range
      if (positions[i3 + 1] < -20) positions[i3 + 1] = 20
      if (positions[i3] > 20) positions[i3] = -20
      if (positions[i3] < -20) positions[i3] = 20
    }

    positionAttribute.needsUpdate = true
    meshRef.current.rotation.y = time * 0.02 // Slower rotation
  })
  
  return (
    <points ref={meshRef}>
      <bufferGeometry />
      <pointsMaterial
        size={0.1}
        vertexColors
        transparent
        opacity={0.6}
        sizeAttenuation
        blending={THREE.AdditiveBlending}
      />
    </points>
  )
}

// Quantum Field Grid
function QuantumGrid() {
  const gridRef = useRef<THREE.Group>(null)
  
  useFrame((state) => {
    if (!gridRef.current) return
    
    const time = state.clock.elapsedTime
    gridRef.current.rotation.x = Math.sin(time * 0.1) * 0.1
    gridRef.current.rotation.z = Math.cos(time * 0.15) * 0.05
  })
  
  return (
    <group ref={gridRef}>
      <gridHelper
        args={[100, 50]}
        position={[0, -10, 0]}
        material-color="#22D3EE"
        material-opacity={0.1}
        material-transparent
      />
      <gridHelper
        args={[100, 50]}
        position={[0, 10, 0]}
        rotation={[Math.PI, 0, 0]}
        material-color="#8B5CF6"
        material-opacity={0.05}
        material-transparent
      />
    </group>
  )
}

// Energy Waves
function EnergyWaves() {
  const waveRef = useRef<THREE.Mesh>(null)
  
  useFrame((state) => {
    if (!waveRef.current) return
    
    const time = state.clock.elapsedTime
    waveRef.current.rotation.z = time * 0.2
    const material = waveRef.current.material as THREE.Material
    if (material && 'opacity' in material) {
      material.opacity = 0.1 + Math.sin(time * 2) * 0.05
    }
  })
  
  return (
    <mesh ref={waveRef} position={[0, 0, -20]}>
      <ringGeometry args={[10, 30, 32]} />
      <meshBasicMaterial
        color="#22D3EE"
        transparent
        opacity={0.1}
        side={THREE.DoubleSide}
      />
    </mesh>
  )
}

// Main Quantum Background Component
export default function QuantumBackground() {
  const [cyanDots, setCyanDots] = useState<Array<{id: number, left: number, top: number, animationDelay: number, animationDuration: number}>>([])
  const [purpleDots, setPurpleDots] = useState<Array<{id: number, left: number, top: number, animationDelay: number, animationDuration: number}>>([])
  const [orangeDots, setOrangeDots] = useState<Array<{id: number, left: number, top: number, animationDelay: number, animationDuration: number}>>([])
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    setCyanDots(generateQuantumDots(20))
    setPurpleDots(generateQuantumDots(15))
    setOrangeDots(generateQuantumDots(10))
  }, [])

  if (!isClient) {
    return (
      <div className="fixed inset-0 pointer-events-none z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-neural-cyan/5 via-transparent to-quantum-purple/5" />
        <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-flame-orange/3 to-transparent" />
      </div>
    )
  }

  return (
    <div className="fixed inset-0 pointer-events-none z-0">
      <Canvas
        camera={{ position: [0, 0, 20], fov: 60 }}
        className="w-full h-full"
        gl={{
          antialias: false,
          alpha: true,
          powerPreference: 'low-power',
          stencil: false
        }}
        performance={{ min: 0.5 }}
        frameloop="demand"
      >
        <ambientLight intensity={0.15} /> {/* Reduced */}
        <pointLight position={[10, 10, 10]} intensity={0.2} color="#22D3EE" /> {/* Reduced */}
        <pointLight position={[-10, -10, -10]} intensity={0.15} color="#8B5CF6" /> {/* Reduced */}

        <QuantumParticles />
        {/* Conditionally render expensive components */}
        {/* <QuantumGrid /> */}
        {/* <EnergyWaves /> */}
      </Canvas>

      {/* CSS Gradient Overlays */}
      <div className="absolute inset-0 bg-gradient-to-br from-neural-cyan/5 via-transparent to-quantum-purple/5" />
      <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-flame-orange/3 to-transparent" />

      {/* Reduced Animated Quantum Dots */}
      <div className="absolute inset-0">
        {/* Reduced from all to 15 */}
        {cyanDots.slice(0, 15).map((dot) => (
          <div
            key={dot.id}
            className="absolute w-1 h-1 bg-neural-cyan rounded-full animate-pulse"
            style={{
              left: `${dot.left}%`,
              top: `${dot.top}%`,
              animationDelay: `${dot.animationDelay}s`,
              animationDuration: `${dot.animationDuration}s`
            }}
          />
        ))}

        {/* Reduced from all to 10 */}
        {purpleDots.slice(0, 10).map((dot) => (
          <div
            key={`purple-${dot.id}`}
            className="absolute w-0.5 h-0.5 bg-quantum-purple rounded-full animate-pulse"
            style={{
              left: `${dot.left}%`,
              top: `${dot.top}%`,
              animationDelay: `${dot.animationDelay * 1.3}s`,
              animationDuration: `${dot.animationDuration + 1}s`
            }}
          />
        ))}

        {/* Reduced from all to 8 */}
        {orangeDots.slice(0, 8).map((dot) => (
          <div
            key={`orange-${dot.id}`}
            className="absolute w-1.5 h-1.5 bg-flame-orange rounded-full animate-pulse opacity-60"
            style={{
              left: `${dot.left}%`,
              top: `${dot.top}%`,
              animationDelay: `${dot.animationDelay * 1.7}s`,
              animationDuration: `${dot.animationDuration + 2}s`
            }}
          />
        ))}
      </div>

      {/* Scanning Lines Effect */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute w-full h-0.5 bg-gradient-to-r from-transparent via-neural-cyan/30 to-transparent animate-scan-vertical" />
        <div className="absolute w-0.5 h-full bg-gradient-to-b from-transparent via-quantum-purple/20 to-transparent animate-scan-horizontal" />
      </div>
    </div>
  )
}
