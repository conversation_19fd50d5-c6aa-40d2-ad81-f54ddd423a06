"use client"

import React, { forwardRef, useRef, useEffect, useMemo } from 'react'
import { motion, MotionProps, useReducedMotion } from 'framer-motion'
import { 
  useOptimizedMotionProps, 
  optimizedAnimationPresets, 
  useWillChange,
  useInViewAnimation,
  globalAnimationConfig,
  type AnimationQuality
} from '@/utils/animationOptimization'

interface OptimizedMotionProps extends MotionProps {
  quality?: AnimationQuality
  enableWillChange?: boolean
  triggerOnView?: boolean
  viewThreshold?: number
  preset?: 'fadeIn' | 'scaleIn' | 'slideUp' | 'rotate' | 'pulse'
  children: React.ReactNode
  className?: string
}

/**
 * Optimized Motion component that automatically applies performance optimizations
 */
export const OptimizedMotion = forwardRef<HTMLDivElement, OptimizedMotionProps>(
  ({ 
    quality, 
    enableWillChange = true, 
    triggerOnView = false,
    viewThreshold = 0.1,
    preset,
    children, 
    ...motionProps 
  }, ref) => {
    const shouldReduceMotion = useReducedMotion()
    const willChangeRef = useWillChange(['transform', 'opacity'])
    
    // Get global quality setting if not provided
    const animationQuality = quality || globalAnimationConfig.getQuality()
    
    // Apply preset if specified
    const presetProps = preset ? optimizedAnimationPresets[preset](animationQuality) : {}
    
    // Merge preset with custom props
    const combinedProps = {
      ...presetProps,
      ...motionProps,
      // Merge transitions
      transition: {
        ...presetProps.transition,
        ...motionProps.transition
      }
    }
    
    // Apply optimizations
    const optimizedProps = useOptimizedMotionProps(combinedProps, {
      quality: animationQuality,
      enableWillChange
    })
    
    // Handle intersection observer for view-triggered animations
    const { elementRef, optimizedProps: viewOptimizedProps } = useInViewAnimation(
      triggerOnView ? optimizedProps : combinedProps,
      viewThreshold
    )
    
    const finalProps = triggerOnView ? viewOptimizedProps : optimizedProps
    
    // Disable animations if user prefers reduced motion
    if (shouldReduceMotion) {
      return (
        <div ref={ref} style={finalProps.style as React.CSSProperties}>
          {children}
        </div>
      )
    }
    
    return (
      <motion.div
        ref={triggerOnView ? elementRef as any : (enableWillChange ? willChangeRef as any : ref)}
        {...finalProps}
      >
        {children}
      </motion.div>
    )
  }
)

OptimizedMotion.displayName = 'OptimizedMotion'

/**
 * Lightweight CSS-only animation component for simple animations
 */
interface CSSAnimationProps {
  type: 'fadeIn' | 'slideUp' | 'scaleIn' | 'pulse' | 'rotate'
  duration?: number
  delay?: number
  children: React.ReactNode
  className?: string
}

export const CSSAnimation: React.FC<CSSAnimationProps> = ({
  type,
  duration = 0.3,
  delay = 0,
  children,
  className = ''
}) => {
  const animationClass = useMemo(() => {
    const baseClass = 'animate-'
    switch (type) {
      case 'fadeIn':
        return `${baseClass}fade-in`
      case 'slideUp':
        return `${baseClass}slide-up`
      case 'scaleIn':
        return `${baseClass}scale-in`
      case 'pulse':
        return `${baseClass}pulse`
      case 'rotate':
        return `${baseClass}spin`
      default:
        return ''
    }
  }, [type])
  
  const style = {
    animationDuration: `${duration}s`,
    animationDelay: `${delay}s`,
    animationFillMode: 'both'
  }
  
  return (
    <div className={`${animationClass} ${className}`} style={style}>
      {children}
    </div>
  )
}

/**
 * Performance-optimized loading spinner
 */
interface OptimizedSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  color?: string
  className?: string
}

export const OptimizedSpinner: React.FC<OptimizedSpinnerProps> = ({
  size = 'md',
  color = '#22D3EE',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  }
  
  return (
    <div 
      className={`${sizeClasses[size]} ${className}`}
      style={{
        border: `2px solid transparent`,
        borderTop: `2px solid ${color}`,
        borderRadius: '50%',
        animation: 'spin 1s linear infinite',
        willChange: 'transform'
      }}
    />
  )
}

/**
 * Optimized stagger animation container
 */
interface StaggerContainerProps {
  children: React.ReactNode
  staggerDelay?: number
  quality?: AnimationQuality
  className?: string
}

export const StaggerContainer: React.FC<StaggerContainerProps> = ({
  children,
  staggerDelay = 0.1,
  quality = 'medium',
  className = ''
}) => {
  const shouldReduceMotion = useReducedMotion()
  
  if (shouldReduceMotion) {
    return <div className={className}>{children}</div>
  }
  
  const variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: quality === 'low' ? staggerDelay * 0.5 : staggerDelay,
        delayChildren: quality === 'low' ? 0 : 0.1
      }
    }
  }
  
  const itemVariants = {
    hidden: { opacity: 0, y: quality === 'low' ? 10 : 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: quality === 'low' ? 0.2 : 0.4,
        ease: [0.4, 0, 0.2, 1] as any
      }
    }
  }
  
  return (
    <motion.div
      className={className}
      variants={variants}
      initial="hidden"
      animate="visible"
    >
      {React.Children.map(children, (child, index) => (
        <motion.div key={index} variants={itemVariants}>
          {child}
        </motion.div>
      ))}
    </motion.div>
  )
}

/**
 * Optimized parallax component
 */
interface OptimizedParallaxProps {
  children: React.ReactNode
  speed?: number
  className?: string
}

export const OptimizedParallax: React.FC<OptimizedParallaxProps> = ({
  children,
  speed = 0.5,
  className = ''
}) => {
  const elementRef = useRef<HTMLDivElement>(null)
  const shouldReduceMotion = useReducedMotion()
  
  useEffect(() => {
    if (shouldReduceMotion) return
    
    const element = elementRef.current
    if (!element) return
    
    let ticking = false
    
    const updateTransform = () => {
      const scrolled = window.pageYOffset
      const rect = element.getBoundingClientRect()
      const elementTop = rect.top + scrolled
      const elementHeight = rect.height
      const windowHeight = window.innerHeight
      
      // Only animate when element is in viewport
      if (scrolled + windowHeight > elementTop && scrolled < elementTop + elementHeight) {
        const yPos = -(scrolled - elementTop) * speed
        element.style.transform = `translate3d(0, ${yPos}px, 0)`
      }
      
      ticking = false
    }
    
    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(updateTransform)
        ticking = true
      }
    }
    
    window.addEventListener('scroll', handleScroll, { passive: true })
    
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [speed, shouldReduceMotion])
  
  return (
    <div 
      ref={elementRef} 
      className={className}
      style={{ willChange: shouldReduceMotion ? 'auto' : 'transform' }}
    >
      {children}
    </div>
  )
}

/**
 * Optimized hover animation wrapper
 */
interface OptimizedHoverProps {
  children: React.ReactNode
  scale?: number
  duration?: number
  className?: string
}

export const OptimizedHover: React.FC<OptimizedHoverProps> = ({
  children,
  scale = 1.05,
  duration = 0.2,
  className = ''
}) => {
  const shouldReduceMotion = useReducedMotion()
  
  if (shouldReduceMotion) {
    return <div className={className}>{children}</div>
  }
  
  return (
    <motion.div
      className={className}
      whileHover={{ scale }}
      transition={{ duration, ease: 'easeOut' }}
      style={{ willChange: 'transform' }}
    >
      {children}
    </motion.div>
  )
}

/**
 * Export optimized motion components
 */
export {
  OptimizedMotion as Motion,
  OptimizedSpinner as Spinner,
  OptimizedParallax as Parallax,
  OptimizedHover as Hover
}

export default OptimizedMotion
