'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  BookOpen,
  Search,
  Clock,
  Eye,
  Plus,
  Users,
  Target,
  ChevronDown,
  ChevronRight,
  Grid,
  List
} from 'lucide-react'
import { useCurriculumPlanner } from './CurriculumPlannerProvider'
import { SUBJECT_AREAS, LESSON_CATEGORIES } from '../data/lessonLibrary'
import { Lesson } from '../types/curriculum'

interface SubjectAccordionProps {
  subject: typeof SUBJECT_AREAS[0]
  lessons: Lesson[]
  isExpanded: boolean
  onToggle: () => void
  onScheduleLesson: (lessonId: string) => void
  selectedChild: any
}

function SubjectAccordion({ subject, lessons, isExpanded, onToggle, onScheduleLesson, selectedChild }: SubjectAccordionProps) {
  const subjectLessons = lessons.filter(lesson => lesson.subject === subject.id)

  // Group by category for better organization
  const lessonsByCategory = LESSON_CATEGORIES.reduce((acc, category) => {
    acc[category.id] = subjectLessons.filter(lesson => lesson.category === category.id)
    return acc
  }, {} as Record<string, Lesson[]>)

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'border-green-500/30 text-green-400'
      case 'intermediate': return 'border-yellow-500/30 text-yellow-400'
      case 'advanced': return 'border-red-500/30 text-red-400'
      default: return 'border-gray-500/30 text-gray-400'
    }
  }

  return (
    <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl overflow-hidden">
      <CardHeader
        className="cursor-pointer hover:bg-gray-800/30 transition-colors"
        onClick={onToggle}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className={`p-3 rounded-lg bg-gradient-to-r ${subject.color}`}>
              <span className="text-2xl">{subject.icon}</span>
            </div>
            <div>
              <CardTitle className="text-white font-space-grotesk text-xl">
                {subject.name}
              </CardTitle>
              <p className="text-gray-400 mt-1">{subject.description}</p>
              <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                <span>{subjectLessons.length} lessons</span>
                <span>{subject.estimatedHours}h total</span>
                <span>Age {subject.ageRange[0]}-{subject.ageRange[1]}</span>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <Badge variant="outline" className="border-gray-600 text-gray-400">
              {subjectLessons.length} lessons
            </Badge>
            {isExpanded ? (
              <ChevronDown className="w-5 h-5 text-gray-400" />
            ) : (
              <ChevronRight className="w-5 h-5 text-gray-400" />
            )}
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0 space-y-6">
          {LESSON_CATEGORIES.map((category) => {
            const categoryLessons = lessonsByCategory[category.id]
            if (!categoryLessons || categoryLessons.length === 0) return null

            return (
              <div key={category.id} className="space-y-3">
                <div className="flex items-center gap-3 pb-2 border-b border-gray-700/30">
                  <span className="text-2xl">{category.icon}</span>
                  <div>
                    <h3 className="font-semibold text-white">{category.name}</h3>
                    <p className="text-xs text-gray-400">{category.description}</p>
                  </div>
                  <Badge variant="outline" className="border-gray-600 text-gray-400 ml-auto">
                    {categoryLessons.length}
                  </Badge>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {categoryLessons.map((lesson) => (
                    <Card key={lesson.id} className="bg-gray-800/30 border-gray-700/30 hover:border-gray-600/50 transition-all">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <h4 className="font-semibold text-white text-sm leading-tight flex-1">
                            {lesson.title}
                          </h4>
                          <Badge
                            variant="outline"
                            className={`text-xs ml-2 ${getDifficultyColor(lesson.difficulty)}`}
                          >
                            {lesson.difficulty}
                          </Badge>
                        </div>

                        <p className="text-xs text-gray-400 mb-3 line-clamp-2">
                          {lesson.description}
                        </p>

                        <div className="flex items-center gap-3 text-xs text-gray-500 mb-3">
                          <div className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {lesson.duration} min
                          </div>
                          <div className="flex items-center gap-1">
                            <Users className="w-3 h-3" />
                            Age {lesson.ageRange[0]}-{lesson.ageRange[1]}
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-1 mb-3">
                          {lesson.skills.slice(0, 2).map((skill) => (
                            <Badge key={skill} variant="outline" className="text-xs border-gray-600 text-gray-400">
                              {skill.replace('-', ' ')}
                            </Badge>
                          ))}
                          {lesson.skills.length > 2 && (
                            <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
                              +{lesson.skills.length - 2}
                            </Badge>
                          )}
                        </div>

                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-700/50 text-xs"
                          >
                            <Eye className="w-3 h-3 mr-1" />
                            Preview
                          </Button>
                          <Button
                            size="sm"
                            className="flex-1 bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-xs"
                            onClick={() => onScheduleLesson(lesson.id)}
                            disabled={!selectedChild}
                          >
                            <Plus className="w-3 h-3 mr-1" />
                            Schedule
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )
          })}
        </CardContent>
      )}
    </Card>
  )
}

export function LessonLibrary() {
  const { state: _state, getFilteredLessons, scheduleLesson, getSelectedChild } = useCurriculumPlanner()
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedSubject, setSelectedSubject] = useState<string | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [expandedSubjects, setExpandedSubjects] = useState<Set<string>>(new Set(['quantum-physics']))
  const [viewMode, setViewMode] = useState<'accordion' | 'grid'>('accordion')

  const filteredLessons = getFilteredLessons()
  const selectedChild = getSelectedChild()

  const handleScheduleLesson = (lessonId: string) => {
    if (selectedChild) {
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      scheduleLesson(lessonId, tomorrow, selectedChild.id)
    }
  }

  const toggleSubject = (subjectId: string) => {
    const newExpanded = new Set(expandedSubjects)
    if (newExpanded.has(subjectId)) {
      newExpanded.delete(subjectId)
    } else {
      newExpanded.add(subjectId)
    }
    setExpandedSubjects(newExpanded)
  }

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white font-space-grotesk">
            Lesson Library
          </h2>
          <p className="text-gray-400 mt-1">
            Browse and schedule lessons organized by subject and category
          </p>
        </div>

        <div className="flex items-center gap-3">
          {/* View Mode Toggle */}
          <div className="flex bg-gray-800/50 rounded-lg p-1">
            <Button
              size="sm"
              variant="ghost"
              className={`${
                viewMode === 'accordion'
                  ? 'bg-cyan-500/20 text-cyan-400'
                  : 'text-gray-400 hover:text-white'
              }`}
              onClick={() => setViewMode('accordion')}
            >
              <List className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className={`${
                viewMode === 'grid'
                  ? 'bg-cyan-500/20 text-cyan-400'
                  : 'text-gray-400 hover:text-white'
              }`}
              onClick={() => setViewMode('grid')}
            >
              <Grid className="w-4 h-4" />
            </Button>
          </div>

          {viewMode === 'accordion' && (
            <>
              <Button
                variant="outline"
                size="sm"
                className="border-cyan-500/30 text-cyan-400 hover:bg-cyan-500/10"
                onClick={() => setExpandedSubjects(new Set(SUBJECT_AREAS.map(s => s.id)))}
              >
                Expand All
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="border-gray-600 text-gray-300 hover:bg-gray-700/50"
                onClick={() => setExpandedSubjects(new Set())}
              >
                Collapse All
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Search and Filters */}
      <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search lessons..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-gray-800/50 border-gray-700/50 text-white"
              />
            </div>

            {/* Subject Filter */}
            <select
              className="p-2 bg-gray-800/50 border border-gray-700/50 rounded-lg text-white"
              value={selectedSubject || ''}
              onChange={(e) => setSelectedSubject(e.target.value || null)}
            >
              <option value="">All Subjects</option>
              {SUBJECT_AREAS.map((subject) => (
                <option key={subject.id} value={subject.id}>
                  {subject.name}
                </option>
              ))}
            </select>

            {/* Category Filter */}
            <select
              className="p-2 bg-gray-800/50 border border-gray-700/50 rounded-lg text-white"
              value={selectedCategory || ''}
              onChange={(e) => setSelectedCategory(e.target.value || null)}
            >
              <option value="">All Categories</option>
              {LESSON_CATEGORIES.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>

            {/* Quick Stats */}
            <div className="flex items-center justify-center bg-gray-800/30 rounded-lg p-2">
              <div className="text-center">
                <div className="text-lg font-bold text-cyan-400">{filteredLessons.length}</div>
                <div className="text-xs text-gray-400">Available</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content based on view mode */}
      {viewMode === 'accordion' ? (
        /* Accordion View */
        <div className="space-y-4">
          {SUBJECT_AREAS.map((subject) => (
            <SubjectAccordion
              key={subject.id}
              subject={subject}
              lessons={filteredLessons}
              isExpanded={expandedSubjects.has(subject.id)}
              onToggle={() => toggleSubject(subject.id)}
              onScheduleLesson={handleScheduleLesson}
              selectedChild={selectedChild}
            />
          ))}
        </div>
      ) : (
        /* Grid View */
        <div className="space-y-6">
          {/* Subject Areas Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {SUBJECT_AREAS.map((subject) => (
              <motion.div
                key={subject.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Card
                  className={`cursor-pointer transition-all duration-300 ${
                    selectedSubject === subject.id
                      ? 'bg-gradient-to-r from-cyan-500/20 to-blue-500/20 border-cyan-500/50'
                      : 'bg-black/40 border-gray-800/50 hover:border-gray-700/50'
                  }`}
                  onClick={() => setSelectedSubject(selectedSubject === subject.id ? null : subject.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3 mb-3">
                      <span className="text-2xl">{subject.icon}</span>
                      <div>
                        <h3 className="font-semibold text-white">{subject.name}</h3>
                        <p className="text-xs text-gray-400">{subject.description}</p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-xs text-gray-400">
                      <span>{subject.totalLessons} lessons</span>
                      <span>{subject.estimatedHours}h total</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Lesson Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredLessons.map((lesson) => (
              <motion.div
                key={lesson.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                whileHover={{ scale: 1.02 }}
              >
                <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl h-full">
                  <CardContent className="p-4 h-full flex flex-col">
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-3">
                        <h3 className="font-semibold text-white text-sm leading-tight">
                          {lesson.title}
                        </h3>
                        <Badge
                          variant="outline"
                          className={`text-xs ml-2 ${
                            lesson.difficulty === 'beginner' ? 'border-green-500/30 text-green-400' :
                            lesson.difficulty === 'intermediate' ? 'border-yellow-500/30 text-yellow-400' :
                            'border-red-500/30 text-red-400'
                          }`}
                        >
                          {lesson.difficulty}
                        </Badge>
                      </div>

                      <p className="text-xs text-gray-400 mb-3 line-clamp-2">
                        {lesson.description}
                      </p>

                      <div className="flex items-center gap-4 text-xs text-gray-500 mb-3">
                        <div className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {lesson.duration} min
                        </div>
                        <div className="flex items-center gap-1">
                          <Users className="w-3 h-3" />
                          Age {lesson.ageRange[0]}-{lesson.ageRange[1]}
                        </div>
                        <div className="flex items-center gap-1">
                          <Target className="w-3 h-3" />
                          {lesson.type}
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-1 mb-3">
                        {lesson.tags.slice(0, 3).map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs border-gray-600 text-gray-400">
                            {tag}
                          </Badge>
                        ))}
                        {lesson.tags.length > 3 && (
                          <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
                            +{lesson.tags.length - 3}
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div className="flex gap-2 pt-3 border-t border-gray-700/30">
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-700/50"
                      >
                        <Eye className="w-3 h-3 mr-1" />
                        Preview
                      </Button>
                      <Button
                        size="sm"
                        className="flex-1 bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600"
                        onClick={() => handleScheduleLesson(lesson.id)}
                        disabled={!selectedChild}
                      >
                        <Plus className="w-3 h-3 mr-1" />
                        Schedule
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {filteredLessons.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-400 mb-2">No lessons found</h3>
          <p className="text-gray-500">Try adjusting your search or filters</p>
        </div>
      )}
    </div>
  )
}
