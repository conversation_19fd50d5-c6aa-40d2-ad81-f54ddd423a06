'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useDraggable } from '@dnd-kit/core'
import { CSS } from '@dnd-kit/utilities'
import {
  Clock,
  Eye,
  GripVertical,
  Plus
} from 'lucide-react'

import { Lesson, DifficultyLevel, KanbanLessonCard } from '../types/curriculum'

interface DraggableLibraryCardProps {
  lesson: Lesson
  onPreview: (lessonId: string) => void
  onQuickAdd: (lesson: Lesson) => void
}

export function DraggableLibraryCard({ 
  lesson, 
  onPreview, 
  onQuickAdd 
}: DraggableLibraryCardProps) {
  // Create lesson card data for dragging
  const createLessonCard = (lesson: Lesson): KanbanLessonCard => ({
    id: `card-${lesson.id}-${Date.now()}`,
    lessonId: lesson.id,
    title: lesson.title,
    duration: lesson.duration,
    difficulty: lesson.difficulty,
    subject: lesson.subject,
    category: lesson.category,
    status: 'not-started',
    priority: 'medium'
  })

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging
  } = useDraggable({
    id: `library-${lesson.id}`,
    data: {
      type: 'library-lesson',
      lesson: createLessonCard(lesson),
      originalLesson: lesson
    }
  })

  const style = {
    transform: CSS.Translate.toString(transform),
    opacity: isDragging ? 0.8 : 1,
    zIndex: isDragging ? 1000 : 'auto'
  }

  // Get difficulty color
  const getDifficultyColor = (difficulty: DifficultyLevel) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'intermediate': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'advanced': return 'bg-red-500/20 text-red-400 border-red-500/30'
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  return (
    <div ref={setNodeRef} style={style}>
      <motion.div
        className="group"
        whileHover={{ scale: 1.02 }}
      >
        <Card
          {...attributes}
          {...listeners}
          className={`bg-gray-800/30 border-gray-700/50 hover:border-cyan-500/30 transition-all duration-200 cursor-grab active:cursor-grabbing ${
            isDragging ? 'border-cyan-500/50 bg-cyan-500/10 shadow-lg shadow-cyan-500/20' : ''
          }`}
        >
          <CardContent className="p-3">
            <div className="flex items-start gap-2">
              {/* Drag Handle */}
              <div className="p-1 rounded transition-colors">
                <GripVertical className="w-4 h-4 text-gray-500 group-hover:text-cyan-400 transition-colors pointer-events-none" />
              </div>
              
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-white truncate">
                  {lesson.title}
                </h4>
                
                <div className="flex items-center gap-2 mt-1">
                  <Badge className={`text-xs ${getDifficultyColor(lesson.difficulty)}`}>
                    {lesson.difficulty}
                  </Badge>
                  
                  <div className="flex items-center text-xs text-gray-400">
                    <Clock className="w-3 h-3 mr-1" />
                    {lesson.duration}m
                  </div>
                </div>

                <p className="text-xs text-gray-400 mt-1 line-clamp-2">
                  {lesson.description}
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    e.preventDefault()
                    onPreview(lesson.id)
                  }}
                  className="p-1 h-auto hover:bg-blue-500/20 rounded transition-colors"
                  title="Preview lesson"
                  type="button"
                >
                  <Eye className="w-3 h-3 text-blue-400" />
                </button>

                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    e.preventDefault()
                    onQuickAdd(lesson)
                  }}
                  className="p-1 h-auto hover:bg-green-500/20 rounded transition-colors"
                  title="Quick add to today"
                  type="button"
                >
                  <Plus className="w-3 h-3 text-green-400" />
                </button>
              </div>
            </div>

            {/* Drag Instruction */}
            {isDragging && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-2 text-xs text-cyan-400 text-center"
              >
                Drop on any day to schedule
              </motion.div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
