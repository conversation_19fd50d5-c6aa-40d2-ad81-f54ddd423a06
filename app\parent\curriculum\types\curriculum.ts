// Parent Curriculum Planner - Data Types and Interfaces

export type LessonStatus = 'pending' | 'approved' | 'rejected' | 'completed' | 'in-progress' | 'locked'
export type DifficultyLevel = 'beginner' | 'intermediate' | 'advanced'
export type LessonType = 'video' | 'interactive' | 'reading' | 'project' | 'assessment' | 'lab' | 'learning-capsule'
export type PlanningMode = 'auto' | 'manual'
export type ScheduleView = 'daily' | 'weekly' | 'monthly'

// AI-Powered Learning Capsule Types
export type PanelType =
  | 'concept-card'
  | 'micro-quiz'
  | 'decision-point'
  | 'pattern-discovery'
  | 'simulated-scenario'
  | 'mini-puzzle'
  | 'reflective-prompt'

export type InteractionType =
  | 'drag-drop'
  | 'multiple-choice'
  | 'matching'
  | 'fill-gap'
  | 'logic-puzzle'
  | 'canvas-draw'
  | 'voice-input'
  | 'gesture'

export type AdaptivityTrigger =
  | 'time-spent'
  | 'accuracy-rate'
  | 'engagement-drop'
  | 'emotional-state'
  | 'retry-count'
  | 'help-requests'

export type EmotionalState = 'engaged' | 'frustrated' | 'confused' | 'excited' | 'bored' | 'confident'

// Core Lesson Interface
export interface Lesson {
  id: string
  title: string
  description: string
  type: LessonType
  subject: string
  category: string
  tags: string[]
  difficulty: DifficultyLevel
  duration: number // minutes
  ageRange: [number, number]
  prerequisites: string[] // lesson IDs
  learningObjectives: string[]
  skills: string[] // skills developed
  content: {
    summary: string
    materials: string[]
    activities: string[]
    assessments: string[]
  }
  metadata: {
    author: string
    createdAt: Date
    updatedAt: Date
    version: string
    gradeLevel: string
    standardsAlignment: string[]
  }
}

// Lesson Assignment for Calendar
export interface LessonAssignment {
  id: string
  lessonId: string
  childId: string
  scheduledDate: Date
  status: LessonStatus
  parentNotes?: string
  completedAt?: Date
  timeSpent?: number // minutes
  feedback?: {
    rating: number
    comments: string
    parentReview?: string
  }
}

// AI-Powered Learning Capsule Interfaces

// Interactive Panel Base Interface
export interface LearningPanel {
  id: string
  type: PanelType
  title: string
  content: string
  audioNarration?: {
    text: string
    voiceSettings: {
      speed: number
      pitch: number
      voice: string
    }
    ambientEffects?: string[]
  }
  visualElements: {
    backgroundImage?: string
    animations?: AnimationConfig[]
    highlights?: HighlightConfig[]
    particles?: ParticleConfig
  }
  interactions: InteractionConfig[]
  adaptivity: AdaptivityConfig
  metadata: {
    estimatedDuration: number // seconds
    cognitiveLoad: 'low' | 'medium' | 'high'
    learningObjectives: string[]
    prerequisites?: string[]
  }
}

// Specific Panel Types
export interface ConceptCardPanel extends LearningPanel {
  type: 'concept-card'
  concept: {
    definition: string
    examples: string[]
    visualAids: string[]
    relatedConcepts: string[]
  }
}

export interface MicroQuizPanel extends LearningPanel {
  type: 'micro-quiz'
  question: {
    text: string
    type: 'multiple-choice' | 'true-false' | 'fill-blank' | 'matching'
    options?: string[]
    correctAnswer: string | string[]
    explanation: string
    hints: string[]
  }
  feedback: {
    correct: string
    incorrect: string
    encouragement: string
  }
}

export interface DecisionPointPanel extends LearningPanel {
  type: 'decision-point'
  scenario: {
    description: string
    choices: {
      id: string
      text: string
      consequences: string
      learningNote: string
    }[]
  }
  reflection: {
    prompt: string
    guidedQuestions: string[]
  }
}

export interface PatternDiscoveryPanel extends LearningPanel {
  type: 'pattern-discovery'
  puzzle: {
    elements: any[]
    pattern: string
    discoverySteps: string[]
    hints: string[]
  }
  validation: {
    checkFunction: string // JavaScript function as string
    successMessage: string
    retryMessage: string
  }
}

export interface SimulatedScenarioPanel extends LearningPanel {
  type: 'simulated-scenario'
  simulation: {
    environment: string
    objectives: string[]
    tools: string[]
    constraints: string[]
  }
  assessment: {
    criteria: string[]
    rubric: Record<string, number>
  }
}

export interface MiniPuzzlePanel extends LearningPanel {
  type: 'mini-puzzle'
  puzzle: {
    type: 'logic' | 'spatial' | 'mathematical' | 'word'
    difficulty: DifficultyLevel
    instructions: string
    initialState: any
    solution: any
    hints: string[]
  }
}

export interface ReflectivePromptPanel extends LearningPanel {
  type: 'reflective-prompt'
  prompt: {
    question: string
    guidingQuestions: string[]
    journalPrompt?: string
    shareOption: boolean
  }
  analysis: {
    keywordTracking: string[]
    sentimentAnalysis: boolean
    responseCategories: string[]
  }
}

// Configuration Interfaces
export interface AnimationConfig {
  type: 'fade' | 'slide' | 'bounce' | 'pulse' | 'rotate' | 'scale'
  duration: number
  delay?: number
  easing?: string
  trigger?: 'onLoad' | 'onClick' | 'onHover' | 'onScroll'
}

export interface HighlightConfig {
  selector: string
  color: string
  animation?: AnimationConfig
  timing: number // when to trigger in narration
}

export interface ParticleConfig {
  type: 'quantum' | 'stars' | 'bubbles' | 'sparks'
  count: number
  colors: string[]
  animation: {
    speed: number
    direction: string
    pattern: string
  }
}

export interface InteractionConfig {
  type: InteractionType
  target: string
  validation?: {
    rules: string[]
    feedback: string
  }
  rewards?: {
    xp: number
    badges?: string[]
    unlocks?: string[]
  }
}

export interface AdaptivityConfig {
  triggers: AdaptivityTrigger[]
  responses: {
    [key in AdaptivityTrigger]?: {
      condition: string
      action: 'provide-hint' | 'simplify-content' | 'add-scaffolding' | 'skip-ahead' | 'retry-different'
      content?: string
    }
  }
  difficultyScaling: {
    factors: string[]
    algorithm: 'linear' | 'exponential' | 'adaptive'
  }
}

// Learning Capsule Main Structure
export interface LearningCapsule {
  id: string
  title: string
  description: string
  subject: string
  topic: string
  level: DifficultyLevel
  estimatedDuration: number // minutes
  ageRange: [number, number]

  // Core Learning Data
  learningObjectives: string[]
  skills: string[]
  prerequisites: string[]

  // Capsule Structure
  panels: LearningPanel[]
  flow: {
    sequence: string[] // panel IDs in order
    branches?: {
      [panelId: string]: {
        condition: string
        nextPanel: string
      }[]
    }
  }

  // AI Configuration
  aiSidekick: {
    character: string
    personality: string
    responses: {
      encouragement: string[]
      hints: string[]
      explanations: string[]
      celebrations: string[]
    }
    adaptivity: {
      emotionalSupport: boolean
      difficultyAdjustment: boolean
      personalizedFeedback: boolean
    }
  }

  // Assessment & Analytics
  assessment: {
    criteria: string[]
    rubric: Record<string, number>
    passingScore: number
    retryPolicy: {
      maxAttempts: number
      cooldownPeriod: number // minutes
      adaptOnRetry: boolean
    }
  }

  // Metadata
  metadata: {
    author: string
    version: string
    createdAt: Date
    updatedAt: Date
    tags: string[]
    cognitiveScience: {
      principles: string[]
      techniques: string[]
    }
    accessibility: {
      audioDescriptions: boolean
      closedCaptions: boolean
      keyboardNavigation: boolean
      screenReaderCompatible: boolean
    }
  }
}

// Performance Tracking Interfaces
export interface LearningSession {
  id: string
  capsuleId: string
  userId: string
  startTime: Date
  endTime?: Date

  // Engagement Metrics
  metrics: {
    totalTimeSpent: number // seconds
    activeTimeSpent: number // seconds (excluding idle time)
    panelCompletionTimes: Record<string, number>
    interactionCount: number
    helpRequestCount: number
    retryCount: number
  }

  // Performance Data
  performance: {
    accuracy: number // percentage
    completionRate: number // percentage
    engagementScore: number // 0-100
    focusRetention: number // percentage
    emotionalStates: {
      timestamp: number
      state: EmotionalState
      confidence: number
    }[]
  }

  // Learning Evidence
  evidence: {
    correctAnswers: string[]
    incorrectAnswers: string[]
    reflectionResponses: string[]
    creativeOutputs: any[]
    skillDemonstrations: string[]
  }

  // AI Adaptations Made
  adaptations: {
    timestamp: number
    trigger: AdaptivityTrigger
    action: string
    effectiveness: number // 0-1
  }[]
}

export interface LearningAnalytics {
  userId: string
  period: {
    start: Date
    end: Date
  }

  // Aggregate Metrics
  summary: {
    totalCapsules: number
    completedCapsules: number
    totalLearningTime: number // minutes
    averageEngagement: number
    skillProgress: Record<string, number>
    preferredLearningTimes: string[]
  }

  // Detailed Analytics
  patterns: {
    strongSubjects: string[]
    challengingAreas: string[]
    learningVelocity: number
    retentionRate: number
    preferredInteractionTypes: InteractionType[]
  }

  // Recommendations
  recommendations: {
    nextCapsules: string[]
    skillFocus: string[]
    difficultyAdjustments: Record<string, DifficultyLevel>
    learningPathOptimizations: string[]
  }
}

// Child Profile for Curriculum Planning
export interface ChildProfile {
  id: string
  name: string
  age: number
  gradeLevel: string
  avatar: string
  interests: string[]
  learningStyle: string[]
  skillLevels: Record<string, number> // skill -> level (0-100)
  preferences: {
    dailyTimeLimit: number // minutes
    preferredSubjects: string[]
    avoidSubjects: string[]
    bestLearningTimes: string[] // time slots
  }
  progress: {
    completedLessons: string[]
    currentStreak: number
    totalHours: number
    skillProgress: Record<string, number>
  }
}

// Curriculum Plan
export interface CurriculumPlan {
  id: string
  name: string
  childId: string
  parentId: string
  mode: PlanningMode
  isTemplate: boolean
  createdAt: Date
  updatedAt: Date
  settings: {
    autoSchedule: boolean
    dailyLessonLimit: number
    weeklyHours: number
    skipWeekends: boolean
    preferredTimeSlots: string[]
    subjectBalance: Record<string, number> // subject -> percentage
  }
  assignments: LessonAssignment[]
  goals: {
    shortTerm: string[]
    longTerm: string[]
    skillTargets: Record<string, number>
  }
}

// Planning Template
export interface PlanningTemplate {
  id: string
  name: string
  description: string
  ageRange: [number, number]
  duration: number // weeks
  subjects: string[]
  isPublic: boolean
  createdBy: string
  usageCount: number
  rating: number
  lessons: {
    week: number
    day: number
    lessonId: string
    notes?: string
  }[]
}

// Parent Preferences
export interface ParentPreferences {
  id: string
  parentId: string
  notifications: {
    lessonReminders: boolean
    approvalNeeded: boolean
    progressReports: boolean
    weeklyDigest: boolean
    emailFrequency: 'daily' | 'weekly' | 'monthly'
  }
  planning: {
    defaultMode: PlanningMode
    autoApproveRecommended: boolean
    requireApprovalForNew: boolean
    allowChildSelfSchedule: boolean
  }
  content: {
    maxDifficulty: DifficultyLevel
    blockedCategories: string[]
    preferredAuthors: <AUTHORS>
    contentFilters: string[]
  }
}

// Lesson Library Filters
export interface LessonFilters {
  subjects: string[]
  categories: string[]
  difficulty: DifficultyLevel[]
  duration: [number, number] // min-max minutes
  ageRange: [number, number]
  tags: string[]
  type: LessonType[]
  searchQuery: string
}

// Calendar Event for Scheduling
export interface CalendarEvent {
  id: string
  title: string
  description?: string
  start: Date
  end: Date
  type: 'lesson' | 'break' | 'assessment' | 'project'
  lessonId?: string
  childId: string
  status: LessonStatus
  color: string
  isRecurring: boolean
  recurrenceRule?: string
}

// Analytics and Reports
export interface CurriculumAnalytics {
  childId: string
  period: {
    start: Date
    end: Date
  }
  metrics: {
    totalLessons: number
    completedLessons: number
    totalHours: number
    averageRating: number
    subjectDistribution: Record<string, number>
    skillProgress: Record<string, number>
    streakDays: number
    engagementScore: number
  }
  comparisons: {
    suggestedVsActual: {
      planned: number
      completed: number
      modified: number
    }
    peerComparison?: {
      percentile: number
      averageForAge: number
    }
  }
}

// AI Planning Assistant
export interface PlanningAssistant {
  recommendations: {
    lessonId: string
    reason: string
    confidence: number
    priority: 'high' | 'medium' | 'low'
  }[]
  insights: {
    type: 'skill_gap' | 'interest_match' | 'difficulty_adjustment' | 'pacing'
    message: string
    actionable: boolean
    suggestedAction?: string
  }[]
  optimizations: {
    scheduleConflicts: CalendarEvent[]
    balanceIssues: {
      subject: string
      current: number
      recommended: number
    }[]
    pacingConcerns: {
      area: string
      issue: string
      suggestion: string
    }[]
  }
}

// Notification System
export interface CurriculumNotification {
  id: string
  type: 'approval_needed' | 'lesson_reminder' | 'progress_update' | 'schedule_conflict'
  title: string
  message: string
  childId?: string
  lessonId?: string
  priority: 'low' | 'medium' | 'high'
  read: boolean
  createdAt: Date
  actionRequired: boolean
  actions?: {
    label: string
    action: string
    data?: any
  }[]
}

// Multi-Child Management
export interface MultiChildPlan {
  id: string
  name: string
  parentId: string
  childIds: string[]
  sharedLessons: string[]
  individualPlans: Record<string, CurriculumPlan>
  synchronization: {
    sharedSchedule: boolean
    groupActivities: CalendarEvent[]
    peerLearning: boolean
  }
}

// Kanban Weekly Planner Types
export type WeekDay = 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday'

export interface KanbanLessonCard {
  id: string
  lessonId: string
  title: string
  duration: number // minutes
  difficulty: DifficultyLevel
  subject: string
  category: string
  status: 'not-started' | 'in-progress' | 'completed' | 'skipped'
  parentNotes?: string
  priority: 'low' | 'medium' | 'high'
  completionStats?: {
    timeSpent: number
    score?: number
    skillsEarned: string[]
    xpEarned: number
    reflectionSummary?: string
  }
}

export interface KanbanDayColumn {
  day: WeekDay
  displayName: string
  lessons: KanbanLessonCard[]
  totalTime: number // minutes
  maxTime: number // daily limit in minutes
  isWeekend: boolean
}

export interface WeeklyKanbanPlan {
  id: string
  childId: string
  parentId: string
  weekStartDate: Date
  weekEndDate: Date
  columns: KanbanDayColumn[]
  totalWeeklyTime: number
  weeklyTimeLimit: number
  isTemplate: boolean
  templateName?: string
  createdAt: Date
  updatedAt: Date
  status: 'draft' | 'active' | 'completed' | 'archived'
}

export interface LessonTreeNode {
  id: string
  title: string
  type: 'category' | 'subcategory' | 'lesson'
  children?: LessonTreeNode[]
  lesson?: Lesson
  isExpanded: boolean
  isDraggable: boolean
}

export interface KanbanPlannerState {
  currentWeek: WeeklyKanbanPlan | null
  selectedChild: ChildProfile | null
  lessonTree: LessonTreeNode[]
  draggedLesson: KanbanLessonCard | null
  weekHistory: WeeklyKanbanPlan[]
  templates: WeeklyKanbanPlan[]
  filters: {
    subjects: string[]
    difficulty: DifficultyLevel[]
    duration: [number, number]
    searchQuery: string
  }
  settings: {
    autoSave: boolean
    showCompletedLessons: boolean
    enableTimeValidation: boolean
    defaultDailyLimit: number
    skipWeekends: boolean
  }
}
