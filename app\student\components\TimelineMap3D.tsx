"use client"

import { useRef, useState } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { OrbitControls, Text, Sphere, Box } from '@react-three/drei'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Map, Eye, Focus, Users, Zap } from 'lucide-react'

interface NanoNode {
  id: string
  type: 'learning' | 'echo' | 'meditation' | 'timelinegen'
  title: string
  position: { x: number; y: number; z: number }
  color: string
  status: 'active' | 'dormant' | 'completed'
  participants: number
}

interface NodeMeshProps {
  node: NanoNode
  onClick: (node: NanoNode) => void
  isSelected: boolean
}

function NodeMesh({ node, onClick, isSelected }: NodeMeshProps) {
  const meshRef = useRef<any>(null)
  const [hovered, setHovered] = useState(false)

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.01
      if (hovered || isSelected) {
        meshRef.current.scale.setScalar(1.2)
      } else {
        meshRef.current.scale.setScalar(1)
      }
    }
  })

  const getNodeGeometry = () => {
    switch (node.type) {
      case 'learning':
        return <Box args={[1, 1, 1]} />
      case 'echo':
        return <Sphere args={[0.6, 8, 8]} />
      case 'meditation':
        return <Sphere args={[0.5, 6, 6]} />
      case 'timelinegen':
        return <Box args={[0.8, 1.2, 0.8]} />
      default:
        return <Sphere args={[0.5, 8, 8]} />
    }
  }

  return (
    <group
      ref={meshRef}
      position={[node.position.x / 50, node.position.y / 50, node.position.z / 50]}
      onClick={() => onClick(node)}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
    >
      <mesh>
        {getNodeGeometry()}
        <meshStandardMaterial
          color={node.color}
          emissive={node.color}
          emissiveIntensity={node.status === 'active' ? 0.3 : 0.1}
          transparent
          opacity={node.status === 'dormant' ? 0.6 : 1}
        />
      </mesh>
      
      {/* Node label */}
      <Text
        position={[0, 1, 0]}
        fontSize={0.3}
        color="white"
        anchorX="center"
        anchorY="middle"
      >
        {node.title}
      </Text>
      
      {/* Participant indicator */}
      {node.participants > 0 && (
        <Text
          position={[0, -1, 0]}
          fontSize={0.2}
          color="#22D3EE"
          anchorX="center"
          anchorY="middle"
        >
          {node.participants} users
        </Text>
      )}
    </group>
  )
}

function Scene({ nodes, onNodeClick, selectedNode }: {
  nodes: NanoNode[]
  onNodeClick: (node: NanoNode) => void
  selectedNode: NanoNode | null
}) {
  return (
    <>
      <ambientLight intensity={0.5} />
      <pointLight position={[10, 10, 10]} />
      <pointLight position={[-10, -10, -10]} color="#8B5CF6" intensity={0.5} />
      
      {nodes.map((node) => (
        <NodeMesh
          key={node.id}
          node={node}
          onClick={onNodeClick}
          isSelected={selectedNode?.id === node.id}
        />
      ))}
      
      <OrbitControls enablePan={true} enableZoom={true} enableRotate={true} />
    </>
  )
}

interface TimelineMap3DProps {
  nodes: NanoNode[]
  onNodeSelect: (node: NanoNode) => void
  selectedNode: NanoNode | null
  viewMode: 'explore' | 'focus' | 'assist' | 'syntropy'
  onViewModeChange: (mode: 'explore' | 'focus' | 'assist' | 'syntropy') => void
}

export default function TimelineMap3D({
  nodes,
  onNodeSelect,
  selectedNode,
  viewMode,
  onViewModeChange
}: TimelineMap3DProps) {
  return (
    <Card className="bg-black/40 border-blue-500/30 backdrop-blur-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-blue-400">
          <Map className="w-5 h-5" />
          3D Timeline World View
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative h-96 rounded-lg overflow-hidden border border-blue-500/30">
          <Canvas camera={{ position: [5, 5, 5], fov: 60 }}>
            <Scene
              nodes={nodes}
              onNodeClick={onNodeSelect}
              selectedNode={selectedNode}
            />
          </Canvas>
          
          {/* Navigation Controls Overlay */}
          <div className="absolute bottom-4 right-4 flex gap-2">
            <Button
              size="sm"
              variant="outline"
              className="border-blue-500/30 text-blue-400 bg-black/50"
              onClick={() => onViewModeChange('explore')}
            >
              <Eye className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              className="border-green-500/30 text-green-400 bg-black/50"
              onClick={() => onViewModeChange('focus')}
            >
              <Focus className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              className="border-purple-500/30 text-purple-400 bg-black/50"
              onClick={() => onViewModeChange('assist')}
            >
              <Users className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              className="border-yellow-500/30 text-yellow-400 bg-black/50"
              onClick={() => onViewModeChange('syntropy')}
            >
              <Zap className="w-4 h-4" />
            </Button>
          </div>
          
          {/* View Mode Indicator */}
          <div className="absolute top-4 left-4">
            <div className="bg-black/70 px-3 py-1 rounded-lg border border-gray-600">
              <span className="text-white/80 text-sm capitalize">{viewMode} Mode</span>
            </div>
          </div>
        </div>
        
        {/* View Mode Controls */}
        <div className="flex gap-2 mt-4">
          {(['explore', 'focus', 'assist', 'syntropy'] as const).map((mode) => (
            <Button
              key={mode}
              size="sm"
              variant={viewMode === mode ? "default" : "outline"}
              onClick={() => onViewModeChange(mode)}
              className={`capitalize ${
                viewMode === mode 
                  ? 'bg-blue-500/20 text-blue-400 border-blue-500/30' 
                  : 'border-gray-600 text-gray-400'
              }`}
            >
              {mode}
            </Button>
          ))}
        </div>
        
        {/* Mode Descriptions */}
        <div className="mt-4 p-3 bg-gray-800/30 rounded-lg border border-gray-700">
          <div className="text-sm text-white/80">
            {viewMode === 'explore' && "Free camera movement through the 3D Timeline world"}
            {viewMode === 'focus' && "Zoom into selected node/task, distraction-free UI"}
            {viewMode === 'assist' && "Highlights users needing help (glow pulses / call signals)"}
            {viewMode === 'syntropy' && "Timeline-wide collaboration events (rituals, builds)"}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
