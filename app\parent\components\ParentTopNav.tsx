'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Bell, 
  Search, 
  Settings, 
  User, 
  Shield,
  AlertTriangle,
  CheckCircle,
  Clock,
  ChevronDown
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'

interface ParentTopNavProps {
  sidebarCollapsed: boolean
  className?: string
}

export function ParentTopNav({ sidebarCollapsed, className }: ParentTopNavProps) {
  const [showNotifications, setShowNotifications] = useState(false)
  const [showProfile, setShowProfile] = useState(false)
  const { userProfile } = useAuth()

  // Mock notifications for parent dashboard
  const notifications = [
    {
      id: '1',
      type: 'safety',
      title: 'Safety Alert',
      message: 'Your child completed a safe chat interaction',
      time: '5 min ago',
      read: false,
      icon: Shield,
      color: 'text-green-400'
    },
    {
      id: '2',
      type: 'progress',
      title: 'Learning Milestone',
      message: 'Emma completed the Quantum Physics module',
      time: '1 hour ago',
      read: false,
      icon: CheckCircle,
      color: 'text-blue-400'
    },
    {
      id: '3',
      type: 'time',
      title: 'Screen Time Alert',
      message: 'Daily limit approaching (45 min remaining)',
      time: '2 hours ago',
      read: true,
      icon: Clock,
      color: 'text-yellow-400'
    },
    {
      id: '4',
      type: 'alert',
      title: 'Content Flag',
      message: 'Inappropriate content was automatically blocked',
      time: '1 day ago',
      read: true,
      icon: AlertTriangle,
      color: 'text-red-400'
    }
  ]

  const unreadCount = notifications.filter(n => !n.read).length

  return (
    <motion.nav
      initial={{ y: -64 }}
      animate={{ y: 0 }}
      className={`fixed top-0 right-0 h-16 bg-black/40 backdrop-blur-xl border-b border-gray-800/50 z-20 transition-all duration-300 ${
        sidebarCollapsed ? 'left-20' : 'left-70'
      } ${className}`}
      style={{
        left: sidebarCollapsed ? '80px' : '280px',
        width: sidebarCollapsed ? 'calc(100% - 80px)' : 'calc(100% - 280px)'
      }}
    >
      <div className="flex items-center justify-between h-full px-6">
        {/* Left Section - Search */}
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search child activities, reports..."
              className="pl-10 pr-4 py-2 bg-gray-800/50 border border-gray-700/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500/50 focus:bg-gray-800/70 transition-all w-80"
            />
          </div>
        </div>

        {/* Right Section - Actions */}
        <div className="flex items-center gap-4">
          {/* Quick Status */}
          <div className="hidden md:flex items-center gap-3">
            <div className="flex items-center gap-2 px-3 py-1 bg-green-500/20 border border-green-500/30 rounded-full">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span className="text-green-400 text-xs font-medium">Child Online</span>
            </div>
            
            <div className="flex items-center gap-2 px-3 py-1 bg-blue-500/20 border border-blue-500/30 rounded-full">
              <Shield className="w-3 h-3 text-blue-400" />
              <span className="text-blue-400 text-xs font-medium">Safe Mode</span>
            </div>
          </div>

          {/* Notifications */}
          <div className="relative">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative text-gray-400 hover:text-white hover:bg-gray-800/50"
            >
              <Bell className="w-5 h-5" />
              {unreadCount > 0 && (
                <Badge 
                  variant="destructive" 
                  className="absolute -top-1 -right-1 w-5 h-5 text-xs flex items-center justify-center p-0 bg-red-500"
                >
                  {unreadCount}
                </Badge>
              )}
            </Button>

            {/* Notifications Dropdown */}
            <AnimatePresence>
              {showNotifications && (
                <motion.div
                  initial={{ opacity: 0, y: 10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 10, scale: 0.95 }}
                  className="absolute right-0 top-12 w-80 bg-black/90 backdrop-blur-xl border border-gray-800/50 rounded-lg shadow-2xl z-50"
                >
                  <div className="p-4 border-b border-gray-800/50">
                    <h3 className="text-white font-semibold">Notifications</h3>
                    <p className="text-gray-400 text-sm">{unreadCount} unread</p>
                  </div>
                  
                  <div className="max-h-80 overflow-y-auto">
                    {notifications.map((notification) => (
                      <div
                        key={notification.id}
                        className={`p-4 border-b border-gray-800/30 hover:bg-gray-800/30 transition-colors ${
                          !notification.read ? 'bg-blue-500/5' : ''
                        }`}
                      >
                        <div className="flex items-start gap-3">
                          <div className={`p-2 rounded-lg bg-gray-800/50 ${notification.color}`}>
                            <notification.icon className="w-4 h-4" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <h4 className="text-white font-medium text-sm">{notification.title}</h4>
                              {!notification.read && (
                                <div className="w-2 h-2 bg-blue-400 rounded-full" />
                              )}
                            </div>
                            <p className="text-gray-400 text-sm mt-1">{notification.message}</p>
                            <p className="text-gray-500 text-xs mt-2">{notification.time}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="p-4 border-t border-gray-800/50">
                    <Button variant="ghost" className="w-full text-blue-400 hover:text-blue-300">
                      View All Notifications
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Settings */}
          <Button
            variant="ghost"
            size="sm"
            className="text-gray-400 hover:text-white hover:bg-gray-800/50"
          >
            <Settings className="w-5 h-5" />
          </Button>

          {/* Profile */}
          <div className="relative">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowProfile(!showProfile)}
              className="flex items-center gap-2 text-gray-400 hover:text-white hover:bg-gray-800/50"
            >
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-green-500 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-white" />
              </div>
              <span className="hidden md:block text-sm font-medium text-white">
                {userProfile?.full_name || 'Parent'}
              </span>
              <ChevronDown className="w-4 h-4" />
            </Button>

            {/* Profile Dropdown */}
            <AnimatePresence>
              {showProfile && (
                <motion.div
                  initial={{ opacity: 0, y: 10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 10, scale: 0.95 }}
                  className="absolute right-0 top-12 w-64 bg-black/90 backdrop-blur-xl border border-gray-800/50 rounded-lg shadow-2xl z-50"
                >
                  <div className="p-4 border-b border-gray-800/50">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-green-500 rounded-full flex items-center justify-center">
                        <User className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-white font-semibold">{userProfile?.full_name || 'Parent'}</h3>
                        <p className="text-gray-400 text-sm">{userProfile?.email}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-2">
                    <Button variant="ghost" className="w-full justify-start text-gray-300 hover:text-white">
                      <User className="w-4 h-4 mr-3" />
                      Profile Settings
                    </Button>
                    <Button variant="ghost" className="w-full justify-start text-gray-300 hover:text-white">
                      <Shield className="w-4 h-4 mr-3" />
                      Parental Controls
                    </Button>
                    <Button variant="ghost" className="w-full justify-start text-gray-300 hover:text-white">
                      <Settings className="w-4 h-4 mr-3" />
                      Account Settings
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </motion.nav>
  )
}
