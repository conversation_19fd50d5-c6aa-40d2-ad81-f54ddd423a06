/**
 * Advanced Rendering Pipeline Optimization for NanoHero Platform
 * Implements render order optimization, material batching, and occlusion culling
 */

import * as THREE from 'three'

export interface RenderBatch {
  material: THREE.Material
  geometries: THREE.BufferGeometry[]
  instances: THREE.Matrix4[]
  renderOrder: number
}

export interface RenderLayer {
  name: string
  priority: number
  transparent: boolean
  depthWrite: boolean
  depthTest: boolean
}

export const RENDER_LAYERS = {
  BACKGROUND: { name: 'background', priority: 0, transparent: false, depthWrite: true, depthTest: true },
  OPAQUE: { name: 'opaque', priority: 1, transparent: false, depthWrite: true, depthTest: true },
  TRANSPARENT: { name: 'transparent', priority: 2, transparent: true, depthWrite: false, depthTest: true },
  OVERLAY: { name: 'overlay', priority: 3, transparent: true, depthWrite: false, depthTest: false },
  UI: { name: 'ui', priority: 4, transparent: true, depthWrite: false, depthTest: false }
} as const

export class RenderingPipelineManager {
  private renderer: THREE.WebGLRenderer | null = null
  private scene: THREE.Scene | null = null
  private camera: THREE.Camera | null = null
  
  private renderBatches: Map<string, RenderBatch> = new Map()
  private materialCache: Map<string, THREE.Material> = new Map()
  private geometryCache: Map<string, THREE.BufferGeometry> = new Map()
  
  private occlusionQueries: Map<THREE.Object3D, WebGLQuery> = new Map()
  private visibilityResults: Map<THREE.Object3D, boolean> = new Map()
  
  private renderStats = {
    drawCalls: 0,
    triangles: 0,
    batches: 0,
    culledObjects: 0
  }

  constructor() {
    this.resetStats()
  }

  public initialize(renderer: THREE.WebGLRenderer, scene: THREE.Scene, camera: THREE.Camera) {
    this.renderer = renderer
    this.scene = scene
    this.camera = camera
    
    // Enable occlusion queries if supported
    const gl = renderer.getContext()
    if ('createQuery' in gl && typeof gl.createQuery === 'function') {
      console.log('Occlusion queries supported - enabling advanced culling')
    }
  }

  /**
   * Optimize render order for maximum performance
   */
  public optimizeRenderOrder(objects: THREE.Object3D[]): THREE.Object3D[] {
    return objects.sort((a, b) => {
      // 1. Sort by render layer priority
      const layerA = this.getRenderLayer(a)
      const layerB = this.getRenderLayer(b)
      if (layerA.priority !== layerB.priority) {
        return layerA.priority - layerB.priority
      }

      // 2. Within same layer, sort opaque objects front-to-back
      if (!layerA.transparent && !layerB.transparent) {
        const distA = this.getDistanceToCamera(a)
        const distB = this.getDistanceToCamera(b)
        return distA - distB
      }

      // 3. Sort transparent objects back-to-front
      if (layerA.transparent && layerB.transparent) {
        const distA = this.getDistanceToCamera(a)
        const distB = this.getDistanceToCamera(b)
        return distB - distA
      }

      // 4. Sort by material to reduce state changes
      const matA = this.getMaterialHash(a)
      const matB = this.getMaterialHash(b)
      return matA.localeCompare(matB)
    })
  }

  /**
   * Create optimized render batches
   */
  public createRenderBatches(objects: THREE.Object3D[]): RenderBatch[] {
    const batches = new Map<string, RenderBatch>()

    objects.forEach(obj => {
      if (obj instanceof THREE.Mesh) {
        const materialHash = this.getMaterialHash(obj)
        const geometryHash = this.getGeometryHash(obj)
        const batchKey = `${materialHash}_${geometryHash}`

        if (!batches.has(batchKey)) {
          batches.set(batchKey, {
            material: obj.material as THREE.Material,
            geometries: [obj.geometry],
            instances: [],
            renderOrder: obj.renderOrder
          })
        }

        const batch = batches.get(batchKey)!
        batch.instances.push(obj.matrixWorld.clone())
      }
    })

    return Array.from(batches.values())
  }

  /**
   * Perform occlusion culling
   */
  public performOcclusionCulling(objects: THREE.Object3D[]): THREE.Object3D[] {
    if (!this.renderer || !this.camera) return objects

    const gl = this.renderer.getContext()
    if (!('createQuery' in gl) || !gl.createQuery) return objects // Fallback if not supported

    const visibleObjects: THREE.Object3D[] = []
    const frustum = new THREE.Frustum()
    const matrix = new THREE.Matrix4()

    // Update frustum from camera
    matrix.multiplyMatrices(this.camera.projectionMatrix, this.camera.matrixWorldInverse)
    frustum.setFromProjectionMatrix(matrix)

    objects.forEach(obj => {
      // First check frustum culling
      if (!this.isInFrustum(obj, frustum)) {
        this.renderStats.culledObjects++
        return
      }

      // Then check occlusion
      if (this.isOccluded(obj)) {
        this.renderStats.culledObjects++
        return
      }

      visibleObjects.push(obj)
    })

    return visibleObjects
  }

  /**
   * Optimize material usage
   */
  public optimizeMaterials(objects: THREE.Object3D[]): void {
    const materialUsage = new Map<string, THREE.Material>()

    objects.forEach(obj => {
      if (obj instanceof THREE.Mesh) {
        const hash = this.getMaterialHash(obj)
        if (!materialUsage.has(hash)) {
          materialUsage.set(hash, obj.material as THREE.Material)
        } else {
          // Reuse existing material
          obj.material = materialUsage.get(hash)!
        }
      }
    })
  }

  /**
   * Batch similar draw calls
   */
  public batchDrawCalls(batches: RenderBatch[]): void {
    if (!this.renderer) return

    batches.forEach(batch => {
      if (batch.instances.length > 1) {
        // Create instanced mesh for multiple instances
        const instancedMesh = new THREE.InstancedMesh(
          batch.geometries[0],
          batch.material,
          batch.instances.length
        )

        batch.instances.forEach((matrix, index) => {
          instancedMesh.setMatrixAt(index, matrix)
        })

        instancedMesh.instanceMatrix.needsUpdate = true
        this.renderStats.batches++
      }
    })
  }

  private getRenderLayer(obj: THREE.Object3D): RenderLayer {
    // Determine render layer based on object properties
    if (obj.userData.renderLayer) {
      return obj.userData.renderLayer
    }

    if (obj instanceof THREE.Mesh) {
      const material = obj.material as THREE.Material
      if (material.transparent) {
        return RENDER_LAYERS.TRANSPARENT
      }
    }

    return RENDER_LAYERS.OPAQUE
  }

  private getDistanceToCamera(obj: THREE.Object3D): number {
    if (!this.camera) return 0
    return obj.position.distanceTo(this.camera.position)
  }

  private getMaterialHash(obj: THREE.Object3D): string {
    if (obj instanceof THREE.Mesh) {
      const material = obj.material as THREE.Material
      return `${material.type}_${material.uuid}`
    }
    return 'unknown'
  }

  private getGeometryHash(obj: THREE.Object3D): string {
    if (obj instanceof THREE.Mesh) {
      return obj.geometry.uuid
    }
    return 'unknown'
  }

  private isInFrustum(obj: THREE.Object3D, frustum: THREE.Frustum): boolean {
    // Simple bounding sphere test
    const sphere = new THREE.Sphere()
    if (obj instanceof THREE.Mesh) {
      obj.geometry.computeBoundingSphere()
      if (obj.geometry.boundingSphere) {
        sphere.copy(obj.geometry.boundingSphere)
        sphere.applyMatrix4(obj.matrixWorld)
        return frustum.intersectsSphere(sphere)
      }
    }
    return true
  }

  private isOccluded(obj: THREE.Object3D): boolean {
    // Simple occlusion test - can be enhanced with actual occlusion queries
    return this.visibilityResults.get(obj) === false
  }

  public resetStats(): void {
    this.renderStats = {
      drawCalls: 0,
      triangles: 0,
      batches: 0,
      culledObjects: 0
    }
  }

  public getStats() {
    return { ...this.renderStats }
  }

  public dispose(): void {
    this.renderBatches.clear()
    this.materialCache.clear()
    this.geometryCache.clear()
    this.occlusionQueries.clear()
    this.visibilityResults.clear()
  }
}

/**
 * Render order optimization utilities
 */
export class RenderOrderOptimizer {
  static optimizeForTransparency(objects: THREE.Object3D[], camera: THREE.Camera): THREE.Object3D[] {
    return objects.sort((a, b) => {
      const distA = a.position.distanceTo(camera.position)
      const distB = b.position.distanceTo(camera.position)
      
      // Transparent objects should be rendered back-to-front
      const aTransparent = RenderOrderOptimizer.isTransparent(a)
      const bTransparent = RenderOrderOptimizer.isTransparent(b)
      
      if (aTransparent && bTransparent) {
        return distB - distA // Back to front
      } else if (!aTransparent && !bTransparent) {
        return distA - distB // Front to back
      } else {
        return aTransparent ? 1 : -1 // Opaque first, then transparent
      }
    })
  }

  static isTransparent(obj: THREE.Object3D): boolean {
    if (obj instanceof THREE.Mesh) {
      const material = obj.material as THREE.Material
      return material.transparent || material.opacity < 1
    }
    return false
  }

  static optimizeForMaterial(objects: THREE.Object3D[]): THREE.Object3D[] {
    return objects.sort((a, b) => {
      const matA = RenderOrderOptimizer.getMaterialId(a)
      const matB = RenderOrderOptimizer.getMaterialId(b)
      return matA.localeCompare(matB)
    })
  }

  static getMaterialId(obj: THREE.Object3D): string {
    if (obj instanceof THREE.Mesh) {
      const material = obj.material as THREE.Material
      return material.uuid
    }
    return ''
  }
}

// Global rendering pipeline instance
export const renderingPipeline = new RenderingPipelineManager()
