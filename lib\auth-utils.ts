import { User } from '@supabase/supabase-js'

export interface AuthUser {
  id: string
  email: string | null
  user_metadata: {
    full_name?: string
    name?: string
    avatar_url?: string
    [key: string]: any
  }
  app_metadata: {
    provider?: string
    [key: string]: any
  }
  created_at: string
  last_sign_in_at?: string
  email_confirmed_at?: string
}

export function serializeUser(user: User): AuthUser {
  return {
    id: user.id,
    email: user.email ?? null,
    user_metadata: user.user_metadata,
    app_metadata: user.app_metadata,
    created_at: user.created_at,
    last_sign_in_at: user.last_sign_in_at,
    email_confirmed_at: user.email_confirmed_at,
  }
}

export function getProviderName(providerId: string): string {
  switch (providerId) {
    case 'google':
      return 'Google'
    case 'facebook':
      return 'Facebook'
    case 'twitter':
      return 'Twitter'
    case 'github':
      return 'GitHub'
    case 'apple':
      return 'Apple'
    default:
      return 'Unknown Provider'
  }
}

export function getUserInitials(user: AuthUser): string {
  const fullName = user.user_metadata?.full_name || user.user_metadata?.name

  if (fullName) {
    return fullName
      .split(' ')
      .map(name => name.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  if (user.email) {
    return user.email.charAt(0).toUpperCase()
  }

  return 'U'
}

export function isNewUser(user: AuthUser): boolean {
  const createdAt = user.created_at
  const lastSignInAt = user.last_sign_in_at

  if (!createdAt || !lastSignInAt) return false

  // Consider user new if they signed up within the last 5 minutes
  const timeDiff = new Date(lastSignInAt).getTime() - new Date(createdAt).getTime()
  return timeDiff < 5 * 60 * 1000 // 5 minutes in milliseconds
}
