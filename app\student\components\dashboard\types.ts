// Dashboard component types and interfaces

export interface DNACoreDashboardProps {
  className?: string
}

// Enhanced node types for the NanoGenesis system
export interface NanoNode {
  id: string
  type: 'learning' | 'echo' | 'meditation' | 'timelinegen'
  title: string
  description: string
  status: 'active' | 'dormant' | 'completed'
  position: { x: number; y: number; z: number }
  color: string
  participants: number
  lastActivity: string
  completionRate: number
  rewards: { ce: number; qs: number }
}

// Timeline evolution stages
export type EvolutionStage = 'dormant' | 'awakening' | 'harmonic' | 'syntropic' | 'ascended'

// View modes for the dashboard
export type ViewMode = 'explore' | 'focus' | 'assist' | 'syntropy'

// Evolution theme interface
export interface EvolutionTheme {
  primary: string
  secondary: string
  accent: string
  glow: string
  background: string
  gradient: string
}

// Timeline progress interface
export interface TimelineProgress {
  current: EvolutionStage
  progress: number
  nextStage: EvolutionStage | 'ascended'
}

// Dashboard header props
export interface DashboardHeaderProps {
  evolutionTheme: EvolutionTheme
  playerData: any
  dnaFragment: string
}

// DNA analysis props
export interface DNAAnalysisProps {
  dnaFragment: string
  playerData: any
  systemStatus: any
  evolutionTheme: EvolutionTheme
}

// Timeline components props
export interface TimelineComponentProps {
  timelineStage: EvolutionStage
  playerData: any
  systemStatus: any
}

// Metrics component props
export interface MetricsComponentProps {
  systemStatus: any
  playerData: any
}

// Node management props
export interface NodeManagementProps {
  nanoNodes: NanoNode[]
  selectedNode: NanoNode | null
  onNodeSelect: (node: NanoNode) => void
}

// Consciousness feed item
export interface ConsciousnessFeedItem {
  user: string
  action: string
  time: string
  type: 'achievement' | 'collaboration' | 'meditation' | 'system'
}

// Performance metric
export interface PerformanceMetric {
  metric: string
  value: string
  target: string
  status: 'good' | 'excellent' | 'warning' | 'critical'
}

// Quantum task interface
export interface QuantumTask {
  id: string
  title: string
  description: string
  type: 'meditation' | 'learning' | 'collaboration' | 'exploration'
  difficulty: 'easy' | 'medium' | 'hard' | 'expert'
  rewards: { ce: number; qs: number }
  timeEstimate: string
  status: 'available' | 'in-progress' | 'completed'
  participants?: number
}
