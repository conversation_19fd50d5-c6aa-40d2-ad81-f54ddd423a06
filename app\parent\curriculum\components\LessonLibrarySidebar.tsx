'use client'

import React, { useState, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Search,
  Filter,
  ChevronDown,
  ChevronRight,
  BookOpen,
  Atom,
  Dna,
  Brain,
  Shield,
  Heart,
  Lightbulb
} from 'lucide-react'

import {
  ChildProfile,
  KanbanLessonCard,
  WeekDay,
  DifficultyLevel,
  Lesson
} from '../types/curriculum'
import { LESSON_LIBRARY, SUBJECT_AREAS } from '../data/lessonLibrary'
import { LessonPreviewModal } from './LessonPreviewModal'
import { DraggableLibraryCard } from './DraggableLibraryCard'

interface LessonLibrarySidebarProps {
  selectedChild: ChildProfile
  onLessonDrag: (lesson: KanbanLessonCard | null) => void
  onLessonDrop: (lesson: KanbanLessonCard, targetDay: WeekDay) => void
  onQuickAdd?: (lesson: Lesson, day?: WeekDay) => void
}

export function LessonLibrarySidebar({
  selectedChild,
  onLessonDrag,
  onLessonDrop,
  onQuickAdd
}: LessonLibrarySidebarProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([])
  const [selectedDifficulty, setSelectedDifficulty] = useState<DifficultyLevel[]>([])
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['quantum-physics', 'critical-thinking']))
  const [previewLesson, setPreviewLesson] = useState<string | null>(null)
  const [showFilters, setShowFilters] = useState(false)

  // Subject icons mapping
  const subjectIcons = {
    'quantum-physics': Atom,
    'dna-biology': Dna,
    'critical-thinking': Brain,
    'cybersecurity': Shield,
    'emotional-intelligence': Heart,
    'creativity': Lightbulb
  }

  // Filter lessons based on child's age and preferences
  const filteredLessons = useMemo(() => {
    return LESSON_LIBRARY.filter(lesson => {
      // Age appropriateness
      const [minAge, maxAge] = lesson.ageRange
      if (selectedChild.age < minAge || selectedChild.age > maxAge) {
        return false
      }

      // Search query
      if (searchQuery && !lesson.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !lesson.description.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !lesson.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))) {
        return false
      }

      // Subject filter
      if (selectedSubjects.length > 0 && !selectedSubjects.includes(lesson.subject)) {
        return false
      }

      // Difficulty filter
      if (selectedDifficulty.length > 0 && !selectedDifficulty.includes(lesson.difficulty)) {
        return false
      }

      return true
    })
  }, [selectedChild.age, searchQuery, selectedSubjects, selectedDifficulty])

  // Group lessons by subject
  const lessonsBySubject = useMemo(() => {
    const grouped: Record<string, typeof filteredLessons> = {}
    
    filteredLessons.forEach(lesson => {
      if (!grouped[lesson.subject]) {
        grouped[lesson.subject] = []
      }
      grouped[lesson.subject].push(lesson)
    })

    return grouped
  }, [filteredLessons])

  // Convert lesson to Kanban card
  const createLessonCard = (lesson: typeof LESSON_LIBRARY[0]): KanbanLessonCard => ({
    id: `card-${lesson.id}-${Date.now()}`,
    lessonId: lesson.id,
    title: lesson.title,
    duration: lesson.duration,
    difficulty: lesson.difficulty,
    subject: lesson.subject,
    category: lesson.category,
    status: 'not-started',
    priority: 'medium'
  })

  // Handle lesson selection for adding to week
  const _handleLessonSelect = (lesson: typeof LESSON_LIBRARY[0]) => {
    const lessonCard = createLessonCard(lesson)
    onLessonDrag(lessonCard)
  }

  // Handle quick add to today
  const handleQuickAdd = (lesson: Lesson) => {
    if (onQuickAdd) {
      // Add to today (or Monday if it's weekend)
      const today = new Date()
      const dayOfWeek = today.getDay()
      const weekDays: WeekDay[] = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
      const targetDay = dayOfWeek === 0 || dayOfWeek === 6 ? 'monday' : weekDays[dayOfWeek]

      onQuickAdd(lesson, targetDay)
    }
  }

  // Toggle category expansion
  const toggleCategory = (subject: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(subject)) {
      newExpanded.delete(subject)
    } else {
      newExpanded.add(subject)
    }
    setExpandedCategories(newExpanded)
  }

  // Get difficulty color
  const getDifficultyColor = (difficulty: DifficultyLevel) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'intermediate': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'advanced': return 'bg-red-500/20 text-red-400 border-red-500/30'
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  // Get subject info
  const getSubjectInfo = (subjectId: string) => {
    return SUBJECT_AREAS.find(subject => subject.id === subjectId)
  }

  return (
    <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl h-full">
      <CardHeader className="pb-4">
        <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
          <BookOpen className="w-5 h-5 text-cyan-400" />
          Lesson Library
        </CardTitle>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Search lessons..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-gray-800/50 border-gray-700/50 text-white placeholder-gray-400"
          />
        </div>

        {/* Filter Toggle */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowFilters(!showFilters)}
          className="border-gray-700/50 text-gray-300 hover:bg-gray-800/50"
        >
          <Filter className="w-4 h-4 mr-2" />
          Filters
          <ChevronDown className={`w-4 h-4 ml-2 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
        </Button>
      </CardHeader>

      <CardContent className="space-y-4 max-h-[calc(100vh-300px)] overflow-y-auto">
        {/* Filters Panel */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              className="space-y-4 border-b border-gray-700/50 pb-4"
            >
              {/* Subject Filter */}
              <div>
                <Label className="text-gray-300 text-sm">Subjects</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {SUBJECT_AREAS.map(subject => (
                    <Button
                      key={subject.id}
                      variant={selectedSubjects.includes(subject.id) ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        setSelectedSubjects(prev => 
                          prev.includes(subject.id)
                            ? prev.filter(s => s !== subject.id)
                            : [...prev, subject.id]
                        )
                      }}
                      className={`text-xs ${
                        selectedSubjects.includes(subject.id)
                          ? 'bg-cyan-500/20 text-cyan-400 border-cyan-500/30'
                          : 'border-gray-700/50 text-gray-300 hover:bg-gray-800/50'
                      }`}
                    >
                      {subject.icon} {subject.name}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Difficulty Filter */}
              <div>
                <Label className="text-gray-300 text-sm">Difficulty</Label>
                <div className="flex gap-2 mt-2">
                  {(['beginner', 'intermediate', 'advanced'] as DifficultyLevel[]).map(difficulty => (
                    <Button
                      key={difficulty}
                      variant={selectedDifficulty.includes(difficulty) ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        setSelectedDifficulty(prev => 
                          prev.includes(difficulty)
                            ? prev.filter(d => d !== difficulty)
                            : [...prev, difficulty]
                        )
                      }}
                      className={`text-xs capitalize ${
                        selectedDifficulty.includes(difficulty)
                          ? getDifficultyColor(difficulty)
                          : 'border-gray-700/50 text-gray-300 hover:bg-gray-800/50'
                      }`}
                    >
                      {difficulty}
                    </Button>
                  ))}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Lesson Tree */}
        <div className="space-y-2">
          {Object.entries(lessonsBySubject).map(([subjectId, lessons]) => {
            const subjectInfo = getSubjectInfo(subjectId)
            const IconComponent = subjectIcons[subjectId as keyof typeof subjectIcons] || BookOpen
            const isExpanded = expandedCategories.has(subjectId)

            return (
              <div key={subjectId} className="space-y-2">
                {/* Subject Header */}
                <Button
                  variant="ghost"
                  onClick={() => toggleCategory(subjectId)}
                  className="w-full justify-start p-2 hover:bg-gray-800/50 text-left"
                >
                  {isExpanded ? (
                    <ChevronDown className="w-4 h-4 mr-2 text-gray-400" />
                  ) : (
                    <ChevronRight className="w-4 h-4 mr-2 text-gray-400" />
                  )}
                  <IconComponent className="w-4 h-4 mr-2 text-cyan-400" />
                  <span className="text-white font-medium">{subjectInfo?.name || subjectId}</span>
                  <Badge className="ml-auto bg-gray-700/50 text-gray-300">
                    {lessons.length}
                  </Badge>
                </Button>

                {/* Lessons */}
                <AnimatePresence>
                  {isExpanded && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      className="ml-6 space-y-2"
                    >
                      {lessons.map(lesson => (
                        <DraggableLibraryCard
                          key={lesson.id}
                          lesson={lesson}
                          onPreview={setPreviewLesson}
                          onQuickAdd={handleQuickAdd}
                        />
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )
          })}
        </div>

        {/* Empty State */}
        {filteredLessons.length === 0 && (
          <div className="text-center py-8">
            <BookOpen className="w-12 h-12 text-gray-600 mx-auto mb-3" />
            <h3 className="text-lg font-medium text-gray-400 mb-2">No lessons found</h3>
            <p className="text-sm text-gray-500">
              Try adjusting your search or filter criteria
            </p>
          </div>
        )}
      </CardContent>

      {/* Lesson Preview Modal */}
      {previewLesson && (
        <LessonPreviewModal
          lessonId={previewLesson}
          isOpen={!!previewLesson}
          onClose={() => setPreviewLesson(null)}
          onAddToWeek={(lesson, day) => {
            const lessonCard = createLessonCard(lesson)
            onLessonDrop(lessonCard, day)
            setPreviewLesson(null)
          }}
        />
      )}
    </Card>
  )
}
