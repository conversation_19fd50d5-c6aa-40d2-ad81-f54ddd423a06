'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  TrendingUp, 
  Star, 
  Trophy, 
  Zap, 
  Brain, 
  Heart, 
  Code, 
  Shield,
  Target,
  Award,
  BookOpen,
  Sparkles,
  ChevronRight
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { MetricsComponentProps, EvolutionTheme } from './types'

interface MyProgressSnapshotProps extends MetricsComponentProps {
  evolutionTheme: EvolutionTheme
}

interface SkillData {
  category: string
  value: number
  maxValue: number
  color: string
  icon: React.ReactNode
}

interface XPActivity {
  id: string
  type: 'xp' | 'badge' | 'lesson' | 'milestone'
  title: string
  description: string
  value: number
  timestamp: string
  icon: string
  color: string
  isNew?: boolean
}

interface Milestone {
  id: string
  title: string
  description: string
  completedAt: string
  category: string
  icon: string
  color: string
  isRecent: boolean
}

export default function MyProgressSnapshot({ 
  systemStatus, 
  playerData,
  evolutionTheme 
}: MyProgressSnapshotProps) {
  const [selectedSkill, setSelectedSkill] = useState<string | null>(null)
  const [showCelebration, setShowCelebration] = useState(false)

  // Mock skill data - in real app this would come from analytics
  const skillData: SkillData[] = [
    {
      category: 'Logic',
      value: 85,
      maxValue: 100,
      color: '#22D3EE',
      icon: <Brain className="w-4 h-4" />
    },
    {
      category: 'Emotional Intelligence',
      value: 72,
      maxValue: 100,
      color: '#F59E0B',
      icon: <Heart className="w-4 h-4" />
    },
    {
      category: 'Technology',
      value: 91,
      maxValue: 100,
      color: '#8B5CF6',
      icon: <Code className="w-4 h-4" />
    },
    {
      category: 'Security',
      value: 78,
      maxValue: 100,
      color: '#EF4444',
      icon: <Shield className="w-4 h-4" />
    },
    {
      category: 'Problem Solving',
      value: 88,
      maxValue: 100,
      color: '#10B981',
      icon: <Target className="w-4 h-4" />
    }
  ]

  // Mock XP activities
  const xpActivities: XPActivity[] = [
    {
      id: '1',
      type: 'milestone',
      title: 'CyberSafe Hacker 101 Completed!',
      description: 'Mastered cybersecurity fundamentals',
      value: 500,
      timestamp: '2 hours ago',
      icon: '🎯',
      color: 'from-green-500 to-emerald-500',
      isNew: true
    },
    {
      id: '2',
      type: 'badge',
      title: 'Logic Master Badge',
      description: 'Solved 50 logic puzzles',
      value: 200,
      timestamp: '1 day ago',
      icon: '🧠',
      color: 'from-purple-500 to-pink-500'
    },
    {
      id: '3',
      type: 'xp',
      title: 'Daily Learning Streak',
      description: '7 days in a row',
      value: 150,
      timestamp: '1 day ago',
      icon: '🔥',
      color: 'from-orange-500 to-red-500'
    },
    {
      id: '4',
      type: 'lesson',
      title: 'Quantum Computing Basics',
      description: 'Completed with 95% score',
      value: 300,
      timestamp: '2 days ago',
      icon: '⚛️',
      color: 'from-cyan-500 to-blue-500'
    }
  ]

  // Mock recent milestones
  const recentMilestones: Milestone[] = [
    {
      id: '1',
      title: 'CyberSafe Hacker 101',
      description: 'Completed advanced cybersecurity course',
      completedAt: '2 hours ago',
      category: 'Security',
      icon: '🛡️',
      color: 'from-red-500 to-pink-500',
      isRecent: true
    },
    {
      id: '2',
      title: 'Neural Network Explorer',
      description: 'Built your first AI model',
      completedAt: '1 day ago',
      category: 'Technology',
      icon: '🤖',
      color: 'from-purple-500 to-indigo-500',
      isRecent: true
    }
  ]

  // Trigger celebration for new milestones
  useEffect(() => {
    const hasNewMilestone = recentMilestones.some(m => m.isRecent)
    if (hasNewMilestone) {
      setShowCelebration(true)
      const timer = setTimeout(() => setShowCelebration(false), 3000)
      return () => clearTimeout(timer)
    }
  }, [recentMilestones])

  // Create radar chart points
  const createRadarPath = () => {
    const centerX = 80
    const centerY = 80
    const radius = 60
    const angleStep = (2 * Math.PI) / skillData.length

    const points = skillData.map((skill, index) => {
      const angle = index * angleStep - Math.PI / 2
      const value = (skill.value / skill.maxValue) * radius
      const x = centerX + Math.cos(angle) * value
      const y = centerY + Math.sin(angle) * value
      return `${x},${y}`
    })

    return `M ${points.join(' L ')} Z`
  }

  return (
    <div className="space-y-6">
      {/* Celebration Animation */}
      <AnimatePresence>
        {showCelebration && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: -50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: -50 }}
            className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-6 py-3 rounded-full shadow-2xl"
          >
            <div className="flex items-center gap-2">
              <Sparkles className="w-5 h-5" />
              <span className="font-bold">🎉 New Milestone Unlocked!</span>
              <Sparkles className="w-5 h-5" />
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <Card className="bg-black/40 border-cyan-500/30 backdrop-blur-xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-cyan-400">
            <TrendingUp className="w-5 h-5" />
            My Progress Snapshot
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Skill Radar Chart */}
            <div className="space-y-4">
              <h3 className="text-lg font-bold text-white flex items-center gap-2">
                <Target className="w-5 h-5 text-cyan-400" />
                Skill Radar
              </h3>
              
              <div className="relative">
                {/* SVG Radar Chart */}
                <svg width="160" height="160" className="mx-auto">
                  {/* Background grid */}
                  {[20, 40, 60].map((radius) => (
                    <circle
                      key={radius}
                      cx="80"
                      cy="80"
                      r={radius}
                      fill="none"
                      stroke="rgba(255,255,255,0.1)"
                      strokeWidth="1"
                    />
                  ))}
                  
                  {/* Axis lines */}
                  {skillData.map((_, index) => {
                    const angle = (index * 2 * Math.PI) / skillData.length - Math.PI / 2
                    const x2 = 80 + Math.cos(angle) * 60
                    const y2 = 80 + Math.sin(angle) * 60
                    return (
                      <line
                        key={index}
                        x1="80"
                        y1="80"
                        x2={x2}
                        y2={y2}
                        stroke="rgba(255,255,255,0.1)"
                        strokeWidth="1"
                      />
                    )
                  })}
                  
                  {/* Data area */}
                  <motion.path
                    d={createRadarPath()}
                    fill={`url(#radarGradient)`}
                    stroke={evolutionTheme.primary}
                    strokeWidth="2"
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: 1 }}
                    transition={{ duration: 2, ease: "easeOut" }}
                  />
                  
                  {/* Gradient definition */}
                  <defs>
                    <radialGradient id="radarGradient" cx="50%" cy="50%" r="50%">
                      <stop offset="0%" stopColor={evolutionTheme.primary} stopOpacity="0.3" />
                      <stop offset="100%" stopColor={evolutionTheme.primary} stopOpacity="0.1" />
                    </radialGradient>
                  </defs>
                  
                  {/* Data points */}
                  {skillData.map((skill, index) => {
                    const angle = (index * 2 * Math.PI) / skillData.length - Math.PI / 2
                    const value = (skill.value / skill.maxValue) * 60
                    const x = 80 + Math.cos(angle) * value
                    const y = 80 + Math.sin(angle) * value
                    return (
                      <motion.circle
                        key={skill.category}
                        cx={x}
                        cy={y}
                        r="4"
                        fill={skill.color}
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: index * 0.2 }}
                        className="cursor-pointer"
                        onClick={() => setSelectedSkill(skill.category)}
                      />
                    )
                  })}
                </svg>
                
                {/* Skill Labels */}
                <div className="grid grid-cols-2 gap-2 mt-4">
                  {skillData.map((skill) => (
                    <motion.div
                      key={skill.category}
                      className={`flex items-center gap-2 p-2 rounded-lg cursor-pointer transition-colors ${
                        selectedSkill === skill.category 
                          ? 'bg-white/10 border border-white/20' 
                          : 'hover:bg-white/5'
                      }`}
                      onClick={() => setSelectedSkill(skill.category)}
                      whileHover={{ scale: 1.02 }}
                    >
                      <div style={{ color: skill.color }}>
                        {skill.icon}
                      </div>
                      <div className="flex-1">
                        <div className="text-xs text-white/80">{skill.category}</div>
                        <div className="text-sm font-bold" style={{ color: skill.color }}>
                          {skill.value}%
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>

            {/* XP & Badge Wall */}
            <div className="space-y-4">
              <h3 className="text-lg font-bold text-white flex items-center gap-2">
                <Star className="w-5 h-5 text-yellow-400" />
                XP & Badge Wall
              </h3>
              
              <div className="space-y-3 max-h-80 overflow-y-auto">
                {xpActivities.map((activity, index) => (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`p-3 rounded-lg border ${
                      activity.isNew 
                        ? 'bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border-yellow-500/30' 
                        : 'bg-gray-800/30 border-gray-700/50'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">{activity.icon}</div>
                        <div>
                          <div className="text-white font-medium text-sm">
                            {activity.title}
                          </div>
                          <div className="text-white/60 text-xs">
                            {activity.description}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-yellow-400 font-bold text-sm">
                          +{activity.value} XP
                        </div>
                        <div className="text-white/60 text-xs">
                          {activity.timestamp}
                        </div>
                      </div>
                    </div>
                    {activity.isNew && (
                      <Badge className="mt-2 bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                        New!
                      </Badge>
                    )}
                  </motion.div>
                ))}
              </div>
            </div>
          </div>

          {/* Recent Milestones */}
          <div className="mt-6 pt-6 border-t border-white/10">
            <h3 className="text-lg font-bold text-white flex items-center gap-2 mb-4">
              <Award className="w-5 h-5 text-purple-400" />
              Recent Milestones
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {recentMilestones.map((milestone, index) => (
                <motion.div
                  key={milestone.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.2 }}
                  className={`p-4 rounded-lg border bg-gradient-to-r ${milestone.color} bg-opacity-10 border-white/20`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="text-2xl">{milestone.icon}</div>
                    {milestone.isRecent && (
                      <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <Sparkles className="w-4 h-4 text-yellow-400" />
                      </motion.div>
                    )}
                  </div>
                  <h4 className="text-white font-bold">{milestone.title}</h4>
                  <p className="text-white/60 text-sm mb-2">{milestone.description}</p>
                  <div className="flex items-center justify-between">
                    <Badge className="bg-white/10 text-white/80 border-white/20">
                      {milestone.category}
                    </Badge>
                    <span className="text-xs text-white/60">{milestone.completedAt}</span>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* View All Button */}
          <div className="mt-6 text-center">
            <Button 
              className="bg-cyan-500/20 text-cyan-400 border border-cyan-500/30 hover:bg-cyan-500/30"
            >
              <BookOpen className="w-4 h-4 mr-2" />
              View Full Progress Report
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
