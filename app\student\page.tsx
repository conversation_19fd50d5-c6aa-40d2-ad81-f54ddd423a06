'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Minimize2, Activity, Settings } from 'lucide-react'

// UI Components
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

// Core Dashboard Components
import DNACoreDashboard from './components/DNACoreDashboard'
import DNACore from './components/DNACore'


// Placeholder Components
import {
  NodeInteractionModal,
  LibraryOfTheGrid,
  WelcomeMessage,
  PerformanceMonitor
} from './components/placeholders'

// Store
import { useDashboardStore } from './store/dashboardStore'



export default function NanoGenesisDashboard() {
  // Store state
  const {
    selectedNode,
    activePanel,
    setSelectedNode,
    setActivePanel
  } = useDashboardStore()





  // Loading and error states
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isMobile, setIsMobile] = useState(false)

  const [showWelcome, setShowWelcome] = useState(true)







  // Initialize dashboard
  useEffect(() => {
    const initializeDashboard = async () => {
      try {
        // Simulate initialization
        await new Promise(resolve => setTimeout(resolve, 2000))
        setIsLoading(false)
      } catch (_err) {
        setError('Failed to initialize dashboard')
        setIsLoading(false)
      }
    }

    // Check if mobile
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    initializeDashboard()

    return () => window.removeEventListener('resize', checkMobile)
  }, [])



  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-space-gradient flex items-center justify-center">
        <div className="text-center">
          <motion.div
            className="w-16 h-16 border-4 border-neural-cyan/30 border-t-neural-cyan rounded-full mx-auto mb-4"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
          <h2 className="text-xl font-orbitron font-bold text-neural-cyan mb-2">
            Initializing NanoCore Dashboard
          </h2>
          <p className="text-white/60">Synchronizing with NanoCore...</p>
        </div>
      </div>
    )
  }

  // NanoCore Dashboard - Content only (layout handled by dashboard layout)
  return (
    <>
      {/* Main Dashboard Content */}
      <div className='flex flex-col h-full overflow-hidden relative p-4'>
        <DNACoreDashboard />
      </div>
      
      

      {/* Welcome Message */}

      {/* Node Interaction Modal */}
      <NodeInteractionModal
        nodeId={selectedNode}
        onClose={() => setSelectedNode(null)}
      />

      {/* Library of the Grid Panel */}
      <AnimatePresence>
        {activePanel === 'library' && (
          <motion.div
            initial={{ opacity: 0, x: '100%' }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: '100%' }}
            transition={{ duration: 0.3 }}
            className="fixed inset-y-0 right-0 w-1/3 z-50"
          >
            <Card className="h-full gaming-panel m-4 bg-gray-900/95 border-cyan-400/30">
              <LibraryOfTheGrid onClose={() => setActivePanel(null)} />
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* DNA Core Dashboard Panel */}
      <AnimatePresence>
        {(activePanel === 'dashboard-overview' || activePanel === 'dna-focus') && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.4 }}
            className="fixed inset-0 z-50 bg-space-dark/95 backdrop-blur-xl"
          >
            <div className="h-full overflow-y-auto">
              <DNACoreDashboard />
            </div>

            {/* Close Button */}
            <button
              onClick={() => setActivePanel(null)}
              className="absolute top-4 right-4 p-2 rounded-lg bg-white/10 hover:bg-white/20 text-white/60 hover:text-white transition-all duration-300"
            >
              <Minimize2 className="w-5 h-5" />
            </button>
          </motion.div>
        )}
      </AnimatePresence>





    </>
  )
}