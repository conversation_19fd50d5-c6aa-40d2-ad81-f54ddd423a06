/**
 * State Management Optimization Utilities
 * Provides tools for optimizing Zustand stores and preventing excessive re-renders
 */

import { useRef, useEffect, useMemo } from 'react'

/**
 * Debounced state updates to prevent excessive re-renders
 */
export function createDebouncedUpdater<T>(
  updateFn: (value: T) => void,
  delay: number = 100
) {
  let timeoutId: NodeJS.Timeout | null = null
  
  return (value: T) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    timeoutId = setTimeout(() => {
      updateFn(value)
      timeoutId = null
    }, delay)
  }
}

/**
 * Batched state updates to reduce the number of store updates
 */
export class StateBatcher<T> {
  private updates: Partial<T>[] = []
  private updateFn: (updates: Partial<T>) => void
  private batchTimeout: NodeJS.Timeout | null = null
  private batchDelay: number

  constructor(updateFn: (updates: Partial<T>) => void, batchDelay: number = 16) {
    this.updateFn = updateFn
    this.batchDelay = batchDelay
  }

  addUpdate(update: Partial<T>) {
    this.updates.push(update)
    
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout)
    }
    
    this.batchTimeout = setTimeout(() => {
      this.flush()
    }, this.batchDelay)
  }

  flush() {
    if (this.updates.length === 0) return
    
    // Merge all updates
    const mergedUpdate = this.updates.reduce((acc, update) => ({
      ...acc,
      ...update
    }), {} as Partial<T>)
    
    this.updateFn(mergedUpdate)
    this.updates = []
    this.batchTimeout = null
  }

  dispose() {
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout)
      this.batchTimeout = null
    }
    this.updates = []
  }
}

/**
 * Memoized selector to prevent unnecessary re-renders
 */
export function useMemoizedSelector<T, R>(
  selector: (state: T) => R,
  deps: any[] = []
): (state: T) => R {
  return useMemo(() => selector, deps)
}

/**
 * Shallow comparison hook for Zustand
 */
export function useShallowSelector<T, R>(
  useStore: (selector: (state: T) => R) => R,
  selector: (state: T) => R
): R {
  return useStore(selector)
}

/**
 * Performance monitoring for state updates
 */
export class StatePerformanceMonitor {
  private updateCounts = new Map<string, number>()
  private lastReset = Date.now()
  private resetInterval = 5000 // 5 seconds

  trackUpdate(storeName: string) {
    const current = this.updateCounts.get(storeName) || 0
    this.updateCounts.set(storeName, current + 1)
    
    // Auto-reset counters periodically
    if (Date.now() - this.lastReset > this.resetInterval) {
      this.logStats()
      this.reset()
    }
  }

  getUpdateCount(storeName: string): number {
    return this.updateCounts.get(storeName) || 0
  }

  getUpdatesPerSecond(storeName: string): number {
    const count = this.getUpdateCount(storeName)
    const timeElapsed = (Date.now() - this.lastReset) / 1000
    return count / timeElapsed
  }

  logStats() {
    console.group('State Performance Stats')
    this.updateCounts.forEach((count, storeName) => {
      const updatesPerSecond = this.getUpdatesPerSecond(storeName)
      console.log(`${storeName}: ${count} updates (${updatesPerSecond.toFixed(2)}/sec)`)
      
      if (updatesPerSecond > 10) {
        console.warn(`⚠️ High update frequency detected for ${storeName}`)
      }
    })
    console.groupEnd()
  }

  reset() {
    this.updateCounts.clear()
    this.lastReset = Date.now()
  }
}

// Global performance monitor
export const statePerformanceMonitor = new StatePerformanceMonitor()

/**
 * Optimized store creator with performance monitoring
 */
export function createOptimizedStore<T>(
  storeName: string,
  storeCreator: any
) {
  return (...args: any[]) => {
    const store = storeCreator(...args)
    
    // Wrap setState to monitor performance
    const originalSetState = store.setState
    store.setState = (...setStateArgs: any[]) => {
      statePerformanceMonitor.trackUpdate(storeName)
      return originalSetState(...setStateArgs)
    }
    
    return store
  }
}

/**
 * React hook for preventing unnecessary re-renders
 */
export function useStableCallback<T extends (...args: any[]) => any>(
  callback: T,
  deps: any[]
): T {
  const callbackRef = useRef<T>(callback)
  
  useEffect(() => {
    callbackRef.current = callback
  }, deps)
  
  return useMemo(
    () => ((...args: any[]) => callbackRef.current(...args)) as T,
    []
  )
}

/**
 * Optimized subscription hook that only re-renders when specific values change
 */
export function useOptimizedSubscription<T, R>(
  useStore: (selector: (state: T) => R, equalityFn?: (a: R, b: R) => boolean) => R,
  selector: (state: T) => R,
  equalityFn?: (a: R, b: R) => boolean
): R {
  const defaultEqualityFn = (a: R, b: R) => {
    // Deep comparison for objects, shallow for primitives
    if (typeof a === 'object' && typeof b === 'object' && a !== null && b !== null) {
      return JSON.stringify(a) === JSON.stringify(b)
    }
    return a === b
  }
  
  return useStore(selector, equalityFn || defaultEqualityFn)
}

/**
 * State change throttling utility
 */
export function createThrottledUpdater<T>(
  updateFn: (value: T) => void,
  throttleMs: number = 100
) {
  let lastUpdate = 0
  let pendingValue: T | null = null
  let timeoutId: NodeJS.Timeout | null = null
  
  return (value: T) => {
    const now = Date.now()
    
    if (now - lastUpdate >= throttleMs) {
      // Can update immediately
      updateFn(value)
      lastUpdate = now
      pendingValue = null
      
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
    } else {
      // Throttle the update
      pendingValue = value
      
      if (!timeoutId) {
        const remainingTime = throttleMs - (now - lastUpdate)
        timeoutId = setTimeout(() => {
          if (pendingValue !== null) {
            updateFn(pendingValue)
            lastUpdate = Date.now()
            pendingValue = null
          }
          timeoutId = null
        }, remainingTime)
      }
    }
  }
}

/**
 * Utility for creating computed selectors that are memoized
 */
export function createComputedSelector<T, R>(
  selector: (state: T) => R,
  dependencies: (state: T) => any[]
) {
  let lastDeps: any[] = []
  let lastResult: R
  let hasComputed = false
  
  return (state: T): R => {
    const currentDeps = dependencies(state)
    
    // Check if dependencies have changed
    const depsChanged = !hasComputed || 
      currentDeps.length !== lastDeps.length ||
      currentDeps.some((dep, index) => dep !== lastDeps[index])
    
    if (depsChanged) {
      lastResult = selector(state)
      lastDeps = currentDeps
      hasComputed = true
    }
    
    return lastResult
  }
}

/**
 * Store middleware for automatic performance optimization
 */
export function performanceMiddleware<T>(
  storeName: string,
  config: any
) {
  return (set: any, get: any, api: any) => {
    const originalSet = set
    
    // Wrap set function with performance monitoring and batching
    const optimizedSet = (partial: any, replace?: boolean) => {
      statePerformanceMonitor.trackUpdate(storeName)
      
      // Batch updates if they happen in quick succession
      if (typeof partial === 'function') {
        return originalSet(partial, replace)
      }
      
      // For object updates, we can implement batching here
      return originalSet(partial, replace)
    }
    
    return config(optimizedSet, get, api)
  }
}

/**
 * React hook for monitoring component re-renders
 */
export function useRenderMonitor(componentName: string) {
  const renderCount = useRef(0)
  const lastRender = useRef(Date.now())
  
  renderCount.current++
  
  useEffect(() => {
    const now = Date.now()
    const timeSinceLastRender = now - lastRender.current
    
    if (renderCount.current > 1) {
      console.log(
        `🔄 ${componentName} rendered ${renderCount.current} times ` +
        `(${timeSinceLastRender}ms since last render)`
      )
      
      if (timeSinceLastRender < 16) {
        console.warn(`⚠️ ${componentName} is re-rendering very frequently!`)
      }
    }
    
    lastRender.current = now
  })
  
  return renderCount.current
}

/**
 * Utility for creating stable object references
 */
export function useStableObject<T extends Record<string, any>>(obj: T): T {
  const stableRef = useRef<T>(obj)
  
  // Only update if the object has actually changed
  const hasChanged = useMemo(() => {
    const keys = Object.keys(obj)
    const prevKeys = Object.keys(stableRef.current)
    
    if (keys.length !== prevKeys.length) return true
    
    return keys.some(key => obj[key] !== stableRef.current[key])
  }, [obj])
  
  if (hasChanged) {
    stableRef.current = obj
  }
  
  return stableRef.current
}
