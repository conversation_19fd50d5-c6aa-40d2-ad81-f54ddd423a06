import type { Metada<PERSON> } from 'next'
import { AuthProvider } from '@/contexts/AuthContext'
import { Toaster } from 'sonner'

export const metadata: Metadata = {
  title: 'Sign Up - NanoHero',
  description: 'Create your NanoHero account and start your quantum learning adventure',
}

export default function SignupLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <AuthProvider>
      {children}
      <Toaster 
        position="top-center"
        toastOptions={{
          style: {
            background: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            border: '1px solid rgba(34, 211, 238, 0.3)',
          },
        }}
      />
    </AuthProvider>
  )
}
