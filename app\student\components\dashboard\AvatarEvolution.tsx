'use client'

import { motion } from 'framer-motion'
import { Star } from 'lucide-react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import DNA3DHelix from '../DNA3DHelix'
import { MetricsComponentProps, EvolutionTheme } from './types'

interface AvatarEvolutionProps extends MetricsComponentProps {
  evolutionTheme: EvolutionTheme
}

export default function AvatarEvolution({ 
  systemStatus, 
  playerData,
  evolutionTheme 
}: AvatarEvolutionProps) {
  return (
    <Card className="bg-black/40 border-yellow-500/30 backdrop-blur-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-yellow-400">
          <Star className="w-5 h-5" />
          Avatar Evolution
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Evolution DNA Helix */}
          <div className="relative h-32 rounded-lg border border-yellow-500/30 overflow-hidden bg-gradient-to-br from-yellow-900/10 to-orange-900/10">
            <DNA3DHelix
              height={2}
              radius={0.3}
              turns={1}
              baseCount={8}
              evolutionStage={playerData?.level || 1}
              ceLevel={0.65} // 65% progress to next level
              qsLevel={(systemStatus?.systemHealth || 0)}
              isAnimated={true}
            />
            <div className="absolute top-2 left-2 bg-black/70 px-2 py-1 rounded text-xs text-yellow-400">
              Evolution DNA
            </div>
          </div>

          {/* Evolution Progress */}
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-white/60">
                Level {playerData?.level || 1} → {(playerData?.level || 1) + 1}
              </span>
              <span className="text-sm font-bold text-yellow-400">65%</span>
            </div>
            <div className="relative h-3 bg-space-dark/60 rounded-full overflow-hidden">
              <motion.div
                className={`absolute inset-y-0 left-0 bg-gradient-to-r ${evolutionTheme.gradient} rounded-full`}
                initial={{ width: 0 }}
                animate={{ width: '65%' }}
                transition={{ duration: 2, ease: 'easeOut' }}
              />
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
            </div>
          </div>

          {/* Evolution Benefits */}
          <div className="grid grid-cols-1 gap-2 text-xs">
            <div className="p-2 bg-yellow-500/10 border border-yellow-500/30 rounded">
              <div className="text-yellow-400 font-bold">Next Unlock:</div>
              <div className="text-white/60">Enhanced Neural Pathways</div>
            </div>
            <div className="p-2 bg-orange-500/10 border border-orange-500/30 rounded">
              <div className="text-orange-400 font-bold">New Ability:</div>
              <div className="text-white/60">Quantum Meditation</div>
            </div>
          </div>

          {/* Evolution Stats */}
          <div className="grid grid-cols-2 gap-3 text-xs">
            <div className="text-center">
              <div className="text-white/60">DNA Complexity</div>
              <div className="font-bold text-cyan-400">Level {playerData?.level || 1}</div>
            </div>
            <div className="text-center">
              <div className="text-white/60">Uniqueness</div>
              <div className="font-bold text-purple-400">99.{97 - (playerData?.level || 1)}%</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
