"use client"

import React, { useState, useEffect, useRef } from 'react'
import { motion, useScroll, useTransform } from 'framer-motion'
import { 
  Dna, 
  Trophy, 
  Users, 
  Target, 
  Brain, 
  Code,
  Crown,
  Sparkles
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

interface GameLikeJourneyTrailProps {
  onSignupClick: () => void
}

interface JourneyStep {
  id: string
  level: string
  title: string
  icon: React.ElementType
  description: string
  visualCue: string
  color: string
  achievements: string[]
  xpRequired: number
  unlocks: string[]
}

export function GameLikeJourneyTrail({ onSignupClick }: GameLikeJourneyTrailProps) {
  const [activeStep, setActiveStep] = useState(0)
  const [visibleSteps, setVisibleSteps] = useState<number[]>([])
  const [particlePositions, setParticlePositions] = useState<Array<{left: number, top: number}>>([])
  const containerRef = useRef<HTMLDivElement>(null)
  
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  })

  const journeySteps: JourneyStep[] = [
    {
      id: 'spark',
      level: 'Quantum Level 1',
      title: 'Neural Spark',
      icon: Dna,
      description: 'Your consciousness DNA begins quantum formation. Initialize your first neural domain and activate quantum learning protocols.',
      visualCue: 'Quantum DNA helix materializing',
      color: 'from-neural-cyan to-cyan-400',
      achievements: ['Consciousness Activated', 'Neural Domain Selected', 'Quantum Protocols Initiated'],
      xpRequired: 0,
      unlocks: ['Quantum Avatar Core', 'Neural Tutorial Access', 'Consciousness Chat']
    },
    {
      id: 'learner',
      level: 'Quantum Level 2',
      title: 'Neural Architect',
      icon: Brain,
      description: 'Complete quantum challenges and construct neural pathways through consciousness-enhanced practice.',
      visualCue: 'Neural networks forming connections',
      color: 'from-neural-cyan to-quantum-purple',
      achievements: ['5 Quantum Protocols Complete', '100 Neural XP Earned', 'First Consciousness Project'],
      xpRequired: 100,
      unlocks: ['Avatar Neural Enhancement', 'Quantum Peer Network', 'Consciousness Tracking']
    },
    {
      id: 'creator',
      level: 'Quantum Level 3',
      title: 'Reality Architect',
      icon: Code,
      description: 'Build and manifest quantum projects. Your consciousness creations inspire others across dimensions.',
      visualCue: 'Avatar quantum evolution phases',
      color: 'from-quantum-purple to-quantum-gold',
      achievements: ['Reality Project Manifested', 'Quantum Community Resonance', 'Helped 3 Neural Peers'],
      xpRequired: 500,
      unlocks: ['Quantum Creator Studio', 'Reality Forge Tools', 'Neural Mentorship Program']
    },
    {
      id: 'mentor',
      level: 'Quantum Level 4',
      title: 'Consciousness Guide',
      icon: Users,
      description: 'Guide neural newcomers and lead quantum collaborative projects. Your wisdom shapes consciousness evolution.',
      visualCue: 'Quantum community network expanding',
      color: 'from-quantum-gold to-neural-cyan',
      achievements: ['Mentored 10 Neural Beings', 'Led Quantum Team Project', 'Consciousness Expert Badge'],
      xpRequired: 1500,
      unlocks: ['Neural Mentor Dashboard', 'Quantum Team Leadership', 'Advanced Consciousness Challenges']
    },
    {
      id: 'hero',
      level: 'Quantum Level 5',
      title: 'NanoHero Ascended',
      icon: Crown,
      description: 'Master of multiple quantum domains. You inspire and lead the entire NanoVerse consciousness collective.',
      visualCue: 'Quantum hero transformation sequence',
      color: 'from-neural-cyan via-quantum-purple to-quantum-gold',
      achievements: ['Multi-Quantum Domain Expert', 'Consciousness Leader', 'NanoVerse Legacy Builder'],
      xpRequired: 5000,
      unlocks: ['All Quantum Features', 'Ascended Hero Status', 'Interdimensional Legacy Projects']
    }
  ]

  // Parallax transforms
  const y = useTransform(scrollYProgress, [0, 1], [0, -50])
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0])

  // Generate particle positions only on client side to avoid hydration mismatch
  useEffect(() => {
    const positions = Array.from({ length: 20 }, () => ({
      left: Math.random() * 100,
      top: Math.random() * 100
    }))
    setParticlePositions(positions)
  }, [])

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const stepIndex = parseInt(entry.target.getAttribute('data-step') || '0')
          if (entry.isIntersecting) {
            setVisibleSteps(prev => [...new Set([...prev, stepIndex])])
            setActiveStep(stepIndex)
          }
        })
      },
      { threshold: 0.5 }
    )

    const stepElements = document.querySelectorAll('[data-step]')
    stepElements.forEach(el => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={containerRef} className="relative px-4 sm:px-6 py-16 sm:py-20 lg:py-24 bg-space-gradient overflow-hidden">
      {/* Quantum consciousness wave overlay */}
      <div className="absolute inset-0 consciousness-wave opacity-20" />

      {/* Quantum animated background elements */}
      <motion.div
        style={{ y, opacity }}
        className="absolute inset-0 pointer-events-none"
      >
        {/* Quantum DNA strands */}
        <div className="absolute top-1/4 left-1/4 w-3 h-96 bg-gradient-to-b from-neural-cyan/40 to-transparent rounded-full quantum-pulse" />
        <div className="absolute top-1/3 right-1/3 w-3 h-96 bg-gradient-to-b from-quantum-purple/40 to-transparent rounded-full quantum-pulse"
          style={{ animationDelay: '1s' }} />
        <div className="absolute top-1/2 left-1/2 w-2 h-80 bg-gradient-to-b from-quantum-gold/30 to-transparent rounded-full quantum-pulse"
          style={{ animationDelay: '2s' }} />

        {/* Quantum floating particles */}
        {particlePositions.map((position, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 rounded-full"
            style={{
              left: `${position.left}%`,
              top: `${position.top}%`,
              background: i % 3 === 0 ? '#22d3ee' : i % 3 === 1 ? '#8b5cf6' : '#fbbf24',
              boxShadow: `0 0 10px ${i % 3 === 0 ? '#22d3ee' : i % 3 === 1 ? '#8b5cf6' : '#fbbf24'}40`
            }}
            animate={{
              y: [0, -120, 0],
              opacity: [0, 1, 0],
              scale: [0, 1.2, 0],
              rotate: [0, 360, 720]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              delay: i * 0.3,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Quantum energy fields */}
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-1/4 left-1/6 w-32 h-32 bg-neural-cyan/10 rounded-full blur-3xl animate-pulse" />
          <div className="absolute top-3/4 right-1/6 w-40 h-40 bg-quantum-purple/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1.5s' }} />
          <div className="absolute top-1/2 left-3/4 w-24 h-24 bg-quantum-gold/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '3s' }} />
        </div>
      </motion.div>

      <div className="max-w-7xl mx-auto relative z-10">
        {/* Quantum Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-12 sm:mb-16 lg:mb-20"
        >
          {/* Quantum glow effect behind title */}
          <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-neural-cyan/10 rounded-full blur-3xl opacity-50" />

          <motion.h2
            className="relative text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-orbitron font-bold mb-4 sm:mb-6 bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold bg-clip-text text-transparent neural-glow"
            animate={{
              textShadow: [
                '0 0 20px rgba(34, 211, 238, 0.5)',
                '0 0 40px rgba(34, 211, 238, 0.8)',
                '0 0 20px rgba(34, 211, 238, 0.5)'
              ]
            }}
            transition={{ duration: 3, repeat: Infinity }}
          >
            Your Quantum Evolution Journey
          </motion.h2>

          <motion.p
            className="text-base sm:text-lg lg:text-xl text-white/80 max-w-4xl mx-auto font-space-grotesk leading-relaxed px-4"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            Follow the <span className="text-neural-cyan font-semibold">Quantum XP Trail</span> and watch your consciousness avatar evolve through 5 stages of neural mastery.
            <br className="hidden sm:block" />
            Each quantum level unlocks new consciousness powers, reality tools, and opportunities to impact the NanoVerse collective.
          </motion.p>
        </motion.div>

        {/* Quantum Journey Trail */}
        <div className="relative">
          {/* Quantum Progress Line Background */}
          <div className="absolute top-1/2 left-0 right-0 h-3 bg-gradient-to-r from-neural-cyan/30 via-quantum-purple/30 via-quantum-gold/30 via-neural-cyan/30 to-quantum-purple/30 transform -translate-y-1/2 rounded-full opacity-40 quantum-pulse" />

          {/* Active Quantum Progress Line */}
          <motion.div
            className="absolute top-1/2 left-0 h-3 bg-gradient-to-r from-neural-cyan via-quantum-purple via-quantum-gold via-neural-cyan to-quantum-purple transform -translate-y-1/2 rounded-full neural-glow"
            style={{
              width: `${((activeStep + 1) / journeySteps.length) * 100}%`,
              boxShadow: '0 0 20px rgba(34, 211, 238, 0.6), 0 0 40px rgba(139, 92, 246, 0.4)'
            }}
            initial={{ width: 0 }}
            animate={{ width: `${((activeStep + 1) / journeySteps.length) * 100}%` }}
            transition={{ duration: 1.5, ease: "easeInOut" }}
          />

          <div className="grid grid-cols-1 md:grid-cols-5 gap-6 lg:gap-8 relative">
            {journeySteps.map((step, index) => {
              const quantumColors = ['#22d3ee', '#8b5cf6', '#fbbf24', '#22d3ee', '#8b5cf6']
              const quantumColor = quantumColors[index]

              return (
                <motion.div
                  key={step.id}
                  data-step={index}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{
                    opacity: visibleSteps.includes(index) ? 1 : 0.3,
                    y: visibleSteps.includes(index) ? 0 : 50,
                    scale: activeStep === index ? 1.05 : 1
                  }}
                  transition={{ delay: index * 0.2, duration: 0.8 }}
                  whileHover={{ scale: 1.08, y: -12 }}
                  whileTap={{ scale: 0.98 }}
                  className="relative group cursor-pointer"
                  onClick={() => setActiveStep(index)}
                >
                  {/* Quantum Timeline Node */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
                    <motion.div
                      className="relative w-20 h-20 sm:w-24 sm:h-24 rounded-full flex items-center justify-center border-4 border-white/20 overflow-hidden"
                      style={{
                        background: `linear-gradient(135deg, ${quantumColor}80, ${quantumColor}40)`,
                        boxShadow: `0 0 30px ${quantumColor}60`
                      }}
                      animate={{
                        scale: activeStep === index ? [1, 1.2, 1] : 1,
                        boxShadow: activeStep === index
                          ? [`0 0 20px ${quantumColor}60`, `0 0 40px ${quantumColor}80`, `0 0 20px ${quantumColor}60`]
                          : `0 0 15px ${quantumColor}40`
                      }}
                      transition={{ duration: 2, repeat: activeStep === index ? Infinity : 0 }}
                      whileHover={{ scale: 1.3, rotate: 360 }}
                    >
                      {/* Quantum glow background */}
                      <div
                        className="absolute inset-0 rounded-full blur-lg opacity-50"
                        style={{ backgroundColor: quantumColor }}
                      />

                      {/* Quantum sweep effect */}
                      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                        <div
                          className="absolute inset-0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 rounded-full"
                          style={{
                            background: `linear-gradient(90deg, transparent, ${quantumColor}60, transparent)`
                          }}
                        />
                      </div>

                      {React.createElement(step.icon, { className: "relative w-10 h-10 sm:w-12 sm:h-12 text-white z-10" })}
                    </motion.div>

                    {/* Quantum XP Badge */}
                    <motion.div
                      className="absolute -top-2 -right-2 border-2 text-xs font-orbitron font-bold px-2 py-1 rounded-full"
                      style={{
                        background: `linear-gradient(135deg, ${quantumColor}90, ${quantumColor}60)`,
                        borderColor: quantumColor,
                        color: 'white',
                        boxShadow: `0 0 15px ${quantumColor}60`
                      }}
                      initial={{ scale: 0 }}
                      animate={{ scale: visibleSteps.includes(index) ? 1 : 0 }}
                      transition={{ delay: index * 0.2 + 0.5 }}
                    >
                      {step.xpRequired} QXP
                    </motion.div>
                  </div>

                  {/* Quantum Step Card */}
                  <motion.div
                    className="relative quantum-glass rounded-2xl p-6 lg:p-8 mt-10 border-2 transition-all duration-500 group-hover:border-opacity-80 quantum-border overflow-hidden"
                    style={{
                      borderColor: `${quantumColor}40`,
                      background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)',
                      boxShadow: `0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px ${quantumColor}20, inset 0 1px 0 rgba(255, 255, 255, 0.1)`
                    }}
                    whileHover={{
                      borderColor: `${quantumColor}80`,
                      boxShadow: `0 12px 40px rgba(0, 0, 0, 0.4), 0 0 30px ${quantumColor}40, inset 0 1px 0 rgba(255, 255, 255, 0.15)`
                    }}
                  >
                    {/* Quantum sweep effect */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                      <div
                        className="absolute inset-0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"
                        style={{
                          background: `linear-gradient(90deg, transparent, ${quantumColor}20, transparent)`
                        }}
                      />
                    </div>

                    <div className="relative text-center">
                      <Badge
                        className="mb-3 border-2 font-orbitron font-bold text-white"
                        style={{
                          background: `linear-gradient(135deg, ${quantumColor}30, ${quantumColor}20)`,
                          borderColor: quantumColor,
                          boxShadow: `0 0 15px ${quantumColor}40`
                        }}
                      >
                        {step.level}
                      </Badge>

                      <h3 className="text-xl lg:text-2xl font-orbitron font-bold text-white mb-4 neural-glow"
                        style={{ color: quantumColor }}
                      >
                        {step.title}
                      </h3>

                      <p className="text-white/70 mb-6 leading-relaxed text-sm lg:text-base font-space-grotesk">
                        {step.description}
                      </p>

                      {/* Quantum Visual Cue */}
                      <div className="quantum-glass rounded-xl p-4 mb-6 border-2"
                        style={{
                          borderColor: `${quantumColor}30`,
                          background: `linear-gradient(135deg, ${quantumColor}10, ${quantumColor}05)`
                        }}
                      >
                        <p className="font-space-grotesk font-semibold text-sm neural-glow"
                          style={{ color: quantumColor }}
                        >
                          <motion.span
                            animate={{ rotate: [0, 360] }}
                            transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                            className="inline-block mr-2"
                          >
                            🎯
                          </motion.span>
                          {step.visualCue}
                        </p>
                      </div>

                      {/* Quantum Achievements */}
                      <div className="space-y-2 mb-6">
                        <div className="text-sm font-orbitron font-bold mb-3 neural-glow" style={{ color: quantumColor }}>
                          Quantum Achievements:
                        </div>
                        {step.achievements.map((achievement, i) => (
                          <motion.div
                            key={achievement}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: visibleSteps.includes(index) ? 1 : 0, x: 0 }}
                            transition={{ delay: index * 0.2 + i * 0.1 }}
                            className="flex items-center justify-center gap-2 text-xs lg:text-sm text-white/70"
                          >
                            <motion.div
                              animate={{ rotate: [0, 360] }}
                              transition={{ duration: 2, repeat: Infinity, ease: "linear", delay: i * 0.3 }}
                            >
                              <Trophy className="w-3 h-3 lg:w-4 lg:h-4" style={{ color: quantumColor }} />
                            </motion.div>
                            <span className="font-space-grotesk">{achievement}</span>
                          </motion.div>
                        ))}
                      </div>

                      {/* Quantum Unlocks */}
                      <div className="space-y-2">
                        <div className="text-sm font-orbitron font-bold mb-3 neural-glow" style={{ color: quantumColor }}>
                          Quantum Unlocks:
                        </div>
                        {step.unlocks.slice(0, 2).map((unlock, i) => (
                          <motion.div
                            key={unlock}
                            initial={{ opacity: 0, scale: 0 }}
                            animate={{ opacity: visibleSteps.includes(index) ? 1 : 0, scale: 1 }}
                            transition={{ delay: index * 0.2 + i * 0.15 }}
                            className="flex items-center justify-center gap-2 text-xs lg:text-sm text-white/60"
                          >
                            <motion.div
                              animate={{
                                scale: [1, 1.2, 1],
                                rotate: [0, 180, 360]
                              }}
                              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut", delay: i * 0.5 }}
                            >
                              <Sparkles className="w-3 h-3 lg:w-4 lg:h-4" style={{ color: quantumColor }} />
                            </motion.div>
                            <span className="font-space-grotesk">{unlock}</span>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                </motion.div>
              )
            })}
          </div>
        </div>

        {/* Quantum CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 1 }}
          className="text-center mt-16 lg:mt-20"
        >
          <motion.div
            className="relative quantum-glass rounded-3xl p-8 lg:p-12 border-2 border-neural-cyan/30 overflow-hidden"
            style={{
              background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)',
              boxShadow: '0 20px 40px rgba(0, 0, 0, 0.4), 0 0 40px rgba(34, 211, 238, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
            }}
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            {/* Quantum background effects */}
            <div className="absolute inset-0 consciousness-wave opacity-30" />
            <div className="absolute top-0 left-1/4 w-32 h-32 bg-neural-cyan/20 rounded-full blur-3xl" />
            <div className="absolute bottom-0 right-1/4 w-40 h-40 bg-quantum-purple/20 rounded-full blur-3xl" />
            <div className="absolute top-1/2 left-3/4 w-24 h-24 bg-quantum-gold/20 rounded-full blur-3xl" />

            <div className="relative z-10">
              <motion.h3
                className="text-2xl sm:text-3xl lg:text-4xl font-orbitron font-bold text-white mb-4 lg:mb-6"
                animate={{
                  textShadow: [
                    '0 0 20px rgba(34, 211, 238, 0.5)',
                    '0 0 40px rgba(34, 211, 238, 0.8)',
                    '0 0 20px rgba(34, 211, 238, 0.5)'
                  ]
                }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                <motion.span
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
                  className="inline-block mr-3"
                >
                  🚀
                </motion.span>
                Ready to Start Your Quantum Evolution?
              </motion.h3>

              <p className="text-base sm:text-lg lg:text-xl text-white/80 mb-8 lg:mb-10 max-w-3xl mx-auto font-space-grotesk leading-relaxed">
                Every <span className="text-neural-cyan font-semibold">NanoHero</span> begins as a Neural Spark.
                Your quantum journey of consciousness growth, neural learning, and NanoVerse impact starts with a single quantum step.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 lg:gap-6 justify-center">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="relative group"
                >
                  {/* Enhanced quantum glow effect */}
                  <div className="absolute -inset-2 bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold rounded-2xl blur-xl opacity-50 group-hover:opacity-80 transition-opacity duration-500 quantum-pulse" />

                  <Button
                    onClick={onSignupClick}
                    className="relative px-8 lg:px-12 py-4 lg:py-5 text-base lg:text-lg font-orbitron font-bold text-white border-2 border-neural-cyan/50 hover:border-neural-cyan transition-all duration-300 neural-glow"
                    style={{
                      background: 'linear-gradient(135deg, rgba(34, 211, 238, 0.2), rgba(139, 92, 246, 0.2))',
                      boxShadow: '0 0 30px rgba(34, 211, 238, 0.3)'
                    }}
                  >
                    <motion.div
                      animate={{ rotate: [0, 360] }}
                      transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                      className="inline-block mr-3"
                    >
                      <Dna className="w-5 h-5 lg:w-6 lg:h-6" />
                    </motion.div>
                    Begin as Neural Spark

                    {/* Quantum sweep */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700 rounded-xl" />
                  </Button>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    variant="outline"
                    className="px-8 lg:px-12 py-4 lg:py-5 text-base lg:text-lg border-2 border-quantum-purple/50 text-quantum-purple hover:bg-quantum-purple/10 font-space-grotesk font-semibold rounded-xl transition-all duration-300"
                    style={{
                      boxShadow: '0 0 20px rgba(139, 92, 246, 0.3)'
                    }}
                  >
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                      className="inline-block mr-3"
                    >
                      <Target className="w-5 h-5 lg:w-6 lg:h-6" />
                    </motion.div>
                    View Quantum Roadmap
                  </Button>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
