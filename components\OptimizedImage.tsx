"use client"

import React, { useState, useCallback, memo } from 'react'
import Image from 'next/image'
import { motion, AnimatePresence } from 'framer-motion'
import { ImageIcon, Loader2 } from 'lucide-react'

interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  quality?: number
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
  sizes?: string
  fill?: boolean
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'
  loading?: 'lazy' | 'eager'
  onLoad?: () => void
  onError?: () => void
}

const OptimizedImage = memo(function OptimizedImage({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  quality = 75,
  placeholder = 'empty',
  blurDataURL,
  sizes,
  fill = false,
  objectFit = 'cover',
  loading = 'lazy',
  onLoad,
  onError
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  const handleLoad = useCallback(() => {
    setIsLoading(false)
    onLoad?.()
  }, [onLoad])

  const handleError = useCallback(() => {
    setIsLoading(false)
    setHasError(true)
    onError?.()
  }, [onError])

  const imageProps = {
    src,
    alt,
    className: `${className} ${objectFit === 'cover' ? 'object-cover' : 
                objectFit === 'contain' ? 'object-contain' : 
                objectFit === 'fill' ? 'object-fill' : 
                objectFit === 'none' ? 'object-none' : 'object-scale-down'}`,
    priority,
    quality,
    placeholder,
    blurDataURL,
    sizes,
    loading,
    onLoad: handleLoad,
    onError: handleError,
    ...(fill ? { fill: true } : { width, height })
  }

  return (
    <div className={`relative ${fill ? 'w-full h-full' : ''}`}>
      {/* Loading State */}
      <AnimatePresence>
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 flex items-center justify-center bg-gray-900/50 backdrop-blur-sm z-10"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            >
              <Loader2 className="w-6 h-6 text-neural-cyan" />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Error State */}
      <AnimatePresence>
        {hasError && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="absolute inset-0 flex items-center justify-center bg-gray-900/80 backdrop-blur-sm z-10"
          >
            <div className="text-center space-y-2">
              <ImageIcon className="w-8 h-8 text-gray-400 mx-auto" />
              <p className="text-sm text-gray-400">Failed to load image</p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Actual Image */}
      {!hasError && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: isLoading ? 0 : 1 }}
          transition={{ duration: 0.3 }}
        >
          <Image {...imageProps} alt={imageProps.alt || ""} />
        </motion.div>
      )}
    </div>
  )
})

export default OptimizedImage

// Utility function to generate optimized blur data URLs
export function generateBlurDataURL(width: number = 10, height: number = 10): string {
  const canvas = document.createElement('canvas')
  canvas.width = width
  canvas.height = height
  
  const ctx = canvas.getContext('2d')
  if (!ctx) return ''
  
  // Create a simple gradient blur placeholder
  const gradient = ctx.createLinearGradient(0, 0, width, height)
  gradient.addColorStop(0, '#1a1a2e')
  gradient.addColorStop(0.5, '#16213e')
  gradient.addColorStop(1, '#0f172a')
  
  ctx.fillStyle = gradient
  ctx.fillRect(0, 0, width, height)
  
  return canvas.toDataURL()
}

// Preload critical images
export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new window.Image()
    img.onload = () => resolve()
    img.onerror = reject
    img.src = src
  })
}

// Batch preload multiple images
export async function preloadImages(sources: string[]): Promise<void[]> {
  return Promise.all(sources.map(preloadImage))
}

// Image optimization utilities
export const ImageOptimization = {
  // Get responsive sizes string
  getResponsiveSizes: (breakpoints: { [key: string]: number }) => {
    return Object.entries(breakpoints)
      .map(([breakpoint, width]) => `(max-width: ${breakpoint}) ${width}px`)
      .join(', ')
  },

  // Get optimal quality based on image type
  getOptimalQuality: (imageType: 'photo' | 'graphic' | 'icon') => {
    switch (imageType) {
      case 'photo': return 80
      case 'graphic': return 90
      case 'icon': return 95
      default: return 75
    }
  },

  // Generate srcSet for different densities
  generateSrcSet: (baseSrc: string, densities: number[] = [1, 2, 3]) => {
    return densities
      .map(density => `${baseSrc}?w=${Math.round(density * 100)} ${density}x`)
      .join(', ')
  }
}

// Common responsive breakpoints
export const ResponsiveBreakpoints = {
  mobile: '640px',
  tablet: '768px',
  laptop: '1024px',
  desktop: '1280px'
}

// Common image sizes for the platform
export const CommonImageSizes = {
  avatar: { width: 40, height: 40 },
  avatarLarge: { width: 80, height: 80 },
  thumbnail: { width: 150, height: 150 },
  card: { width: 300, height: 200 },
  hero: { width: 1200, height: 600 },
  background: { width: 1920, height: 1080 }
}

// Lazy loading intersection observer options
export const LazyLoadOptions = {
  rootMargin: '50px',
  threshold: 0.1
}

// Image format recommendations
export const ImageFormats = {
  // Use WebP for modern browsers, fallback to JPEG/PNG
  getOptimalFormat: (hasTransparency: boolean = false) => {
    const supportsWebP = typeof window !== 'undefined' && 
      window.document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp') === 0
    
    if (supportsWebP) return 'webp'
    return hasTransparency ? 'png' : 'jpeg'
  }
}
