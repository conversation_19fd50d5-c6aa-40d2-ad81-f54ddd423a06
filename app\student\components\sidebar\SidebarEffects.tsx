'use client'

import { motion } from 'framer-motion'
import { EvolutionTheme } from './types'

interface SidebarEffectsProps {
  evolutionTheme: EvolutionTheme
}

export function SidebarEffects({ evolutionTheme }: SidebarEffectsProps) {
  return (
    <>
      {/* Enhanced Quantum Aura Effect */}
      <motion.div
        className="absolute inset-0 pointer-events-none rounded-r-2xl"
        style={{
          background: `linear-gradient(135deg, ${evolutionTheme.primary}05, transparent, ${evolutionTheme.secondary}05)`,
          border: `1px solid ${evolutionTheme.primary}20`
        }}
        animate={{
          boxShadow: [
            `0 0 30px ${evolutionTheme.glow}, inset 0 0 30px ${evolutionTheme.primary}10`,
            `0 0 50px ${evolutionTheme.glow}, inset 0 0 50px ${evolutionTheme.primary}15`,
            `0 0 30px ${evolutionTheme.glow}, inset 0 0 30px ${evolutionTheme.primary}10`
          ]
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: 'easeInOut'
        }}
      />
    </>
  )
}
