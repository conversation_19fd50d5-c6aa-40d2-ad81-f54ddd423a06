/**
 * Adaptive Quality System for NanoHero Platform
 * Dynamically adjusts rendering quality based on device performance
 */

export type QualityLevel = 'ultra-low' | 'low' | 'medium' | 'high' | 'ultra-high'

export interface QualitySettings {
  // Rendering settings
  shadowMapSize: number
  antialias: boolean
  pixelRatio: number
  
  // 3D Scene settings
  particleCount: number
  maxAnimatedObjects: number
  lodDistance: number
  frustumCulling: boolean
  
  // Shader settings
  complexShaders: boolean
  shaderPrecision: 'lowp' | 'mediump' | 'highp'
  
  // Animation settings
  animationFPS: number
  reducedMotion: boolean
  
  // Memory settings
  textureSize: number
  geometryDetail: number
  
  // Performance thresholds
  targetFPS: number
  memoryLimit: number // MB
}

export const QUALITY_PRESETS: Record<QualityLevel, QualitySettings> = {
  'ultra-low': {
    shadowMapSize: 256,
    antialias: false,
    pixelRatio: 0.5,
    particleCount: 200,
    maxAnimatedObjects: 5,
    lodDistance: 8,
    frustumCulling: true,
    complexShaders: false,
    shaderPrecision: 'lowp',
    animationFPS: 20,
    reducedMotion: true,
    textureSize: 256,
    geometryDetail: 0.3,
    targetFPS: 20,
    memoryLimit: 50
  },
  'low': {
    shadowMapSize: 512,
    antialias: false,
    pixelRatio: 0.75,
    particleCount: 500,
    maxAnimatedObjects: 10,
    lodDistance: 12,
    frustumCulling: true,
    complexShaders: false,
    shaderPrecision: 'mediump',
    animationFPS: 30,
    reducedMotion: false,
    textureSize: 512,
    geometryDetail: 0.5,
    targetFPS: 30,
    memoryLimit: 100
  },
  'medium': {
    shadowMapSize: 1024,
    antialias: false,
    pixelRatio: 1.0,
    particleCount: 1000,
    maxAnimatedObjects: 20,
    lodDistance: 16,
    frustumCulling: true,
    complexShaders: true,
    shaderPrecision: 'mediump',
    animationFPS: 45,
    reducedMotion: false,
    textureSize: 1024,
    geometryDetail: 0.7,
    targetFPS: 45,
    memoryLimit: 200
  },
  'high': {
    shadowMapSize: 2048,
    antialias: true,
    pixelRatio: 1.0,
    particleCount: 2000,
    maxAnimatedObjects: 40,
    lodDistance: 20,
    frustumCulling: false,
    complexShaders: true,
    shaderPrecision: 'highp',
    animationFPS: 60,
    reducedMotion: false,
    textureSize: 2048,
    geometryDetail: 1.0,
    targetFPS: 60,
    memoryLimit: 400
  },
  'ultra-high': {
    shadowMapSize: 4096,
    antialias: true,
    pixelRatio: 1.5,
    particleCount: 4000,
    maxAnimatedObjects: 80,
    lodDistance: 32,
    frustumCulling: false,
    complexShaders: true,
    shaderPrecision: 'highp',
    animationFPS: 60,
    reducedMotion: false,
    textureSize: 4096,
    geometryDetail: 1.0,
    targetFPS: 60,
    memoryLimit: 800
  }
}

export interface DeviceCapabilities {
  gpu: 'integrated' | 'discrete' | 'high-end'
  memory: number // GB
  cores: number
  mobile: boolean
  webgl2: boolean
  maxTextureSize: number
}

export class AdaptiveQualityManager {
  private currentQuality: QualityLevel = 'medium'
  private performanceHistory: number[] = []
  private memoryHistory: number[] = []
  private deviceCapabilities: DeviceCapabilities
  private qualityChangeCallbacks: Array<(quality: QualityLevel, settings: QualitySettings) => void> = []
  
  private frameCount = 0
  private lastTime = performance.now()
  private lastQualityCheck = 0
  
  constructor() {
    this.deviceCapabilities = this.detectDeviceCapabilities()
    this.currentQuality = this.getInitialQuality()
  }

  private detectDeviceCapabilities(): DeviceCapabilities {
    // Check if we're in a browser environment
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      // Return default capabilities for server-side rendering
      return {
        gpu: 'integrated',
        memory: 4,
        cores: 4,
        mobile: false,
        webgl2: false,
        maxTextureSize: 2048
      }
    }

    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl2') || canvas.getContext('webgl')
    
    const capabilities: DeviceCapabilities = {
      gpu: 'integrated',
      memory: (navigator as any).deviceMemory || 4,
      cores: navigator.hardwareConcurrency || 4,
      mobile: /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
      webgl2: !!canvas.getContext('webgl2'),
      maxTextureSize: 2048
    }

    if (gl) {
      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info')
      if (debugInfo) {
        const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)
        
        // GPU classification based on renderer string
        if (renderer.includes('RTX') || renderer.includes('GTX 1060') || renderer.includes('RX 580')) {
          capabilities.gpu = 'high-end'
        } else if (renderer.includes('GTX') || renderer.includes('RX') || renderer.includes('Radeon')) {
          capabilities.gpu = 'discrete'
        }
      }
      
      capabilities.maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE)
    }

    return capabilities
  }

  private getInitialQuality(): QualityLevel {
    const { gpu, memory, mobile, cores } = this.deviceCapabilities
    
    if (mobile) {
      return memory >= 6 ? 'medium' : 'low'
    }
    
    if (gpu === 'high-end' && memory >= 8 && cores >= 8) {
      return 'ultra-high'
    } else if (gpu === 'discrete' && memory >= 6) {
      return 'high'
    } else if (memory >= 4 && cores >= 4) {
      return 'medium'
    } else {
      return 'low'
    }
  }

  public getCurrentQuality(): QualityLevel {
    return this.currentQuality
  }

  public getCurrentSettings(): QualitySettings {
    return QUALITY_PRESETS[this.currentQuality]
  }

  public updatePerformance(fps: number, memoryMB?: number) {
    this.frameCount++
    const currentTime = performance.now()
    
    // Update performance history
    this.performanceHistory.push(fps)
    if (this.performanceHistory.length > 60) { // Keep last 60 samples
      this.performanceHistory.shift()
    }
    
    // Update memory history
    if (memoryMB !== undefined) {
      this.memoryHistory.push(memoryMB)
      if (this.memoryHistory.length > 30) { // Keep last 30 samples
        this.memoryHistory.shift()
      }
    }
    
    // Check for quality adjustment every 3 seconds
    if (currentTime - this.lastQualityCheck > 3000) {
      this.evaluateQualityAdjustment()
      this.lastQualityCheck = currentTime
    }
  }

  private evaluateQualityAdjustment() {
    if (this.performanceHistory.length < 10) return
    
    const avgFPS = this.performanceHistory.reduce((a, b) => a + b, 0) / this.performanceHistory.length
    const avgMemory = this.memoryHistory.length > 0 
      ? this.memoryHistory.reduce((a, b) => a + b, 0) / this.memoryHistory.length 
      : 0
    
    const currentSettings = this.getCurrentSettings()
    const targetFPS = currentSettings.targetFPS
    const memoryLimit = currentSettings.memoryLimit
    
    let newQuality = this.currentQuality
    
    // Determine if we need to adjust quality
    if (avgFPS < targetFPS * 0.8) {
      // Performance is poor, reduce quality
      newQuality = this.getNextLowerQuality()
    } else if (avgFPS > targetFPS * 1.2 && avgMemory < memoryLimit * 0.7) {
      // Performance is good and memory usage is low, try higher quality
      newQuality = this.getNextHigherQuality()
    }
    
    // Memory pressure check
    if (avgMemory > memoryLimit * 1.2) {
      newQuality = this.getNextLowerQuality()
    }
    
    if (newQuality !== this.currentQuality) {
      this.setQuality(newQuality)
    }
  }

  private getNextLowerQuality(): QualityLevel {
    const levels: QualityLevel[] = ['ultra-high', 'high', 'medium', 'low', 'ultra-low']
    const currentIndex = levels.indexOf(this.currentQuality)
    return levels[Math.min(currentIndex + 1, levels.length - 1)]
  }

  private getNextHigherQuality(): QualityLevel {
    const levels: QualityLevel[] = ['ultra-low', 'low', 'medium', 'high', 'ultra-high']
    const currentIndex = levels.indexOf(this.currentQuality)
    return levels[Math.min(currentIndex + 1, levels.length - 1)]
  }

  public setQuality(quality: QualityLevel) {
    if (quality === this.currentQuality) return
    
    const oldQuality = this.currentQuality
    this.currentQuality = quality
    const settings = this.getCurrentSettings()
    
    console.log(`Quality adjusted: ${oldQuality} → ${quality}`)
    
    // Notify all callbacks
    this.qualityChangeCallbacks.forEach(callback => {
      callback(quality, settings)
    })
  }

  public onQualityChange(callback: (quality: QualityLevel, settings: QualitySettings) => void) {
    this.qualityChangeCallbacks.push(callback)
    
    // Return unsubscribe function
    return () => {
      const index = this.qualityChangeCallbacks.indexOf(callback)
      if (index > -1) {
        this.qualityChangeCallbacks.splice(index, 1)
      }
    }
  }

  public getDeviceCapabilities(): DeviceCapabilities {
    return { ...this.deviceCapabilities }
  }

  public getPerformanceStats() {
    const avgFPS = this.performanceHistory.length > 0
      ? this.performanceHistory.reduce((a, b) => a + b, 0) / this.performanceHistory.length
      : 0
    
    const avgMemory = this.memoryHistory.length > 0
      ? this.memoryHistory.reduce((a, b) => a + b, 0) / this.memoryHistory.length
      : 0
    
    return {
      currentQuality: this.currentQuality,
      averageFPS: Math.round(avgFPS),
      averageMemory: Math.round(avgMemory),
      deviceCapabilities: this.deviceCapabilities,
      performanceSamples: this.performanceHistory.length,
      memorySamples: this.memoryHistory.length
    }
  }

  public forceQuality(quality: QualityLevel) {
    this.setQuality(quality)
    // Clear history to prevent automatic adjustments for a while
    this.performanceHistory = []
    this.memoryHistory = []
  }
}

// Global instance
export const adaptiveQuality = new AdaptiveQualityManager()
