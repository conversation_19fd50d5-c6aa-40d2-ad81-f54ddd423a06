'use client'

import { motion } from 'framer-motion'
import { TrendingUp, TrendingDown, Minus, Activity, Target, Zap } from 'lucide-react'
import { Progress } from '@/components/ui/progress'
import { PerformanceIndicatorProps } from './types'

export default function PerformanceIndicator({
  metrics,
  className = '',
  size = 'md',
  variant = 'detailed'
}: PerformanceIndicatorProps) {
  const sizeClasses = {
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6'
  }

  const textSizes = {
    sm: { title: 'text-sm', value: 'text-base', label: 'text-xs' },
    md: { title: 'text-base', value: 'text-lg', label: 'text-sm' },
    lg: { title: 'text-lg', value: 'text-xl', label: 'text-base' }
  }

  const getTrendIcon = (value: number, threshold: number = 70) => {
    if (value >= threshold + 10) return <TrendingUp className="w-4 h-4 text-green-400" />
    if (value <= threshold - 10) return <TrendingDown className="w-4 h-4 text-red-400" />
    return <Minus className="w-4 h-4 text-yellow-400" />
  }

  const getPerformanceColor = (value: number) => {
    if (value >= 80) return 'text-green-400'
    if (value >= 60) return 'text-yellow-400'
    return 'text-red-400'
  }

  const performanceMetrics = [
    {
      label: 'Accuracy',
      value: metrics.accuracyRate,
      icon: <Target className="w-4 h-4" />,
      color: 'cyan'
    },
    {
      label: 'Completion',
      value: metrics.completionRate,
      icon: <Activity className="w-4 h-4" />,
      color: 'green'
    },
    {
      label: 'Engagement',
      value: metrics.engagementScore,
      icon: <Zap className="w-4 h-4" />,
      color: 'purple'
    }
  ]

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`bg-black/40 border border-purple-500/30 backdrop-blur-xl rounded-lg ${sizeClasses[size]} ${className}`}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Activity className="w-5 h-5 text-purple-400" />
          <span className={`font-medium text-white ${textSizes[size].title}`}>
            Performance
          </span>
        </div>
        <div className="flex items-center gap-1">
          {getTrendIcon(metrics.engagementScore)}
          <span className={`text-xs ${getPerformanceColor(metrics.engagementScore)}`}>
            {metrics.engagementScore}%
          </span>
        </div>
      </div>

      {variant === 'detailed' && (
        <div className="space-y-3">
          {performanceMetrics.map((metric, index) => (
            <motion.div
              key={metric.label}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="space-y-2"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className={`text-${metric.color}-400`}>
                    {metric.icon}
                  </span>
                  <span className={`text-white/80 ${textSizes[size].label}`}>
                    {metric.label}
                  </span>
                </div>
                <span className={`font-bold ${getPerformanceColor(metric.value)} ${textSizes[size].value}`}>
                  {metric.value}%
                </span>
              </div>
              <Progress 
                value={metric.value} 
                className="h-1"
              />
            </motion.div>
          ))}

          <div className="pt-2 border-t border-white/10">
            <div className="flex justify-between items-center">
              <span className={`text-white/60 ${textSizes[size].label}`}>
                Learning Velocity
              </span>
              <span className={`font-bold text-cyan-400 ${textSizes[size].value}`}>
                {metrics.learningVelocity}x
              </span>
            </div>
          </div>
        </div>
      )}

      {variant === 'compact' && (
        <div className="grid grid-cols-3 gap-2">
          {performanceMetrics.map((metric) => (
            <div key={metric.label} className="text-center">
              <div className={`text-${metric.color}-400 mb-1`}>
                {metric.icon}
              </div>
              <div className={`font-bold ${getPerformanceColor(metric.value)} text-sm`}>
                {metric.value}%
              </div>
              <div className="text-xs text-white/60">
                {metric.label}
              </div>
            </div>
          ))}
        </div>
      )}
    </motion.div>
  )
}
