'use client'

import { motion } from 'framer-motion'
import { Globe, Users } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { NodeManagementProps } from './types'

export default function ActiveNodes({ 
  nanoNodes, 
  selectedNode, 
  onNodeSelect 
}: NodeManagementProps) {
  const activeNodes = nanoNodes.filter(node => node.status === 'active')

  return (
    <Card className="bg-black/40 border-green-500/30 backdrop-blur-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-green-400">
          <Globe className="w-5 h-5" />
          Active Nodes
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {activeNodes.map((node, index) => (
            <motion.div
              key={node.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="p-3 rounded-lg bg-gray-800/30 border border-gray-700/50 hover:border-green-500/30 transition-colors cursor-pointer"
              onClick={() => onNodeSelect(node)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: node.color }}
                  />
                  <span className="text-white text-sm font-medium">{node.title}</span>
                </div>
                <div className="flex items-center gap-2 text-xs">
                  <Users className="w-3 h-3 text-gray-400" />
                  <span className="text-gray-400">{node.participants}</span>
                </div>
              </div>
              <div className="mt-2 text-xs text-gray-400">
                {node.description}
              </div>
              <div className="mt-2 flex justify-between text-xs">
                <span className="text-cyan-400">+{node.rewards.ce} CE</span>
                <span className="text-purple-400">+{node.rewards.qs} QS</span>
              </div>
            </motion.div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
