"use client"

import React, { useState } from "react"

// Force dynamic rendering for dashboard
export const dynamic = 'force-dynamic'
import { motion } from "framer-motion"
import { useDashboardStore } from "./store/dashboardStore"
import { AuthProvider } from "@/contexts/AuthContext"

// import { QuantumLinkPanel } from "@/components/chat/QuantumLinkPanel"
import Sidebar from "./components/Sidebar"
import TopNav from "./components/TopNav"
import { ClientOnlyWrapper, ClientOnlyLoadingFallback } from '@/components/ClientOnlyWrapper'

interface DashboardLayoutProps {
  children: React.ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  const { viewMode } = useDashboardStore()

  return (
    <ClientOnlyWrapper fallback={<ClientOnlyLoadingFallback />}>
      <AuthProvider>       
      <div className={`min-h-screen relative overflow-hidden transition-all duration-1000 ${
        viewMode === 'focus' ? 'bg-gradient-to-br from-space-dark via-neural-cyan/10 to-space-blue' :
        viewMode === 'assist' ? 'bg-gradient-to-br from-space-dark via-green-500/10 to-space-teal' :
        viewMode === 'syntropy' ? 'bg-gradient-to-br from-space-dark via-quantum-gold/10 to-space-blue' :
        'bg-gradient-to-br from-space-dark via-quantum-purple/8 to-space-blue'
      }`}>
     
      {/* Sidebar Navigation */}
      <div className="fixed left-0 top-0 z-40">
        <Sidebar onCollapseChange={setSidebarCollapsed} />
      </div>

      {/* Top Navigation */}
      <TopNav sidebarCollapsed={sidebarCollapsed} />

      </div> 
      {/* Main Content Area */}
      <div className={`flex-1 relative z-20 transition-all duration-500 ${
        sidebarCollapsed ? 'ml-16' : 'ml-64'
      }`}>
        {children}
      </div>    
      </AuthProvider>
    </ClientOnlyWrapper>
  )
}
