"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Rocket, Sparkles } from 'lucide-react'
import { interests } from '../data/constants'
import { SignupData } from '../hooks/useLandingPageState'

interface SignupModalProps {
  isOpen: boolean
  onClose: () => void
  signupData: SignupData
  setSignupData: (data: SignupData | ((prev: SignupData) => SignupData)) => void
  onSignup: () => void
  onToggleInterest: (interest: string) => void
}

export function SignupModal({
  isOpen,
  onClose,
  signupData,
  setSignupData,
  onSignup,
  onToggleInterest
}: SignupModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-black/95 border-gray-800 text-white max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3 text-2xl">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full flex items-center justify-center"
            >
              <Rocket className="w-5 h-5 text-white" />
            </motion.div>
            Join the NanoHero Adventure!
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Welcome Message */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center p-6 bg-gradient-to-r from-purple-500/10 to-cyan-500/10 rounded-xl border border-purple-500/20"
          >
            <Sparkles className="w-12 h-12 text-yellow-400 mx-auto mb-3" />
            <h3 className="text-xl font-bold mb-2">Welcome, Future Tech Hero!</h3>
            <p className="text-gray-400">
              You&apos;re about to join thousands of young learners building amazing things with code.
              Let&apos;s get you started on your epic journey!
            </p>
          </motion.div>

          {/* Basic Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-cyan-400">Tell us about yourself</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-gray-300">Your Name</Label>
                <Input
                  id="name"
                  value={signupData.name}
                  onChange={(e) => setSignupData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter your name"
                  className="bg-gray-900/50 border-gray-700 text-white"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="age" className="text-gray-300">Your Age</Label>
                <Input
                  id="age"
                  value={signupData.age}
                  onChange={(e) => setSignupData(prev => ({ ...prev, age: e.target.value }))}
                  placeholder="How old are you?"
                  className="bg-gray-900/50 border-gray-700 text-white"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="text-gray-300">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={signupData.email}
                onChange={(e) => setSignupData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
                className="bg-gray-900/50 border-gray-700 text-white"
              />
              <p className="text-xs text-gray-500">
                We&apos;ll use this to send you updates and login information
              </p>
            </div>
          </div>

          {/* Interests Selection */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-purple-400">What interests you most?</h4>
            <p className="text-gray-400 text-sm">
              Select all that apply - this helps us personalize your learning experience
            </p>
            
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {interests.map((interest) => (
                <motion.div
                  key={interest}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Badge
                    variant={signupData.interests.includes(interest) ? "default" : "outline"}
                    className={`cursor-pointer p-3 text-center w-full transition-all duration-200 ${
                      signupData.interests.includes(interest)
                        ? 'bg-gradient-to-r from-cyan-500 to-blue-500 text-white border-cyan-400'
                        : 'border-gray-600 text-gray-400 hover:border-cyan-400 hover:text-cyan-400'
                    }`}
                    onClick={() => onToggleInterest(interest)}
                  >
                    {interest}
                  </Badge>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Safety Notice */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="p-4 bg-green-500/10 rounded-lg border border-green-500/20"
          >
            <h5 className="text-green-400 font-semibold mb-2">🛡️ Safe Learning Environment</h5>
            <ul className="text-sm text-gray-400 space-y-1">
              <li>• COPPA compliant platform with robust privacy protection</li>
              <li>• All interactions are monitored and age-appropriate</li>
              <li>• Parents receive regular progress updates</li>
              <li>• Expert mentors guide your learning journey</li>
            </ul>
          </motion.div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="flex-1"
            >
              <Button
                onClick={onSignup}
                disabled={!signupData.name || !signupData.email || !signupData.age}
                className="w-full bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white py-3 font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Rocket className="w-5 h-5 mr-2" />
                Start My Adventure!
              </Button>
            </motion.div>
            
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                variant="outline"
                onClick={onClose}
                className="border-gray-600 text-gray-400 hover:bg-gray-800 py-3"
              >
                Maybe Later
              </Button>
            </motion.div>
          </div>

          {/* Additional Info */}
          <div className="text-center text-xs text-gray-500 pt-2">
            By signing up, you agree to our Terms of Service and Privacy Policy. 
            <br />
            Free to start • No credit card required • Cancel anytime
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
