'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import {
  User,
  Bell,
  Shield,
  Palette,
  Globe,
  Save,
  Mail
} from 'lucide-react'

export default function SettingsPage() {
  const [settings, setSettings] = useState({
    notifications: {
      email: true,
      push: true,
      weekly: true,
      achievements: true
    },
    privacy: {
      dataSharing: false,
      analytics: true,
      marketing: false
    },
    preferences: {
      theme: 'dark',
      language: 'en',
      timezone: 'PST'
    }
  })

  const [profile, setProfile] = useState({
    name: 'Parent User',
    email: '<EMAIL>',
    phone: '+****************'
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-white font-space-grotesk">
            Settings
          </h1>
          <p className="text-gray-400 mt-1">
            Manage your account and preferences
          </p>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Profile Settings */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                <User className="w-5 h-5 text-blue-400" />
                Profile Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-gray-400 text-sm">Full Name</Label>
                <Input
                  value={profile.name}
                  onChange={(e) => setProfile({...profile, name: e.target.value})}
                  className="mt-1"
                />
              </div>
              <div>
                <Label className="text-gray-400 text-sm">Email</Label>
                <Input
                  type="email"
                  value={profile.email}
                  onChange={(e) => setProfile({...profile, email: e.target.value})}
                  className="mt-1"
                />
              </div>
              <div>
                <Label className="text-gray-400 text-sm">Phone</Label>
                <Input
                  value={profile.phone}
                  onChange={(e) => setProfile({...profile, phone: e.target.value})}
                  className="mt-1"
                />
              </div>
              <Button className="w-full flex items-center gap-2">
                <Save className="w-4 h-4" />
                Save Profile
              </Button>
            </CardContent>
          </Card>
        </motion.div>

        {/* Notification Settings */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                <Bell className="w-5 h-5 text-green-400" />
                Notifications
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {[
                { key: 'email', label: 'Email Notifications', icon: Mail },
                { key: 'push', label: 'Push Notifications', icon: Bell },
                { key: 'weekly', label: 'Weekly Reports', icon: Globe },
                { key: 'achievements', label: 'Achievement Alerts', icon: Shield }
              ].map((item) => (
                <div key={item.key} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <item.icon className="w-4 h-4 text-gray-400" />
                    <Label className="text-white">{item.label}</Label>
                  </div>
                  <Switch
                    checked={settings.notifications[item.key as keyof typeof settings.notifications]}
                    onCheckedChange={(checked) => 
                      setSettings(prev => ({
                        ...prev,
                        notifications: { ...prev.notifications, [item.key]: checked }
                      }))
                    }
                  />
                </div>
              ))}
            </CardContent>
          </Card>
        </motion.div>

        {/* Privacy Settings */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                <Shield className="w-5 h-5 text-purple-400" />
                Privacy & Data
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {[
                { key: 'dataSharing', label: 'Data Sharing', description: 'Share anonymized data for research' },
                { key: 'analytics', label: 'Usage Analytics', description: 'Help improve the platform' },
                { key: 'marketing', label: 'Marketing Communications', description: 'Receive product updates' }
              ].map((item) => (
                <div key={item.key} className="flex items-center justify-between">
                  <div>
                    <Label className="text-white">{item.label}</Label>
                    <p className="text-gray-400 text-xs">{item.description}</p>
                  </div>
                  <Switch
                    checked={settings.privacy[item.key as keyof typeof settings.privacy]}
                    onCheckedChange={(checked) => 
                      setSettings(prev => ({
                        ...prev,
                        privacy: { ...prev.privacy, [item.key]: checked }
                      }))
                    }
                  />
                </div>
              ))}
            </CardContent>
          </Card>
        </motion.div>

        {/* Preferences */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                <Palette className="w-5 h-5 text-yellow-400" />
                Preferences
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-gray-400 text-sm">Theme</Label>
                <div className="mt-2 text-white">Dark Mode (Default)</div>
              </div>
              <div>
                <Label className="text-gray-400 text-sm">Language</Label>
                <div className="mt-2 text-white">English (US)</div>
              </div>
              <div>
                <Label className="text-gray-400 text-sm">Timezone</Label>
                <div className="mt-2 text-white">Pacific Standard Time</div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
