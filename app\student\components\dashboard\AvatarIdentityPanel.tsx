'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  User, 
  Star, 
  Trophy, 
  Zap, 
  Crown, 
  Shield, 
  Sparkles,
  ChevronRight,
  Settings
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { MetricsComponentProps, EvolutionTheme } from './types'

interface AvatarIdentityPanelProps extends MetricsComponentProps {
  evolutionTheme: EvolutionTheme
}

interface AvatarGear {
  id: string
  name: string
  type: 'helmet' | 'suit' | 'gloves' | 'boots' | 'weapon'
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
  unlocked: boolean
  requiredLevel: number
  icon: string
}

interface StudentBadge {
  id: string
  name: string
  description: string
  icon: string
  earned: boolean
  earnedDate?: string
  rarity: 'bronze' | 'silver' | 'gold' | 'platinum'
}

export default function AvatarIdentityPanel({ 
  systemStatus, 
  playerData,
  evolutionTheme 
}: AvatarIdentityPanelProps) {
  const [showGearUpgrade, setShowGearUpgrade] = useState(false)
  const [selectedGear, setSelectedGear] = useState<AvatarGear | null>(null)

  // Mock student data - in real app this would come from props/store
  const studentData = {
    username: playerData?.username || 'NanoArchitect_42',
    level: playerData?.level || 7,
    xp: playerData?.xp || 2850,
    xpToNextLevel: 3200,
    rank: 'Quantum Explorer',
    totalXP: 15420,
    joinDate: '2024-01-15'
  }

  // Calculate progress to next level
  const progressToNext = ((studentData.xp / studentData.xpToNextLevel) * 100)
  const xpNeeded = studentData.xpToNextLevel - studentData.xp

  // Mock badges data
  const badges: StudentBadge[] = [
    {
      id: 'first-lesson',
      name: 'First Steps',
      description: 'Completed your first lesson',
      icon: '🎯',
      earned: true,
      earnedDate: '2024-01-15',
      rarity: 'bronze'
    },
    {
      id: 'helper',
      name: 'Peer Helper',
      description: 'Helped 5 fellow students',
      icon: '🤝',
      earned: true,
      earnedDate: '2024-02-01',
      rarity: 'silver'
    },
    {
      id: 'streak-master',
      name: 'Streak Master',
      description: '7-day learning streak',
      icon: '🔥',
      earned: true,
      earnedDate: '2024-02-10',
      rarity: 'gold'
    },
    {
      id: 'quantum-pioneer',
      name: 'Quantum Pioneer',
      description: 'Mastered quantum concepts',
      icon: '⚛️',
      earned: false,
      rarity: 'platinum'
    }
  ]

  // Mock avatar gear
  const avatarGear: AvatarGear[] = [
    {
      id: 'neural-helmet',
      name: 'Neural Interface Helmet',
      type: 'helmet',
      rarity: 'rare',
      unlocked: true,
      requiredLevel: 5,
      icon: '🪖'
    },
    {
      id: 'quantum-suit',
      name: 'Quantum Field Suit',
      type: 'suit',
      rarity: 'epic',
      unlocked: false,
      requiredLevel: 10,
      icon: '🦾'
    },
    {
      id: 'nano-gloves',
      name: 'Nano-Tech Gloves',
      type: 'gloves',
      rarity: 'common',
      unlocked: true,
      requiredLevel: 3,
      icon: '🧤'
    }
  ]

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'text-gray-400 border-gray-400/30'
      case 'rare': return 'text-blue-400 border-blue-400/30'
      case 'epic': return 'text-purple-400 border-purple-400/30'
      case 'legendary': return 'text-yellow-400 border-yellow-400/30'
      case 'bronze': return 'text-orange-400'
      case 'silver': return 'text-gray-300'
      case 'gold': return 'text-yellow-400'
      case 'platinum': return 'text-cyan-400'
      default: return 'text-gray-400'
    }
  }

  const earnedBadges = badges.filter(badge => badge.earned)

  return (
    <Card className="bg-black/40 border-cyan-500/30 backdrop-blur-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-cyan-400">
          <User className="w-5 h-5" />
          Avatar & Identity
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Live Avatar Display */}
        <div className="relative">
          <div className="flex items-center gap-4">
            {/* 3D Avatar Placeholder - Would integrate Three.js here */}
            <div className="relative w-20 h-20 rounded-full bg-gradient-to-br from-cyan-500/20 to-purple-500/20 border-2 border-cyan-500/30 flex items-center justify-center overflow-hidden">
              <motion.div
                className="text-3xl"
                animate={{ 
                  rotateY: [0, 360],
                  scale: [1, 1.1, 1]
                }}
                transition={{ 
                  duration: 4, 
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                🧑‍🚀
              </motion.div>
              
              {/* Level indicator */}
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center text-xs font-bold text-black">
                {studentData.level}
              </div>
            </div>

            {/* Username & Rank */}
            <div className="flex-1">
              <h3 className="text-lg font-bold text-white">{studentData.username}</h3>
              <div className="flex items-center gap-2">
                <Crown className="w-4 h-4 text-yellow-400" />
                <span className="text-yellow-400 font-medium">{studentData.rank}</span>
              </div>
              <div className="text-sm text-white/60">
                Level {studentData.level} • {studentData.totalXP.toLocaleString()} Total XP
              </div>
            </div>

            {/* Settings */}
            <Button 
              size="sm" 
              variant="outline" 
              className="border-gray-600/30 text-gray-400 hover:border-cyan-500/30 hover:text-cyan-400"
              onClick={() => setShowGearUpgrade(!showGearUpgrade)}
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Next Milestone Progress */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-white/80 font-medium">Next Level Progress</span>
            <span className="text-cyan-400 font-bold">
              {studentData.xp.toLocaleString()} / {studentData.xpToNextLevel.toLocaleString()} XP
            </span>
          </div>
          
          <div className="relative">
            <Progress 
              value={progressToNext} 
              className="h-3 bg-gray-700"
            />
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-full"
              animate={{ x: [-100, 200] }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            />
          </div>
          
          <div className="flex justify-between text-xs">
            <span className="text-green-400">
              {Math.round(progressToNext)}% Complete
            </span>
            <span className="text-white/60">
              {xpNeeded.toLocaleString()} XP needed
            </span>
          </div>
        </div>

        {/* Badges Earned */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-white font-medium flex items-center gap-2">
              <Trophy className="w-4 h-4 text-yellow-400" />
              Badges ({earnedBadges.length}/{badges.length})
            </h4>
            <ChevronRight className="w-4 h-4 text-gray-400" />
          </div>
          
          <div className="grid grid-cols-4 gap-2">
            {badges.slice(0, 4).map((badge) => (
              <motion.div
                key={badge.id}
                className={`relative p-2 rounded-lg border text-center ${
                  badge.earned 
                    ? `bg-${badge.rarity === 'gold' ? 'yellow' : badge.rarity === 'silver' ? 'gray' : badge.rarity === 'platinum' ? 'cyan' : 'orange'}-500/10 border-${badge.rarity === 'gold' ? 'yellow' : badge.rarity === 'silver' ? 'gray' : badge.rarity === 'platinum' ? 'cyan' : 'orange'}-500/30` 
                    : 'bg-gray-800/30 border-gray-700/50 opacity-50'
                }`}
                whileHover={{ scale: badge.earned ? 1.05 : 1 }}
                title={badge.description}
              >
                <div className="text-lg">{badge.icon}</div>
                <div className="text-xs text-white/80 mt-1 truncate">
                  {badge.name}
                </div>
                {badge.earned && (
                  <motion.div
                    className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2 }}
                  />
                )}
              </motion.div>
            ))}
          </div>
        </div>

        {/* Avatar Gear Upgrade Panel */}
        <AnimatePresence>
          {showGearUpgrade && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="space-y-3 border-t border-white/10 pt-4"
            >
              <h4 className="text-white font-medium flex items-center gap-2">
                <Shield className="w-4 h-4 text-purple-400" />
                Avatar Gear
              </h4>
              
              <div className="grid grid-cols-3 gap-2">
                {avatarGear.map((gear) => (
                  <motion.div
                    key={gear.id}
                    className={`p-2 rounded-lg border text-center cursor-pointer ${
                      gear.unlocked 
                        ? `${getRarityColor(gear.rarity)} bg-gray-800/30 hover:bg-gray-700/30` 
                        : 'bg-gray-900/50 border-gray-700/30 opacity-50'
                    }`}
                    whileHover={{ scale: gear.unlocked ? 1.05 : 1 }}
                    onClick={() => gear.unlocked && setSelectedGear(gear)}
                  >
                    <div className="text-lg">{gear.icon}</div>
                    <div className="text-xs text-white/80 mt-1 truncate">
                      {gear.name}
                    </div>
                    {!gear.unlocked && (
                      <div className="text-xs text-red-400 mt-1">
                        Lvl {gear.requiredLevel}
                      </div>
                    )}
                  </motion.div>
                ))}
              </div>
              
              <div className="text-center">
                <Button 
                  size="sm" 
                  className="bg-purple-500/20 text-purple-400 border border-purple-500/30 hover:bg-purple-500/30"
                >
                  <Sparkles className="w-4 h-4 mr-2" />
                  Upgrade Avatar
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>
    </Card>
  )
}
