'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Bar<PERSON>hart3,
  <PERSON>,
  Brain,
  Heart,
  Star,
  Activity
} from 'lucide-react'

export default function AnalyticsPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-white font-space-grotesk">
            Analytics Dashboard
          </h1>
          <p className="text-gray-400 mt-1">
            Detailed insights into your child&apos;s learning progress
          </p>
        </div>
      </motion.div>

      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          {
            title: 'Learning Time',
            value: '24.5 hrs',
            change: '+12%',
            icon: Clock,
            color: 'text-blue-400'
          },
          {
            title: 'Skill Progress',
            value: '85%',
            change: '+8%',
            icon: Brain,
            color: 'text-green-400'
          },
          {
            title: 'Achievements',
            value: '12',
            change: '+3',
            icon: Star,
            color: 'text-yellow-400'
          },
          {
            title: 'Engagement',
            value: '92%',
            change: '+5%',
            icon: Heart,
            color: 'text-red-400'
          }
        ].map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-400 text-sm">{stat.title}</p>
                    <p className="text-2xl font-bold text-white">{stat.value}</p>
                    <Badge variant="outline" className="mt-2 text-green-400 border-green-500/30">
                      {stat.change}
                    </Badge>
                  </div>
                  <stat.icon className={`w-8 h-8 ${stat.color}`} />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Detailed Analytics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardHeader>
            <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-blue-400" />
              Detailed Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <Activity className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">
                Advanced Analytics Coming Soon
              </h3>
              <p className="text-gray-400">
                Detailed charts and insights will be available in the next update.
              </p>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
