'use client'

import { motion } from 'framer-motion'
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Heart, Setting<PERSON>, <PERSON>, Zap } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { usePlayerData, useSystemStatus } from '../store/dashboardStore'
import { cn } from '@/lib/utils'

interface UserProfileProps {
  className?: string
  variant?: 'compact' | 'detailed'
  showQuickActions?: boolean
  onLibraryClick?: () => void
  onSyntropyClick?: () => void
}

export default function UserProfile({ 
  className, 
  variant = 'detailed',
  showQuickActions = true,
  onLibraryClick,
  onSyntropyClick
}: UserProfileProps) {
  const playerData = usePlayerData()
  const systemStatus = useSystemStatus()

  if (variant === 'compact') {
    return (
      <motion.div
        className={cn(
          'flex items-center gap-3 p-3 rounded-xl bg-gradient-to-r from-space-blue/20 to-quantum-purple/10 border border-neural-cyan/20',
          className
        )}
        whileHover={{ scale: 1.02 }}
        transition={{ duration: 0.2 }}
      >
        {/* Compact Avatar */}
        <div className="relative">
          <div className="w-12 h-12 bg-gradient-to-br from-neural-cyan via-quantum-purple to-flame-orange rounded-full flex items-center justify-center shadow-lg border border-neural-cyan/30">
            <User className="w-6 h-6 text-white" />
          </div>
          <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-gradient-to-br from-flame-orange to-flame-red rounded-full flex items-center justify-center border border-space-dark">
            <Crown className="w-2 h-2 text-white" />
          </div>
          <div className="absolute -top-1 -left-1 w-3 h-3 bg-green-400 rounded-full border border-space-dark animate-pulse" />
        </div>
        
        {/* Compact Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <h4 className="text-sm font-orbitron font-bold text-white truncate">{playerData?.username || 'NanoArchitect'}</h4>
            <div className="px-2 py-0.5 bg-neural-cyan/20 rounded-full">
              <span className="text-xs font-medium text-neural-cyan">L{playerData?.level || 1}</span>
            </div>
          </div>
          <div className="flex gap-2 mt-1">
            <span className="text-xs text-neural-cyan font-medium">{Math.round((systemStatus?.consciousnessLevel || 0) * 100)}% CE</span>
            <span className="text-xs text-quantum-purple font-medium">{Math.round((systemStatus?.systemHealth || 0) * 100)}% QS</span>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      className={cn(
        'h-full flex items-center gap-4 p-4 bg-gradient-to-r from-space-blue/10 to-quantum-purple/5 rounded-xl border border-neural-cyan/20',
        className
      )}
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* User Avatar */}
      <motion.div 
        className="relative"
        whileHover={{ scale: 1.05 }}
        transition={{ duration: 0.2 }}
      >
        <div className="w-16 h-16 bg-gradient-to-br from-neural-cyan via-quantum-purple to-flame-orange rounded-full flex items-center justify-center shadow-xl border-2 border-neural-cyan/30 relative overflow-hidden">
          <User className="w-8 h-8 text-white relative z-10" />
          {/* Animated glow effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-neural-cyan/20 via-quantum-purple/20 to-flame-orange/20 animate-pulse" />
        </div>
        
        {/* Evolution Stage Badge */}
        <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-gradient-to-br from-flame-orange to-flame-red rounded-full flex items-center justify-center border-2 border-space-dark shadow-lg">
          <Crown className="w-3 h-3 text-white" />
        </div>
        
        {/* Online Status */}
        <div className="absolute -top-1 -left-1 w-4 h-4 bg-green-400 rounded-full border-2 border-space-dark animate-pulse shadow-lg" />
        
        {/* Quantum Energy Ring */}
        <div className="absolute inset-0 rounded-full border-2 border-neural-cyan/30 animate-spin" style={{ animationDuration: '8s' }} />

        {/* Pulsing Quantum Field */}
        <div className="absolute inset-0 rounded-full border border-quantum-purple/20 animate-ping" style={{ animationDuration: '3s' }} />
      </motion.div>
      
      {/* User Info */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <h4 className="text-lg font-orbitron font-bold text-white truncate">{playerData?.username || 'NanoArchitect'}</h4>
          <motion.div
            className="px-2 py-1 bg-neural-cyan/20 rounded-full border border-neural-cyan/30 relative overflow-hidden"
            whileHover={{ scale: 1.05, backgroundColor: 'rgba(34, 211, 238, 0.3)' }}
          >
            <span className="text-xs font-medium text-neural-cyan relative z-10">Level {playerData?.level || 1}</span>
            {/* Progress bar for next evolution */}
            <div className="absolute bottom-0 left-0 h-0.5 bg-neural-cyan/50 transition-all duration-1000"
                 style={{ width: `${((playerData?.xp || 0) / 3000) * 100}%` }} />
          </motion.div>
        </div>
        
        {/* DNA Fragment */}
        <div className="text-xs text-white/60 mb-2 font-mono tracking-wider bg-space-blue/20 px-2 py-1 rounded border border-white/10">
          {playerData?.id?.slice(0, 12).toUpperCase() || 'NANO-GENESIS'}
        </div>

        {/* Achievement Badges */}
        <div className="flex gap-1 mb-2">
          {(playerData?.achievements || []).slice(0, 3).map((achievement, index) => (
            <motion.div
              key={achievement}
              className="px-2 py-1 bg-flame-orange/20 rounded-full border border-flame-orange/30"
              whileHover={{ scale: 1.1, backgroundColor: 'rgba(251, 146, 60, 0.3)' }}
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1 }}
            >
              <span className="text-xs text-flame-orange font-medium">{achievement}</span>
            </motion.div>
          ))}
          {(playerData?.achievements || []).length > 3 && (
            <div className="px-2 py-1 bg-white/10 rounded-full border border-white/20">
              <span className="text-xs text-white/60">+{(playerData?.achievements || []).length - 3}</span>
            </div>
          )}
        </div>
        
        {/* Stats */}
        <div className="flex gap-4">
          <motion.div 
            className="text-center"
            whileHover={{ scale: 1.1 }}
          >
            <div className="text-sm font-bold text-neural-cyan flex items-center gap-1">
              <Zap className="w-3 h-3" />
              {Math.round((systemStatus?.consciousnessLevel || 0) * 100)}%
            </div>
            <div className="text-xs text-white/50">Consciousness</div>
          </motion.div>
          
          <motion.div 
            className="text-center"
            whileHover={{ scale: 1.1 }}
          >
            <div className="text-sm font-bold text-quantum-purple flex items-center gap-1">
              <div className="w-3 h-3 bg-quantum-purple rounded-full animate-pulse" />
              {Math.round((systemStatus?.systemHealth || 0) * 100)}%
            </div>
            <div className="text-xs text-white/50">Quantum</div>
          </motion.div>
          
          <motion.div 
            className="text-center"
            whileHover={{ scale: 1.1 }}
          >
            <div className="text-sm font-bold text-flame-orange flex items-center gap-1">
              <Trophy className="w-3 h-3" />
              {playerData?.stats.learningStreak || 0}
            </div>
            <div className="text-xs text-white/50">Streak</div>
          </motion.div>
        </div>
      </div>
      
      {/* Quick Actions */}
      {showQuickActions && (
        <div className="flex flex-col gap-2">
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button
              size="sm"
              variant="quantum"
              onClick={onLibraryClick}
              className="px-3 shadow-lg"
              glow
            >
              <BookOpen className="w-4 h-4" />
            </Button>
          </motion.div>
          
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button
              size="sm"
              variant="quantum"
              onClick={onSyntropyClick}
              className="px-3 shadow-lg"
              glow
            >
              <Heart className="w-4 h-4" />
            </Button>
          </motion.div>
          
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button
              size="sm"
              variant="ghost"
              className="px-3 border border-white/20 hover:border-neural-cyan/50"
            >
              <Settings className="w-4 h-4" />
            </Button>
          </motion.div>
        </div>
      )}
    </motion.div>
  )
}
