"use client"

import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"

import { cn } from "@/lib/utils"

interface ProgressProps extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> {
  variant?: 'default' | 'quantum' | 'gaming'
  glow?: boolean
}

const Progress = React.forwardRef<
  React.ComponentRef<typeof ProgressPrimitive.Root>,
  ProgressProps
>(({ className, value, variant = 'default', glow = false, ...props }, ref) => {
  const variantClasses = {
    default: 'bg-white/10 border border-white/20',
    quantum: 'bg-black/40 border border-neural-cyan/30',
    gaming: 'bg-black/40 border border-quantum-purple/30'
  }

  const indicatorClasses = {
    default: 'bg-gradient-to-r from-neural-cyan to-quantum-purple',
    quantum: 'bg-gradient-to-r from-neural-cyan to-quantum-purple',
    gaming: 'bg-gradient-to-r from-quantum-purple to-quantum-gold'
  }

  const glowClasses = glow ? 'shadow-lg shadow-cyan-400/25' : ''

  return (
    <ProgressPrimitive.Root
      ref={ref}
      className={cn(
        "relative h-3 w-full overflow-hidden rounded-full",
        variantClasses[variant],
        glowClasses,
        className
      )}
      {...props}
    >
      <ProgressPrimitive.Indicator
        className={cn(
          "h-full w-full flex-1 transition-all duration-500 ease-out rounded-full",
          indicatorClasses[variant]
        )}
        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
      />
    </ProgressPrimitive.Root>
  )
})
Progress.displayName = ProgressPrimitive.Root.displayName

export { Progress }
