import { PresetPhrase } from '@/types/chat'

export const PRESET_PHRASES: PresetPhrase[] = [
  // Greetings
  {
    id: 'greeting_1',
    text: 'Hello, fellow NanoArchitect! 👋',
    category: 'greeting',
    emoji: '👋',
    ageGroup: 'all',
    cubitsRequired: 0,
    unlockLevel: 1
  },
  {
    id: 'greeting_2',
    text: 'Welcome to our quantum timeline! ✨',
    category: 'greeting',
    emoji: '✨',
    ageGroup: 'all',
    cubitsRequired: 1,
    unlockLevel: 1
  },
  {
    id: 'greeting_3',
    text: 'Ready to explore the NanoVerse together? 🚀',
    category: 'greeting',
    emoji: '🚀',
    ageGroup: 'all',
    cubitsRequired: 2,
    unlockLevel: 2
  },

  // Help & Support
  {
    id: 'help_1',
    text: 'I can help you with that! What do you need? 🤝',
    category: 'help',
    emoji: '🤝',
    ageGroup: 'all',
    cubitsRequired: 0,
    unlockLevel: 1
  },
  {
    id: 'help_2',
    text: 'Let me share what I learned about this... 📚',
    category: 'help',
    emoji: '📚',
    ageGroup: 'all',
    cubitsRequired: 1,
    unlockLevel: 2
  },
  {
    id: 'help_3',
    text: 'We can solve this puzzle together! 🧩',
    category: 'help',
    emoji: '🧩',
    ageGroup: 'all',
    cubitsRequired: 2,
    unlockLevel: 3
  },
  {
    id: 'help_4',
    text: 'I had the same question! Here\'s what worked for me... 💡',
    category: 'help',
    emoji: '💡',
    ageGroup: '10-13',
    cubitsRequired: 3,
    unlockLevel: 4
  },

  // Thanks & Appreciation
  {
    id: 'thanks_1',
    text: 'Thank you so much! That really helped! 🙏',
    category: 'thanks',
    emoji: '🙏',
    ageGroup: 'all',
    cubitsRequired: 0,
    unlockLevel: 1
  },
  {
    id: 'thanks_2',
    text: 'You\'re amazing! Thanks for explaining that! ⭐',
    category: 'thanks',
    emoji: '⭐',
    ageGroup: 'all',
    cubitsRequired: 1,
    unlockLevel: 1
  },
  {
    id: 'thanks_3',
    text: 'Your help boosted our quantum harmony! 🌟',
    category: 'thanks',
    emoji: '🌟',
    ageGroup: 'all',
    cubitsRequired: 2,
    unlockLevel: 3
  },

  // Encouragement
  {
    id: 'encourage_1',
    text: 'You\'ve got this! Keep going! 💪',
    category: 'encouragement',
    emoji: '💪',
    ageGroup: 'all',
    cubitsRequired: 0,
    unlockLevel: 1
  },
  {
    id: 'encourage_2',
    text: 'That\'s a brilliant idea! 🌟',
    category: 'encouragement',
    emoji: '🌟',
    ageGroup: 'all',
    cubitsRequired: 1,
    unlockLevel: 2
  },
  {
    id: 'encourage_3',
    text: 'Your creativity is inspiring the whole timeline! ✨',
    category: 'encouragement',
    emoji: '✨',
    ageGroup: 'all',
    cubitsRequired: 3,
    unlockLevel: 4
  },
  {
    id: 'encourage_4',
    text: 'Don\'t give up! Every NanoArchitect learns at their own pace 🌱',
    category: 'encouragement',
    emoji: '🌱',
    ageGroup: 'all',
    cubitsRequired: 2,
    unlockLevel: 3
  },

  // Questions
  {
    id: 'question_1',
    text: 'Can someone help me understand this? 🤔',
    category: 'question',
    emoji: '🤔',
    ageGroup: 'all',
    cubitsRequired: 0,
    unlockLevel: 1
  },
  {
    id: 'question_2',
    text: 'What\'s your favorite learning strategy? 📖',
    category: 'question',
    emoji: '📖',
    ageGroup: 'all',
    cubitsRequired: 1,
    unlockLevel: 2
  },
  {
    id: 'question_3',
    text: 'Has anyone tried this approach before? 🔍',
    category: 'question',
    emoji: '🔍',
    ageGroup: '10-13',
    cubitsRequired: 2,
    unlockLevel: 3
  },
  {
    id: 'question_4',
    text: 'What would happen if we combined our ideas? 🤝',
    category: 'question',
    emoji: '🤝',
    ageGroup: '10-13',
    cubitsRequired: 3,
    unlockLevel: 4
  },

  // Celebration
  {
    id: 'celebrate_1',
    text: 'Awesome work, everyone! 🎉',
    category: 'celebration',
    emoji: '🎉',
    ageGroup: 'all',
    cubitsRequired: 0,
    unlockLevel: 1
  },
  {
    id: 'celebrate_2',
    text: 'We did it! Great teamwork! 🏆',
    category: 'celebration',
    emoji: '🏆',
    ageGroup: 'all',
    cubitsRequired: 1,
    unlockLevel: 2
  },
  {
    id: 'celebrate_3',
    text: 'Our quantum harmony is off the charts! 📈✨',
    category: 'celebration',
    emoji: '📈',
    ageGroup: 'all',
    cubitsRequired: 3,
    unlockLevel: 4
  },
  {
    id: 'celebrate_4',
    text: 'This timeline is becoming legendary! 🌟🚀',
    category: 'celebration',
    emoji: '🌟',
    ageGroup: 'all',
    cubitsRequired: 5,
    unlockLevel: 5
  },

  // Age-specific phrases for younger users (6-9)
  {
    id: 'young_1',
    text: 'That\'s so cool! 😄',
    category: 'encouragement',
    emoji: '😄',
    ageGroup: '6-9',
    cubitsRequired: 0,
    unlockLevel: 1
  },
  {
    id: 'young_2',
    text: 'Can we be learning buddies? 👫',
    category: 'question',
    emoji: '👫',
    ageGroup: '6-9',
    cubitsRequired: 1,
    unlockLevel: 1
  },
  {
    id: 'young_3',
    text: 'I learned something new today! 🌈',
    category: 'celebration',
    emoji: '🌈',
    ageGroup: '6-9',
    cubitsRequired: 1,
    unlockLevel: 2
  },

  // Advanced phrases for older users (14-17)
  {
    id: 'advanced_1',
    text: 'Let\'s approach this systematically and break it down step by step 🔬',
    category: 'help',
    emoji: '🔬',
    ageGroup: '14-17',
    cubitsRequired: 5,
    unlockLevel: 6
  },
  {
    id: 'advanced_2',
    text: 'I appreciate your perspective - it made me think differently 🧠',
    category: 'thanks',
    emoji: '🧠',
    ageGroup: '14-17',
    cubitsRequired: 4,
    unlockLevel: 5
  },
  {
    id: 'advanced_3',
    text: 'What if we collaborated on a project to explore this further? 🔬🤝',
    category: 'question',
    emoji: '🔬',
    ageGroup: '14-17',
    cubitsRequired: 6,
    unlockLevel: 7
  }
]

export const PHRASE_CATEGORIES = [
  { id: 'greeting', name: 'Greetings', icon: '👋', color: 'cyan' },
  { id: 'help', name: 'Help & Support', icon: '🤝', color: 'green' },
  { id: 'thanks', name: 'Thanks', icon: '🙏', color: 'purple' },
  { id: 'encouragement', name: 'Encouragement', icon: '💪', color: 'yellow' },
  { id: 'question', name: 'Questions', icon: '🤔', color: 'blue' },
  { id: 'celebration', name: 'Celebration', icon: '🎉', color: 'pink' }
]

export function getPhrasesForUser(userLevel: number, ageGroup: string, cubits: number): PresetPhrase[] {
  return PRESET_PHRASES.filter(phrase => 
    phrase.unlockLevel <= userLevel &&
    (phrase.ageGroup === 'all' || phrase.ageGroup === ageGroup) &&
    phrase.cubitsRequired <= cubits
  )
}

export function getPhrasesByCategory(category: string, userLevel: number, ageGroup: string, cubits: number): PresetPhrase[] {
  return getPhrasesForUser(userLevel, ageGroup, cubits).filter(phrase => phrase.category === category)
}
