// Comprehensive Lesson Library for Parent Curriculum Planner
import { Lesson, DifficultyLevel, LessonType } from '../types/curriculum'

// Subject Categories
export const SUBJECTS = {
  QUANTUM_PHYSICS: 'quantum-physics',
  DNA_BIOLOGY: 'dna-biology',
  NANOTECHNOLOGY: 'nanotechnology',
  SPACE_SCIENCE: 'space-science',
  CYBERSECURITY: 'cybersecurity',
  CRITICAL_THINKING: 'critical-thinking',
  EMOTIONAL_INTELLIGENCE: 'emotional-intelligence',
  DIGITAL_LITERACY: 'digital-literacy',
  COMMUNICATION: 'communication',
  PROBLEM_SOLVING: 'problem-solving',
  CREATIVITY: 'creativity',
  LIFE_SKILLS: 'life-skills'
} as const

// Learning Categories
export const CATEGORIES = {
  CORE_CONCEPTS: 'core-concepts',
  PRACTICAL_APPLICATION: 'practical-application',
  HANDS_ON_LAB: 'hands-on-lab',
  CREATIVE_PROJECT: 'creative-project',
  ASSESSMENT: 'assessment',
  COLLABORATION: 'collaboration',
  REFLECTION: 'reflection',
  RESEARCH: 'research'
} as const

// Skill Tags
export const SKILL_TAGS = [
  'analytical-thinking',
  'pattern-recognition',
  'hypothesis-formation',
  'data-analysis',
  'scientific-method',
  'creative-problem-solving',
  'collaboration',
  'communication',
  'empathy',
  'self-awareness',
  'digital-citizenship',
  'online-safety',
  'coding-basics',
  'logical-reasoning',
  'systems-thinking',
  'innovation',
  'adaptability',
  'leadership',
  'time-management',
  'goal-setting'
] as const

// Sample Lesson Library
export const LESSON_LIBRARY: Lesson[] = [
  // Quantum Physics Lessons
  {
    id: 'qp-001',
    title: 'What is Quantum Physics?',
    description: 'An introduction to the fascinating world of quantum mechanics for young minds',
    type: 'video' as LessonType,
    subject: SUBJECTS.QUANTUM_PHYSICS,
    category: CATEGORIES.CORE_CONCEPTS,
    tags: ['quantum-basics', 'wave-particle-duality', 'introduction'],
    difficulty: 'beginner' as DifficultyLevel,
    duration: 15,
    ageRange: [8, 12],
    prerequisites: [],
    learningObjectives: [
      'Understand what quantum physics studies',
      'Learn about wave-particle duality',
      'Recognize quantum effects in everyday life'
    ],
    skills: ['analytical-thinking', 'pattern-recognition'],
    content: {
      summary: 'Interactive introduction to quantum concepts using animations and simple experiments',
      materials: ['Virtual quantum simulator', 'Interactive animations', 'Quiz questions'],
      activities: ['Watch quantum animation', 'Virtual double-slit experiment', 'Concept mapping'],
      assessments: ['Multiple choice quiz', 'Draw what you learned', 'Explain to a friend']
    },
    metadata: {
      author: 'Dr. Quantum Explorer',
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15'),
      version: '1.0',
      gradeLevel: '3-6',
      standardsAlignment: ['NGSS-K-PS2-1', 'NGSS-5-PS1-1']
    }
  },
  {
    id: 'qp-002',
    title: 'Quantum Superposition Lab',
    description: 'Hands-on exploration of quantum superposition using interactive simulations',
    type: 'lab' as LessonType,
    subject: SUBJECTS.QUANTUM_PHYSICS,
    category: CATEGORIES.HANDS_ON_LAB,
    tags: ['superposition', 'quantum-states', 'simulation'],
    difficulty: 'intermediate' as DifficultyLevel,
    duration: 30,
    ageRange: [10, 14],
    prerequisites: ['qp-001'],
    learningObjectives: [
      'Understand quantum superposition',
      'Use quantum simulators',
      'Apply quantum concepts to solve problems'
    ],
    skills: ['scientific-method', 'data-analysis', 'hypothesis-formation'],
    content: {
      summary: 'Interactive lab using quantum simulators to explore superposition states',
      materials: ['Quantum circuit simulator', 'Lab worksheet', 'Measurement tools'],
      activities: ['Build quantum circuits', 'Measure quantum states', 'Analyze results'],
      assessments: ['Lab report', 'Circuit design challenge', 'Peer explanation']
    },
    metadata: {
      author: 'Prof. Quantum Lab',
      createdAt: new Date('2024-01-20'),
      updatedAt: new Date('2024-01-20'),
      version: '1.0',
      gradeLevel: '5-8',
      standardsAlignment: ['NGSS-MS-PS4-2']
    }
  },

  // DNA Biology Lessons
  {
    id: 'bio-001',
    title: 'DNA: The Code of Life',
    description: 'Discover how DNA stores and transmits genetic information',
    type: 'interactive' as LessonType,
    subject: SUBJECTS.DNA_BIOLOGY,
    category: CATEGORIES.CORE_CONCEPTS,
    tags: ['dna-structure', 'genetic-code', 'heredity'],
    difficulty: 'beginner' as DifficultyLevel,
    duration: 20,
    ageRange: [9, 13],
    prerequisites: [],
    learningObjectives: [
      'Understand DNA structure',
      'Learn about genetic information',
      'Connect DNA to traits'
    ],
    skills: ['pattern-recognition', 'systems-thinking'],
    content: {
      summary: '3D interactive exploration of DNA structure and function',
      materials: ['3D DNA model', 'Interactive timeline', 'Trait comparison tool'],
      activities: ['Build DNA model', 'Decode genetic messages', 'Family trait analysis'],
      assessments: ['DNA structure quiz', 'Trait prediction game', 'Create DNA story']
    },
    metadata: {
      author: 'Dr. Gene Explorer',
      createdAt: new Date('2024-01-18'),
      updatedAt: new Date('2024-01-18'),
      version: '1.0',
      gradeLevel: '4-7',
      standardsAlignment: ['NGSS-MS-LS3-1', 'NGSS-5-LS2-1']
    }
  },

  // Critical Thinking Lessons
  {
    id: 'ct-001',
    title: 'Logical Reasoning Fundamentals',
    description: 'Build strong logical thinking skills through puzzles and problems',
    type: 'interactive' as LessonType,
    subject: SUBJECTS.CRITICAL_THINKING,
    category: CATEGORIES.CORE_CONCEPTS,
    tags: ['logic', 'reasoning', 'problem-solving'],
    difficulty: 'beginner' as DifficultyLevel,
    duration: 25,
    ageRange: [8, 12],
    prerequisites: [],
    learningObjectives: [
      'Identify logical patterns',
      'Solve reasoning puzzles',
      'Apply logic to real situations'
    ],
    skills: ['logical-reasoning', 'analytical-thinking', 'creative-problem-solving'],
    content: {
      summary: 'Interactive puzzles and games to develop logical reasoning skills',
      materials: ['Logic puzzle games', 'Pattern recognition tools', 'Real-world scenarios'],
      activities: ['Solve logic puzzles', 'Pattern completion', 'Scenario analysis'],
      assessments: ['Puzzle completion', 'Logic explanation', 'Create your own puzzle']
    },
    metadata: {
      author: 'Logic Master',
      createdAt: new Date('2024-01-22'),
      updatedAt: new Date('2024-01-22'),
      version: '1.0',
      gradeLevel: '3-6',
      standardsAlignment: ['CCSS.MATH.PRACTICE.MP1', 'CCSS.MATH.PRACTICE.MP3']
    }
  },

  // Cybersecurity Lessons
  {
    id: 'cs-001',
    title: 'Digital Footprints and Online Safety',
    description: 'Learn how to protect yourself and your information online',
    type: 'interactive' as LessonType,
    subject: SUBJECTS.CYBERSECURITY,
    category: CATEGORIES.PRACTICAL_APPLICATION,
    tags: ['online-safety', 'digital-footprint', 'privacy'],
    difficulty: 'beginner' as DifficultyLevel,
    duration: 30,
    ageRange: [8, 14],
    prerequisites: [],
    learningObjectives: [
      'Understand digital footprints',
      'Learn privacy protection strategies',
      'Recognize online risks'
    ],
    skills: ['digital-citizenship', 'critical-thinking', 'self-awareness'],
    content: {
      summary: 'Interactive scenarios and tools to learn online safety practices',
      materials: ['Digital footprint tracker', 'Privacy settings simulator', 'Safety scenarios'],
      activities: ['Audit digital footprint', 'Practice privacy settings', 'Safety scenario responses'],
      assessments: ['Safety quiz', 'Privacy plan creation', 'Peer safety tips']
    },
    metadata: {
      author: 'Cyber Guardian',
      createdAt: new Date('2024-01-25'),
      updatedAt: new Date('2024-01-25'),
      version: '1.0',
      gradeLevel: '3-8',
      standardsAlignment: ['ISTE.1.1.d', 'ISTE.1.2.b']
    }
  },

  // Emotional Intelligence Lessons
  {
    id: 'ei-001',
    title: 'Understanding Emotions',
    description: 'Explore different emotions and learn to recognize them in yourself and others',
    type: 'interactive' as LessonType,
    subject: SUBJECTS.EMOTIONAL_INTELLIGENCE,
    category: CATEGORIES.CORE_CONCEPTS,
    tags: ['emotion-recognition', 'self-awareness', 'empathy'],
    difficulty: 'beginner' as DifficultyLevel,
    duration: 20,
    ageRange: [6, 12],
    prerequisites: [],
    learningObjectives: [
      'Identify different emotions',
      'Recognize emotional triggers',
      'Practice empathy skills'
    ],
    skills: ['self-awareness', 'empathy', 'communication'],
    content: {
      summary: 'Interactive emotion exploration with scenarios and reflection activities',
      materials: ['Emotion wheel', 'Scenario cards', 'Reflection journal'],
      activities: ['Emotion identification game', 'Scenario role-play', 'Emotion journaling'],
      assessments: ['Emotion matching', 'Empathy scenarios', 'Personal reflection']
    },
    metadata: {
      author: 'Dr. Heart Mind',
      createdAt: new Date('2024-01-28'),
      updatedAt: new Date('2024-01-28'),
      version: '1.0',
      gradeLevel: 'K-6',
      standardsAlignment: ['SEL.1A.1', 'SEL.2A.1']
    }
  },

  // Additional Quantum Physics Lessons for Emma's Curriculum
  {
    id: 'qp-003',
    title: 'Quantum Computing Basics',
    description: 'Introduction to quantum computers and how they differ from classical computers',
    type: 'interactive' as LessonType,
    subject: SUBJECTS.QUANTUM_PHYSICS,
    category: CATEGORIES.HANDS_ON_LAB,
    tags: ['quantum-computing', 'qubits', 'algorithms'],
    difficulty: 'intermediate' as DifficultyLevel,
    duration: 40,
    ageRange: [10, 14],
    prerequisites: ['qp-001', 'qp-002'],
    learningObjectives: [
      'Understand quantum vs classical computing',
      'Learn about qubits and quantum gates',
      'Explore simple quantum algorithms'
    ],
    skills: ['logical-reasoning', 'systems-thinking', 'innovation'],
    content: {
      summary: 'Hands-on introduction to quantum computing concepts and simple programming',
      materials: ['Quantum computer simulator', 'Visual programming interface', 'Algorithm examples'],
      activities: ['Build quantum circuits', 'Program simple algorithms', 'Compare quantum vs classical'],
      assessments: ['Circuit building challenge', 'Algorithm explanation', 'Performance comparison']
    },
    metadata: {
      author: 'Dr. Quantum Computing',
      createdAt: new Date('2024-01-20'),
      updatedAt: new Date('2024-01-20'),
      version: '1.0',
      gradeLevel: '5-8',
      standardsAlignment: ['NGSS-MS-ETS1-2']
    }
  },
  {
    id: 'qp-004',
    title: 'Quantum Entanglement',
    description: 'Explore the mysterious phenomenon of quantum entanglement',
    type: 'lab' as LessonType,
    subject: SUBJECTS.QUANTUM_PHYSICS,
    category: CATEGORIES.CORE_CONCEPTS,
    tags: ['entanglement', 'spooky-action', 'quantum-correlation'],
    difficulty: 'advanced' as DifficultyLevel,
    duration: 45,
    ageRange: [11, 15],
    prerequisites: ['qp-001', 'qp-002', 'qp-003'],
    learningObjectives: [
      'Understand quantum entanglement',
      'Explore Einstein\'s "spooky action"',
      'Learn about quantum correlations'
    ],
    skills: ['analytical-thinking', 'scientific-method', 'hypothesis-formation'],
    content: {
      summary: 'Advanced exploration of quantum entanglement through simulations and experiments',
      materials: ['Entanglement simulator', 'Correlation analyzer', 'Historical context videos'],
      activities: ['Entanglement experiments', 'Correlation measurements', 'Einstein debate simulation'],
      assessments: ['Entanglement explanation', 'Experiment analysis', 'Concept application']
    },
    metadata: {
      author: 'Prof. Quantum Entanglement',
      createdAt: new Date('2024-01-22'),
      updatedAt: new Date('2024-01-22'),
      version: '1.0',
      gradeLevel: '6-9',
      standardsAlignment: ['NGSS-HS-PS4-5']
    }
  },
  {
    id: 'qp-005',
    title: 'Quantum Applications',
    description: 'Real-world applications of quantum physics in technology and science',
    type: 'project' as LessonType,
    subject: SUBJECTS.QUANTUM_PHYSICS,
    category: CATEGORIES.PRACTICAL_APPLICATION,
    tags: ['quantum-technology', 'applications', 'future-tech'],
    difficulty: 'advanced' as DifficultyLevel,
    duration: 50,
    ageRange: [12, 16],
    prerequisites: ['qp-001', 'qp-002', 'qp-003', 'qp-004'],
    learningObjectives: [
      'Identify quantum applications in technology',
      'Design a quantum-inspired solution',
      'Present quantum concepts to others'
    ],
    skills: ['innovation', 'creative-problem-solving', 'communication', 'systems-thinking'],
    content: {
      summary: 'Capstone project exploring real-world quantum applications and future possibilities',
      materials: ['Application database', 'Design tools', 'Presentation platform'],
      activities: ['Research quantum technologies', 'Design quantum solution', 'Create presentation'],
      assessments: ['Technology analysis', 'Solution design', 'Peer presentation']
    },
    metadata: {
      author: 'Dr. Quantum Applications',
      createdAt: new Date('2024-01-25'),
      updatedAt: new Date('2024-01-25'),
      version: '1.0',
      gradeLevel: '7-10',
      standardsAlignment: ['NGSS-HS-ETS1-3']
    }
  },

  // Additional Critical Thinking Lessons
  {
    id: 'ct-002',
    title: 'Advanced Logic Puzzles',
    description: 'Complex logical reasoning through challenging puzzles and brain teasers',
    type: 'interactive' as LessonType,
    subject: SUBJECTS.CRITICAL_THINKING,
    category: CATEGORIES.HANDS_ON_LAB,
    tags: ['advanced-logic', 'puzzles', 'deduction'],
    difficulty: 'intermediate' as DifficultyLevel,
    duration: 35,
    ageRange: [9, 13],
    prerequisites: ['ct-001'],
    learningObjectives: [
      'Solve complex logic puzzles',
      'Apply deductive reasoning',
      'Develop systematic problem-solving'
    ],
    skills: ['logical-reasoning', 'analytical-thinking', 'pattern-recognition'],
    content: {
      summary: 'Advanced logic puzzles that challenge reasoning and deduction skills',
      materials: ['Logic puzzle collection', 'Deduction tools', 'Strategy guides'],
      activities: ['Solve multi-step puzzles', 'Create logic chains', 'Puzzle competitions'],
      assessments: ['Puzzle solving speed', 'Strategy explanation', 'Create own puzzle']
    },
    metadata: {
      author: 'Logic Puzzle Master',
      createdAt: new Date('2024-01-23'),
      updatedAt: new Date('2024-01-23'),
      version: '1.0',
      gradeLevel: '4-7',
      standardsAlignment: ['CCSS.MATH.PRACTICE.MP3']
    }
  },
  {
    id: 'ct-003',
    title: 'Problem Solving Strategies',
    description: 'Learn systematic approaches to solving complex problems',
    type: 'interactive' as LessonType,
    subject: SUBJECTS.CRITICAL_THINKING,
    category: CATEGORIES.PRACTICAL_APPLICATION,
    tags: ['problem-solving', 'strategies', 'systematic-thinking'],
    difficulty: 'intermediate' as DifficultyLevel,
    duration: 40,
    ageRange: [10, 14],
    prerequisites: ['ct-001', 'ct-002'],
    learningObjectives: [
      'Learn problem-solving frameworks',
      'Apply strategies to real problems',
      'Develop systematic thinking'
    ],
    skills: ['creative-problem-solving', 'systems-thinking', 'analytical-thinking'],
    content: {
      summary: 'Systematic problem-solving strategies applied to real-world challenges',
      materials: ['Strategy frameworks', 'Problem scenarios', 'Solution templates'],
      activities: ['Apply problem-solving steps', 'Tackle real challenges', 'Strategy comparison'],
      assessments: ['Strategy application', 'Problem solution', 'Method evaluation']
    },
    metadata: {
      author: 'Problem Solving Expert',
      createdAt: new Date('2024-01-26'),
      updatedAt: new Date('2024-01-26'),
      version: '1.0',
      gradeLevel: '5-8',
      standardsAlignment: ['CCSS.MATH.PRACTICE.MP1']
    }
  },
  {
    id: 'ct-004',
    title: 'Systems Thinking',
    description: 'Understanding complex systems and their interconnections',
    type: 'project' as LessonType,
    subject: SUBJECTS.CRITICAL_THINKING,
    category: CATEGORIES.CORE_CONCEPTS,
    tags: ['systems-thinking', 'interconnections', 'complexity'],
    difficulty: 'advanced' as DifficultyLevel,
    duration: 45,
    ageRange: [11, 15],
    prerequisites: ['ct-001', 'ct-002', 'ct-003'],
    learningObjectives: [
      'Understand system components',
      'Identify interconnections',
      'Analyze system behavior'
    ],
    skills: ['systems-thinking', 'analytical-thinking', 'pattern-recognition'],
    content: {
      summary: 'Explore complex systems and learn to think systematically about interconnected problems',
      materials: ['System diagrams', 'Modeling tools', 'Case studies'],
      activities: ['Map system components', 'Trace interconnections', 'Predict system behavior'],
      assessments: ['System analysis', 'Prediction accuracy', 'Systems design']
    },
    metadata: {
      author: 'Systems Thinking Specialist',
      createdAt: new Date('2024-01-28'),
      updatedAt: new Date('2024-01-28'),
      version: '1.0',
      gradeLevel: '6-9',
      standardsAlignment: ['NGSS-MS-ESS3-3']
    }
  }
]

// Lesson Categories for Organization
export const LESSON_CATEGORIES = [
  {
    id: 'core-concepts',
    name: 'Core Concepts',
    description: 'Fundamental knowledge and understanding',
    icon: '🧠',
    color: 'blue'
  },
  {
    id: 'hands-on-lab',
    name: 'Hands-on Labs',
    description: 'Interactive experiments and practical activities',
    icon: '🔬',
    color: 'green'
  },
  {
    id: 'creative-project',
    name: 'Creative Projects',
    description: 'Build, create, and express learning creatively',
    icon: '🎨',
    color: 'purple'
  },
  {
    id: 'practical-application',
    name: 'Real-World Application',
    description: 'Apply learning to real-world situations',
    icon: '🌍',
    color: 'orange'
  },
  {
    id: 'collaboration',
    name: 'Collaboration',
    description: 'Work together and learn from peers',
    icon: '👥',
    color: 'cyan'
  },
  {
    id: 'assessment',
    name: 'Assessment',
    description: 'Check understanding and progress',
    icon: '📊',
    color: 'red'
  }
]

// Subject Areas with Metadata
export const SUBJECT_AREAS = [
  {
    id: SUBJECTS.QUANTUM_PHYSICS,
    name: 'Quantum Physics',
    description: 'Explore the quantum world and its mysteries',
    icon: '⚛️',
    color: 'from-blue-500 to-cyan-500',
    ageRange: [8, 16],
    totalLessons: 24,
    estimatedHours: 12
  },
  {
    id: SUBJECTS.DNA_BIOLOGY,
    name: 'DNA & Biology',
    description: 'Discover the building blocks of life',
    icon: '🧬',
    color: 'from-green-500 to-emerald-500',
    ageRange: [7, 15],
    totalLessons: 18,
    estimatedHours: 10
  },
  {
    id: SUBJECTS.CRITICAL_THINKING,
    name: 'Critical Thinking',
    description: 'Develop logical reasoning and problem-solving skills',
    icon: '🤔',
    color: 'from-purple-500 to-pink-500',
    ageRange: [6, 16],
    totalLessons: 20,
    estimatedHours: 8
  },
  {
    id: SUBJECTS.CYBERSECURITY,
    name: 'Cybersecurity',
    description: 'Learn to stay safe and secure in the digital world',
    icon: '🛡️',
    color: 'from-red-500 to-orange-500',
    ageRange: [8, 16],
    totalLessons: 15,
    estimatedHours: 9
  },
  {
    id: SUBJECTS.EMOTIONAL_INTELLIGENCE,
    name: 'Emotional Intelligence',
    description: 'Understand emotions and build social skills',
    icon: '❤️',
    color: 'from-pink-500 to-rose-500',
    ageRange: [5, 14],
    totalLessons: 16,
    estimatedHours: 6
  }
]
