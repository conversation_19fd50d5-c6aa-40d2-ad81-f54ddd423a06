'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Zap, 
  Edit, 
  Settings, 
  Brain,
  Clock,
  Target
} from 'lucide-react'
import { useCurriculumPlanner } from './CurriculumPlannerProvider'
import { PlanningMode } from '../types/curriculum'

export function PlanningModeToggle() {
  const { state, dispatch } = useCurriculumPlanner()

  const handleModeChange = (mode: PlanningMode) => {
    dispatch({ type: 'SET_PLANNING_MODE', payload: mode })
  }

  const modes = [
    {
      id: 'auto' as PlanningMode,
      name: 'Auto Mode',
      icon: Zap,
      description: 'AI-powered automatic scheduling',
      color: 'from-cyan-500 to-blue-500',
      features: [
        'Smart lesson recommendations',
        'Optimal pacing and timing',
        'Skill gap analysis',
        'Automatic conflict resolution'
      ],
      benefits: [
        'Saves time',
        'Optimized learning',
        'Balanced curriculum'
      ]
    },
    {
      id: 'manual' as PlanningMode,
      name: 'Manual Mode',
      icon: Edit,
      description: 'Full parent control over scheduling',
      color: 'from-purple-500 to-pink-500',
      features: [
        'Drag-and-drop scheduling',
        'Custom lesson selection',
        'Flexible timing',
        'Personal preferences'
      ],
      benefits: [
        'Complete control',
        'Custom pacing',
        'Family schedule fit'
      ]
    }
  ]

  const currentMode = modes.find(mode => mode.id === state.planningMode)

  return (
    <div className="space-y-4">
      {/* Mode Toggle Buttons */}
      <div className="flex bg-gray-800/50 rounded-lg p-1 border border-gray-700/50">
        {modes.map((mode) => (
          <Button
            key={mode.id}
            variant="ghost"
            className={`flex-1 relative transition-all duration-300 ${
              state.planningMode === mode.id
                ? `bg-gradient-to-r ${mode.color} text-white shadow-lg`
                : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
            }`}
            onClick={() => handleModeChange(mode.id)}
          >
            <mode.icon className="w-4 h-4 mr-2" />
            {mode.name}
            {state.planningMode === mode.id && (
              <motion.div
                layoutId="activeMode"
                className="absolute inset-0 bg-gradient-to-r from-white/10 to-white/5 rounded-md"
                initial={false}
                transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
              />
            )}
          </Button>
        ))}
      </div>

      {/* Current Mode Info */}
      {currentMode && (
        <motion.div
          key={state.planningMode}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800/30 rounded-lg p-4 border border-gray-700/30"
        >
          <div className="flex items-center gap-3 mb-3">
            <div className={`p-2 rounded-lg bg-gradient-to-r ${currentMode.color}`}>
              <currentMode.icon className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-white">{currentMode.name}</h3>
              <p className="text-sm text-gray-400">{currentMode.description}</p>
            </div>
          </div>

          {/* Features */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium text-white mb-2 flex items-center gap-2">
                <Settings className="w-4 h-4" />
                Features
              </h4>
              <ul className="space-y-1">
                {currentMode.features.map((feature, index) => (
                  <li key={index} className="text-xs text-gray-400 flex items-center gap-2">
                    <div className="w-1 h-1 bg-cyan-400 rounded-full" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-medium text-white mb-2 flex items-center gap-2">
                <Target className="w-4 h-4" />
                Benefits
              </h4>
              <div className="flex flex-wrap gap-1">
                {currentMode.benefits.map((benefit, index) => (
                  <Badge
                    key={index}
                    variant="outline"
                    className="text-xs border-gray-600 text-gray-300"
                  >
                    {benefit}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* Mode-specific Actions */}
          <div className="mt-4 pt-3 border-t border-gray-700/30">
            {state.planningMode === 'auto' ? (
              <div className="flex items-center gap-3">
                <Button
                  size="sm"
                  className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600"
                >
                  <Brain className="w-4 h-4 mr-2" />
                  Generate Schedule
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="border-gray-600 text-gray-300 hover:bg-gray-700/50"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  AI Settings
                </Button>
              </div>
            ) : (
              <div className="flex items-center gap-3">
                <Button
                  size="sm"
                  className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Start Planning
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="border-gray-600 text-gray-300 hover:bg-gray-700/50"
                >
                  <Clock className="w-4 h-4 mr-2" />
                  Templates
                </Button>
              </div>
            )}
          </div>
        </motion.div>
      )}

      {/* Quick Tips */}
      <div className="bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/20 rounded-lg p-3">
        <div className="flex items-start gap-3">
          <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0" />
          <div>
            <h4 className="text-sm font-medium text-yellow-400 mb-1">Pro Tip</h4>
            <p className="text-xs text-gray-300">
              {state.planningMode === 'auto' 
                ? 'You can always review and modify AI suggestions before they\'re scheduled.'
                : 'Switch to Auto Mode anytime to get AI recommendations for your manual plan.'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
