export interface MenuItem {
  id: string
  label: string
  icon: any
  badge?: string | number
  route?: string
  action?: () => void
}

export interface SidebarProps {
  className?: string
  onCollapseChange?: (collapsed: boolean) => void
}

export interface EvolutionTheme {
  primary: string
  secondary: string
  glow: string
}

export interface SidebarState {
  isCollapsed: boolean
  activeItem: string
  compactMode: boolean
  showCommandPalette: boolean
}
