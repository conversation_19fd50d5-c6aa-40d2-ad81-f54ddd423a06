'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Alert<PERSON>riangle, RefreshCw, Home } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Dashboard Error:', error, errorInfo)
    this.setState({ error, errorInfo })
    
    // Log to error reporting service
    // reportError(error, errorInfo)
  }

  retry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback
        return <FallbackComponent error={this.state.error!} retry={this.retry} />
      }

      return <DefaultErrorFallback error={this.state.error!} retry={this.retry} />
    }

    return this.props.children
  }
}

// Default error fallback component
function DefaultErrorFallback({ error, retry }: { error: Error; retry: () => void }) {
  return (
    <div className="min-h-screen bg-space-gradient flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full"
      >
        <Card variant="quantum" className="p-8 text-center">
          <motion.div
            className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6"
            animate={{ 
              scale: [1, 1.1, 1],
              rotate: [0, 5, -5, 0]
            }}
            transition={{ 
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <AlertTriangle className="w-8 h-8 text-red-500" />
          </motion.div>
          
          <h2 className="text-xl font-orbitron font-bold text-red-500 mb-4">
            Quantum Field Disruption
          </h2>
          
          <p className="text-white/70 mb-6">
            The consciousness matrix encountered an unexpected anomaly. 
            The Timeline&apos;s quantum field is attempting to self-repair.
          </p>
          
          <div className="space-y-4">
            <details className="text-left">
              <summary className="text-sm text-white/50 cursor-pointer hover:text-white/70 transition-colors">
                Technical Details
              </summary>
              <div className="mt-2 p-3 bg-red-500/10 rounded border border-red-500/20">
                <code className="text-xs text-red-400 break-all">
                  {error.message}
                </code>
              </div>
            </details>
            
            <div className="flex gap-3 justify-center">
              <Button
                variant="quantum"
                onClick={retry}
                glow
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Restore Field
              </Button>
              
              <Button
                variant="ghost"
                onClick={() => window.location.href = '/'}
              >
                <Home className="w-4 h-4 mr-2" />
                Return Home
              </Button>
            </div>
          </div>
          
          <div className="mt-6 text-xs text-white/40">
            Error ID: {Date.now().toString(36)}
          </div>
        </Card>
      </motion.div>
    </div>
  )
}

// Specific error fallbacks for different components
export function DNAErrorFallback({ error, retry }: { error: Error; retry: () => void }) {
  return (
    <div className="h-full flex flex-col items-center justify-center p-4 text-center">
      <AlertTriangle className="w-8 h-8 text-red-500 mb-3" />
      <h3 className="text-lg font-bold text-red-500 mb-2">DNA Core Offline</h3>
      <p className="text-sm text-white/60 mb-4">
        Consciousness energy readings unavailable
      </p>
      <Button size="sm" variant="quantum" onClick={retry}>
        <RefreshCw className="w-3 h-3 mr-1" />
        Reconnect
      </Button>
    </div>
  )
}

export function TimelineMapErrorFallback({ error, retry }: { error: Error; retry: () => void }) {
  return (
    <div className="h-full flex flex-col items-center justify-center p-4 text-center">
      <AlertTriangle className="w-8 h-8 text-red-500 mb-3" />
      <h3 className="text-lg font-bold text-red-500 mb-2">Timeline Map Disrupted</h3>
      <p className="text-sm text-white/60 mb-4">
        3D consciousness field temporarily unavailable
      </p>
      <Button size="sm" variant="quantum" onClick={retry}>
        <RefreshCw className="w-3 h-3 mr-1" />
        Restore Map
      </Button>
    </div>
  )
}

export function FeedErrorFallback({ error, retry }: { error: Error; retry: () => void }) {
  return (
    <div className="h-full flex flex-col items-center justify-center p-4 text-center">
      <AlertTriangle className="w-6 h-6 text-red-500 mb-3" />
      <h4 className="text-sm font-bold text-red-500 mb-2">Feed Disconnected</h4>
      <p className="text-xs text-white/60 mb-3">
        Consciousness stream interrupted
      </p>
      <Button size="sm" variant="ghost" onClick={retry}>
        <RefreshCw className="w-3 h-3 mr-1" />
        Reconnect
      </Button>
    </div>
  )
}

export default ErrorBoundary
