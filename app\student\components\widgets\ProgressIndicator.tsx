'use client'

import { motion } from 'framer-motion'
import { TrendingUp, TrendingDown, Minus } from 'lucide-react'
import { Progress } from '@/components/ui/progress'
import { ProgressWidgetProps } from './types'

export default function ProgressIndicator({
  current,
  target,
  label,
  color = 'cyan',
  showPercentage = true,
  className = '',
  size = 'md',
  variant = 'detailed'
}: ProgressWidgetProps) {
  const percentage = target > 0 ? (current / target) * 100 : 0
  const isOverTarget = current > target
  const remaining = Math.max(0, target - current)

  const sizeClasses = {
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6'
  }

  const textSizes = {
    sm: { title: 'text-sm', value: 'text-base', label: 'text-xs' },
    md: { title: 'text-base', value: 'text-lg', label: 'text-sm' },
    lg: { title: 'text-lg', value: 'text-xl', label: 'text-base' }
  }

  const colorClasses = {
    cyan: {
      text: 'text-cyan-400',
      border: 'border-cyan-500/30',
      bg: 'bg-cyan-500/10'
    },
    green: {
      text: 'text-green-400',
      border: 'border-green-500/30',
      bg: 'bg-green-500/10'
    },
    yellow: {
      text: 'text-yellow-400',
      border: 'border-yellow-500/30',
      bg: 'bg-yellow-500/10'
    },
    purple: {
      text: 'text-purple-400',
      border: 'border-purple-500/30',
      bg: 'bg-purple-500/10'
    },
    orange: {
      text: 'text-orange-400',
      border: 'border-orange-500/30',
      bg: 'bg-orange-500/10'
    }
  }

  const selectedColor = colorClasses[color as keyof typeof colorClasses] || colorClasses.cyan

  const getTrendIcon = () => {
    if (percentage >= 100) return <TrendingUp className="w-4 h-4 text-green-400" />
    if (percentage >= 75) return <TrendingUp className="w-4 h-4 text-yellow-400" />
    if (percentage >= 50) return <Minus className="w-4 h-4 text-yellow-400" />
    return <TrendingDown className="w-4 h-4 text-red-400" />
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`bg-black/40 border ${selectedColor.border} backdrop-blur-xl rounded-lg ${sizeClasses[size]} ${className}`}
    >
      <div className="flex items-center justify-between mb-3">
        <span className={`font-medium text-white ${textSizes[size].title}`}>
          {label}
        </span>
        <div className="flex items-center gap-2">
          {getTrendIcon()}
          {showPercentage && (
            <span className={`font-bold ${selectedColor.text} ${textSizes[size].value}`}>
              {Math.round(percentage)}%
            </span>
          )}
        </div>
      </div>

      {variant === 'detailed' && (
        <>
          <div className="space-y-2 mb-3">
            <div className="flex justify-between items-center">
              <span className="text-white/80 text-sm">
                {current.toLocaleString()} / {target.toLocaleString()}
              </span>
              <span className={`text-xs ${selectedColor.text}`}>
                {isOverTarget ? 'Target exceeded!' : `${remaining.toLocaleString()} to go`}
              </span>
            </div>
            <Progress 
              value={Math.min(percentage, 100)} 
              className="h-2"
            />
          </div>

          {isOverTarget && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className={`p-2 rounded ${selectedColor.bg} ${selectedColor.border} border text-center`}
            >
              <span className={`text-xs font-medium ${selectedColor.text}`}>
                🎉 Exceeded target by {(current - target).toLocaleString()}!
              </span>
            </motion.div>
          )}
        </>
      )}

      {variant === 'compact' && (
        <div className="flex items-center justify-between">
          <Progress 
            value={Math.min(percentage, 100)} 
            className="h-1 flex-1 mr-3"
          />
          <span className={`font-bold ${selectedColor.text} text-sm`}>
            {current}/{target}
          </span>
        </div>
      )}
    </motion.div>
  )
}
