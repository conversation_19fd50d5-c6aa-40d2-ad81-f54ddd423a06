'use client'

import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { performanceMiddleware, createDebouncedUpdater } from '@/utils/stateOptimization'

// Types
export interface PlayerData {
  id: string
  username: string
  level: number
  xp: number
  totalXP: number
  avatar: {
    type: 'nanocore' | 'custom'
    customization: Record<string, any>
  }
  achievements: string[]
  badges: string[]
  stats: {
    learningStreak: number
    totalLessons: number
    totalProjects: number
    mentorshipHours: number
  }
}

export interface TimelineNode {
  id: string
  type: 'lesson' | 'project' | 'milestone' | 'achievement'
  title: string
  description: string
  status: 'locked' | 'available' | 'in-progress' | 'completed'
  position: { x: number; y: number; z: number }
  connections: string[]
  metadata: Record<string, any>
}

export interface TimelineData {
  nodes: TimelineNode[]
  currentPath: string[]
  completedNodes: string[]
  availableNodes: string[]
}

export interface SystemStatus {
  isOnline: boolean
  lastSync: Date
  quantumState: 'stable' | 'fluctuating' | 'critical'
  consciousnessLevel: number
  systemHealth: number
}

export interface DashboardState {
  // View modes
  viewMode: 'explore' | 'focus' | 'assist' | 'syntropy'
  
  // UI state
  selectedNode: string | null
  activePanel: string | null
  isFullscreen: boolean
  sidebarCollapsed: boolean
  
  // Data
  playerData: PlayerData | null
  timelineData: TimelineData | null
  systemStatus: SystemStatus | null
  
  // Actions
  setViewMode: (mode: DashboardState['viewMode']) => void
  setSelectedNode: (nodeId: string | null) => void
  setActivePanel: (panelId: string | null) => void
  toggleFullscreen: () => void
  setSidebarCollapsed: (collapsed: boolean) => void
  
  // Data actions
  setPlayerData: (data: PlayerData) => void
  setTimelineData: (data: TimelineData) => void
  setSystemStatus: (status: SystemStatus) => void
  
  // Computed
  getNodeById: (id: string) => TimelineNode | null
  getAvailableNodes: () => TimelineNode[]
  getCompletedNodes: () => TimelineNode[]
}

// Default data
const defaultPlayerData: PlayerData = {
  id: 'player-1',
  username: 'NanoArchitect',
  level: 12,
  xp: 2847,
  totalXP: 15420,
  avatar: {
    type: 'nanocore',
    customization: {
      coreColor: '#22d3ee',
      energyPattern: 'quantum',
      accessories: ['neural-crown']
    }
  },
  achievements: ['first-steps', 'code-warrior', 'mentor-initiate'],
  badges: ['early-adopter', 'quantum-explorer'],
  stats: {
    learningStreak: 15,
    totalLessons: 47,
    totalProjects: 12,
    mentorshipHours: 8
  }
}

const defaultTimelineData: TimelineData = {
  nodes: [
    {
      id: 'node-1',
      type: 'lesson',
      title: 'Quantum Fundamentals',
      description: 'Learn the basics of quantum computing',
      status: 'completed',
      position: { x: 0, y: 0, z: 0 },
      connections: ['node-2'],
      metadata: { difficulty: 'beginner', duration: 30 }
    },
    {
      id: 'node-2',
      type: 'project',
      title: 'Build Your First Quantum Circuit',
      description: 'Create a simple quantum circuit',
      status: 'in-progress',
      position: { x: 2, y: 1, z: 0 },
      connections: ['node-3'],
      metadata: { difficulty: 'intermediate', duration: 60 }
    }
  ],
  currentPath: ['node-1', 'node-2'],
  completedNodes: ['node-1'],
  availableNodes: ['node-2']
}

const defaultSystemStatus: SystemStatus = {
  isOnline: true,
  lastSync: new Date(),
  quantumState: 'stable',
  consciousnessLevel: 0.85,
  systemHealth: 0.92
}

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined'

// Store with performance optimizations
export const useDashboardStore = create<DashboardState>()(
  devtools(
    isBrowser ? persist(
      performanceMiddleware('dashboard', (set: (partial: Partial<DashboardState> | ((state: DashboardState) => Partial<DashboardState>)) => void, get: () => DashboardState) => {
        // Create debounced updaters for frequently changing values
        const debouncedSetPlayerData = createDebouncedUpdater(
          (data: PlayerData) => set({ playerData: data }),
          100
        )

        const debouncedSetSystemStatus = createDebouncedUpdater(
          (status: SystemStatus) => set({ systemStatus: status }),
          200
        )

        return {
        // Initial state
        viewMode: 'explore',
        selectedNode: null,
        activePanel: null,
        isFullscreen: false,
        sidebarCollapsed: false,
        
        playerData: defaultPlayerData,
        timelineData: defaultTimelineData,
        systemStatus: defaultSystemStatus,
        
        // Actions
        setViewMode: (mode: DashboardState['viewMode']) => set({ viewMode: mode }),
        setSelectedNode: (nodeId: string | null) => set({ selectedNode: nodeId }),
        setActivePanel: (panelId: string | null) => set({ activePanel: panelId }),
        toggleFullscreen: () => set((state: DashboardState) => ({ isFullscreen: !state.isFullscreen })),
        setSidebarCollapsed: (collapsed: boolean) => set({ sidebarCollapsed: collapsed }),

        // Optimized data actions with debouncing
        setPlayerData: debouncedSetPlayerData,
        setTimelineData: (data: any) => set({ timelineData: data }),
        setSystemStatus: debouncedSetSystemStatus,

        // Computed
        getNodeById: (id: string) => {
          const { timelineData } = get()
          return timelineData?.nodes.find((node: any) => node.id === id) || null
        },
        
        getAvailableNodes: () => {
          const { timelineData } = get()
          return timelineData?.nodes.filter((node: any) =>
            timelineData.availableNodes.includes(node.id)
          ) || []
        },

        getCompletedNodes: () => {
          const { timelineData } = get()
          return timelineData?.nodes.filter((node: any) =>
            timelineData.completedNodes.includes(node.id)
          ) || []
        }
        }
      }),
      {
        name: 'nanogenesis-dashboard',
        partialize: (state: DashboardState) => ({
          viewMode: state.viewMode,
          sidebarCollapsed: state.sidebarCollapsed,
          playerData: state.playerData
        })
      }
    ) : performanceMiddleware('dashboard', (set: (partial: Partial<DashboardState> | ((state: DashboardState) => Partial<DashboardState>)) => void, get: () => DashboardState) => {
        // Create debounced updaters for frequently changing values
        const debouncedSetPlayerData = createDebouncedUpdater(
          (data: PlayerData) => set({ playerData: data }),
          100
        )

        const debouncedSetSystemStatus = createDebouncedUpdater(
          (status: SystemStatus) => set({ systemStatus: status }),
          200
        )

        return {
        // Initial state
        viewMode: 'explore',
        selectedNode: null,
        activePanel: null,
        isFullscreen: false,
        sidebarCollapsed: false,

        playerData: defaultPlayerData,
        timelineData: defaultTimelineData,
        systemStatus: defaultSystemStatus,

        // Actions
        setViewMode: (mode: DashboardState['viewMode']) => set({ viewMode: mode }),
        setSelectedNode: (nodeId: string | null) => set({ selectedNode: nodeId }),
        setActivePanel: (panelId: string | null) => set({ activePanel: panelId }),
        toggleFullscreen: () => set((state: DashboardState) => ({ isFullscreen: !state.isFullscreen })),
        setSidebarCollapsed: (collapsed: boolean) => set({ sidebarCollapsed: collapsed }),

        // Optimized data actions with debouncing
        setPlayerData: debouncedSetPlayerData,
        setTimelineData: (data: any) => set({ timelineData: data }),
        setSystemStatus: debouncedSetSystemStatus,

        // Computed
        getNodeById: (id: string) => {
          const { timelineData } = get()
          return timelineData?.nodes.find((node: any) => node.id === id) || null
        },

        getAvailableNodes: () => {
          const { timelineData } = get()
          return timelineData?.nodes.filter((node: any) =>
            timelineData.availableNodes.includes(node.id)
          ) || []
        },

        getCompletedNodes: () => {
          const { timelineData } = get()
          return timelineData?.nodes.filter((node: any) =>
            timelineData.completedNodes.includes(node.id)
          ) || []
        }
        }
      }),
    { name: 'NanoGenesis Dashboard' }
  )
)

// Optimized selective hooks to prevent unnecessary re-renders
export const usePlayerData = () => useDashboardStore(state => state.playerData)
export const useTimelineData = () => useDashboardStore(state => state.timelineData)
export const useSystemStatus = () => useDashboardStore(state => state.systemStatus)
export const useViewMode = () => useDashboardStore(state => state.viewMode)
export const useSelectedNode = () => useDashboardStore(state => state.selectedNode)
export const useActivePanel = () => useDashboardStore(state => state.activePanel)

// Granular hooks for specific player data to reduce re-renders
export const usePlayerLevel = () => useDashboardStore(state => state.playerData?.level ?? 1)
export const usePlayerXP = () => useDashboardStore(state => state.playerData?.xp ?? 0)
export const usePlayerStats = () => useDashboardStore(state => state.playerData?.stats ?? null)
export const usePlayerAvatar = () => useDashboardStore(state => state.playerData?.avatar ?? null)
export const usePlayerAchievements = () => useDashboardStore(state => state.playerData?.achievements ?? [])

// UI state hooks
export const useUIState = () => useDashboardStore(state => ({
  isFullscreen: state.isFullscreen,
  sidebarCollapsed: state.sidebarCollapsed,
  selectedNode: state.selectedNode,
  activePanel: state.activePanel
}))

// System status specific hooks
export const useQuantumState = () => useDashboardStore(state => state.systemStatus?.quantumState ?? 'stable')
export const useConsciousnessLevel = () => useDashboardStore(state => state.systemStatus?.consciousnessLevel ?? 0)
export const useSystemHealth = () => useDashboardStore(state => state.systemStatus?.systemHealth ?? 0)

// Timeline specific hooks
export const useAvailableNodes = () => useDashboardStore(state => {
  const timelineData = state.timelineData
  return timelineData?.nodes.filter(node =>
    timelineData.availableNodes.includes(node.id)
  ) || []
})

export const useCompletedNodes = () => useDashboardStore(state => {
  const timelineData = state.timelineData
  return timelineData?.nodes.filter(node =>
    timelineData.completedNodes.includes(node.id)
  ) || []
})

// Actions-only hook (doesn't cause re-renders on state changes)
export const useDashboardActions = () => useDashboardStore(state => ({
  setViewMode: state.setViewMode,
  setSelectedNode: state.setSelectedNode,
  setActivePanel: state.setActivePanel,
  toggleFullscreen: state.toggleFullscreen,
  setSidebarCollapsed: state.setSidebarCollapsed,
  setPlayerData: state.setPlayerData,
  setTimelineData: state.setTimelineData,
  setSystemStatus: state.setSystemStatus
}))
