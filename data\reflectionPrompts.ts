import { ReflectionPrompt } from '@/types/chat'

export const REFLECTION_PROMPTS = {
  post_chat: [
    {
      id: 'post_chat_1',
      question: 'How did you help someone in your conversation today?',
      type: 'post_chat' as const,
      responses: [
        'I answered their question',
        'I encouraged them',
        'I shared what I learned',
        'I listened carefully',
        'I didn\'t help anyone today'
      ],
      timestamp: new Date()
    },
    {
      id: 'post_chat_2',
      question: 'How did the conversation make you feel?',
      type: 'post_chat' as const,
      responses: [
        'Happy and excited',
        'Proud of helping others',
        'Curious to learn more',
        'Grateful for new friends',
        'A bit confused but interested'
      ],
      timestamp: new Date()
    },
    {
      id: 'post_chat_3',
      question: 'What new thing did you learn from talking with others?',
      type: 'post_chat' as const,
      responses: [
        'A new way to solve problems',
        'Something about science or math',
        'How to be more helpful',
        'A different perspective',
        'I mostly shared what I knew'
      ],
      timestamp: new Date()
    },
    {
      id: 'post_chat_4',
      question: 'Did you use kind words in your conversations?',
      type: 'post_chat' as const,
      responses: [
        'Yes, I was very encouraging',
        'Yes, I said thank you often',
        'Yes, I complimented others',
        'Mostly, but I could do better',
        'I need to work on this'
      ],
      timestamp: new Date()
    }
  ],

  daily: [
    {
      id: 'daily_1',
      question: 'What\'s one way you want to help others in the NanoVerse today?',
      type: 'daily' as const,
      responses: [
        'Answer questions in chat',
        'Welcome new users',
        'Share something I learned',
        'Encourage someone struggling',
        'Work on a team project'
      ],
      timestamp: new Date()
    },
    {
      id: 'daily_2',
      question: 'How can you make the timeline more harmonious today?',
      type: 'daily' as const,
      responses: [
        'Be extra kind in my messages',
        'Help resolve any conflicts',
        'Celebrate others\' achievements',
        'Ask thoughtful questions',
        'Listen more than I speak'
      ],
      timestamp: new Date()
    },
    {
      id: 'daily_3',
      question: 'What communication skill do you want to practice today?',
      type: 'daily' as const,
      responses: [
        'Explaining things clearly',
        'Asking better questions',
        'Being more encouraging',
        'Listening more carefully',
        'Resolving disagreements peacefully'
      ],
      timestamp: new Date()
    }
  ],

  conflict_resolution: [
    {
      id: 'conflict_1',
      question: 'When someone disagrees with you, what\'s the best first step?',
      type: 'conflict_resolution' as const,
      responses: [
        'Try to understand their point of view',
        'Ask questions to learn more',
        'Stay calm and respectful',
        'Look for common ground',
        'Ask for help from others'
      ],
      timestamp: new Date()
    },
    {
      id: 'conflict_2',
      question: 'How can you turn a disagreement into a learning opportunity?',
      type: 'conflict_resolution' as const,
      responses: [
        'Ask why they think differently',
        'Share your reasoning calmly',
        'Look for what you both agree on',
        'Suggest working together on a solution',
        'Thank them for their perspective'
      ],
      timestamp: new Date()
    },
    {
      id: 'conflict_3',
      question: 'What should you do if someone is being unkind in chat?',
      type: 'conflict_resolution' as const,
      responses: [
        'Stay calm and don\'t respond with unkindness',
        'Try to redirect to something positive',
        'Ask a mentor or moderator for help',
        'Use the flag system if needed',
        'Focus on helping others instead'
      ],
      timestamp: new Date()
    }
  ],

  kindness_check: [
    {
      id: 'kindness_1',
      question: 'Think about your last few messages. Were they helpful and kind?',
      type: 'kindness_check' as const,
      responses: [
        'Yes, I was very supportive',
        'Mostly, with room to improve',
        'I tried my best to be helpful',
        'I could have been more encouraging',
        'I need to focus more on kindness'
      ],
      timestamp: new Date()
    },
    {
      id: 'kindness_2',
      question: 'How did you make someone smile in chat today?',
      type: 'kindness_check' as const,
      responses: [
        'I gave them a genuine compliment',
        'I helped solve their problem',
        'I celebrated their success',
        'I shared something funny (appropriately)',
        'I haven\'t made anyone smile yet'
      ],
      timestamp: new Date()
    },
    {
      id: 'kindness_3',
      question: 'What\'s one kind thing someone did for you in chat?',
      type: 'kindness_check' as const,
      responses: [
        'They helped me understand something',
        'They encouraged me when I was stuck',
        'They welcomed me warmly',
        'They shared their knowledge generously',
        'They listened to my ideas'
      ],
      timestamp: new Date()
    }
  ]
}

export const REFLECTION_REWARDS = {
  post_chat: {
    ce: 5,
    cubits: 1,
    harmonyBonus: 2
  },
  daily: {
    ce: 10,
    cubits: 2,
    harmonyBonus: 5
  },
  conflict_resolution: {
    ce: 15,
    cubits: 3,
    harmonyBonus: 10
  },
  kindness_check: {
    ce: 8,
    cubits: 1,
    harmonyBonus: 4
  }
}

export function getRandomReflectionPrompt(type: keyof typeof REFLECTION_PROMPTS): ReflectionPrompt {
  const prompts = REFLECTION_PROMPTS[type]
  const randomIndex = Math.floor(Math.random() * prompts.length)
  return {
    ...prompts[randomIndex],
    timestamp: new Date()
  }
}

export function shouldShowReflectionPrompt(
  lastChatTime: Date,
  lastReflectionTime: Date | null,
  messageCount: number
): boolean {
  // Show reflection prompt if:
  // 1. User has sent at least 5 messages in a session
  // 2. At least 30 minutes have passed since last reflection
  // 3. At least 10 minutes have passed since last chat message
  
  const now = new Date()
  const timeSinceLastChat = now.getTime() - lastChatTime.getTime()
  const timeSinceLastReflection = lastReflectionTime 
    ? now.getTime() - lastReflectionTime.getTime()
    : Infinity

  return (
    messageCount >= 5 &&
    timeSinceLastChat >= 10 * 60 * 1000 && // 10 minutes
    timeSinceLastReflection >= 30 * 60 * 1000 // 30 minutes
  )
}

export function getReflectionPromptForContext(
  hasConflict: boolean,
  isEndOfDay: boolean,
  isStartOfDay: boolean,
  recentKindnessScore: number
): ReflectionPrompt {
  if (hasConflict) {
    return getRandomReflectionPrompt('conflict_resolution')
  }
  
  if (isStartOfDay) {
    return getRandomReflectionPrompt('daily')
  }
  
  if (recentKindnessScore < 0.7) {
    return getRandomReflectionPrompt('kindness_check')
  }
  
  return getRandomReflectionPrompt('post_chat')
}

export function calculateReflectionImpact(
  prompt: ReflectionPrompt,
  selectedResponse: string,
  responseIndex: number
): {
  ceBonus: number
  cubitsBonus: number
  harmonyBonus: number
  kindnessImpact: number
} {
  const baseReward = REFLECTION_REWARDS[prompt.type]
  
  // Positive responses (first 3-4 options) get full rewards
  // Less positive responses get reduced rewards but still some recognition
  const positivityMultiplier = responseIndex < 3 ? 1.0 : 0.5
  
  return {
    ceBonus: Math.floor(baseReward.ce * positivityMultiplier),
    cubitsBonus: Math.floor(baseReward.cubits * positivityMultiplier),
    harmonyBonus: Math.floor(baseReward.harmonyBonus * positivityMultiplier),
    kindnessImpact: responseIndex < 2 ? 0.1 : responseIndex < 4 ? 0.05 : 0
  }
}
