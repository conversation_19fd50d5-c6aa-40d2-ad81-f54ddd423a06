/**
 * Animation Optimization Utilities for NanoHero Platform
 * Provides optimized animation patterns and performance monitoring
 */

import { useRef, useEffect, useMemo, useState } from 'react'
import { MotionProps, Variants, useReducedMotion } from 'framer-motion'

export type AnimationQuality = 'low' | 'medium' | 'high'

export interface OptimizedAnimationConfig {
  quality: AnimationQuality
  respectReducedMotion: boolean
  enableWillChange: boolean
  batchAnimations: boolean
  maxConcurrentAnimations: number
}

const DEFAULT_CONFIG: OptimizedAnimationConfig = {
  quality: 'medium',
  respectReducedMotion: true,
  enableWillChange: true,
  batchAnimations: true,
  maxConcurrentAnimations: 10
}

/**
 * Animation performance monitor
 */
class AnimationPerformanceMonitor {
  private activeAnimations = new Set<string>()
  private animationCounts = new Map<string, number>()
  private lastCheck = Date.now()

  trackAnimation(id: string, action: 'start' | 'end') {
    if (action === 'start') {
      this.activeAnimations.add(id)
      const count = this.animationCounts.get(id) || 0
      this.animationCounts.set(id, count + 1)
    } else {
      this.activeAnimations.delete(id)
    }

    // Log warnings for performance issues
    if (this.activeAnimations.size > 15) {
      console.warn(`⚠️ High animation load: ${this.activeAnimations.size} concurrent animations`)
    }
  }

  getStats() {
    return {
      activeAnimations: this.activeAnimations.size,
      totalAnimations: Array.from(this.animationCounts.values()).reduce((a, b) => a + b, 0)
    }
  }

  reset() {
    this.activeAnimations.clear()
    this.animationCounts.clear()
    this.lastCheck = Date.now()
  }
}

export const animationMonitor = new AnimationPerformanceMonitor()

/**
 * Optimized animation variants based on quality level
 */
export const createOptimizedVariants = (
  quality: AnimationQuality,
  baseVariants: Variants
): Variants => {
  const optimizedVariants: Variants = {}

  Object.entries(baseVariants).forEach(([key, variant]) => {
    if (typeof variant === 'object' && variant !== null) {
      optimizedVariants[key] = {
        ...variant,
        transition: {
          ...variant.transition,
          // Adjust animation complexity based on quality
          duration: quality === 'low' ? 
            (variant.transition?.duration || 0.3) * 0.5 : 
            variant.transition?.duration,
          ease: quality === 'low' ? 'linear' : variant.transition?.ease,
          // Disable complex easing for low quality
          type: quality === 'low' ? undefined : variant.transition?.type
        }
      }
    } else {
      optimizedVariants[key] = variant
    }
  })

  return optimizedVariants
}

/**
 * Performance-optimized motion props
 */
export const useOptimizedMotionProps = (
  baseProps: MotionProps,
  config: Partial<OptimizedAnimationConfig> = {}
): MotionProps => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config }
  const shouldReduceMotion = useReducedMotion()
  const animationId = useRef(`anim_${Math.random().toString(36).substr(2, 9)}`)

  // Respect user's reduced motion preference
  if (shouldReduceMotion && finalConfig.respectReducedMotion) {
    return {
      ...baseProps,
      initial: false,
      animate: baseProps.initial || {},
      transition: { duration: 0 }
    }
  }

  // Optimize based on quality level
  const optimizedProps: MotionProps = useMemo(() => {
    const props = { ...baseProps }

    // Add will-change CSS property for better performance
    if (finalConfig.enableWillChange) {
      props.style = {
        ...props.style,
        willChange: 'transform, opacity'
      }
    }

    // Optimize transition based on quality
    if (props.transition) {
      switch (finalConfig.quality) {
        case 'low':
          props.transition = {
            ...props.transition,
            duration: (props.transition.duration || 0.3) * 0.5,
            ease: 'linear'
          }
          break
        case 'medium':
          props.transition = {
            ...props.transition,
            duration: props.transition.duration || 0.3
          }
          break
        case 'high':
          // Keep original transition
          break
      }
    }

    return props
  }, [baseProps, finalConfig])

  // Track animation lifecycle
  useEffect(() => {
    animationMonitor.trackAnimation(animationId.current, 'start')
    return () => {
      animationMonitor.trackAnimation(animationId.current, 'end')
    }
  }, [])

  return optimizedProps
}

/**
 * Optimized animation presets
 */
export const optimizedAnimationPresets = {
  // Fade animations
  fadeIn: (quality: AnimationQuality = 'medium'): MotionProps => ({
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: {
      duration: quality === 'low' ? 0.15 : quality === 'medium' ? 0.3 : 0.5,
      ease: quality === 'low' ? 'linear' : 'easeOut'
    }
  }),

  // Scale animations
  scaleIn: (quality: AnimationQuality = 'medium'): MotionProps => ({
    initial: { scale: 0.8, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0.8, opacity: 0 },
    transition: {
      duration: quality === 'low' ? 0.2 : quality === 'medium' ? 0.4 : 0.6,
      ease: quality === 'low' ? 'linear' : 'easeOut'
    }
  }),

  // Slide animations
  slideUp: (quality: AnimationQuality = 'medium'): MotionProps => ({
    initial: { y: 20, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: -20, opacity: 0 },
    transition: {
      duration: quality === 'low' ? 0.2 : quality === 'medium' ? 0.4 : 0.6,
      ease: quality === 'low' ? 'linear' : 'easeOut'
    }
  }),

  // Rotation animations (simplified for performance)
  rotate: (quality: AnimationQuality = 'medium'): MotionProps => ({
    animate: { rotate: 360 },
    transition: {
      duration: quality === 'low' ? 1 : quality === 'medium' ? 2 : 3,
      repeat: Infinity,
      ease: 'linear'
    }
  }),

  // Pulse animations
  pulse: (quality: AnimationQuality = 'medium'): MotionProps => ({
    animate: {
      scale: [1, 1.05, 1],
      opacity: [0.8, 1, 0.8]
    },
    transition: {
      duration: quality === 'low' ? 1 : quality === 'medium' ? 2 : 3,
      repeat: Infinity,
      ease: quality === 'low' ? 'linear' : 'easeInOut'
    }
  })
}

/**
 * Animation batching utility
 */
export class AnimationBatcher {
  private animationQueue: Array<() => void> = []
  private isProcessing = false
  private batchSize = 5

  addAnimation(animationFn: () => void) {
    this.animationQueue.push(animationFn)
    this.processBatch()
  }

  private async processBatch() {
    if (this.isProcessing || this.animationQueue.length === 0) return

    this.isProcessing = true

    while (this.animationQueue.length > 0) {
      const batch = this.animationQueue.splice(0, this.batchSize)
      
      // Execute batch
      batch.forEach(animationFn => animationFn())
      
      // Wait for next frame
      await new Promise(resolve => requestAnimationFrame(resolve))
    }

    this.isProcessing = false
  }
}

export const animationBatcher = new AnimationBatcher()

/**
 * CSS will-change optimization hook
 */
export const useWillChange = (properties: string[] = ['transform', 'opacity']) => {
  const elementRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    // Set will-change property
    element.style.willChange = properties.join(', ')

    // Clean up will-change after animation
    const cleanup = () => {
      if (element) {
        element.style.willChange = 'auto'
      }
    }

    // Clean up on unmount or after a delay
    const timeoutId = setTimeout(cleanup, 5000)

    return () => {
      clearTimeout(timeoutId)
      cleanup()
    }
  }, [properties])

  return elementRef
}

/**
 * Performance-aware animation hook
 */
export const usePerformanceAwareAnimation = (
  animationProps: MotionProps,
  performanceThreshold: number = 30 // FPS threshold
) => {
  const [currentFPS, setCurrentFPS] = useState(60)
  const frameCount = useRef(0)
  const lastTime = useRef(performance.now())

  // Monitor FPS
  useEffect(() => {
    const updateFPS = () => {
      frameCount.current++
      const currentTime = performance.now()
      
      if (currentTime - lastTime.current >= 1000) {
        setCurrentFPS(frameCount.current)
        frameCount.current = 0
        lastTime.current = currentTime
      }
      
      requestAnimationFrame(updateFPS)
    }
    
    const rafId = requestAnimationFrame(updateFPS)
    return () => cancelAnimationFrame(rafId)
  }, [])

  // Adjust animation quality based on performance
  const optimizedProps = useMemo(() => {
    if (currentFPS < performanceThreshold) {
      // Reduce animation complexity
      return {
        ...animationProps,
        transition: {
          ...animationProps.transition,
          duration: (animationProps.transition?.duration || 0.3) * 0.5,
          ease: 'linear'
        }
      }
    }
    
    return animationProps
  }, [animationProps, currentFPS, performanceThreshold])

  return { optimizedProps, currentFPS }
}

/**
 * Intersection observer for animation triggering
 */
export const useInViewAnimation = (
  animationProps: MotionProps,
  threshold: number = 0.1
) => {
  const [isInView, setIsInView] = useState(false)
  const elementRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsInView(entry.isIntersecting)
      },
      { threshold }
    )

    observer.observe(element)

    return () => {
      observer.unobserve(element)
    }
  }, [threshold])

  const optimizedProps = useMemo(() => {
    if (!isInView) {
      return {
        ...animationProps,
        animate: animationProps.initial || {}
      }
    }
    
    return animationProps
  }, [animationProps, isInView])

  return { elementRef, optimizedProps, isInView }
}

/**
 * Global animation configuration
 */
export class GlobalAnimationConfig {
  private static instance: GlobalAnimationConfig
  private config: OptimizedAnimationConfig = DEFAULT_CONFIG

  static getInstance(): GlobalAnimationConfig {
    if (!GlobalAnimationConfig.instance) {
      GlobalAnimationConfig.instance = new GlobalAnimationConfig()
    }
    return GlobalAnimationConfig.instance
  }

  setConfig(config: Partial<OptimizedAnimationConfig>) {
    this.config = { ...this.config, ...config }
  }

  getConfig(): OptimizedAnimationConfig {
    return { ...this.config }
  }

  setQuality(quality: AnimationQuality) {
    this.config.quality = quality
  }

  getQuality(): AnimationQuality {
    return this.config.quality
  }
}

export const globalAnimationConfig = GlobalAnimationConfig.getInstance()
