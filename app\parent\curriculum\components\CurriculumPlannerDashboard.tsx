'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import {
  Calendar,
  CalendarDays,
  Settings,
  BookOpen,
  BarChart3,
  Bell,
  Plus,
  Copy
} from 'lucide-react'

import { useCurriculumPlanner } from './CurriculumPlannerProvider'
import { ChildSelector } from './ChildSelector'
import { PlanningModeToggle } from './PlanningModeToggle'
import { CurriculumFlowChart } from './CurriculumFlowChart'
import { LessonLibrary } from './LessonLibrary'
import { PlanningAnalytics } from './PlanningAnalytics'
import { KanbanWeeklyPlanner } from './KanbanWeeklyPlanner'

export function CurriculumPlannerDashboard() {
  const { state, getSelectedChild } = useCurriculumPlanner()
  const [activeTab, setActiveTab] = useState('kanban') // Default to Kanban planner

  const selectedChild = getSelectedChild()
  const unreadNotifications = state.notifications.filter(n => !n.read).length

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-white font-space-grotesk">
            Curriculum Planner
          </h1>
          <p className="text-gray-400 mt-1">
            Plan and manage your child&apos;s learning journey
          </p>
        </div>
        
        {/* Quick Actions */}
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            className="border-cyan-500/30 text-cyan-400 hover:bg-cyan-500/10"
          >
            <Plus className="w-4 h-4 mr-2" />
            Quick Schedule
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10 relative"
          >
            <Bell className="w-4 h-4 mr-2" />
            Notifications
            {unreadNotifications > 0 && (
              <Badge className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-1.5 py-0.5">
                {unreadNotifications}
              </Badge>
            )}
          </Button>
        </div>
      </motion.div>

      {/* Control Panel */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Child Selection */}
              <div className="space-y-2">
                <Label className="text-white font-medium">Select Child</Label>
                <ChildSelector />
              </div>

              {/* Planning Mode */}
              <div className="space-y-2">
                <Label className="text-white font-medium">Planning Mode</Label>
                <PlanningModeToggle />
              </div>

              {/* Quick Stats */}
              <div className="space-y-2">
                <Label className="text-white font-medium">This Week</Label>
                <div className="flex items-center gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-cyan-400">
                      {selectedChild ? state.assignments.filter(a => a.childId === selectedChild.id).length : 0}
                    </div>
                    <div className="text-xs text-gray-400">Scheduled</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-400">
                      {selectedChild ? state.assignments.filter(a => a.childId === selectedChild.id && a.status === 'completed').length : 0}
                    </div>
                    <div className="text-xs text-gray-400">Completed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-400">
                      {selectedChild ? state.assignments.filter(a => a.childId === selectedChild.id && a.status === 'pending').length : 0}
                    </div>
                    <div className="text-xs text-gray-400">Pending</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Main Content Tabs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6 bg-gray-800/50 border border-gray-700/50">
            <TabsTrigger
              value="kanban"
              className="data-[state=active]:bg-cyan-500/20 data-[state=active]:text-cyan-400"
            >
              <CalendarDays className="w-4 h-4 mr-2" />
              Weekly Planner
            </TabsTrigger>
            <TabsTrigger
              value="calendar"
              className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400"
            >
              <Calendar className="w-4 h-4 mr-2" />
              Curriculum Flow
            </TabsTrigger>
            <TabsTrigger
              value="library"
              className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-400"
            >
              <BookOpen className="w-4 h-4 mr-2" />
              Library
            </TabsTrigger>
            <TabsTrigger
              value="analytics"
              className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400"
            >
              <BarChart3 className="w-4 h-4 mr-2" />
              Analytics
            </TabsTrigger>
            <TabsTrigger
              value="templates"
              className="data-[state=active]:bg-orange-500/20 data-[state=active]:text-orange-400"
            >
              <Copy className="w-4 h-4 mr-2" />
              Templates
            </TabsTrigger>
            <TabsTrigger
              value="settings"
              className="data-[state=active]:bg-gray-500/20 data-[state=active]:text-gray-400"
            >
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </TabsTrigger>
          </TabsList>

          {/* Kanban Weekly Planner */}
          <TabsContent value="kanban" className="space-y-6">
            <KanbanWeeklyPlanner
              selectedChild={selectedChild}
              onChildChange={(child) => {
                // TODO: Update selected child in context
                console.log('Child changed:', child)
              }}
            />
          </TabsContent>

          {/* Curriculum Flow View */}
          <TabsContent value="calendar" className="space-y-6">
            <CurriculumFlowChart />
          </TabsContent>

          {/* Lesson Library */}
          <TabsContent value="library" className="space-y-6">
            <LessonLibrary />
          </TabsContent>

          {/* Analytics */}
          <TabsContent value="analytics" className="space-y-6">
            <PlanningAnalytics />
          </TabsContent>

          {/* Templates */}
          <TabsContent value="templates" className="space-y-6">
            <div className="text-center py-12">
              <Copy className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-400 mb-2">Planning Templates</h3>
              <p className="text-gray-500 mb-6">Create and manage reusable curriculum templates</p>
              <Button className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600">
                <Plus className="w-4 h-4 mr-2" />
                Create Template
              </Button>
            </div>
          </TabsContent>

          {/* Settings */}
          <TabsContent value="settings" className="space-y-6">
            <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
              <CardHeader>
                <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                  <Settings className="w-5 h-5 text-gray-400" />
                  Planner Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Notification Preferences */}
                <div className="space-y-4">
                  <h3 className="text-white font-medium">Notifications</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label className="text-gray-300">Lesson reminders</Label>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label className="text-gray-300">Approval needed alerts</Label>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label className="text-gray-300">Weekly progress reports</Label>
                      <Switch defaultChecked />
                    </div>
                  </div>
                </div>

                {/* Planning Preferences */}
                <div className="space-y-4">
                  <h3 className="text-white font-medium">Planning Preferences</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label className="text-gray-300">Auto-approve recommended lessons</Label>
                      <Switch />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label className="text-gray-300">Allow child self-scheduling</Label>
                      <Switch />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label className="text-gray-300">Skip weekends by default</Label>
                      <Switch defaultChecked />
                    </div>
                  </div>
                </div>

                {/* Content Filters */}
                <div className="space-y-4">
                  <h3 className="text-white font-medium">Content Filters</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-gray-300 text-sm">Maximum Difficulty</Label>
                      <select className="w-full mt-1 p-2 bg-gray-800 border border-gray-700 rounded-lg text-white">
                        <option value="beginner">Beginner</option>
                        <option value="intermediate">Intermediate</option>
                        <option value="advanced">Advanced</option>
                      </select>
                    </div>
                    <div>
                      <Label className="text-gray-300 text-sm">Daily Time Limit</Label>
                      <select className="w-full mt-1 p-2 bg-gray-800 border border-gray-700 rounded-lg text-white">
                        <option value="30">30 minutes</option>
                        <option value="60">1 hour</option>
                        <option value="90">1.5 hours</option>
                        <option value="120">2 hours</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end pt-4">
                  <Button className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600">
                    Save Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  )
}
