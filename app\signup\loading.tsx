"use client"

import { motion } from 'framer-motion'
import { Loader2 } from 'lucide-react'

export default function SignupLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        className="flex items-center gap-3 text-white"
      >
        <Loader2 className="w-8 h-8 animate-spin text-cyan-400" />
        <span className="text-xl font-space-grotesk">Loading signup...</span>
      </motion.div>
    </div>
  )
}
