'use client'

import { Atom } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import DNASequenceVisualizer from '../DNASequenceVisualizer'
import { DNAAnalysisProps } from './types'

export default function DNASequenceBreakdown({ 
  dnaFragment, 
  playerData, 
  systemStatus 
}: DNAAnalysisProps) {
  return (
    <Card className="bg-black/40 border-purple-500/30 backdrop-blur-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-purple-400">
          <Atom className="w-5 h-5" />
          Sequence Analysis
        </CardTitle>
      </CardHeader>
      <CardContent>
        <DNASequenceVisualizer
          sequence={dnaFragment}
          evolutionStage={playerData?.level || 1}
          ceLevel={(systemStatus?.consciousnessLevel || 0)}
          qsLevel={(systemStatus?.systemHealth || 0)}
        />
      </CardContent>
    </Card>
  )
}
