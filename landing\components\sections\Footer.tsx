"use client"

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  <PERSON>,
  Atom,
  Shield,
  Heart,
  Sparkles
} from 'lucide-react'

export function Footer() {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Quantum footer particles
  const footerParticles = Array.from({ length: 12 }, (_, i) => ({
    left: (i * 31 + 7) % 100,
    top: (i * 23 + 11) % 100,
    delay: i * 0.4,
    color: i % 3 === 0 ? '#22d3ee' : i % 3 === 1 ? '#8b5cf6' : '#fbbf24'
  }))
  return (
    <footer className="relative px-6 py-12 overflow-hidden">
      {/* Quantum footer background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-space-dark via-space-blue to-space-dark" />
        <div className="absolute inset-0 consciousness-wave opacity-20" />
        <div className="absolute inset-0 border-t-2 border-neural-cyan/30" />

        {/* Footer quantum particles */}
        {isClient && footerParticles.map((particle, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 rounded-full"
            style={{
              left: `${particle.left}%`,
              top: `${particle.top}%`,
              backgroundColor: particle.color
            }}
            animate={{
              opacity: [0, 0.6, 0],
              scale: [0.5, 1, 0.5],
              y: [0, -10, 0]
            }}
            transition={{
              duration: 4,
              repeat: Number.POSITIVE_INFINITY,
              delay: particle.delay,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Quantum footer glow orbs */}
        <div className="absolute top-1/4 left-1/4 w-24 h-24 bg-neural-cyan/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/3 right-1/4 w-32 h-32 bg-quantum-purple/10 rounded-full blur-3xl" />
        <div className="absolute top-1/2 right-1/3 w-20 h-20 bg-quantum-gold/10 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-6xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Quantum NanoHero Brand Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <div className="flex items-center gap-3 mb-4">
              <motion.div
                className="w-10 h-10 rounded-xl flex items-center justify-center border-2 relative"
                style={{
                  background: `linear-gradient(135deg, #22d3ee, #8b5cf6)`,
                  borderColor: '#22d3ee',
                  boxShadow: `0 0 20px #22d3ee40`
                }}
                whileHover={{ scale: 1.1, rotate: 360 }}
                transition={{ duration: 0.8 }}
              >
                <Atom className="w-5 h-5 text-white" />

                {/* Quantum brand glow */}
                <div
                  className="absolute inset-0 rounded-xl blur-lg opacity-50"
                  style={{ backgroundColor: '#22d3ee' }}
                />
              </motion.div>
              <h3
                className="text-xl font-bold text-white font-orbitron"
                style={{ textShadow: '0 0 15px #22d3ee60' }}
              >
                NanoHero
              </h3>
            </div>
            <p className="text-white/80 text-sm lg:text-base leading-relaxed font-space-grotesk">
              Empowering the next generation of <span className="text-neural-cyan font-semibold">quantum consciousness creators</span> through
              safe, engaging, and <span className="text-quantum-purple font-semibold">neural educational experiences</span>.
            </p>
          </motion.div>
          {/* Quantum Platform Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <h4
              className="font-semibold text-white mb-4 font-orbitron flex items-center gap-2"
              style={{ textShadow: '0 0 10px #8b5cf660' }}
            >
              <Brain className="w-4 h-4 text-quantum-purple" />
              Quantum Platform
            </h4>
            <ul className="space-y-3 text-sm lg:text-base">
              {[
                { name: "Neural Learning Nexus", href: "#", color: "#22d3ee" },
                { name: "Quantum Security Matrix", href: "#", color: "#8b5cf6" },
                { name: "Consciousness Gaming Sphere", href: "#", color: "#a855f7" },
                { name: "Quantum Creation Lab", href: "#", color: "#fbbf24" }
              ].map((link, index) => (
                <motion.li
                  key={link.name}
                  initial={{ opacity: 0, x: -10 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.2 + index * 0.1 }}
                >
                  <motion.a
                    href={link.href}
                    className="text-white/70 hover:text-white transition-all duration-300 font-space-grotesk flex items-center gap-2 group"
                    whileHover={{ x: 5 }}
                    style={{
                      textShadow: `0 0 10px ${link.color}40`
                    }}
                  >
                    <div
                      className="w-2 h-2 rounded-full group-hover:scale-125 transition-transform duration-300"
                      style={{ backgroundColor: link.color }}
                    />
                    {link.name}
                  </motion.a>
                </motion.li>
              ))}
            </ul>
          </motion.div>
          {/* Quantum Security Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <h4
              className="font-semibold text-white mb-4 font-orbitron flex items-center gap-2"
              style={{ textShadow: '0 0 10px #10b98160' }}
            >
              <Shield className="w-4 h-4 text-emerald-400" />
              Quantum Security
            </h4>
            <ul className="space-y-3 text-sm lg:text-base">
              {[
                { name: "Neural Privacy Protocols", href: "#", color: "#10b981" },
                { name: "Consciousness Terms of Service", href: "#", color: "#22d3ee" },
                { name: "Quantum COPPA Compliance", href: "#", color: "#8b5cf6" },
                { name: "Report Neural Content", href: "#", color: "#fbbf24" }
              ].map((link, index) => (
                <motion.li
                  key={link.name}
                  initial={{ opacity: 0, x: -10 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.3 + index * 0.1 }}
                >
                  <motion.a
                    href={link.href}
                    className="text-white/70 hover:text-white transition-all duration-300 font-space-grotesk flex items-center gap-2 group"
                    whileHover={{ x: 5 }}
                    style={{
                      textShadow: `0 0 10px ${link.color}40`
                    }}
                  >
                    <div
                      className="w-2 h-2 rounded-full group-hover:scale-125 transition-transform duration-300"
                      style={{ backgroundColor: link.color }}
                    />
                    {link.name}
                  </motion.a>
                </motion.li>
              ))}
            </ul>
          </motion.div>
          {/* Quantum Support Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h4
              className="font-semibold text-white mb-4 font-orbitron flex items-center gap-2"
              style={{ textShadow: '0 0 10px #22d3ee60' }}
            >
              <Sparkles className="w-4 h-4 text-neural-cyan" />
              Quantum Support
            </h4>
            <ul className="space-y-3 text-sm lg:text-base">
              {[
                { name: "Neural Help Center", href: "#", color: "#22d3ee" },
                { name: "Consciousness Parent Guide", href: "#", color: "#10b981" },
                { name: "Quantum Contact Us", href: "#", color: "#8b5cf6" },
                { name: "Neural Community Guidelines", href: "#", color: "#fbbf24" }
              ].map((link, index) => (
                <motion.li
                  key={link.name}
                  initial={{ opacity: 0, x: -10 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.4 + index * 0.1 }}
                >
                  <motion.a
                    href={link.href}
                    className="text-white/70 hover:text-white transition-all duration-300 font-space-grotesk flex items-center gap-2 group"
                    whileHover={{ x: 5 }}
                    style={{
                      textShadow: `0 0 10px ${link.color}40`
                    }}
                  >
                    <div
                      className="w-2 h-2 rounded-full group-hover:scale-125 transition-transform duration-300"
                      style={{ backgroundColor: link.color }}
                    />
                    {link.name}
                  </motion.a>
                </motion.li>
              ))}
            </ul>
          </motion.div>
        </div>

        {/* Quantum Footer Bottom */}
        <motion.div
          className="border-t-2 mt-12 pt-8 text-center relative"
          style={{ borderColor: '#22d3ee30' }}
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          {/* Quantum footer bottom particles */}
          {isClient && (
            <div className="absolute inset-0 overflow-hidden">
              {footerParticles.slice(0, 6).map((particle, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 h-1 rounded-full"
                  style={{
                    left: `${particle.left}%`,
                    top: `${particle.top}%`,
                    backgroundColor: particle.color
                  }}
                  animate={{
                    opacity: [0, 0.4, 0],
                    scale: [0.5, 1, 0.5]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Number.POSITIVE_INFINITY,
                    delay: particle.delay
                  }}
                />
              ))}
            </div>
          )}

          <div className="relative z-10">
            <motion.p
              className="text-white/70 text-sm lg:text-base font-space-grotesk mb-4"
              whileHover={{ scale: 1.02 }}
            >
              &copy; 2024 <span className="text-neural-cyan font-semibold font-orbitron">NanoHero</span>.
              All quantum rights reserved. Made with{' '}
              <motion.span
                className="inline-block"
                animate={{
                  scale: [1, 1.2, 1],
                  rotate: [0, 10, -10, 0]
                }}
                transition={{
                  duration: 2,
                  repeat: Number.POSITIVE_INFINITY,
                  ease: "easeInOut"
                }}
              >
                <Heart className="w-4 h-4 text-red-400 inline" />
              </motion.span>{' '}
              for <span className="text-quantum-purple font-semibold">young neural learners</span> everywhere.
            </motion.p>

            {/* Quantum status indicators */}
            <div className="flex justify-center items-center gap-6 text-xs lg:text-sm">
              <motion.div
                className="flex items-center gap-2"
                whileHover={{ scale: 1.05 }}
              >
                <motion.div
                  className="w-2 h-2 bg-emerald-400 rounded-full"
                  animate={{
                    opacity: [0.5, 1, 0.5],
                    scale: [1, 1.2, 1]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Number.POSITIVE_INFINITY
                  }}
                />
                <span className="text-emerald-400 font-medium font-space-grotesk">
                  Quantum Systems Online
                </span>
              </motion.div>

              <motion.div
                className="flex items-center gap-2"
                whileHover={{ scale: 1.05 }}
              >
                <motion.div
                  className="w-2 h-2 bg-neural-cyan rounded-full"
                  animate={{
                    opacity: [0.5, 1, 0.5],
                    scale: [1, 1.2, 1]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Number.POSITIVE_INFINITY,
                    delay: 0.5
                  }}
                />
                <span className="text-neural-cyan font-medium font-space-grotesk">
                  Neural Network Active
                </span>
              </motion.div>

              <motion.div
                className="flex items-center gap-2"
                whileHover={{ scale: 1.05 }}
              >
                <motion.div
                  className="w-2 h-2 bg-quantum-purple rounded-full"
                  animate={{
                    opacity: [0.5, 1, 0.5],
                    scale: [1, 1.2, 1]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Number.POSITIVE_INFINITY,
                    delay: 1
                  }}
                />
                <span className="text-quantum-purple font-medium font-space-grotesk">
                  Consciousness Ready
                </span>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  )
}
