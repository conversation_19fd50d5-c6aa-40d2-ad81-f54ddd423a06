'use client'

import { motion } from 'framer-motion'
import { Brain } from 'lucide-react'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  variant?: 'quantum' | 'neural' | 'simple'
  message?: string
  className?: string
}

export default function LoadingSpinner({ 
  size = 'md', 
  variant = 'quantum', 
  message,
  className = '' 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  }

  const messageSizes = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  }

  if (variant === 'simple') {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <motion.div
          className={`${sizeClasses[size]} border-2 border-neural-cyan/30 border-t-neural-cyan rounded-full`}
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        />
        {message && (
          <span className={`ml-2 text-white/60 ${messageSizes[size]}`}>
            {message}
          </span>
        )}
      </div>
    )
  }

  if (variant === 'neural') {
    return (
      <div className={`flex flex-col items-center justify-center ${className}`}>
        <div className="relative">
          <motion.div
            className={`${sizeClasses[size]} text-neural-cyan`}
            animate={{ 
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{ 
              duration: 1.5, 
              repeat: Infinity, 
              ease: "easeInOut" 
            }}
          >
            <Brain className="w-full h-full" />
          </motion.div>
          
          {/* Neural pulse rings */}
          <motion.div
            className="absolute inset-0 border-2 border-neural-cyan/30 rounded-full"
            animate={{
              scale: [1, 2, 1],
              opacity: [0.8, 0, 0.8]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeOut"
            }}
          />
          <motion.div
            className="absolute inset-0 border-2 border-neural-cyan/20 rounded-full"
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.6, 0, 0.6]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeOut",
              delay: 0.5
            }}
          />
        </div>
        
        {message && (
          <motion.p
            className={`mt-3 text-neural-cyan font-orbitron ${messageSizes[size]}`}
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            {message}
          </motion.p>
        )}
      </div>
    )
  }

  // Quantum variant (default)
  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <div className="relative">
        {/* Central quantum core */}
        <motion.div
          className={`${sizeClasses[size]} relative`}
          animate={{ rotate: 360 }}
          transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-neural-cyan via-quantum-purple to-flame-orange rounded-full opacity-80" />
          <motion.div
            className="absolute inset-1 bg-space-dark rounded-full"
            animate={{ 
              boxShadow: [
                '0 0 10px rgba(34, 211, 238, 0.5)',
                '0 0 20px rgba(139, 92, 246, 0.5)',
                '0 0 10px rgba(245, 158, 11, 0.5)',
                '0 0 10px rgba(34, 211, 238, 0.5)'
              ]
            }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </motion.div>

        {/* Orbiting particles */}
        {Array.from({ length: 3 }, (_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-neural-cyan rounded-full"
            style={{
              top: '50%',
              left: '50%',
              transformOrigin: `${20 + i * 8}px 0px`
            }}
            animate={{ rotate: 360 }}
            transition={{
              duration: 2 + i * 0.5,
              repeat: Infinity,
              ease: "linear"
            }}
          />
        ))}

        {/* Quantum field rings */}
        <motion.div
          className="absolute inset-0 border border-neural-cyan/20 rounded-full"
          style={{ 
            width: size === 'lg' ? '80px' : size === 'md' ? '60px' : '40px',
            height: size === 'lg' ? '80px' : size === 'md' ? '60px' : '40px',
            left: '50%',
            top: '50%',
            transform: 'translate(-50%, -50%)'
          }}
          animate={{ 
            scale: [1, 1.5, 1],
            opacity: [0.3, 0, 0.3]
          }}
          transition={{ 
            duration: 3, 
            repeat: Infinity, 
            ease: "easeOut" 
          }}
        />
        
        <motion.div
          className="absolute inset-0 border border-quantum-purple/20 rounded-full"
          style={{ 
            width: size === 'lg' ? '100px' : size === 'md' ? '80px' : '60px',
            height: size === 'lg' ? '100px' : size === 'md' ? '80px' : '60px',
            left: '50%',
            top: '50%',
            transform: 'translate(-50%, -50%)'
          }}
          animate={{ 
            scale: [1, 1.3, 1],
            opacity: [0.2, 0, 0.2]
          }}
          transition={{ 
            duration: 3, 
            repeat: Infinity, 
            ease: "easeOut",
            delay: 1
          }}
        />
      </div>

      {message && (
        <motion.div
          className={`mt-4 text-center ${messageSizes[size]}`}
          animate={{ opacity: [0.6, 1, 0.6] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <p className="text-neural-cyan font-orbitron font-bold">
            {message}
          </p>
          <div className="flex items-center justify-center gap-1 mt-1">
            <motion.div
              className="w-1 h-1 bg-neural-cyan rounded-full"
              animate={{ opacity: [0.3, 1, 0.3] }}
              transition={{ duration: 1, repeat: Infinity, delay: 0 }}
            />
            <motion.div
              className="w-1 h-1 bg-quantum-purple rounded-full"
              animate={{ opacity: [0.3, 1, 0.3] }}
              transition={{ duration: 1, repeat: Infinity, delay: 0.2 }}
            />
            <motion.div
              className="w-1 h-1 bg-flame-orange rounded-full"
              animate={{ opacity: [0.3, 1, 0.3] }}
              transition={{ duration: 1, repeat: Infinity, delay: 0.4 }}
            />
          </div>
        </motion.div>
      )}
    </div>
  )
}

// Component-specific loading states
export function DNALoadingSpinner() {
  return (
    <div className="h-full flex items-center justify-center">
      <LoadingSpinner 
        variant="neural" 
        message="Analyzing DNA patterns..." 
        size="lg"
      />
    </div>
  )
}

export function TimelineMapLoadingSpinner() {
  return (
    <div className="h-full flex items-center justify-center">
      <LoadingSpinner 
        variant="quantum" 
        message="Mapping consciousness field..." 
        size="lg"
      />
    </div>
  )
}

export function FeedLoadingSpinner() {
  return (
    <div className="h-full flex items-center justify-center">
      <LoadingSpinner 
        variant="simple" 
        message="Connecting to Timeline..." 
        size="md"
      />
    </div>
  )
}
