# NanoHero Font System

## Overview
The NanoHero platform uses a carefully curated set of Google Fonts to create a modern, tech-forward, and accessible typography system.

## Available Fonts

### 1. **Inter** - Primary Body Font
- **Use case**: Body text, paragraphs, general content
- **Weights**: 300, 400, 500, 600, 700
- **Class**: `font-inter`
- **Semantic class**: `font-primary`, `font-body`

### 2. **Syne** - Heading Font
- **Use case**: Section headings, titles, emphasis
- **Weights**: 400, 500, 600, 700, 800
- **Class**: `font-syne`
- **Semantic class**: `font-heading`, `font-title`

### 3. **Orbitron** - Display/Tech Font
- **Use case**: Hero titles, tech elements, futuristic UI
- **Weights**: 400, 500, 600, 700, 800, 900
- **Class**: `font-orbitron`
- **Semantic class**: `font-display`, `font-hero`, `font-code`

### 4. **Exo 2** - Tech/Modern Font
- **Use case**: Technical content, modern interfaces
- **Weights**: 300, 400, 500, 600, 700
- **Class**: `font-exo`
- **Semantic class**: `font-tech`

### 5. **Space Grotesk** - Modern Sans-Serif
- **Use case**: Buttons, captions, modern elements
- **Weights**: 300, 400, 500, 600, 700
- **Class**: `font-space-grotesk`
- **Semantic class**: `font-modern`, `font-subtitle`, `font-button`

## Semantic Font Classes

Use these semantic classes for consistent typography across the platform:

```css
/* Hero/Display Text */
.font-hero        /* Orbitron, black weight, wide tracking */

/* Headings */
.font-title       /* Syne, bold weight */
.font-subtitle    /* Space Grotesk, medium weight */

/* Body Text */
.font-body        /* Inter, normal weight */
.font-caption     /* Inter, light weight, small size */

/* Interactive Elements */
.font-button      /* Space Grotesk, semibold, wide tracking */

/* Technical/Code */
.font-code        /* Orbitron, monospace style */
```

## Usage Examples

### React Components
```jsx
// Hero section
<h1 className="font-hero text-6xl text-neural-cyan">
  NanoHero
</h1>

// Section title
<h2 className="font-title text-3xl text-white">
  Learn to Code
</h2>

// Subtitle
<h3 className="font-subtitle text-xl text-gray-300">
  Safe, Educational, Fun
</h3>

// Body text
<p className="font-body text-base text-gray-200">
  Join thousands of young learners...
</p>

// Button
<button className="font-button px-6 py-3 bg-neural-cyan">
  Get Started
</button>

// Code/Tech element
<span className="font-code text-sm text-quantum-purple">
  console.log("Hello World!")
</span>
```

### CSS Classes
```css
/* Direct font family usage */
.my-element {
  @apply font-orbitron font-bold text-2xl;
}

/* Semantic usage (recommended) */
.hero-title {
  @apply font-hero text-6xl;
}

.section-heading {
  @apply font-title text-3xl;
}
```

## Font Hierarchy

1. **Hero/Display**: Orbitron (font-hero)
2. **H1-H2**: Syne (font-title)
3. **H3-H4**: Space Grotesk (font-subtitle)
4. **Body**: Inter (font-body)
5. **Captions**: Inter Light (font-caption)
6. **Buttons**: Space Grotesk Semibold (font-button)
7. **Code**: Orbitron (font-code)

## Best Practices

1. **Use semantic classes** instead of direct font classes when possible
2. **Maintain hierarchy** - don't skip font levels
3. **Consider readability** - Inter for long-form content
4. **Use Orbitron sparingly** - for impact and tech elements only
5. **Test accessibility** - ensure sufficient contrast with font weights

## Migration Guide

### From Old System
Replace old font classes:
- `font-sans` → `font-inter` or `font-body`
- `font-mono` → `font-orbitron` or `font-code`
- Custom font imports → Use semantic classes

### Component Updates
Update existing components to use the new semantic classes for consistency.
