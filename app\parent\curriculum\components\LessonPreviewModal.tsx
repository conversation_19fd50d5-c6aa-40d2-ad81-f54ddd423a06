'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { 
  X,
  Clock,
  Users,
  Target,
  BookOpen,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Calendar
} from 'lucide-react'
import { Lesson, LessonAssignment, WeekDay } from '../types/curriculum'
import { useCurriculumPlanner } from './CurriculumPlannerProvider'
import { LESSON_LIBRARY } from '../data/lessonLibrary'

interface LessonPreviewModalProps {
  lesson?: Lesson | null
  lessonId?: string
  assignment?: LessonAssignment
  isOpen: boolean
  onClose: () => void
  onApprove?: (notes?: string) => void
  onReject?: (reason: string) => void
  onSchedule?: (date: Date) => void
  onAddToWeek?: (lesson: Lesson, day: WeekDay) => void
}

export function LessonPreviewModal({
  lesson: propLesson,
  lessonId,
  assignment,
  isOpen,
  onClose,
  onApprove,
  onReject,
  onSchedule,
  onAddToWeek
}: LessonPreviewModalProps) {
  const [parentNotes, setParentNotes] = useState('')
  const [rejectionReason, setRejectionReason] = useState('')
  const [showRejectionForm, setShowRejectionForm] = useState(false)
  const [selectedDate, _setSelectedDate] = useState(new Date())

  const { getSelectedChild } = useCurriculumPlanner()
  const selectedChild = getSelectedChild()

  // Get lesson from props or find by ID
  const lesson = propLesson || (lessonId ? LESSON_LIBRARY.find(l => l.id === lessonId) : null)

  if (!lesson) return null

  const handleApprove = () => {
    onApprove?.(parentNotes)
    onClose()
  }

  const handleReject = () => {
    if (rejectionReason.trim()) {
      onReject?.(rejectionReason)
      onClose()
    }
  }

  const handleSchedule = () => {
    onSchedule?.(selectedDate)
    onClose()
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'border-green-500/30 text-green-400'
      case 'intermediate': return 'border-yellow-500/30 text-yellow-400'
      case 'advanced': return 'border-red-500/30 text-red-400'
      default: return 'border-gray-500/30 text-gray-400'
    }
  }

  const _getStatusColor = (status?: string) => {
    switch (status) {
      case 'approved': return 'text-green-400'
      case 'pending': return 'text-yellow-400'
      case 'rejected': return 'text-red-400'
      case 'completed': return 'text-cyan-400'
      default: return 'text-gray-400'
    }
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-gray-900 border border-gray-700 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="sticky top-0 bg-gray-900 border-b border-gray-700 p-6 flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-2 bg-cyan-500/20 rounded-lg">
                  <BookOpen className="w-6 h-6 text-cyan-400" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-white">{lesson.title}</h2>
                  <p className="text-gray-400">{lesson.subject} • {lesson.category}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                {assignment && (
                  <Badge 
                    variant="outline" 
                    className={getDifficultyColor(assignment.status)}
                  >
                    {assignment.status}
                  </Badge>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                  className="text-gray-400 hover:text-white"
                >
                  <X className="w-5 h-5" />
                </Button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* Lesson Overview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="bg-gray-800/30 border-gray-700/30">
                  <CardContent className="p-4 text-center">
                    <Clock className="w-6 h-6 text-blue-400 mx-auto mb-2" />
                    <div className="text-lg font-bold text-white">{lesson.duration} min</div>
                    <div className="text-xs text-gray-400">Duration</div>
                  </CardContent>
                </Card>

                <Card className="bg-gray-800/30 border-gray-700/30">
                  <CardContent className="p-4 text-center">
                    <Users className="w-6 h-6 text-green-400 mx-auto mb-2" />
                    <div className="text-lg font-bold text-white">
                      {lesson.ageRange[0]}-{lesson.ageRange[1]}
                    </div>
                    <div className="text-xs text-gray-400">Age Range</div>
                  </CardContent>
                </Card>

                <Card className="bg-gray-800/30 border-gray-700/30">
                  <CardContent className="p-4 text-center">
                    <Target className="w-6 h-6 text-purple-400 mx-auto mb-2" />
                    <div className="text-lg font-bold text-white capitalize">{lesson.difficulty}</div>
                    <div className="text-xs text-gray-400">Difficulty</div>
                  </CardContent>
                </Card>
              </div>

              {/* Description */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-3">Description</h3>
                <p className="text-gray-300 leading-relaxed">{lesson.description}</p>
              </div>

              {/* Learning Objectives */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-3">Learning Objectives</h3>
                <div className="space-y-2">
                  {lesson.learningObjectives.map((objective, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-300">{objective}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Skills Developed */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-3">Skills Developed</h3>
                <div className="flex flex-wrap gap-2">
                  {lesson.skills.map((skill) => (
                    <Badge 
                      key={skill} 
                      variant="outline" 
                      className="border-cyan-500/30 text-cyan-400"
                    >
                      {skill.replace('-', ' ')}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Content Overview */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-3">What&apos;s Included</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium text-white mb-2">Materials</h4>
                    <ul className="space-y-1">
                      {lesson.content.materials.map((material, index) => (
                        <li key={index} className="text-gray-400 text-sm flex items-center gap-2">
                          <div className="w-1 h-1 bg-cyan-400 rounded-full" />
                          {material}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-white mb-2">Activities</h4>
                    <ul className="space-y-1">
                      {lesson.content.activities.map((activity, index) => (
                        <li key={index} className="text-gray-400 text-sm flex items-center gap-2">
                          <div className="w-1 h-1 bg-purple-400 rounded-full" />
                          {activity}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>

              {/* Prerequisites */}
              {lesson.prerequisites.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">Prerequisites</h3>
                  <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertTriangle className="w-4 h-4 text-yellow-400" />
                      <span className="text-yellow-400 font-medium">Required Lessons</span>
                    </div>
                    <p className="text-gray-300 text-sm">
                      This lesson requires completion of: {lesson.prerequisites.join(', ')}
                    </p>
                  </div>
                </div>
              )}

              {/* Parent Notes Section */}
              {(assignment?.status === 'pending' || !assignment) && (
                <div>
                  <Label className="text-white font-medium">Parent Notes (Optional)</Label>
                  <Textarea
                    placeholder="Add any notes or instructions for this lesson..."
                    value={parentNotes}
                    onChange={(e) => setParentNotes(e.target.value)}
                    className="mt-2 bg-gray-800/50 border-gray-700/50 text-white"
                    rows={3}
                  />
                </div>
              )}

              {/* Rejection Form */}
              {showRejectionForm && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  className="bg-red-500/10 border border-red-500/20 rounded-lg p-4"
                >
                  <Label className="text-red-400 font-medium">Reason for Rejection</Label>
                  <Textarea
                    placeholder="Please explain why you're rejecting this lesson..."
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    className="mt-2 bg-gray-800/50 border-red-500/30 text-white"
                    rows={3}
                  />
                </motion.div>
              )}
            </div>

            {/* Add to Week Section (for Kanban mode) */}
            {onAddToWeek && (
              <div>
                <h3 className="text-lg font-semibold text-white mb-3">Add to Weekly Plan</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2">
                  {[
                    { day: 'monday' as WeekDay, label: 'Monday' },
                    { day: 'tuesday' as WeekDay, label: 'Tuesday' },
                    { day: 'wednesday' as WeekDay, label: 'Wednesday' },
                    { day: 'thursday' as WeekDay, label: 'Thursday' },
                    { day: 'friday' as WeekDay, label: 'Friday' },
                    { day: 'saturday' as WeekDay, label: 'Saturday' },
                    { day: 'sunday' as WeekDay, label: 'Sunday' }
                  ].map(({ day, label }) => (
                    <Button
                      key={day}
                      onClick={() => onAddToWeek(lesson, day)}
                      className="bg-gray-800/50 hover:bg-cyan-500/20 border border-gray-700/50 hover:border-cyan-500/30 text-gray-300 hover:text-cyan-400 transition-all duration-200"
                    >
                      <Calendar className="w-4 h-4 mr-2" />
                      {label}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* Footer Actions */}
            <div className="sticky bottom-0 bg-gray-900 border-t border-gray-700 p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Button
                    variant="outline"
                    className="border-gray-600 text-gray-300 hover:bg-gray-700/50"
                    onClick={onClose}
                  >
                    Close
                  </Button>

                  {selectedChild && (
                    <span className="text-sm text-gray-400">
                      Planning for {selectedChild.name}
                    </span>
                  )}
                </div>

                <div className="flex items-center gap-3">
                  {assignment?.status === 'pending' ? (
                    <>
                      <Button
                        variant="outline"
                        className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                        onClick={() => setShowRejectionForm(!showRejectionForm)}
                      >
                        <XCircle className="w-4 h-4 mr-2" />
                        Reject
                      </Button>

                      {showRejectionForm ? (
                        <Button
                          className="bg-red-500 hover:bg-red-600"
                          onClick={handleReject}
                          disabled={!rejectionReason.trim()}
                        >
                          Confirm Rejection
                        </Button>
                      ) : (
                        <Button
                          className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
                          onClick={handleApprove}
                        >
                          <CheckCircle className="w-4 h-4 mr-2" />
                          Approve Lesson
                        </Button>
                      )}
                    </>
                  ) : !onAddToWeek && (
                    <Button
                      className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600"
                      onClick={handleSchedule}
                    >
                      <Calendar className="w-4 h-4 mr-2" />
                      Schedule Lesson
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
