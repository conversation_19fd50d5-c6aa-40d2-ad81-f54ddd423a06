"use client"

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  <PERSON><PERSON><PERSON>,
  Trophy,
  Award,
  Users,
  Eye,
  Heart,
  Brain,
  Zap,
  Atom,
  Globe
} from 'lucide-react'
import { communityHighlights } from '../../data/constants'

export function CommunityHighlightsSection() {
  const [isClient, setIsClient] = useState(false)
  const [activeCategory, setActiveCategory] = useState("All")

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Quantum consciousness particles
  const consciousnessParticles = Array.from({ length: 20 }, (_, i) => ({
    left: (i * 19 + 5) % 100,
    top: (i * 23 + 7) % 100,
    delay: i * 0.3,
    color: i % 4 === 0 ? '#22d3ee' : i % 4 === 1 ? '#8b5cf6' : i % 4 === 2 ? '#fbbf24' : '#10b981'
  }))
  // Enhanced quantum consciousness type mapping
  const getQuantumTypeIcon = (type: string) => {
    switch (type) {
      case "tutorial": return Brain
      case "tournament": return Trophy
      case "badge": return Award
      case "mentoring": return Users
      default: return Atom
    }
  }

  const getQuantumTypeColor = (type: string) => {
    switch (type) {
      case "tutorial": return "#22d3ee" // Neural cyan
      case "tournament": return "#fbbf24" // Quantum gold
      case "badge": return "#8b5cf6" // Quantum purple
      case "mentoring": return "#10b981" // Emerald
      default: return "#6b7280" // Gray
    }
  }

  const _getQuantumTypeGradient = (type: string) => {
    switch (type) {
      case "tutorial": return "from-neural-cyan to-cyan-400"
      case "tournament": return "from-quantum-gold to-yellow-400"
      case "badge": return "from-quantum-purple to-purple-400"
      case "mentoring": return "from-emerald-500 to-green-400"
      default: return "from-gray-500 to-gray-600"
    }
  }

  // Enhanced quantum categories
  const quantumCategories = [
    { id: "All", label: "All Consciousness", icon: Globe },
    { id: "Tutorials", label: "Neural Learning", icon: Brain },
    { id: "Tournaments", label: "Quantum Competitions", icon: Trophy },
    { id: "Badges", label: "Consciousness Badges", icon: Award },
    { id: "Mentoring", label: "Neural Mentoring", icon: Users }
  ]

  return (
    <section className="relative px-6 py-20 overflow-hidden">
      {/* Quantum consciousness background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-space-dark via-space-blue to-space-dark" />
        <div className="absolute inset-0 consciousness-wave opacity-20" />

        {/* Consciousness field particles */}
        {isClient && consciousnessParticles.map((particle, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 rounded-full"
            style={{
              left: `${particle.left}%`,
              top: `${particle.top}%`,
              backgroundColor: particle.color
            }}
            animate={{
              opacity: [0, 0.8, 0],
              scale: [0.5, 1.2, 0.5],
              y: [0, -15, 0]
            }}
            transition={{
              duration: 4,
              repeat: Number.POSITIVE_INFINITY,
              delay: particle.delay,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Quantum community glow orbs */}
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-neural-cyan/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/3 right-1/4 w-40 h-40 bg-quantum-purple/10 rounded-full blur-3xl" />
        <div className="absolute top-1/2 right-1/3 w-24 h-24 bg-quantum-gold/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 left-1/3 w-36 h-36 bg-emerald-500/10 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-6xl mx-auto">
        {/* Quantum Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.h2
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 font-orbitron"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <span className="bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold bg-clip-text text-transparent">
              Consciousness
            </span>
            <br />
            <span className="bg-gradient-to-r from-white via-gray-200 to-white bg-clip-text text-transparent text-3xl md:text-4xl lg:text-5xl">
              Community Nexus
            </span>
          </motion.h2>

          <motion.p
            className="text-lg md:text-xl text-white/80 max-w-4xl mx-auto font-space-grotesk leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3, duration: 0.8 }}
          >
            Celebrating <span className="text-neural-cyan font-semibold">quantum achievements</span>,
            consciousness tournaments, and amazing neural creations from our
            <span className="text-quantum-purple font-semibold"> NanoHero</span> community
          </motion.p>

          {/* Quantum community decoration */}
          <motion.div
            className="flex justify-center items-center gap-4 mt-6"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.6 }}
          >
            <div className="h-px w-16 bg-gradient-to-r from-transparent to-neural-cyan" />
            <Users className="w-5 h-5 text-quantum-gold" />
            <div className="h-px w-16 bg-gradient-to-l from-transparent to-quantum-purple" />
          </motion.div>
        </motion.div>

        {/* Quantum Consciousness Categories */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {quantumCategories.map((category, index) => (
            <motion.button
              key={category.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActiveCategory(category.id)}
              className={`relative px-6 py-3 rounded-xl text-sm font-medium transition-all duration-300 font-space-grotesk flex items-center gap-2 ${
                activeCategory === category.id
                  ? "text-white"
                  : "text-white/70 hover:text-white"
              }`}
              style={{
                background: activeCategory === category.id
                  ? `linear-gradient(135deg, rgba(34, 211, 238, 0.2), rgba(139, 92, 246, 0.2))`
                  : `rgba(0, 0, 0, 0.4)`,
                border: activeCategory === category.id
                  ? `2px solid rgba(34, 211, 238, 0.4)`
                  : `1px solid rgba(255, 255, 255, 0.1)`,
                boxShadow: activeCategory === category.id
                  ? `0 0 20px rgba(34, 211, 238, 0.3)`
                  : 'none'
              }}
            >
              {/* Quantum glow effect for active category */}
              {activeCategory === category.id && (
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-neural-cyan/20 to-quantum-purple/20 blur-sm" />
              )}

              <category.icon className="w-4 h-4 relative z-10" />
              <span className="relative z-10">{category.label}</span>

              {/* Quantum particles for active category */}
              {activeCategory === category.id && isClient && (
                <div className="absolute inset-0 overflow-hidden rounded-xl">
                  {[...Array(3)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-1 h-1 bg-neural-cyan rounded-full"
                      style={{
                        left: `${20 + i * 30}%`,
                        top: `${30 + i * 20}%`,
                      }}
                      animate={{
                        opacity: [0, 1, 0],
                        scale: [0.5, 1, 0.5]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Number.POSITIVE_INFINITY,
                        delay: i * 0.3
                      }}
                    />
                  ))}
                </div>
              )}
            </motion.button>
          ))}
        </div>

        {/* Quantum Consciousness Highlights Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {communityHighlights.map((highlight, index) => {
            const QuantumTypeIcon = getQuantumTypeIcon(highlight.type)
            const quantumColor = getQuantumTypeColor(highlight.type)

            return (
              <motion.div
                key={highlight.user}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.15 }}
                whileHover={{ scale: 1.03, y: -8 }}
                className="group"
              >
                <Card
                  className="quantum-glass border-2 h-full relative overflow-hidden group-hover:border-opacity-60 transition-all duration-500"
                  style={{
                    borderColor: `${quantumColor}40`,
                    background: `linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)`,
                    boxShadow: `0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px ${quantumColor}20, inset 0 1px 0 rgba(255, 255, 255, 0.1)`
                  }}
                >
                  {/* Quantum glow effect on hover */}
                  <div
                    className="absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"
                    style={{
                      background: `linear-gradient(135deg, ${quantumColor}30, ${quantumColor}10)`,
                      boxShadow: `0 0 40px ${quantumColor}40`
                    }}
                  />

                  {/* Quantum consciousness particles */}
                  {isClient && (
                    <div className="absolute inset-0 overflow-hidden rounded-xl">
                      {[...Array(3)].map((_, i) => (
                        <motion.div
                          key={i}
                          className="absolute w-1 h-1 rounded-full"
                          style={{
                            left: `${20 + i * 25}%`,
                            top: `${15 + i * 20}%`,
                            backgroundColor: quantumColor
                          }}
                          animate={{
                            opacity: [0, 0.8, 0],
                            scale: [0.5, 1.2, 0.5],
                            rotate: 360
                          }}
                          transition={{
                            duration: 3,
                            repeat: Number.POSITIVE_INFINITY,
                            delay: i * 0.5
                          }}
                        />
                      ))}
                    </div>
                  )}

                  {/* Quantum type indicator */}
                  <div className="absolute top-3 right-3 z-20">
                    <motion.div
                      className="relative p-2 rounded-xl border-2"
                      style={{
                        background: `linear-gradient(135deg, ${quantumColor}20, ${quantumColor}10)`,
                        borderColor: quantumColor,
                        boxShadow: `0 0 15px ${quantumColor}40`
                      }}
                      whileHover={{ scale: 1.1, rotate: 360 }}
                      transition={{ duration: 0.8 }}
                    >
                      <QuantumTypeIcon className="w-4 h-4 text-white" />

                      {/* Icon quantum glow */}
                      <div
                        className="absolute inset-0 rounded-xl blur-lg opacity-50"
                        style={{ backgroundColor: quantumColor }}
                      />
                    </motion.div>
                  </div>

                  <CardContent className="p-6 relative z-10">
                    <div className="flex items-center gap-3 mb-4">
                      <motion.div
                        whileHover={{ scale: 1.1 }}
                        transition={{ type: "spring", stiffness: 300 }}
                      >
                        <Avatar
                          className="w-12 h-12 border-2"
                          style={{ borderColor: `${quantumColor}60` }}
                        >
                          <AvatarImage src={highlight.avatar || "/placeholder.svg"} />
                          <AvatarFallback
                            className="text-white font-orbitron"
                            style={{
                              background: `linear-gradient(135deg, ${quantumColor}, ${quantumColor}80)`,
                              boxShadow: `0 0 15px ${quantumColor}40`
                            }}
                          >
                            {highlight.user.slice(0, 2)}
                          </AvatarFallback>
                        </Avatar>
                      </motion.div>
                      <div className="flex-1">
                        <h4
                          className="font-semibold text-white group-hover:text-opacity-90 transition-all duration-300 font-orbitron"
                          style={{
                            textShadow: `0 0 15px ${quantumColor}60`
                          }}
                        >
                          {highlight.user}
                        </h4>
                        <div className="flex items-center gap-2">
                          <Badge
                            className="text-xs font-medium"
                            style={{
                              backgroundColor: `${quantumColor}20`,
                              borderColor: `${quantumColor}40`,
                              color: quantumColor
                            }}
                          >
                            {highlight.level}
                          </Badge>
                          <p className="text-xs text-white/60 font-space-grotesk">{highlight.time}</p>
                        </div>
                      </div>
                    </div>

                    <p className="text-white/80 mb-4 leading-relaxed text-sm font-space-grotesk">
                      {highlight.achievement}
                    </p>

                    {/* Quantum Badge Display */}
                    <div className="mb-4">
                      <motion.div
                        className="flex items-center gap-2 p-3 quantum-glass rounded-xl border"
                        style={{
                          background: `linear-gradient(135deg, ${quantumColor}10, ${quantumColor}05)`,
                          borderColor: `${quantumColor}30`,
                          boxShadow: `0 0 10px ${quantumColor}20`
                        }}
                        whileHover={{ scale: 1.02 }}
                      >
                        <Award
                          className="w-4 h-4"
                          style={{ color: quantumColor }}
                        />
                        <span
                          className="text-xs font-medium font-space-grotesk"
                          style={{ color: quantumColor }}
                        >
                          {highlight.badge}
                        </span>
                      </motion.div>
                    </div>

                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-3">
                        <motion.div
                          className="flex items-center gap-1"
                          whileHover={{ scale: 1.1 }}
                        >
                          <Eye className="w-4 h-4 text-white/60" />
                          <span className="text-xs text-white/70 font-space-grotesk">{highlight.views}</span>
                        </motion.div>
                        <motion.div
                          className="flex items-center gap-1"
                          whileHover={{ scale: 1.1 }}
                        >
                          <Heart className="w-4 h-4 text-red-400" />
                          <span className="text-xs text-white/70 font-space-grotesk">{highlight.likes}</span>
                        </motion.div>
                      </div>
                      <motion.div
                        whileHover={{ scale: 1.05, x: 3 }}
                        className="cursor-pointer text-xs font-medium font-space-grotesk flex items-center gap-1"
                        style={{ color: quantumColor }}
                      >
                        <span>Explore</span>
                        <Zap className="w-3 h-3" />
                      </motion.div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </div>

        {/* Quantum Consciousness Community Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="mt-20"
        >
          <motion.h3
            className="text-2xl lg:text-3xl font-bold text-white mb-8 text-center font-orbitron"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
          >
            <span className="bg-gradient-to-r from-neural-cyan to-quantum-purple bg-clip-text text-transparent">
              Quantum Community
            </span>
            <span className="text-white/80"> Consciousness Metrics</span>
          </motion.h3>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {[
              {
                label: "Neural Creators",
                value: "2,847",
                icon: Brain,
                quantumColor: "#22d3ee",
                description: "Active consciousness builders"
              },
              {
                label: "Quantum Tutorials",
                value: "1,205",
                icon: BookOpen,
                quantumColor: "#10b981",
                description: "Neural learning experiences shared"
              },
              {
                label: "Consciousness Tournaments",
                value: "89",
                icon: Trophy,
                quantumColor: "#fbbf24",
                description: "Quantum competitions completed"
              },
              {
                label: "Neural Badges",
                value: "5,632",
                icon: Award,
                quantumColor: "#8b5cf6",
                description: "Consciousness achievements unlocked"
              },
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.15 }}
                whileHover={{ scale: 1.05, y: -5 }}
                className="text-center quantum-glass rounded-2xl p-6 border-2 relative overflow-hidden group"
                style={{
                  borderColor: `${stat.quantumColor}40`,
                  background: `linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)`,
                  boxShadow: `0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px ${stat.quantumColor}20`
                }}
              >
                {/* Quantum glow effect on hover */}
                <div
                  className="absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-2xl"
                  style={{
                    background: `linear-gradient(135deg, ${stat.quantumColor}30, ${stat.quantumColor}10)`,
                    boxShadow: `0 0 40px ${stat.quantumColor}40`
                  }}
                />

                {/* Quantum stat particles */}
                {isClient && (
                  <div className="absolute inset-0 overflow-hidden rounded-2xl">
                    {[...Array(2)].map((_, i) => (
                      <motion.div
                        key={i}
                        className="absolute w-1 h-1 rounded-full"
                        style={{
                          left: `${30 + i * 40}%`,
                          top: `${20 + i * 30}%`,
                          backgroundColor: stat.quantumColor
                        }}
                        animate={{
                          opacity: [0, 0.8, 0],
                          scale: [0.5, 1.2, 0.5],
                          y: [0, -10, 0]
                        }}
                        transition={{
                          duration: 3,
                          repeat: Number.POSITIVE_INFINITY,
                          delay: i * 0.5
                        }}
                      />
                    ))}
                  </div>
                )}

                <div className="relative z-10">
                  <motion.div
                    className="relative p-3 rounded-xl border-2 mx-auto mb-4 w-fit"
                    style={{
                      background: `linear-gradient(135deg, ${stat.quantumColor}20, ${stat.quantumColor}10)`,
                      borderColor: stat.quantumColor,
                      boxShadow: `0 0 20px ${stat.quantumColor}40`
                    }}
                    whileHover={{ rotate: 360, scale: 1.1 }}
                    transition={{ duration: 0.8 }}
                  >
                    <stat.icon
                      className="w-8 h-8 text-white"
                    />

                    {/* Icon quantum glow */}
                    <div
                      className="absolute inset-0 rounded-xl blur-lg opacity-50"
                      style={{ backgroundColor: stat.quantumColor }}
                    />
                  </motion.div>

                  <div
                    className="text-3xl font-bold text-white mb-2 font-orbitron"
                    style={{ textShadow: `0 0 20px ${stat.quantumColor}60` }}
                  >
                    {stat.value}
                  </div>
                  <div
                    className="text-sm font-medium mb-1 font-space-grotesk"
                    style={{ color: stat.quantumColor }}
                  >
                    {stat.label}
                  </div>
                  <div className="text-xs text-white/60 font-space-grotesk">
                    {stat.description}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}
