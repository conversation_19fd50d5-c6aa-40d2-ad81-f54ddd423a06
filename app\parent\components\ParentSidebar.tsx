'use client'

import { useState, useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import {
  BarChart3,
  MessageSquare,
  LogOut,
  ChevronLeft,
  ChevronRight,
  Users,
  BookOpen,
  Eye
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { useAuth } from '@/contexts/AuthContext'

interface ParentSidebarProps {
  className?: string
  onCollapseChange?: (collapsed: boolean) => void
}

interface MenuItem {
  id: string
  label: string
  icon: any
  badge?: string
  route?: string
  action?: () => void
}

export function ParentSidebar({ className, onCollapseChange }: ParentSidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [activeItem, setActiveItem] = useState('overview')

  const router = useRouter()
  const pathname = usePathname()
  const { logout, userProfile } = useAuth()

  // Update active item based on current pathname
  useEffect(() => {
    const currentPath = pathname.split('/').pop() || 'overview'
    setActiveItem(currentPath)
  }, [pathname])

  const toggleCollapse = () => {
    const newCollapsed = !isCollapsed
    setIsCollapsed(newCollapsed)
    onCollapseChange?.(newCollapsed)
  }

  const menuItems: MenuItem[] = [
    {
      id: 'child-stats',
      label: 'Child Stats',
      icon: BarChart3,
      badge: 'Stats',
      route: '/parent'
    },
    {
      id: 'profile',
      label: 'Parent Profile',
      icon: Users,
      badge: 'Profile',
      route: '/parent/profile'
    },
    {
      id: 'curriculum',
      label: 'Interactive Curriculum',
      icon: BookOpen,
      badge: 'Learn',
      route: '/parent/curriculum'
    },
    {
      id: 'support',
      label: 'Support',
      icon: MessageSquare,
      badge: 'Help',
      route: '/parent/support'
    }
  ]

  const bottomMenuItems: MenuItem[] = [
    {
      id: 'logout',
      label: 'Sign Out',
      icon: LogOut,
      action: () => logout()
    }
  ]

  const handleItemClick = (item: MenuItem) => {
    setActiveItem(item.id)
    if (item.route) {
      router.push(item.route)
    } else if (item.action) {
      item.action()
    }
  }

  return (
    <motion.div
      initial={{ x: -280 }}
      animate={{ x: 0, width: isCollapsed ? 80 : 280 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className={`fixed left-0 top-0 h-full bg-black/40 backdrop-blur-xl border-r border-gray-800/50 z-30 ${className}`}
    >
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="p-6 border-b border-gray-800/50">
          <div className="flex items-center justify-between">
            <AnimatePresence>
              {!isCollapsed && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="flex items-center gap-3"
                >
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-green-500 rounded-lg flex items-center justify-center">
                    <Eye className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <h2 className="text-white font-bold font-space-grotesk font-orbitron">NanoHero</h2>
                    <p className="text-xs text-gray-400">Parent Dashboard</p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleCollapse}
              className="text-gray-400 hover:text-white hover:bg-gray-800/50"
            >
              {isCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
            </Button>
          </div>
        </div>

        {/* User Info */}
        <div className="p-4 border-b border-gray-800/50">
          <AnimatePresence>
            {!isCollapsed && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="text-center"
              >
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-green-500 rounded-full mx-auto mb-2 flex items-center justify-center">
                  <Users className="w-6 h-6 text-white" />
                </div>
                <p className="text-white font-medium text-sm">{userProfile?.full_name || 'Parent'}</p>
                <p className="text-gray-400 text-xs">
                  {userProfile?.children_ids?.length || 0} child{(userProfile?.children_ids?.length || 0) !== 1 ? 'ren' : ''} monitored
                </p>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
          {menuItems.map((item, index) => (
            <motion.button
              key={item.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.05 }}
              onClick={() => handleItemClick(item)}
              className={`w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-300 group ${
                activeItem === item.id
                  ? "bg-gradient-to-r from-blue-500/20 to-green-500/20 border border-blue-500/30 shadow-lg shadow-blue-500/20"
                  : "hover:bg-gray-800/50"
              }`}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div
                className={`p-2 rounded-lg bg-gradient-to-r from-blue-500 to-green-500 ${
                  activeItem === item.id ? "shadow-lg" : "opacity-70 group-hover:opacity-100"
                }`}
              >
                <item.icon className="w-4 h-4 text-white" />
              </div>
              
              <AnimatePresence>
                {!isCollapsed && (
                  <motion.div
                    initial={{ opacity: 0, width: 0 }}
                    animate={{ opacity: 1, width: "auto" }}
                    exit={{ opacity: 0, width: 0 }}
                    className="flex-1 text-left"
                  >
                    <span className="text-white font-medium text-sm">{item.label}</span>
                    {item.badge && (
                      <span className="block text-xs text-gray-400">{item.badge}</span>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.button>
          ))}
        </nav>

        {/* Bottom Menu */}
        <div className="p-4 border-t border-gray-800/50 space-y-2">
          {bottomMenuItems.map((item) => (
            <motion.button
              key={item.id}
              onClick={() => handleItemClick(item)}
              className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-800/50 transition-all duration-300 group"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="p-2 rounded-lg bg-gray-700 opacity-70 group-hover:opacity-100">
                <item.icon className="w-4 h-4 text-white" />
              </div>
              
              <AnimatePresence>
                {!isCollapsed && (
                  <motion.span
                    initial={{ opacity: 0, width: 0 }}
                    animate={{ opacity: 1, width: "auto" }}
                    exit={{ opacity: 0, width: 0 }}
                    className="text-white font-medium text-sm"
                  >
                    {item.label}
                  </motion.span>
                )}
              </AnimatePresence>
            </motion.button>
          ))}
        </div>
      </div>
    </motion.div>
  )
}
