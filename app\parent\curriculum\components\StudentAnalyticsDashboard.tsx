'use client'

import React, { useState, useMemo } from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  TrendingUp,
  TrendingDown,
  Target,
  Clock,
  Brain,
  Star,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  PieChart,
  Activity,
  Zap,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react'

import { ChildProfile } from '../types/curriculum'
import { <PERSON><PERSON><PERSON> } from './RadarChart'

interface StudentAnalyticsDashboardProps {
  selectedChild: ChildProfile | null
  weeklyPlan?: any
}

// Mock analytics data - in real app this would come from backend
const generateMockAnalytics = (child: ChildProfile | null) => {
  if (!child) return null

  return {
    weeklyStats: {
      totalTime: 285, // minutes this week
      completedLessons: 12,
      averageScore: 87,
      streakDays: 5,
      xpEarned: 450,
      weeklyGoal: 300, // target minutes per week
      focusTime: 180, // deep focus minutes
      engagementRate: 92 // percentage
    },
    subjectPerformance: [
      {
        subject: 'Quantum Physics',
        timeSpent: 120,
        avgScore: 92,
        improvement: 8,
        lessons: 4,
        mastery: 85,
        difficulty: 'advanced',
        engagement: 95,
        needsMore: false,
        recommended: 'maintain'
      },
      {
        subject: 'Critical Thinking',
        timeSpent: 90,
        avgScore: 88,
        improvement: 5,
        lessons: 3,
        mastery: 78,
        difficulty: 'intermediate',
        engagement: 88,
        needsMore: false,
        recommended: 'increase'
      },
      {
        subject: 'DNA Biology',
        timeSpent: 75,
        avgScore: 85,
        improvement: -2,
        lessons: 3,
        mastery: 72,
        difficulty: 'intermediate',
        engagement: 75,
        needsMore: true,
        recommended: 'decrease'
      },
      {
        subject: 'Cybersecurity',
        timeSpent: 45,
        avgScore: 79,
        improvement: 12,
        lessons: 2,
        mastery: 65,
        difficulty: 'beginner',
        engagement: 82,
        needsMore: true,
        recommended: 'increase'
      },
      {
        subject: 'Emotional Intelligence',
        timeSpent: 30,
        avgScore: 94,
        improvement: 3,
        lessons: 1,
        mastery: 88,
        difficulty: 'beginner',
        engagement: 96,
        needsMore: false,
        recommended: 'maintain'
      },
      {
        subject: 'Creative Thinking',
        timeSpent: 15,
        avgScore: 68,
        improvement: -5,
        lessons: 1,
        mastery: 45,
        difficulty: 'beginner',
        engagement: 65,
        needsMore: true,
        recommended: 'increase'
      }
    ],
    skillProgress: [
      { skill: 'Analytical Thinking', current: 85, target: 90, trend: 'up', priority: 'medium' },
      { skill: 'Problem Solving', current: 78, target: 85, trend: 'up', priority: 'high' },
      { skill: 'Scientific Method', current: 82, target: 80, trend: 'stable', priority: 'low' },
      { skill: 'Creative Thinking', current: 65, target: 75, trend: 'down', priority: 'high' },
      { skill: 'Communication', current: 70, target: 80, trend: 'up', priority: 'medium' },
      { skill: 'Digital Literacy', current: 88, target: 85, trend: 'stable', priority: 'low' }
    ],
    recommendations: [
      {
        type: 'increase',
        subject: 'Creative Thinking',
        reason: 'Significantly below target and showing decline. Needs immediate attention.',
        priority: 'high',
        suggestedTime: '+20 min/week',
        action: 'Add more creative problem-solving exercises'
      },
      {
        type: 'increase',
        subject: 'Cybersecurity',
        reason: 'Strong improvement trend, ready for more advanced concepts.',
        priority: 'medium',
        suggestedTime: '+15 min/week',
        action: 'Introduce intermediate security concepts'
      },
      {
        type: 'decrease',
        subject: 'DNA Biology',
        reason: 'Showing signs of cognitive overload with declining performance.',
        priority: 'medium',
        suggestedTime: '-10 min/week',
        action: 'Focus on review and consolidation'
      },
      {
        type: 'maintain',
        subject: 'Quantum Physics',
        reason: 'Excellent progress and high engagement. Current pace is optimal.',
        priority: 'low',
        suggestedTime: 'Keep current',
        action: 'Continue with current difficulty level'
      }
    ],
    learningInsights: {
      bestPerformanceTime: '10:00 AM - 12:00 PM',
      preferredLearningStyle: 'Visual + Interactive',
      attentionSpan: '25 minutes',
      motivationFactors: ['Gamification', 'Progress Tracking', 'Peer Comparison'],
      challengeAreas: ['Abstract Concepts', 'Long Reading Sessions'],
      strengths: ['Pattern Recognition', 'Logical Reasoning', 'Visual Processing']
    }
  }
}

export function StudentAnalyticsDashboard({ selectedChild, weeklyPlan: _weeklyPlan }: StudentAnalyticsDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview')
  
  const analytics = useMemo(() => generateMockAnalytics(selectedChild), [selectedChild])

  if (!selectedChild || !analytics) {
    return (
      <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl mb-6">
        <CardContent className="p-6 text-center">
          <Brain className="w-12 h-12 text-gray-600 mx-auto mb-3" />
          <h3 className="text-lg font-medium text-gray-400 mb-2">No Student Selected</h3>
          <p className="text-gray-500">Select a child to view their learning analytics and recommendations</p>
        </CardContent>
      </Card>
    )
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <ArrowUp className="w-4 h-4 text-green-400" />
      case 'down': return <ArrowDown className="w-4 h-4 text-red-400" />
      default: return <Minus className="w-4 h-4 text-gray-400" />
    }
  }

  const getRecommendationColor = (type: string) => {
    switch (type) {
      case 'increase': return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'decrease': return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'maintain': return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500/20 text-red-400'
      case 'medium': return 'bg-yellow-500/20 text-yellow-400'
      case 'low': return 'bg-green-500/20 text-green-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="mb-6"
    >
      <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
        <CardHeader>
          <CardTitle className="text-white font-space-grotesk flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-lg">
              <BarChart3 className="w-5 h-5 text-cyan-400" />
            </div>
            {selectedChild.name}&apos;s Learning Analytics
            <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
              Week {new Date().getWeek()} Progress
            </Badge>
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center p-4 bg-gray-800/30 rounded-lg">
              <Clock className="w-6 h-6 text-blue-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">{Math.round(analytics.weeklyStats.totalTime / 60)}h</div>
              <div className="text-xs text-gray-400">Time This Week</div>
            </div>
            
            <div className="text-center p-4 bg-gray-800/30 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">{analytics.weeklyStats.completedLessons}</div>
              <div className="text-xs text-gray-400">Lessons Done</div>
            </div>
            
            <div className="text-center p-4 bg-gray-800/30 rounded-lg">
              <Star className="w-6 h-6 text-yellow-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">{analytics.weeklyStats.averageScore}%</div>
              <div className="text-xs text-gray-400">Avg Score</div>
            </div>
            
            <div className="text-center p-4 bg-gray-800/30 rounded-lg">
              <Activity className="w-6 h-6 text-purple-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">{analytics.weeklyStats.streakDays}</div>
              <div className="text-xs text-gray-400">Day Streak</div>
            </div>
            
            <div className="text-center p-4 bg-gray-800/30 rounded-lg">
              <Zap className="w-6 h-6 text-orange-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">{analytics.weeklyStats.xpEarned}</div>
              <div className="text-xs text-gray-400">XP Earned</div>
            </div>
          </div>

          {/* Detailed Analytics Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3 bg-gray-800/50 border border-gray-700/50">
              <TabsTrigger 
                value="overview" 
                className="data-[state=active]:bg-cyan-500/20 data-[state=active]:text-cyan-400"
              >
                <Target className="w-4 h-4 mr-2" />
                Subject Performance
              </TabsTrigger>
              <TabsTrigger 
                value="skills" 
                className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-400"
              >
                <Brain className="w-4 h-4 mr-2" />
                Skill Progress
              </TabsTrigger>
              <TabsTrigger 
                value="recommendations" 
                className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400"
              >
                <AlertTriangle className="w-4 h-4 mr-2" />
                Recommendations
              </TabsTrigger>
            </TabsList>

            {/* Subject Performance */}
            <TabsContent value="overview" className="space-y-6">
              {/* Radar Chart Section */}
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6 }}
              >
                <Card className="bg-gradient-to-br from-purple-500/10 via-blue-500/10 to-cyan-500/10 border-purple-500/30">
                  <CardHeader>
                    <CardTitle className="text-white font-space-grotesk flex items-center gap-3">
                      <div className="p-2 bg-gradient-to-r from-purple-500/20 to-cyan-500/20 rounded-lg">
                        <PieChart className="w-5 h-5 text-purple-400" />
                      </div>
                      Subject Mastery Overview
                      <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30">
                        Spider Chart
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                      {/* Radar Chart */}
                      <div className="flex justify-center">
                        <RadarChart
                          data={analytics.subjectPerformance.map((subject, index) => ({
                            subject: subject.subject.split(' ')[0], // Shortened names for chart
                            value: subject.mastery,
                            maxValue: 100,
                            color: [
                              '#06b6d4', // cyan
                              '#8b5cf6', // violet
                              '#10b981', // emerald
                              '#f59e0b', // amber
                              '#ef4444', // red
                              '#3b82f6'  // blue
                            ][index % 6]
                          }))}
                          size={320}
                          className="drop-shadow-2xl"
                        />
                      </div>

                      {/* Summary Stats */}
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="text-center p-4 bg-black/20 rounded-lg backdrop-blur-sm">
                            <div className="text-2xl font-bold text-cyan-400">
                              {Math.round(analytics.subjectPerformance.reduce((sum, s) => sum + s.mastery, 0) / analytics.subjectPerformance.length)}%
                            </div>
                            <div className="text-xs text-gray-400">Overall Mastery</div>
                          </div>

                          <div className="text-center p-4 bg-black/20 rounded-lg backdrop-blur-sm">
                            <div className="text-2xl font-bold text-green-400">
                              {analytics.subjectPerformance.filter(s => s.mastery >= 80).length}
                            </div>
                            <div className="text-xs text-gray-400">Strong Subjects</div>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <h4 className="text-sm font-medium text-white">Top Performing</h4>
                          {analytics.subjectPerformance
                            .sort((a, b) => b.mastery - a.mastery)
                            .slice(0, 3)
                            .map((subject, index) => (
                              <div key={subject.subject} className="flex items-center justify-between text-sm">
                                <span className="text-gray-300">{index + 1}. {subject.subject}</span>
                                <span className="text-green-400 font-medium">{subject.mastery}%</span>
                              </div>
                            ))}
                        </div>

                        <div className="space-y-2">
                          <h4 className="text-sm font-medium text-white">Needs Focus</h4>
                          {analytics.subjectPerformance
                            .filter(s => s.needsMore)
                            .map((subject, _index) => (
                              <div key={subject.subject} className="flex items-center justify-between text-sm">
                                <span className="text-gray-300">{subject.subject}</span>
                                <Badge className="bg-orange-500/20 text-orange-400 text-xs">
                                  {subject.mastery}%
                                </Badge>
                              </div>
                            ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Detailed Subject Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                {analytics.subjectPerformance.map((subject, index) => {
                  const getRecommendationColor = (rec: string) => {
                    switch (rec) {
                      case 'increase': return 'bg-green-500/20 text-green-400 border-green-500/30'
                      case 'decrease': return 'bg-red-500/20 text-red-400 border-red-500/30'
                      case 'maintain': return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
                      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
                    }
                  }

                  const getDifficultyColor = (diff: string) => {
                    switch (diff) {
                      case 'beginner': return 'bg-green-500/20 text-green-400'
                      case 'intermediate': return 'bg-yellow-500/20 text-yellow-400'
                      case 'advanced': return 'bg-red-500/20 text-red-400'
                      default: return 'bg-gray-500/20 text-gray-400'
                    }
                  }

                  const getSubjectGradient = (index: number) => {
                    const gradients = [
                      'from-cyan-500/10 to-blue-500/10 border-cyan-500/30',
                      'from-purple-500/10 to-violet-500/10 border-purple-500/30',
                      'from-green-500/10 to-emerald-500/10 border-green-500/30',
                      'from-amber-500/10 to-orange-500/10 border-amber-500/30',
                      'from-red-500/10 to-pink-500/10 border-red-500/30',
                      'from-blue-500/10 to-indigo-500/10 border-blue-500/30'
                    ]
                    return gradients[index % gradients.length]
                  }

                  return (
                    <motion.div
                      key={subject.subject}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 + 0.3 }}
                      whileHover={{ scale: 1.02, y: -5 }}
                      className="group"
                    >
                      <Card className={`bg-gradient-to-br ${getSubjectGradient(index)} hover:shadow-xl transition-all duration-300 ${
                        subject.needsMore ? 'ring-2 ring-orange-500/50' : ''
                      }`}>
                        <CardContent className="p-5">
                          {/* Header */}
                          <div className="flex items-start justify-between mb-4">
                            <div>
                              <h4 className="font-semibold text-white text-lg mb-1 group-hover:text-cyan-400 transition-colors">
                                {subject.subject}
                              </h4>
                              <div className="flex items-center gap-2 flex-wrap">
                                <Badge className={getDifficultyColor(subject.difficulty)}>
                                  {subject.difficulty}
                                </Badge>
                                {subject.needsMore && (
                                  <Badge className="bg-orange-500/20 text-orange-400 border-orange-500/30 animate-pulse">
                                    Focus Needed
                                  </Badge>
                                )}
                              </div>
                            </div>

                            <div className="text-right">
                              <div className="text-2xl font-bold text-white mb-1">{subject.avgScore}%</div>
                              <div className="text-xs text-gray-400">{subject.timeSpent}min</div>
                            </div>
                          </div>

                          {/* Progress Rings */}
                          <div className="grid grid-cols-2 gap-4 mb-4">
                            <div className="text-center">
                              <div className="relative w-16 h-16 mx-auto mb-2">
                                <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                                  <path
                                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                                    fill="none"
                                    stroke="rgba(156, 163, 175, 0.2)"
                                    strokeWidth="2"
                                  />
                                  <motion.path
                                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                                    fill="none"
                                    stroke="#06b6d4"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    initial={{ strokeDasharray: "0 100" }}
                                    animate={{ strokeDasharray: `${subject.mastery} 100` }}
                                    transition={{ duration: 1, delay: index * 0.1 }}
                                  />
                                </svg>
                                <div className="absolute inset-0 flex items-center justify-center">
                                  <span className="text-xs font-bold text-white">{subject.mastery}%</span>
                                </div>
                              </div>
                              <div className="text-xs text-gray-400">Mastery</div>
                            </div>

                            <div className="text-center">
                              <div className="relative w-16 h-16 mx-auto mb-2">
                                <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                                  <path
                                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                                    fill="none"
                                    stroke="rgba(156, 163, 175, 0.2)"
                                    strokeWidth="2"
                                  />
                                  <motion.path
                                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                                    fill="none"
                                    stroke="#10b981"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    initial={{ strokeDasharray: "0 100" }}
                                    animate={{ strokeDasharray: `${subject.engagement} 100` }}
                                    transition={{ duration: 1, delay: index * 0.1 + 0.2 }}
                                  />
                                </svg>
                                <div className="absolute inset-0 flex items-center justify-center">
                                  <span className="text-xs font-bold text-white">{subject.engagement}%</span>
                                </div>
                              </div>
                              <div className="text-xs text-gray-400">Engagement</div>
                            </div>
                          </div>

                          {/* Stats */}
                          <div className="space-y-2 mb-4">
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-400">Lessons Completed</span>
                              <span className="text-white font-medium">{subject.lessons}</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-400">Improvement</span>
                              <div className="flex items-center gap-1">
                                {subject.improvement > 0 ? (
                                  <TrendingUp className="w-3 h-3 text-green-400" />
                                ) : subject.improvement < 0 ? (
                                  <TrendingDown className="w-3 h-3 text-red-400" />
                                ) : (
                                  <Minus className="w-3 h-3 text-gray-400" />
                                )}
                                <span className={`font-medium ${
                                  subject.improvement > 0 ? 'text-green-400' :
                                  subject.improvement < 0 ? 'text-red-400' : 'text-gray-400'
                                }`}>
                                  {subject.improvement > 0 ? '+' : ''}{subject.improvement}%
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Recommendation */}
                          <div className="flex items-center justify-between">
                            <Badge className={getRecommendationColor(subject.recommended)}>
                              {subject.recommended.charAt(0).toUpperCase() + subject.recommended.slice(1)}
                            </Badge>

                            <div className="flex items-center gap-1">
                              {subject.needsMore ? (
                                <AlertTriangle className="w-4 h-4 text-orange-400" />
                              ) : (
                                <CheckCircle className="w-4 h-4 text-green-400" />
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  )
                })}
              </div>
            </TabsContent>

            {/* Skill Progress */}
            <TabsContent value="skills" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {analytics.skillProgress.map((skill, index) => (
                  <motion.div
                    key={skill.skill}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card className="bg-gray-800/30 border-gray-700/50">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium text-white">{skill.skill}</h4>
                          {getTrendIcon(skill.trend)}
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-400">Current: {skill.current}%</span>
                            <span className="text-gray-400">Target: {skill.target}%</span>
                          </div>
                          <Progress value={skill.current} className="h-2 bg-gray-700" />
                          <div className="text-xs text-gray-500">
                            {skill.current >= skill.target ? 'Target achieved!' : `${skill.target - skill.current}% to target`}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </TabsContent>

            {/* Recommendations */}
            <TabsContent value="recommendations" className="space-y-4">
              <div className="space-y-3">
                {analytics.recommendations.map((rec, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card className="bg-gray-800/30 border-gray-700/50">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge className={getRecommendationColor(rec.type)}>
                                {rec.type.charAt(0).toUpperCase() + rec.type.slice(1)} Time
                              </Badge>
                              <Badge className={getPriorityColor(rec.priority)}>
                                {rec.priority} priority
                              </Badge>
                              <Badge className="bg-gray-600/30 text-gray-300">
                                {rec.suggestedTime}
                              </Badge>
                            </div>
                            <h4 className="font-medium text-white mb-1">{rec.subject}</h4>
                            <p className="text-sm text-gray-400 mb-2">{rec.reason}</p>
                            <div className="flex items-center gap-2 text-xs text-cyan-400">
                              <Target className="w-3 h-3" />
                              <span>{rec.action}</span>
                            </div>
                          </div>
                          <div className="flex flex-col gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-cyan-500/30 text-cyan-400 hover:bg-cyan-500/10"
                            >
                              Auto-Apply
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="text-gray-400 hover:text-white"
                            >
                              Dismiss
                            </Button>
                          </div>
                        </div>

                        {/* Progress indicator for high priority items */}
                        {rec.priority === 'high' && (
                          <div className="mt-3 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                            <div className="flex items-center gap-2 text-red-400 text-sm">
                              <AlertTriangle className="w-4 h-4" />
                              <span>Immediate attention recommended</span>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>

              {/* Learning Insights Summary */}
              <Card className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 border-purple-500/30">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Brain className="w-5 h-5 text-purple-400" />
                    Learning Insights
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h5 className="text-sm font-medium text-white mb-2">Optimal Learning</h5>
                      <div className="space-y-1 text-sm text-gray-300">
                        <div>⏰ Best time: {analytics.learningInsights.bestPerformanceTime}</div>
                        <div>🎯 Attention span: {analytics.learningInsights.attentionSpan}</div>
                        <div>📚 Style: {analytics.learningInsights.preferredLearningStyle}</div>
                      </div>
                    </div>

                    <div>
                      <h5 className="text-sm font-medium text-white mb-2">Key Strengths</h5>
                      <div className="flex flex-wrap gap-1">
                        {analytics.learningInsights.strengths.map((strength, i) => (
                          <Badge key={i} className="bg-green-500/20 text-green-400 text-xs">
                            {strength}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium text-white mb-2">Challenge Areas</h5>
                    <div className="flex flex-wrap gap-1">
                      {analytics.learningInsights.challengeAreas.map((challenge, i) => (
                        <Badge key={i} className="bg-orange-500/20 text-orange-400 text-xs">
                          {challenge}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </motion.div>
  )
}

// Helper function to get week number
declare global {
  interface Date {
    getWeek(): number
  }
}

Date.prototype.getWeek = function() {
  const date = new Date(this.getTime())
  date.setHours(0, 0, 0, 0)
  date.setDate(date.getDate() + 3 - (date.getDay() + 6) % 7)
  const week1 = new Date(date.getFullYear(), 0, 4)
  return 1 + Math.round(((date.getTime() - week1.getTime()) / 86400000 - 3 + (week1.getDay() + 6) % 7) / 7)
}
