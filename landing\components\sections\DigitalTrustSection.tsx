"use client"

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Shield,
  Eye,
  UserCheck,
  Lock,
  Award,
  CheckCircle,
  Heart,
  Users,
  FileCheck,
  Star,
  Zap,
  Sparkles,
  Brain,
  Cpu,
  Database
} from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export function DigitalTrustSection() {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Quantum security particles
  const securityParticles = Array.from({ length: 20 }, (_, i) => ({
    left: (i * 19 + 5) % 100,
    top: (i * 23 + 7) % 100,
    delay: i * 0.3,
    color: i % 3 === 0 ? '#22d3ee' : i % 3 === 1 ? '#8b5cf6' : '#10b981'
  }))

  // Quantum shield matrix
  const shieldMatrix = Array.from({ length: 15 }, (_, i) => ({
    left: (i * 31 + 11) % 100,
    top: (i * 17 + 13) % 100,
    char: i % 3 === 0 ? "🛡️" : i % 3 === 1 ? "🔒" : "⚡",
    delay: i * 0.2
  }))
  // Enhanced quantum security features
  const quantumSecurityFeatures = [
    {
      icon: Shield,
      title: 'Quantum Security Matrix',
      description: 'Neural-enhanced AI moderation with consciousness-aware filtering ensures a safe quantum learning environment',
      features: [
        'Quantum real-time content analysis',
        'Neural community reporting protocols',
        'Consciousness-appropriate interactions',
        'Zero-tolerance quantum bullying detection'
      ],
      quantumColor: '#10b981',
      stats: '99.9% Quantum Safe',
      animation: 'shield'
    },
    {
      icon: Brain,
      title: 'Neural Parent Interface',
      description: 'Consciousness-driven transparency and quantum control over your child\'s neural learning journey',
      features: [
        'Quantum activity consciousness tracking',
        'Neural progress synthesis reports',
        'Consciousness time management',
        'Quantum communication oversight'
      ],
      quantumColor: '#22d3ee',
      stats: '24/7 Neural Monitoring',
      animation: 'brain'
    },
    {
      icon: Database,
      title: 'Quantum Data Fortress',
      description: 'Multi-dimensional security protocols ensure all consciousness data meets quantum safety standards',
      features: [
        'Quantum content consciousness scanning',
        'Neural human review protocols',
        'Quantum virus and malware protection',
        'Consciousness-first data handling'
      ],
      quantumColor: '#8b5cf6',
      stats: '100% Quantum Verified',
      animation: 'database'
    }
  ]

  // Quantum trust indicators with enhanced terminology
  const quantumTrustIndicators = [
    {
      icon: UserCheck,
      label: 'Quantum COPPA Protocol',
      description: 'Full consciousness privacy protection for young neural learners',
      quantumColor: '#10b981'
    },
    {
      icon: Lock,
      label: 'Neural Encryption Matrix',
      description: 'All consciousness communications are quantum-encrypted',
      quantumColor: '#22d3ee'
    },
    {
      icon: Award,
      label: 'Quantum Learning Standards',
      description: 'Neural curriculum aligned with consciousness STEM objectives',
      quantumColor: '#8b5cf6'
    },
    {
      icon: Eye,
      label: 'Transparent Quantum Policies',
      description: 'Clear consciousness privacy and neural safety protocols',
      quantumColor: '#fbbf24'
    }
  ]

  // Enhanced neural parental controls
  const neuralParentalControls = [
    { feature: 'Weekly Consciousness Time', value: '4.5 neural hours', status: 'healthy', icon: Brain },
    { feature: 'Quantum Skills Developed', value: 'Neural Python, Quantum Logic, Consciousness Teamwork', status: 'excellent', icon: Cpu },
    { feature: 'Quantum Safety Score', value: 'Neural Excellence', status: 'excellent', icon: Shield },
    { feature: 'Consciousness Interactions', value: '23 quantum helpful posts', status: 'positive', icon: Users },
    { feature: 'Neural Learning Progress', value: '85% consciousness completion', status: 'excellent', icon: Zap },
    { feature: 'Quantum Mentor Feedback', value: 'Outstanding neural effort', status: 'positive', icon: Star }
  ]

  return (
    <section className="relative px-6 py-20 overflow-hidden">
      {/* Quantum security background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-space-dark via-space-blue to-space-dark" />
        <div className="absolute inset-0 consciousness-wave opacity-20" />

        {/* Quantum security field particles */}
        {isClient && securityParticles.map((particle, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 rounded-full"
            style={{
              left: `${particle.left}%`,
              top: `${particle.top}%`,
              backgroundColor: particle.color
            }}
            animate={{
              opacity: [0, 0.8, 0],
              scale: [0.5, 1.2, 0.5],
              rotate: 360
            }}
            transition={{
              duration: 4,
              repeat: Number.POSITIVE_INFINITY,
              delay: particle.delay,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Quantum shield matrix background */}
        {isClient && shieldMatrix.map((shield, i) => (
          <motion.div
            key={i}
            className="absolute text-lg opacity-10"
            style={{
              left: `${shield.left}%`,
              top: `${shield.top}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.1, 0.3, 0.1],
              scale: [0.8, 1.2, 0.8]
            }}
            transition={{
              duration: 6,
              repeat: Number.POSITIVE_INFINITY,
              delay: shield.delay
            }}
          >
            {shield.char}
          </motion.div>
        ))}

        {/* Quantum security glow orbs */}
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-emerald-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/3 right-1/4 w-40 h-40 bg-neural-cyan/10 rounded-full blur-3xl" />
        <div className="absolute top-1/2 right-1/3 w-24 h-24 bg-quantum-purple/10 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto">
        {/* Quantum Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.h2
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 font-orbitron"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <span className="bg-gradient-to-r from-emerald-400 via-neural-cyan to-quantum-purple bg-clip-text text-transparent">
              Quantum Security
            </span>
            <br />
            <span className="bg-gradient-to-r from-white via-gray-200 to-white bg-clip-text text-transparent text-3xl md:text-4xl lg:text-5xl">
              & Neural Trust
            </span>
          </motion.h2>

          <motion.p
            className="text-lg md:text-xl text-white/80 max-w-4xl mx-auto font-space-grotesk leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3, duration: 0.8 }}
          >
            <span className="text-neural-cyan font-semibold">NanoHero</span> provides a
            <span className="text-emerald-400 font-semibold"> quantum-secured</span>, consciousness-moderated environment where young
            <span className="text-quantum-purple font-semibold"> neural learners</span> can explore technology,
            develop quantum thinking, and build confidence through hands-on consciousness experiences.
          </motion.p>

          {/* Quantum security decoration */}
          <motion.div
            className="flex justify-center items-center gap-4 mt-6"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.6 }}
          >
            <div className="h-px w-16 bg-gradient-to-r from-transparent to-emerald-400" />
            <Shield className="w-5 h-5 text-neural-cyan" />
            <div className="h-px w-16 bg-gradient-to-l from-transparent to-quantum-purple" />
          </motion.div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* Quantum Security Features */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <motion.h3
              className="text-2xl lg:text-3xl font-bold text-white mb-6 font-orbitron"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
            >
              <span className="bg-gradient-to-r from-emerald-400 to-neural-cyan bg-clip-text text-transparent">
                Three Quantum Pillars
              </span>
              <br />
              <span className="text-white/90 text-xl">of Neural Security</span>
            </motion.h3>

            {quantumSecurityFeatures.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.2 }}
                whileHover={{ scale: 1.02, y: -8 }}
                className="group"
              >
                <Card
                  className="quantum-glass border-2 h-full relative overflow-hidden group-hover:border-opacity-60 transition-all duration-500"
                  style={{
                    borderColor: `${feature.quantumColor}40`,
                    background: `linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)`,
                    boxShadow: `0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px ${feature.quantumColor}20, inset 0 1px 0 rgba(255, 255, 255, 0.1)`
                  }}
                >
                  {/* Quantum glow effect on hover */}
                  <div
                    className="absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"
                    style={{
                      background: `linear-gradient(135deg, ${feature.quantumColor}30, ${feature.quantumColor}10)`,
                      boxShadow: `0 0 40px ${feature.quantumColor}40`
                    }}
                  />

                  {/* Quantum security animation */}
                  {isClient && (
                    <div className="absolute inset-0 overflow-hidden rounded-xl">
                      {feature.animation === 'shield' && securityParticles.slice(0, 8).map((particle, i) => (
                        <motion.div
                          key={i}
                          className="absolute w-1 h-1 rounded-full"
                          style={{
                            left: `${particle.left}%`,
                            top: `${particle.top}%`,
                            backgroundColor: feature.quantumColor
                          }}
                          animate={{
                            scale: [0, 1, 0],
                            opacity: [0, 0.8, 0],
                            rotate: 360
                          }}
                          transition={{
                            duration: 3,
                            repeat: Number.POSITIVE_INFINITY,
                            delay: particle.delay
                          }}
                        />
                      ))}

                      {feature.animation === 'brain' && (
                        <motion.div
                          className="absolute top-4 right-4"
                          animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.3, 0.8, 0.3]
                          }}
                          transition={{
                            duration: 2,
                            repeat: Number.POSITIVE_INFINITY
                          }}
                        >
                          <Brain className="w-8 h-8" style={{ color: feature.quantumColor }} />
                        </motion.div>
                      )}

                      {feature.animation === 'database' && (
                        <motion.div
                          className="absolute top-4 right-4"
                          animate={{
                            rotate: 360,
                            scale: [0.8, 1.1, 0.8]
                          }}
                          transition={{
                            rotate: { duration: 4, repeat: Number.POSITIVE_INFINITY, ease: "linear" },
                            scale: { duration: 2, repeat: Number.POSITIVE_INFINITY }
                          }}
                        >
                          <Database className="w-8 h-8" style={{ color: feature.quantumColor }} />
                        </motion.div>
                      )}
                    </div>
                  )}

                  <CardContent className="p-6 lg:p-8 relative z-10">
                    <div className="flex items-start gap-4">
                      <motion.div
                        className="relative p-4 rounded-xl border-2 group-hover:scale-110 transition-transform duration-300"
                        style={{
                          background: `linear-gradient(135deg, ${feature.quantumColor}20, ${feature.quantumColor}10)`,
                          borderColor: feature.quantumColor,
                          boxShadow: `0 0 20px ${feature.quantumColor}40`
                        }}
                        whileHover={{ rotate: 360 }}
                        transition={{ duration: 0.8 }}
                      >
                        <feature.icon className="w-6 h-6 lg:w-7 lg:h-7 text-white" />

                        {/* Icon quantum glow */}
                        <div
                          className="absolute inset-0 rounded-xl blur-lg opacity-50"
                          style={{ backgroundColor: feature.quantumColor }}
                        />
                      </motion.div>

                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-3">
                          <h4
                            className="text-lg lg:text-xl font-bold text-white group-hover:text-opacity-90 transition-all duration-300 font-orbitron"
                            style={{
                              textShadow: `0 0 20px ${feature.quantumColor}60`
                            }}
                          >
                            {feature.title}
                          </h4>
                          <Badge
                            className="text-xs font-medium"
                            style={{
                              backgroundColor: `${feature.quantumColor}20`,
                              borderColor: `${feature.quantumColor}40`,
                              color: feature.quantumColor
                            }}
                          >
                            {feature.stats}
                          </Badge>
                        </div>

                        <p className="text-white/80 mb-4 leading-relaxed text-sm lg:text-base font-space-grotesk">
                          {feature.description}
                        </p>

                        <div className="space-y-2">
                          {feature.features.map((item, i) => (
                            <motion.div
                              key={item}
                              initial={{ opacity: 0, x: -10 }}
                              whileInView={{ opacity: 1, x: 0 }}
                              viewport={{ once: true }}
                              transition={{ delay: index * 0.2 + i * 0.1 }}
                              className="flex items-center gap-3 text-sm lg:text-base text-white/70"
                            >
                              <CheckCircle
                                className="w-4 h-4 flex-shrink-0"
                                style={{ color: feature.quantumColor }}
                              />
                              {item}
                            </motion.div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}

            {/* Quantum Trust Indicators */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.6 }}
              className="grid grid-cols-2 gap-4"
            >
              {quantumTrustIndicators.map((indicator, _index) => (
                <motion.div
                  key={indicator.label}
                  whileHover={{ scale: 1.05 }}
                  className="quantum-glass rounded-xl p-4 border-2 relative overflow-hidden group"
                  style={{
                    borderColor: `${indicator.quantumColor}40`,
                    background: `linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(10, 15, 28, 0.8) 50%, rgba(0, 0, 0, 0.7) 100%)`,
                    boxShadow: `0 0 20px ${indicator.quantumColor}20`
                  }}
                >
                  {/* Quantum sweep effect */}
                  <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                    <div
                      className="absolute inset-0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 rounded-xl"
                      style={{
                        background: `linear-gradient(90deg, transparent, ${indicator.quantumColor}30, transparent)`
                      }}
                    />
                  </div>

                  <div className="relative z-10">
                    <div className="flex items-center gap-3 mb-2">
                      <indicator.icon
                        className="w-5 h-5"
                        style={{ color: indicator.quantumColor }}
                      />
                      <span
                        className="font-semibold text-white text-sm font-orbitron"
                        style={{ textShadow: `0 0 10px ${indicator.quantumColor}40` }}
                      >
                        {indicator.label}
                      </span>
                    </div>
                    <p className="text-white/70 text-xs leading-relaxed font-space-grotesk">
                      {indicator.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* Neural Parent Interface Preview */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="relative"
          >
            <Card
              className="quantum-glass border-2 relative overflow-hidden"
              style={{
                borderColor: '#22d3ee40',
                background: `linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)`,
                boxShadow: `0 0 40px #22d3ee20, inset 0 1px 0 rgba(255, 255, 255, 0.1)`
              }}
            >
              {/* Neural interface background effects */}
              <div className="absolute inset-0 consciousness-wave opacity-20" />
              {isClient && securityParticles.slice(0, 6).map((particle, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 h-1 bg-neural-cyan rounded-full"
                  style={{
                    left: `${particle.left}%`,
                    top: `${particle.top}%`,
                  }}
                  animate={{
                    opacity: [0, 0.6, 0],
                    scale: [0.5, 1, 0.5]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Number.POSITIVE_INFINITY,
                    delay: particle.delay
                  }}
                />
              ))}

              <CardHeader className="text-center pb-6 relative z-10">
                <CardTitle className="text-2xl lg:text-3xl font-bold text-white mb-2 flex items-center justify-center gap-3 font-orbitron">
                  <Brain className="w-6 h-6 lg:w-7 lg:h-7 text-neural-cyan" />
                  <span className="bg-gradient-to-r from-neural-cyan to-quantum-purple bg-clip-text text-transparent">
                    Neural Parent Interface
                  </span>
                </CardTitle>
                <p className="text-white/80 font-space-grotesk">
                  Quantum insights into your child&apos;s consciousness learning journey
                </p>
              </CardHeader>

              <CardContent className="space-y-4 relative z-10">
                {neuralParentalControls.map((control, index) => (
                  <motion.div
                    key={control.feature}
                    initial={{ opacity: 0, x: 20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center justify-between p-4 quantum-glass rounded-xl hover:bg-black/20 transition-all duration-300 border border-white/10"
                    style={{
                      background: `linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(10, 15, 28, 0.7) 100%)`
                    }}
                  >
                    <div className="flex items-center gap-3">
                      <control.icon
                        className="w-5 h-5"
                        style={{
                          color: control.status === 'excellent' ? '#10b981' :
                                 control.status === 'positive' ? '#22d3ee' : '#fbbf24'
                        }}
                      />
                      <span className="text-white/90 font-medium font-space-grotesk">
                        {control.feature}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      {control.status === 'excellent' && <CheckCircle className="w-4 h-4 text-emerald-400" />}
                      {control.status === 'positive' && <Heart className="w-4 h-4 text-neural-cyan" />}
                      {control.status === 'healthy' && <Star className="w-4 h-4 text-quantum-gold" />}
                      <span
                        className={`font-semibold text-sm font-orbitron ${
                          control.status === 'excellent' ? 'text-emerald-400' :
                          control.status === 'positive' ? 'text-neural-cyan' :
                          'text-quantum-gold'
                        }`}
                        style={{
                          textShadow: `0 0 10px ${
                            control.status === 'excellent' ? '#10b981' :
                            control.status === 'positive' ? '#22d3ee' : '#fbbf24'
                          }40`
                        }}
                      >
                        {control.value}
                      </span>
                    </div>
                  </motion.div>
                ))}

                {/* Quantum Safety Alert Example */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.8 }}
                  className="quantum-glass rounded-xl p-4 mt-6 border-2 relative overflow-hidden"
                  style={{
                    borderColor: '#10b98140',
                    background: `linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%)`,
                    boxShadow: `0 0 20px #10b98120`
                  }}
                >
                  {/* Quantum success particles */}
                  {isClient && (
                    <div className="absolute inset-0 overflow-hidden rounded-xl">
                      {[...Array(5)].map((_, i) => (
                        <motion.div
                          key={i}
                          className="absolute w-1 h-1 bg-emerald-400 rounded-full"
                          style={{
                            left: `${20 + i * 15}%`,
                            top: `${30 + i * 10}%`,
                          }}
                          animate={{
                            opacity: [0, 1, 0],
                            scale: [0.5, 1.2, 0.5],
                            y: [0, -10, 0]
                          }}
                          transition={{
                            duration: 2,
                            repeat: Number.POSITIVE_INFINITY,
                            delay: i * 0.3
                          }}
                        />
                      ))}
                    </div>
                  )}

                  <div className="relative z-10">
                    <div className="flex items-center gap-3 mb-2">
                      <Shield className="w-5 h-5 text-emerald-400" />
                      <span className="font-semibold text-emerald-400 font-orbitron">
                        Quantum Security: All Neural Pathways Clear!
                      </span>
                    </div>
                    <p className="text-white/80 text-sm font-space-grotesk leading-relaxed">
                      No consciousness security concerns detected. Your child is engaging positively with the
                      neural community and making excellent progress in their quantum learning journey.
                    </p>
                  </div>
                </motion.div>
              </CardContent>
            </Card>

            {/* Quantum Parent & Educator CTA */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 1 }}
              className="mt-8 text-center"
            >
              <div
                className="quantum-glass rounded-2xl p-6 lg:p-8 border-2 relative overflow-hidden"
                style={{
                  borderColor: '#22d3ee40',
                  background: `linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(10, 15, 28, 0.8) 50%, rgba(0, 0, 0, 0.7) 100%)`,
                  boxShadow: `0 0 30px #22d3ee20`
                }}
              >
                {/* Quantum background effects */}
                <div className="absolute inset-0 consciousness-wave opacity-20" />
                {isClient && (
                  <div className="absolute top-4 right-4">
                    <motion.div
                      animate={{
                        rotate: 360,
                        scale: [1, 1.2, 1]
                      }}
                      transition={{
                        rotate: { duration: 8, repeat: Number.POSITIVE_INFINITY, ease: "linear" },
                        scale: { duration: 3, repeat: Number.POSITIVE_INFINITY }
                      }}
                    >
                      <Sparkles className="w-6 h-6 text-neural-cyan" />
                    </motion.div>
                  </div>
                )}

                <div className="relative z-10">
                  <h4 className="text-lg lg:text-xl font-bold text-white mb-3 font-orbitron">
                    <span className="text-neural-cyan">🧠</span> For Neural Parents &
                    <span className="bg-gradient-to-r from-neural-cyan to-quantum-purple bg-clip-text text-transparent"> Quantum Educators</span>
                  </h4>
                  <p className="text-white/80 mb-6 text-sm lg:text-base leading-relaxed font-space-grotesk">
                    Get complete consciousness visibility and quantum control over your child&apos;s neural learning experience.
                    Our comprehensive quantum safety measures ensure peace of mind while fostering consciousness growth.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        variant="outline"
                        size="lg"
                        className="border-neural-cyan/50 text-neural-cyan hover:bg-neural-cyan/10 font-space-grotesk"
                      >
                        <FileCheck className="w-4 h-4 mr-2" />
                        Quantum Safety Protocols
                      </Button>
                    </motion.div>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        variant="outline"
                        size="lg"
                        className="border-quantum-purple/50 text-quantum-purple hover:bg-quantum-purple/10 font-space-grotesk"
                      >
                        <Users className="w-4 h-4 mr-2" />
                        Neural Parent Community
                      </Button>
                    </motion.div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Quantum Trust Certification Badges */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 1.2 }}
          className="mt-20 text-center"
        >
          <motion.h3
            className="text-xl lg:text-2xl font-bold text-white mb-8 font-orbitron"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
          >
            <span className="bg-gradient-to-r from-emerald-400 to-neural-cyan bg-clip-text text-transparent">
              Quantum Certifications
            </span>
            <span className="text-white/80"> & Neural Trust Badges</span>
          </motion.h3>

          <div className="flex flex-wrap justify-center gap-4 lg:gap-6 items-center mb-6">
            <motion.div
              whileHover={{ scale: 1.05, y: -2 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Badge
                className="px-4 py-3 text-sm font-medium font-space-grotesk border-2"
                style={{
                  backgroundColor: '#10b98120',
                  borderColor: '#10b98140',
                  color: '#10b981',
                  boxShadow: '0 0 15px #10b98130'
                }}
              >
                🛡️ Quantum COPPA Certified
              </Badge>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05, y: -2 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Badge
                className="px-4 py-3 text-sm font-medium font-space-grotesk border-2"
                style={{
                  backgroundColor: '#22d3ee20',
                  borderColor: '#22d3ee40',
                  color: '#22d3ee',
                  boxShadow: '0 0 15px #22d3ee30'
                }}
              >
                🔒 Neural SOC 2 Compliant
              </Badge>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05, y: -2 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Badge
                className="px-4 py-3 text-sm font-medium font-space-grotesk border-2"
                style={{
                  backgroundColor: '#8b5cf620',
                  borderColor: '#8b5cf640',
                  color: '#8b5cf6',
                  boxShadow: '0 0 15px #8b5cf630'
                }}
              >
                📚 Quantum CSTA Aligned
              </Badge>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05, y: -2 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Badge
                className="px-4 py-3 text-sm font-medium font-space-grotesk border-2"
                style={{
                  backgroundColor: '#fbbf2420',
                  borderColor: '#fbbf2440',
                  color: '#fbbf24',
                  boxShadow: '0 0 15px #fbbf2430'
                }}
              >
                ⭐ 5-Star Neural Safety Rating
              </Badge>
            </motion.div>
          </div>

          <motion.p
            className="text-white/60 text-sm lg:text-base mt-6 font-space-grotesk"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3 }}
          >
            Trusted by <span className="text-neural-cyan font-semibold">10,000+ neural parents</span> and
            <span className="text-quantum-purple font-semibold"> 500+ quantum schools</span> across the consciousness network
          </motion.p>
        </motion.div>
      </div>
    </section>
  )
}
