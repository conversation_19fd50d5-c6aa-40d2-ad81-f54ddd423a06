/**
 * Font Loading Optimization for NanoHero Platform
 * Implements advanced font loading strategies for optimal performance
 */

export interface FontConfig {
  family: string
  weights: string[]
  subsets: string[]
  display: 'auto' | 'block' | 'swap' | 'fallback' | 'optional'
  preload: boolean
  fallback: string[]
  variable?: string
}

export interface FontLoadingStrategy {
  critical: FontConfig[]
  secondary: FontConfig[]
  optional: FontConfig[]
}

/**
 * Optimized font configurations for NanoHero
 */
export const NANOHERO_FONTS: FontLoadingStrategy = {
  // Critical fonts - loaded immediately
  critical: [
    {
      family: 'Inter',
      weights: ['400', '500', '600'],
      subsets: ['latin'],
      display: 'swap',
      preload: true,
      fallback: ['system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Arial', 'sans-serif'],
      variable: '--font-inter'
    }
  ],
  
  // Secondary fonts - loaded after critical content
  secondary: [
    {
      family: 'Syne',
      weights: ['400', '500', '600', '700'],
      subsets: ['latin'],
      display: 'swap',
      preload: false,
      fallback: ['Inter', 'system-ui', 'sans-serif'],
      variable: '--font-syne'
    },
    {
      family: 'Orbitron',
      weights: ['400', '500', '600', '700'],
      subsets: ['latin'],
      display: 'swap',
      preload: false,
      fallback: ['Monaco', 'Consolas', 'monospace'],
      variable: '--font-orbitron'
    }
  ],
  
  // Optional fonts - loaded on demand
  optional: [
    {
      family: 'Exo 2',
      weights: ['300', '400', '500', '600'],
      subsets: ['latin'],
      display: 'optional',
      preload: false,
      fallback: ['Inter', 'system-ui', 'sans-serif'],
      variable: '--font-exo'
    },
    {
      family: 'Space Grotesk',
      weights: ['300', '400', '500', '600'],
      subsets: ['latin'],
      display: 'optional',
      preload: false,
      fallback: ['Inter', 'system-ui', 'sans-serif'],
      variable: '--font-space'
    }
  ]
}

/**
 * Font loading manager
 */
export class FontLoadingManager {
  private static instance: FontLoadingManager
  private loadedFonts = new Set<string>()
  private loadingPromises = new Map<string, Promise<void>>()
  private fontObserver: FontFaceObserver | null = null

  static getInstance(): FontLoadingManager {
    if (!FontLoadingManager.instance) {
      FontLoadingManager.instance = new FontLoadingManager()
    }
    return FontLoadingManager.instance
  }

  constructor() {
    this.initializeFontObserver()
  }

  private initializeFontObserver() {
    // Simple font observer implementation
    this.fontObserver = {
      observe: (fontFamily: string, timeout: number = 3000) => {
        return new Promise<void>((resolve, reject) => {
          if (document.fonts && document.fonts.check) {
            const checkFont = () => {
              if (document.fonts.check(`1em ${fontFamily}`)) {
                resolve()
              } else {
                setTimeout(checkFont, 100)
              }
            }
            
            checkFont()
            setTimeout(() => reject(new Error('Font load timeout')), timeout)
          } else {
            // Fallback for older browsers
            setTimeout(resolve, 100)
          }
        })
      }
    }
  }

  /**
   * Load critical fonts immediately
   */
  async loadCriticalFonts(): Promise<void> {
    const promises = NANOHERO_FONTS.critical.map(font => this.loadFont(font))
    await Promise.allSettled(promises)
    console.log('✅ Critical fonts loaded')
  }

  /**
   * Load secondary fonts after critical content
   */
  async loadSecondaryFonts(): Promise<void> {
    // Wait for page load
    if (document.readyState !== 'complete') {
      await new Promise(resolve => {
        window.addEventListener('load', resolve, { once: true })
      })
    }

    const promises = NANOHERO_FONTS.secondary.map(font => this.loadFont(font))
    await Promise.allSettled(promises)
    console.log('✅ Secondary fonts loaded')
  }

  /**
   * Load optional fonts on demand
   */
  async loadOptionalFonts(): Promise<void> {
    // Use requestIdleCallback if available
    const loadFonts = () => {
      const promises = NANOHERO_FONTS.optional.map(font => this.loadFont(font))
      return Promise.allSettled(promises)
    }

    if ('requestIdleCallback' in window) {
      requestIdleCallback(async () => {
        await loadFonts()
        console.log('✅ Optional fonts loaded')
      })
    } else {
      setTimeout(async () => {
        await loadFonts()
        console.log('✅ Optional fonts loaded')
      }, 1000)
    }
  }

  /**
   * Load specific font
   */
  async loadFont(config: FontConfig): Promise<void> {
    const fontKey = `${config.family}-${config.weights.join(',')}`
    
    if (this.loadedFonts.has(fontKey)) {
      return
    }

    if (this.loadingPromises.has(fontKey)) {
      return this.loadingPromises.get(fontKey)!
    }

    const loadingPromise = this.loadFontInternal(config)
    this.loadingPromises.set(fontKey, loadingPromise)

    try {
      await loadingPromise
      this.loadedFonts.add(fontKey)
    } catch (error) {
      console.warn(`Failed to load font ${config.family}:`, error)
    } finally {
      this.loadingPromises.delete(fontKey)
    }
  }

  private async loadFontInternal(config: FontConfig): Promise<void> {
    // Create Google Fonts URL
    const weightsParam = config.weights.join(';')
    const fontUrl = `https://fonts.googleapis.com/css2?family=${config.family.replace(' ', '+')}:wght@${weightsParam}&display=${config.display}&subset=${config.subsets.join(',')}`

    // Load font CSS
    const link = document.createElement('link')
    link.rel = 'stylesheet'
    link.href = fontUrl
    
    if (config.preload) {
      link.rel = 'preload'
      link.as = 'style'
      link.onload = () => {
        link.rel = 'stylesheet'
      }
    }

    document.head.appendChild(link)

    // Wait for font to be available
    if (this.fontObserver) {
      try {
        await this.fontObserver.observe(config.family)
      } catch (error) {
        // Font load timeout - continue with fallback
        console.warn(`Font ${config.family} load timeout, using fallback`)
      }
    }
  }

  /**
   * Preload font files
   */
  preloadFontFiles(fonts: FontConfig[]): void {
    fonts.forEach(font => {
      if (font.preload) {
        font.weights.forEach(weight => {
          const link = document.createElement('link')
          link.rel = 'preload'
          link.as = 'font'
          link.type = 'font/woff2'
          link.crossOrigin = 'anonymous'
          link.href = this.getFontFileUrl(font.family, weight)
          document.head.appendChild(link)
        })
      }
    })
  }

  private getFontFileUrl(family: string, weight: string): string {
    // This would typically point to your CDN or local font files
    // For Google Fonts, we rely on their CSS to provide the actual font URLs
    return `https://fonts.gstatic.com/s/${family.toLowerCase().replace(' ', '')}/v1/${family.toLowerCase().replace(' ', '')}-${weight}.woff2`
  }

  /**
   * Get font loading statistics
   */
  getStats() {
    return {
      loadedFonts: this.loadedFonts.size,
      loadingFonts: this.loadingPromises.size,
      totalConfiguredFonts: NANOHERO_FONTS.critical.length + NANOHERO_FONTS.secondary.length + NANOHERO_FONTS.optional.length
    }
  }
}

export const fontManager = FontLoadingManager.getInstance()

/**
 * Font face observer interface
 */
interface FontFaceObserver {
  observe: (fontFamily: string, timeout?: number) => Promise<void>
}

/**
 * CSS font-display optimization
 */
export const fontDisplayOptimization = {
  // Generate optimized CSS for font-display
  generateFontCSS: (fonts: FontConfig[]): string => {
    return fonts.map(font => {
      const fallbackStack = font.fallback.join(', ')
      return `
        @font-face {
          font-family: '${font.family}';
          font-display: ${font.display};
          src: url('${font.family.toLowerCase().replace(' ', '-')}.woff2') format('woff2');
        }
        
        .font-${font.family.toLowerCase().replace(' ', '-')} {
          font-family: '${font.family}', ${fallbackStack};
        }
      `
    }).join('\n')
  },

  // Generate CSS custom properties
  generateCSSVariables: (fonts: FontConfig[]): string => {
    return fonts.map(font => {
      if (font.variable) {
        const fallbackStack = font.fallback.join(', ')
        return `${font.variable}: '${font.family}', ${fallbackStack};`
      }
      return ''
    }).filter(Boolean).join('\n')
  }
}

/**
 * React hook for font loading
 */
export const useFontLoading = (fontFamily: string) => {
  const [loaded, setLoaded] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)

  React.useEffect(() => {
    const config = [
      ...NANOHERO_FONTS.critical,
      ...NANOHERO_FONTS.secondary,
      ...NANOHERO_FONTS.optional
    ].find(font => font.family === fontFamily)

    if (!config) {
      setError(`Font ${fontFamily} not found in configuration`)
      return
    }

    fontManager.loadFont(config)
      .then(() => setLoaded(true))
      .catch(err => setError(err.message))
  }, [fontFamily])

  return { loaded, error }
}

/**
 * Font loading performance utilities
 */
export const fontPerformanceUtils = {
  // Measure font loading performance
  measureFontLoadTime: async (fontFamily: string): Promise<number> => {
    const startTime = performance.now()
    
    try {
      if (document.fonts && document.fonts.load) {
        await document.fonts.load(`1em ${fontFamily}`)
      }
      return performance.now() - startTime
    } catch (error) {
      console.warn(`Font measurement failed for ${fontFamily}:`, error)
      return -1
    }
  },

  // Check if font is loaded
  isFontLoaded: (fontFamily: string): boolean => {
    if (document.fonts && document.fonts.check) {
      return document.fonts.check(`1em ${fontFamily}`)
    }
    return false
  },

  // Get font loading metrics
  getFontMetrics: () => {
    const metrics = {
      totalFonts: 0,
      loadedFonts: 0,
      failedFonts: 0
    }

    if (document.fonts) {
      document.fonts.forEach(font => {
        metrics.totalFonts++
        if (font.status === 'loaded') {
          metrics.loadedFonts++
        } else if (font.status === 'error') {
          metrics.failedFonts++
        }
      })
    }

    return metrics
  }
}

/**
 * Initialize font loading strategy
 */
export const initializeFontLoading = async () => {
  console.log('🔤 Initializing font loading...')
  
  // Load critical fonts immediately
  await fontManager.loadCriticalFonts()
  
  // Load secondary fonts after DOM content loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      fontManager.loadSecondaryFonts()
    })
  } else {
    fontManager.loadSecondaryFonts()
  }
  
  // Load optional fonts when idle
  fontManager.loadOptionalFonts()
  
  console.log('✅ Font loading strategy initialized')
}

// Import React for hooks
import React from 'react'
