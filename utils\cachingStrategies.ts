/**
 * Comprehensive Caching Strategies for NanoHero Platform
 * Implements multi-level caching for assets, API responses, and computed values
 */

export type CacheType = 'memory' | 'localStorage' | 'sessionStorage' | 'indexedDB'

export interface CacheConfig {
  type: CacheType
  ttl?: number // Time to live in milliseconds
  maxSize?: number // Maximum cache size
  compression?: boolean
  encryption?: boolean
}

export interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl?: number
  size: number
  accessCount: number
  lastAccessed: number
}

/**
 * Multi-level cache manager
 */
export class CacheManager {
  private static instance: CacheManager
  private memoryCache = new Map<string, CacheEntry<any>>()
  private cacheStats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    totalSize: 0
  }

  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager()
    }
    return CacheManager.instance
  }

  /**
   * Set cache entry
   */
  async set<T>(
    key: string, 
    data: T, 
    config: CacheConfig = { type: 'memory' }
  ): Promise<void> {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: config.ttl,
      size: this.calculateSize(data),
      accessCount: 0,
      lastAccessed: Date.now()
    }

    switch (config.type) {
      case 'memory':
        await this.setMemoryCache(key, entry, config)
        break
      case 'localStorage':
        await this.setLocalStorageCache(key, entry, config)
        break
      case 'sessionStorage':
        await this.setSessionStorageCache(key, entry, config)
        break
      case 'indexedDB':
        await this.setIndexedDBCache(key, entry, config)
        break
    }
  }

  /**
   * Get cache entry
   */
  async get<T>(key: string, config: CacheConfig = { type: 'memory' }): Promise<T | null> {
    let entry: CacheEntry<T> | null = null

    switch (config.type) {
      case 'memory':
        entry = this.getMemoryCache(key)
        break
      case 'localStorage':
        entry = await this.getLocalStorageCache(key)
        break
      case 'sessionStorage':
        entry = await this.getSessionStorageCache(key)
        break
      case 'indexedDB':
        entry = await this.getIndexedDBCache(key)
        break
    }

    if (!entry) {
      this.cacheStats.misses++
      return null
    }

    // Check TTL
    if (entry.ttl && Date.now() - entry.timestamp > entry.ttl) {
      await this.delete(key, config)
      this.cacheStats.misses++
      return null
    }

    // Update access stats
    entry.accessCount++
    entry.lastAccessed = Date.now()
    this.cacheStats.hits++

    return entry.data
  }

  /**
   * Delete cache entry
   */
  async delete(key: string, config: CacheConfig = { type: 'memory' }): Promise<void> {
    switch (config.type) {
      case 'memory':
        this.memoryCache.delete(key)
        break
      case 'localStorage':
        if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
          localStorage.removeItem(key)
        }
        break
      case 'sessionStorage':
        if (typeof window !== 'undefined' && typeof sessionStorage !== 'undefined') {
          sessionStorage.removeItem(key)
        }
        break
      case 'indexedDB':
        await this.deleteIndexedDBCache(key)
        break
    }
  }

  /**
   * Clear all cache
   */
  async clear(config: CacheConfig = { type: 'memory' }): Promise<void> {
    switch (config.type) {
      case 'memory':
        this.memoryCache.clear()
        break
      case 'localStorage':
        if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
          localStorage.clear()
        }
        break
      case 'sessionStorage':
        if (typeof window !== 'undefined' && typeof sessionStorage !== 'undefined') {
          sessionStorage.clear()
        }
        break
      case 'indexedDB':
        await this.clearIndexedDBCache()
        break
    }
  }

  private async setMemoryCache<T>(
    key: string, 
    entry: CacheEntry<T>, 
    config: CacheConfig
  ): Promise<void> {
    // Check memory limits
    if (config.maxSize && this.cacheStats.totalSize + entry.size > config.maxSize) {
      await this.evictLRU()
    }

    this.memoryCache.set(key, entry)
    this.cacheStats.totalSize += entry.size
  }

  private getMemoryCache<T>(key: string): CacheEntry<T> | null {
    return this.memoryCache.get(key) || null
  }

  private async setLocalStorageCache<T>(
    key: string,
    entry: CacheEntry<T>,
    config: CacheConfig
  ): Promise<void> {
    try {
      // Check if we're in the browser environment
      if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
        const serialized = JSON.stringify(entry)
        localStorage.setItem(`cache_${key}`, serialized)
      }
    } catch (error) {
      console.warn('LocalStorage cache failed:', error)
    }
  }

  private async getLocalStorageCache<T>(key: string): Promise<CacheEntry<T> | null> {
    try {
      // Check if we're in the browser environment
      if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
        const serialized = localStorage.getItem(`cache_${key}`)
        return serialized ? JSON.parse(serialized) : null
      }
      return null
    } catch (error) {
      console.warn('LocalStorage cache retrieval failed:', error)
      return null
    }
  }

  private async setSessionStorageCache<T>(
    key: string,
    entry: CacheEntry<T>,
    config: CacheConfig
  ): Promise<void> {
    try {
      // Check if we're in the browser environment
      if (typeof window !== 'undefined' && typeof sessionStorage !== 'undefined') {
        const serialized = JSON.stringify(entry)
        sessionStorage.setItem(`cache_${key}`, serialized)
      }
    } catch (error) {
      console.warn('SessionStorage cache failed:', error)
    }
  }

  private async getSessionStorageCache<T>(key: string): Promise<CacheEntry<T> | null> {
    try {
      // Check if we're in the browser environment
      if (typeof window !== 'undefined' && typeof sessionStorage !== 'undefined') {
        const serialized = sessionStorage.getItem(`cache_${key}`)
        return serialized ? JSON.parse(serialized) : null
      }
      return null
    } catch (error) {
      console.warn('SessionStorage cache retrieval failed:', error)
      return null
    }
  }

  private async setIndexedDBCache<T>(
    key: string, 
    entry: CacheEntry<T>, 
    config: CacheConfig
  ): Promise<void> {
    // IndexedDB implementation would go here
    // For now, fallback to localStorage
    await this.setLocalStorageCache(key, entry, config)
  }

  private async getIndexedDBCache<T>(key: string): Promise<CacheEntry<T> | null> {
    // IndexedDB implementation would go here
    // For now, fallback to localStorage
    return this.getLocalStorageCache(key)
  }

  private async deleteIndexedDBCache(key: string): Promise<void> {
    localStorage.removeItem(`cache_${key}`)
  }

  private async clearIndexedDBCache(): Promise<void> {
    const keys = Object.keys(localStorage).filter(key => key.startsWith('cache_'))
    keys.forEach(key => localStorage.removeItem(key))
  }

  private async evictLRU(): Promise<void> {
    // Evict least recently used items
    const entries = Array.from(this.memoryCache.entries())
    entries.sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed)
    
    // Remove oldest 25% of entries
    const toRemove = Math.ceil(entries.length * 0.25)
    for (let i = 0; i < toRemove; i++) {
      const [key, entry] = entries[i]
      this.memoryCache.delete(key)
      this.cacheStats.totalSize -= entry.size
      this.cacheStats.evictions++
    }
  }

  private calculateSize(data: any): number {
    // Rough size calculation
    return JSON.stringify(data).length * 2 // 2 bytes per character
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const hitRate = this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses) || 0
    
    return {
      ...this.cacheStats,
      hitRate: Math.round(hitRate * 100),
      memoryEntries: this.memoryCache.size
    }
  }
}

export const cacheManager = CacheManager.getInstance()

/**
 * React hook for cached data
 */
export const useCachedData = <T>(
  key: string,
  fetcher: () => Promise<T>,
  config: CacheConfig = { type: 'memory', ttl: 5 * 60 * 1000 } // 5 minutes default
) => {
  const [data, setData] = React.useState<T | null>(null)
  const [loading, setLoading] = React.useState(true)
  const [error, setError] = React.useState<string | null>(null)

  React.useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        setError(null)

        // Try cache first
        const cached = await cacheManager.get<T>(key, config)
        if (cached) {
          setData(cached)
          setLoading(false)
          return
        }

        // Fetch fresh data
        const freshData = await fetcher()
        await cacheManager.set(key, freshData, config)
        setData(freshData)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [key, config])

  return { data, loading, error }
}

/**
 * Computed value cache with memoization
 */
export class ComputedCache {
  private cache = new Map<string, { value: any; deps: any[]; timestamp: number }>()

  compute<T>(key: string, computeFn: () => T, dependencies: any[], ttl: number = 60000): T {
    const cached = this.cache.get(key)
    
    // Check if cache is valid
    if (cached) {
      const isExpired = Date.now() - cached.timestamp > ttl
      const depsChanged = dependencies.some((dep, index) => dep !== cached.deps[index])
      
      if (!isExpired && !depsChanged) {
        return cached.value
      }
    }

    // Compute new value
    const value = computeFn()
    this.cache.set(key, {
      value,
      deps: [...dependencies],
      timestamp: Date.now()
    })

    return value
  }

  clear() {
    this.cache.clear()
  }
}

export const computedCache = new ComputedCache()

/**
 * Asset cache for images, models, etc.
 */
export class AssetCache {
  private cache = new Map<string, { blob: Blob; url: string; timestamp: number }>()
  private maxSize = 50 * 1024 * 1024 // 50MB

  async cacheAsset(url: string, ttl: number = 24 * 60 * 60 * 1000): Promise<string> {
    const cached = this.cache.get(url)
    
    if (cached && Date.now() - cached.timestamp < ttl) {
      return cached.url
    }

    try {
      const response = await fetch(url)
      const blob = await response.blob()
      const objectUrl = URL.createObjectURL(blob)
      
      // Check size limits
      if (blob.size > this.maxSize) {
        return url // Don't cache large assets
      }

      // Clean up old URL if exists
      if (cached) {
        URL.revokeObjectURL(cached.url)
      }

      this.cache.set(url, {
        blob,
        url: objectUrl,
        timestamp: Date.now()
      })

      return objectUrl
    } catch (error) {
      console.warn('Asset caching failed:', error)
      return url
    }
  }

  clearExpired() {
    const now = Date.now()
    const ttl = 24 * 60 * 60 * 1000 // 24 hours

    this.cache.forEach((cached, url) => {
      if (now - cached.timestamp > ttl) {
        URL.revokeObjectURL(cached.url)
        this.cache.delete(url)
      }
    })
  }

  dispose() {
    this.cache.forEach(cached => {
      URL.revokeObjectURL(cached.url)
    })
    this.cache.clear()
  }
}

export const assetCache = new AssetCache()

/**
 * Service Worker cache strategies
 */
export const serviceWorkerCache = {
  // Cache strategies for different resource types
  strategies: {
    static: 'CacheFirst',      // CSS, JS, fonts
    images: 'CacheFirst',      // Images, icons
    api: 'NetworkFirst',       // API responses
    documents: 'StaleWhileRevalidate' // HTML pages
  },

  // Cache configuration
  config: {
    staticCacheName: 'nanohero-static-v1',
    imageCacheName: 'nanohero-images-v1',
    apiCacheName: 'nanohero-api-v1',
    maxEntries: 100,
    maxAgeSeconds: 30 * 24 * 60 * 60 // 30 days
  },

  // Register service worker
  register: async () => {
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js')
        console.log('Service Worker registered:', registration)
        return registration
      } catch (error) {
        console.error('Service Worker registration failed:', error)
      }
    }
  }
}

// Import React for hooks
import React from 'react'
