'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import {
  Clock,
  Star,
  CheckCircle,
  XCircle,
  Play,
  GripVertical,
  Trash2,
  Zap,
  Brain
} from 'lucide-react'

import { KanbanLessonCard, WeekDay, DifficultyLevel } from '../types/curriculum'

interface SortableLessonCardProps {
  lesson: KanbanLessonCard
  onRemove: (lessonId: string, fromDay: WeekDay) => void
  day: WeekDay
}

export function SortableLessonCard({ lesson, onRemove, day }: SortableLessonCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({
    id: lesson.id,
    data: {
      type: 'lesson-card',
      lesson,
      day
    }
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.8 : 1,
    zIndex: isDragging ? 1000 : 'auto'
  }

  // Get status color
  const getStatusColor = (status: KanbanLessonCard['status']) => {
    switch (status) {
      case 'completed': return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'in-progress': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'skipped': return 'bg-red-500/20 text-red-400 border-red-500/30'
      default: return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
    }
  }

  // Get difficulty color
  const getDifficultyColor = (difficulty: DifficultyLevel) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-500/20 text-green-400'
      case 'intermediate': return 'bg-yellow-500/20 text-yellow-400'
      case 'advanced': return 'bg-red-500/20 text-red-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  // Get priority color
  const getPriorityColor = (priority: KanbanLessonCard['priority']) => {
    switch (priority) {
      case 'high': return 'border-l-red-500'
      case 'medium': return 'border-l-yellow-500'
      case 'low': return 'border-l-green-500'
      default: return 'border-l-gray-500'
    }
  }

  // Get status icon
  const getStatusIcon = (status: KanbanLessonCard['status']) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-400" />
      case 'in-progress': return <Play className="w-4 h-4 text-yellow-400" />
      case 'skipped': return <XCircle className="w-4 h-4 text-red-400" />
      default: return <Clock className="w-4 h-4 text-blue-400" />
    }
  }

  return (
    <div ref={setNodeRef} style={style}>
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        whileHover={{ scale: 1.02 }}
        className="group"
      >
        <Card className={`bg-gray-800/30 border-gray-700/50 hover:border-cyan-500/30 transition-all duration-200 border-l-4 ${getPriorityColor(lesson.priority)} ${
          isDragging ? 'border-cyan-500/50 bg-cyan-500/10 shadow-lg shadow-cyan-500/20' : ''
        }`}>
          <CardContent className="p-3">
            {/* Lesson Header */}
            <div className="flex items-start justify-between mb-2">
              <div className="flex items-center gap-2">
                <button
                  {...attributes}
                  {...listeners}
                  className="cursor-grab active:cursor-grabbing p-1 hover:bg-gray-700/50 rounded transition-colors touch-none"
                  aria-label="Drag lesson"
                  type="button"
                >
                  <GripVertical className="w-4 h-4 text-gray-500 group-hover:text-cyan-400 transition-colors" />
                </button>
                {getStatusIcon(lesson.status)}
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onRemove(lesson.id, day)}
                className="opacity-0 group-hover:opacity-100 transition-opacity p-1 h-auto hover:bg-red-500/20"
              >
                <Trash2 className="w-3 h-3 text-red-400" />
              </Button>
            </div>

            {/* Lesson Title */}
            <h4 className="text-sm font-medium text-white mb-2 line-clamp-2">
              {lesson.title}
            </h4>

            {/* Lesson Metadata */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Badge className={`text-xs ${getDifficultyColor(lesson.difficulty)}`}>
                  {lesson.difficulty}
                </Badge>
                
                <div className="flex items-center text-xs text-gray-400">
                  <Clock className="w-3 h-3 mr-1" />
                  {lesson.duration}m
                </div>
              </div>

              <div className="flex items-center justify-between">
                <Badge className={`text-xs ${getStatusColor(lesson.status)}`}>
                  {lesson.status.replace('-', ' ')}
                </Badge>
                
                {lesson.priority !== 'medium' && (
                  <Badge className={`text-xs ${
                    lesson.priority === 'high' 
                      ? 'bg-red-500/20 text-red-400' 
                      : 'bg-green-500/20 text-green-400'
                  }`}>
                    {lesson.priority} priority
                  </Badge>
                )}
              </div>
            </div>

            {/* Completion Stats */}
            {lesson.status === 'completed' && lesson.completionStats && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="mt-3 pt-3 border-t border-gray-700/50 space-y-2"
              >
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-400">Time Spent</span>
                  <span className="text-green-400">{lesson.completionStats.timeSpent}m</span>
                </div>
                
                {lesson.completionStats.score && (
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-400">Score</span>
                    <div className="flex items-center gap-1">
                      <Star className="w-3 h-3 text-yellow-400" />
                      <span className="text-yellow-400">{lesson.completionStats.score}%</span>
                    </div>
                  </div>
                )}
                
                {lesson.completionStats.xpEarned > 0 && (
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-400">XP Earned</span>
                    <div className="flex items-center gap-1">
                      <Zap className="w-3 h-3 text-purple-400" />
                      <span className="text-purple-400">{lesson.completionStats.xpEarned}</span>
                    </div>
                  </div>
                )}
                
                {lesson.completionStats.skillsEarned.length > 0 && (
                  <div className="space-y-1">
                    <span className="text-xs text-gray-400">Skills Developed</span>
                    <div className="flex flex-wrap gap-1">
                      {lesson.completionStats.skillsEarned.slice(0, 2).map(skill => (
                        <Badge key={skill} className="text-xs bg-cyan-500/20 text-cyan-400">
                          <Brain className="w-2 h-2 mr-1" />
                          {skill}
                        </Badge>
                      ))}
                      {lesson.completionStats.skillsEarned.length > 2 && (
                        <Badge className="text-xs bg-gray-500/20 text-gray-400">
                          +{lesson.completionStats.skillsEarned.length - 2}
                        </Badge>
                      )}
                    </div>
                  </div>
                )}
              </motion.div>
            )}

            {/* Parent Notes */}
            {lesson.parentNotes && (
              <div className="mt-3 pt-3 border-t border-gray-700/50">
                <div className="text-xs text-gray-400 mb-1">Parent Note</div>
                <p className="text-xs text-gray-300 italic">&quot;{lesson.parentNotes}&quot;</p>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
