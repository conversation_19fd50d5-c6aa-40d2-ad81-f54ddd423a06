'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import {
  User,
  Edit,
  Save,
  Users,
  Settings,
  Bell,
  CreditCard,
  Shield,
  BarChart3,
  Download,
  Trash2,
  Plus,
  Eye,
  EyeOff,
  Globe,
  Clock,
  Lock,
  Key,
  Calendar,
  DollarSign,
  FileText,
  AlertTriangle,
  CheckCircle,
  UserPlus,
  Crown,
  Gift,
  TrendingUp,
  TrendingDown,
  Award,
  Target,
  BookOpen,
  GraduationCap,
  Lightbulb,
  Send,
  Calculator,
  Receipt,
  Building,
  School,
  UserCheck,
  UserX,
  Clock4,
  Brain,
  Star,
  Info,
  Flag
} from 'lucide-react'

export default function ParentProfilePage() {
  const [activeTab, setActiveTab] = useState('personal')
  const [isEditing, setIsEditing] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showInviteModal, setShowInviteModal] = useState(false)
  const [_showCurriculumInsights, _setShowCurriculumInsights] = useState(false)
  const [selectedInsightPeriod, setSelectedInsightPeriod] = useState('weekly')
  const [showTaxExport, setShowTaxExport] = useState(false)
  const [showAuditLog, setShowAuditLog] = useState(false)
  const [showDataExport, setShowDataExport] = useState(false)
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false)
  const [deleteConfirmationText, setDeleteConfirmationText] = useState('')
  const [showComplianceDetails, setShowComplianceDetails] = useState(false)

  // Enhanced profile state with all PRD requirements
  const [profile, setProfile] = useState({
    // Personal Information
    firstName: 'Sarah',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    country: 'United States',
    timezone: 'America/Los_Angeles',
    language: 'English',
    joinDate: 'January 2024',

    // Children Management
    children: [
      {
        id: 'emma',
        name: 'Emma Johnson',
        age: 12,
        grade: '7th Grade',
        avatar: '👧',
        learningMode: 'AUTO', // AUTO or CUSTOM
        dailyLimit: 180, // minutes
        screenTimeLimit: 120, // minutes
        lastLogin: '2 hours ago',
        completionRate: 78,
        skillGrowth: 15,
        accessPassword: '****',
        status: 'active'
      },
      {
        id: 'alex',
        name: 'Alex Johnson',
        age: 9,
        grade: '4th Grade',
        avatar: '👦',
        learningMode: 'CUSTOM',
        dailyLimit: 120,
        screenTimeLimit: 90,
        lastLogin: '1 day ago',
        completionRate: 65,
        skillGrowth: 22,
        accessPassword: '****',
        status: 'active'
      }
    ],

    // Billing & Subscription
    subscription: {
      plan: 'Family Plan',
      status: 'active',
      nextBilling: '2024-02-15',
      amount: '$29.99',
      paymentMethod: '**** 4532',
      addOns: ['Community Access', 'Avatar Skins'],
      billingHistory: [
        { date: '2024-01-15', amount: '$29.99', status: 'paid', invoice: 'INV-001' },
        { date: '2023-12-15', amount: '$29.99', status: 'paid', invoice: 'INV-002' }
      ]
    },

    // Settings & Preferences
    notifications: {
      email: true,
      push: true,
      sms: false,
      weekly: true,
      lessonCompletion: true,
      reminders: true,
      safety: true
    },
    aiPersonalization: {
      adaptiveDifficulty: true,
      aiMentoring: true,
      personalizedContent: true
    },
    accessibility: {
      highContrast: false,
      textScaling: 'normal',
      screenReader: false
    },
    contentFilters: {
      language: 'English',
      restrictMature: true,
      restrictViolent: true
    },
    rewardSystems: {
      xpRewards: true,
      cosmeticRewards: true,
      badges: true
    },

    // Privacy & Permissions
    consent: {
      dataAnalytics: true,
      aiFeedback: true,
      marketingEmails: false,
      thirdPartySharing: false,
      coppaCompliance: true,
      gdprCompliance: true,
      ccpaCompliance: true
    },

    // Security & Compliance Data
    securityCompliance: {
      gdprStatus: {
        dataProcessingConsent: true,
        rightToBeForgettenRequested: false,
        dataPortabilityRequested: false,
        lastConsentUpdate: '2024-01-15',
        consentVersion: '2.1',
        lawfulBasis: 'consent'
      },
      ccpaStatus: {
        doNotSellPersonalInfo: true,
        optOutRequested: false,
        personalInfoCategories: ['educational_data', 'usage_analytics'],
        lastOptOutUpdate: '2024-01-15'
      },
      coppaStatus: {
        parentalConsentVerified: true,
        consentMethod: 'credit_card_verification',
        consentDate: '2024-01-15',
        parentEmail: '<EMAIL>',
        childrenUnder13: ['alex']
      },
      auditTrail: [
        {
          id: 'audit-001',
          timestamp: '2024-02-10T14:30:00Z',
          action: 'profile_update',
          details: 'Updated notification preferences',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0...',
          location: 'San Francisco, CA',
          verified: true
        },
        {
          id: 'audit-002',
          timestamp: '2024-02-09T10:15:00Z',
          action: 'curriculum_override',
          details: 'Modified Emma\'s math curriculum',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0...',
          location: 'San Francisco, CA',
          verified: true
        },
        {
          id: 'audit-003',
          timestamp: '2024-02-08T16:45:00Z',
          action: 'family_member_invited',
          details: 'Invited <EMAIL> as tutor',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0...',
          location: 'San Francisco, CA',
          verified: true
        }
      ],
      dataRetention: {
        personalDataRetentionPeriod: '7 years',
        learningDataRetentionPeriod: '10 years',
        auditLogRetentionPeriod: '3 years',
        automaticDeletionEnabled: true,
        nextReviewDate: '2025-01-15'
      },
      encryptionStatus: {
        dataAtRest: 'AES-256',
        dataInTransit: 'TLS 1.3',
        backupEncryption: 'AES-256',
        keyRotationEnabled: true,
        lastKeyRotation: '2024-01-01'
      }
    },
    familySharing: {
      spouse: {
        email: '<EMAIL>',
        role: 'co-parent',
        permissions: {
          viewProgress: true,
          editCurriculum: true,
          manageSettings: true,
          viewBilling: true,
          receiveNotifications: true
        },
        joinedDate: '2024-01-15',
        lastActive: '2 hours ago'
      },
      others: [
        {
          id: 'tutor-1',
          email: '<EMAIL>',
          role: 'tutor',
          name: 'Ms. Sarah Chen',
          permissions: {
            viewProgress: true,
            editCurriculum: false,
            manageSettings: false,
            viewBilling: false,
            receiveNotifications: true
          },
          subjects: ['Mathematics', 'Science'],
          joinedDate: '2024-01-20',
          lastActive: '1 day ago',
          status: 'active'
        }
      ]
    },

    // Curriculum Insights Data
    curriculumInsights: {
      weeklyDigest: {
        totalLessonsCompleted: 24,
        averageEngagement: 87,
        topPerformingSubjects: ['Quantum Physics', 'Digital Literacy'],
        needsAttentionSubjects: ['Creative Writing'],
        learningPatterns: {
          bestPerformanceTime: '10:00 AM - 12:00 PM',
          preferredDifficulty: 'Challenging',
          averageSessionLength: 28 // minutes
        }
      },
      monthlyReport: {
        skillDevelopment: [
          { skill: 'Critical Thinking', growth: 15, trend: 'up' },
          { skill: 'Problem Solving', growth: 12, trend: 'up' },
          { skill: 'Communication', growth: 8, trend: 'stable' },
          { skill: 'Creativity', growth: 5, trend: 'down' }
        ],
        achievements: [
          'Completed Advanced Logic Module',
          'Achieved 30-day learning streak',
          'Mastered Quantum Basics'
        ],
        recommendations: [
          'Increase creative writing activities',
          'Introduce peer collaboration projects',
          'Consider advanced mathematics track'
        ]
      }
    },

    // Tax and Invoice Export Data
    taxExport: {
      eligibleForDeduction: true,
      educationalExpenses: {
        yearToDate: 359.88,
        lastYear: 359.88,
        category: 'Educational Software & Services'
      },
      homeschoolCompliant: true,
      receiptsAvailable: 12
    }
  })

  const handleSave = () => {
    setIsEditing(false)
    // Save logic would go here
  }

  const handleTabChange = (tab: string) => {
    setActiveTab(tab)
    setIsEditing(false) // Exit edit mode when switching tabs
  }

  return (
    <>
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-white font-space-grotesk">
            Parent Profile
          </h1>
          <p className="text-gray-400 mt-1">
            Manage your account information and preferences
          </p>
        </div>

        <Button
          onClick={() => isEditing ? handleSave() : setIsEditing(true)}
          className="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-green-500 hover:from-blue-600 hover:to-green-600"
        >
          {isEditing ? (
            <>
              <Save className="w-4 h-4" />
              Save Changes
            </>
          ) : (
            <>
              <Edit className="w-4 h-4" />
              Edit Profile
            </>
          )}
        </Button>
      </motion.div>

      {/* Main Tabbed Interface */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6 bg-black/40 border border-gray-800/50">
            <TabsTrigger value="personal" className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500/20 data-[state=active]:to-green-500/20">
              <User className="w-4 h-4" />
              Personal
            </TabsTrigger>
            <TabsTrigger value="children" className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500/20 data-[state=active]:to-green-500/20">
              <Users className="w-4 h-4" />
              Children
            </TabsTrigger>
            <TabsTrigger value="billing" className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500/20 data-[state=active]:to-green-500/20">
              <CreditCard className="w-4 h-4" />
              Billing
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500/20 data-[state=active]:to-green-500/20">
              <Settings className="w-4 h-4" />
              Settings
            </TabsTrigger>
            <TabsTrigger value="privacy" className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500/20 data-[state=active]:to-green-500/20">
              <Shield className="w-4 h-4" />
              Privacy
            </TabsTrigger>
            <TabsTrigger value="dashboard" className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500/20 data-[state=active]:to-green-500/20">
              <BarChart3 className="w-4 h-4" />
              Dashboard
            </TabsTrigger>
          </TabsList>

          {/* Personal Information Tab */}
          <TabsContent value="personal" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Basic Information */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <User className="w-5 h-5 text-blue-400" />
                    Basic Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-gray-400 text-sm">First Name</Label>
                      {isEditing ? (
                        <Input
                          value={profile.firstName}
                          onChange={(e) => setProfile({...profile, firstName: e.target.value})}
                          className="mt-1 bg-gray-800/50 border-gray-700 text-white"
                        />
                      ) : (
                        <p className="text-white mt-1">{profile.firstName}</p>
                      )}
                    </div>
                    <div>
                      <Label className="text-gray-400 text-sm">Last Name</Label>
                      {isEditing ? (
                        <Input
                          value={profile.lastName}
                          onChange={(e) => setProfile({...profile, lastName: e.target.value})}
                          className="mt-1 bg-gray-800/50 border-gray-700 text-white"
                        />
                      ) : (
                        <p className="text-white mt-1">{profile.lastName}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label className="text-gray-400 text-sm">Email Address</Label>
                    <div className="flex items-center gap-2 mt-1">
                      {isEditing ? (
                        <Input
                          value={profile.email}
                          onChange={(e) => setProfile({...profile, email: e.target.value})}
                          className="flex-1 bg-gray-800/50 border-gray-700 text-white"
                        />
                      ) : (
                        <p className="text-white flex-1">{profile.email}</p>
                      )}
                      <Badge variant="outline" className="text-green-400 border-green-500/30">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Verified
                      </Badge>
                    </div>
                  </div>

                  <div>
                    <Label className="text-gray-400 text-sm">Phone Number</Label>
                    {isEditing ? (
                      <Input
                        value={profile.phone}
                        onChange={(e) => setProfile({...profile, phone: e.target.value})}
                        className="mt-1 bg-gray-800/50 border-gray-700 text-white"
                      />
                    ) : (
                      <p className="text-white mt-1">{profile.phone}</p>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-gray-400 text-sm">Country</Label>
                      {isEditing ? (
                        <Select value={profile.country} onValueChange={(value) => setProfile({...profile, country: value})}>
                          <SelectTrigger className="mt-1 bg-gray-800/50 border-gray-700 text-white">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="United States">United States</SelectItem>
                            <SelectItem value="Canada">Canada</SelectItem>
                            <SelectItem value="United Kingdom">United Kingdom</SelectItem>
                            <SelectItem value="Australia">Australia</SelectItem>
                          </SelectContent>
                        </Select>
                      ) : (
                        <p className="text-white mt-1">{profile.country}</p>
                      )}
                    </div>
                    <div>
                      <Label className="text-gray-400 text-sm">Timezone</Label>
                      {isEditing ? (
                        <Select value={profile.timezone} onValueChange={(value) => setProfile({...profile, timezone: value})}>
                          <SelectTrigger className="mt-1 bg-gray-800/50 border-gray-700 text-white">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                            <SelectItem value="America/Denver">Mountain Time</SelectItem>
                            <SelectItem value="America/Chicago">Central Time</SelectItem>
                            <SelectItem value="America/New_York">Eastern Time</SelectItem>
                          </SelectContent>
                        </Select>
                      ) : (
                        <p className="text-white mt-1">Pacific Time</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label className="text-gray-400 text-sm">Preferred Language</Label>
                    {isEditing ? (
                      <Select value={profile.language} onValueChange={(value) => setProfile({...profile, language: value})}>
                        <SelectTrigger className="mt-1 bg-gray-800/50 border-gray-700 text-white">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="English">English</SelectItem>
                          <SelectItem value="Spanish">Spanish</SelectItem>
                          <SelectItem value="French">French</SelectItem>
                          <SelectItem value="German">German</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <p className="text-white mt-1">{profile.language}</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Security & Account */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Lock className="w-5 h-5 text-green-400" />
                    Security & Account
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label className="text-gray-400 text-sm">Password</Label>
                    <div className="flex items-center gap-2 mt-1">
                      <div className="flex-1 flex items-center gap-2">
                        <Input
                          type={showPassword ? "text" : "password"}
                          value="••••••••••••"
                          readOnly
                          className="bg-gray-800/50 border-gray-700 text-white"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setShowPassword(!showPassword)}
                          className="bg-gray-800/50 border-gray-700 hover:bg-gray-700"
                        >
                          {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </Button>
                      </div>
                      <Button variant="outline" size="sm" className="bg-blue-500/20 border-blue-500/30 text-blue-400 hover:bg-blue-500/30">
                        <Key className="w-4 h-4 mr-1" />
                        Change
                      </Button>
                    </div>
                  </div>

                  <div>
                    <Label className="text-gray-400 text-sm">Two-Factor Authentication</Label>
                    <div className="flex items-center justify-between mt-1 p-3 bg-gray-800/30 rounded-lg">
                      <div>
                        <p className="text-white font-medium">2FA Enabled</p>
                        <p className="text-gray-400 text-sm">Extra security for your account</p>
                      </div>
                      <Badge variant="default" className="bg-green-500/20 text-green-400 border-green-500/30">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Active
                      </Badge>
                    </div>
                  </div>

                  <div>
                    <Label className="text-gray-400 text-sm">Account Information</Label>
                    <div className="mt-2 space-y-2">
                      <div className="flex justify-between items-center p-2 bg-gray-800/30 rounded">
                        <span className="text-gray-400 text-sm">Member Since</span>
                        <span className="text-white text-sm">{profile.joinDate}</span>
                      </div>
                      <div className="flex justify-between items-center p-2 bg-gray-800/30 rounded">
                        <span className="text-gray-400 text-sm">Account Type</span>
                        <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30">
                          <Crown className="w-3 h-3 mr-1" />
                          Parent Account
                        </Badge>
                      </div>
                      <div className="flex justify-between items-center p-2 bg-gray-800/30 rounded">
                        <span className="text-gray-400 text-sm">Children Linked</span>
                        <span className="text-cyan-400 text-sm">{profile.children.length} children</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Children Management Tab */}
          <TabsContent value="children" className="space-y-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-xl font-bold text-white font-space-grotesk">Children Profiles</h3>
                <p className="text-gray-400 text-sm">Manage your children&apos;s learning settings and access</p>
              </div>
              <Button className="bg-gradient-to-r from-blue-500 to-green-500 hover:from-blue-600 hover:to-green-600">
                <Plus className="w-4 h-4 mr-2" />
                Add Child
              </Button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {profile.children.map((child) => (
                <Card key={child.id} className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                  <CardHeader>
                    <CardTitle className="text-white font-space-grotesk flex items-center gap-3">
                      <span className="text-2xl">{child.avatar}</span>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          {child.name}
                          <Badge variant={child.status === 'active' ? 'default' : 'secondary'} className="text-xs">
                            {child.status}
                          </Badge>
                        </div>
                        <p className="text-gray-400 text-sm font-normal">Age {child.age} • {child.grade}</p>
                      </div>
                      <Button variant="outline" size="sm" className="bg-gray-800/50 border-gray-700 hover:bg-gray-700">
                        <Edit className="w-4 h-4" />
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Learning Mode */}
                    <div>
                      <Label className="text-gray-400 text-sm">Learning Mode</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <Select value={child.learningMode} onValueChange={(value) => {
                          const updatedChildren = profile.children.map(c =>
                            c.id === child.id ? {...c, learningMode: value} : c
                          )
                          setProfile({...profile, children: updatedChildren})
                        }}>
                          <SelectTrigger className="flex-1 bg-gray-800/50 border-gray-700 text-white">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="AUTO">AUTO - Platform Decides</SelectItem>
                            <SelectItem value="CUSTOM">CUSTOM - Parent Planner</SelectItem>
                          </SelectContent>
                        </Select>
                        {child.learningMode === 'CUSTOM' && (
                          <Button variant="outline" size="sm" className="bg-blue-500/20 border-blue-500/30 text-blue-400">
                            <Calendar className="w-4 h-4 mr-1" />
                            Plan
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* Daily Limits */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-gray-400 text-sm">Study Time (min)</Label>
                        <Input
                          type="number"
                          value={child.dailyLimit}
                          onChange={(e) => {
                            const updatedChildren = profile.children.map(c =>
                              c.id === child.id ? {...c, dailyLimit: parseInt(e.target.value)} : c
                            )
                            setProfile({...profile, children: updatedChildren})
                          }}
                          className="mt-1 bg-gray-800/50 border-gray-700 text-white"
                        />
                      </div>
                      <div>
                        <Label className="text-gray-400 text-sm">Screen Time (min)</Label>
                        <Input
                          type="number"
                          value={child.screenTimeLimit}
                          onChange={(e) => {
                            const updatedChildren = profile.children.map(c =>
                              c.id === child.id ? {...c, screenTimeLimit: parseInt(e.target.value)} : c
                            )
                            setProfile({...profile, children: updatedChildren})
                          }}
                          className="mt-1 bg-gray-800/50 border-gray-700 text-white"
                        />
                      </div>
                    </div>

                    {/* Activity Summary */}
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center p-3 bg-gray-800/30 rounded-lg">
                        <div className="text-lg font-bold text-blue-400">{child.completionRate}%</div>
                        <div className="text-xs text-gray-400">Completion</div>
                      </div>
                      <div className="text-center p-3 bg-gray-800/30 rounded-lg">
                        <div className="text-lg font-bold text-green-400">+{child.skillGrowth}%</div>
                        <div className="text-xs text-gray-400">Skill Growth</div>
                      </div>
                      <div className="text-center p-3 bg-gray-800/30 rounded-lg">
                        <div className="text-lg font-bold text-cyan-400">{child.lastLogin}</div>
                        <div className="text-xs text-gray-400">Last Active</div>
                      </div>
                    </div>

                    {/* Access Controls */}
                    <div className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg">
                      <div>
                        <p className="text-white font-medium text-sm">Child Access Password</p>
                        <p className="text-gray-400 text-xs">Secure login for this child</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-gray-400 text-sm">{child.accessPassword}</span>
                        <Button variant="outline" size="sm" className="bg-gray-700/50 border-gray-600 hover:bg-gray-600">
                          <Key className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>

                    {/* Quick Actions */}
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" className="flex-1 bg-blue-500/20 border-blue-500/30 text-blue-400 hover:bg-blue-500/30">
                        <BarChart3 className="w-4 h-4 mr-1" />
                        View Stats
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1 bg-green-500/20 border-green-500/30 text-green-400 hover:bg-green-500/30">
                        <Calendar className="w-4 h-4 mr-1" />
                        Curriculum
                      </Button>
                      <Button variant="outline" size="sm" className="bg-red-500/20 border-red-500/30 text-red-400 hover:bg-red-500/30">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Billing & Subscription Tab */}
          <TabsContent value="billing" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Current Plan */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Crown className="w-5 h-5 text-yellow-400" />
                    Current Plan
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-lg border border-yellow-500/20">
                    <div>
                      <h4 className="text-white font-bold text-lg">{profile.subscription.plan}</h4>
                      <p className="text-gray-400 text-sm">Full access for your family</p>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-yellow-400">{profile.subscription.amount}</div>
                      <div className="text-gray-400 text-sm">per month</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-2 bg-gray-800/30 rounded">
                      <span className="text-gray-400 text-sm">Status</span>
                      <Badge variant="default" className="bg-green-500/20 text-green-400 border-green-500/30">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        {profile.subscription.status}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center p-2 bg-gray-800/30 rounded">
                      <span className="text-gray-400 text-sm">Next Billing</span>
                      <span className="text-white text-sm">{profile.subscription.nextBilling}</span>
                    </div>
                    <div className="flex justify-between items-center p-2 bg-gray-800/30 rounded">
                      <span className="text-gray-400 text-sm">Payment Method</span>
                      <span className="text-white text-sm">{profile.subscription.paymentMethod}</span>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" className="flex-1 bg-blue-500/20 border-blue-500/30 text-blue-400 hover:bg-blue-500/30">
                      Upgrade Plan
                    </Button>
                    <Button variant="outline" className="flex-1 bg-gray-700/50 border-gray-600 hover:bg-gray-600">
                      Manage Plan
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Payment Method */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <CreditCard className="w-5 h-5 text-blue-400" />
                    Payment Method
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3 p-3 bg-gray-800/30 rounded-lg">
                    <div className="w-10 h-6 bg-gradient-to-r from-blue-500 to-blue-600 rounded flex items-center justify-center">
                      <CreditCard className="w-4 h-4 text-white" />
                    </div>
                    <div className="flex-1">
                      <p className="text-white font-medium">•••• •••• •••• 4532</p>
                      <p className="text-gray-400 text-sm">Expires 12/26</p>
                    </div>
                    <Badge variant="default" className="bg-green-500/20 text-green-400 border-green-500/30">
                      Primary
                    </Badge>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" className="flex-1 bg-gray-700/50 border-gray-600 hover:bg-gray-600">
                      <Plus className="w-4 h-4 mr-1" />
                      Add Card
                    </Button>
                    <Button variant="outline" className="flex-1 bg-gray-700/50 border-gray-600 hover:bg-gray-600">
                      <Edit className="w-4 h-4 mr-1" />
                      Update
                    </Button>
                  </div>

                  <div className="pt-4 border-t border-gray-800/50">
                    <h5 className="text-white font-medium mb-2">Alternative Payment Methods</h5>
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full justify-start bg-gray-800/30 border-gray-700 hover:bg-gray-700">
                        <div className="w-6 h-6 bg-blue-600 rounded mr-2 flex items-center justify-center">
                          <span className="text-white text-xs font-bold">P</span>
                        </div>
                        Add PayPal
                      </Button>
                      <Button variant="outline" className="w-full justify-start bg-gray-800/30 border-gray-700 hover:bg-gray-700">
                        <DollarSign className="w-4 h-4 mr-2" />
                        Bank Transfer
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Add-ons & Features */}
            <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
              <CardHeader>
                <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                  <Gift className="w-5 h-5 text-purple-400" />
                  Add-ons & Features
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {profile.subscription.addOns.map((addon, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg">
                      <div>
                        <p className="text-white font-medium text-sm">{addon}</p>
                        <p className="text-gray-400 text-xs">Active</p>
                      </div>
                      <Badge variant="default" className="bg-purple-500/20 text-purple-400 border-purple-500/30">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        On
                      </Badge>
                    </div>
                  ))}
                  <div className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg border-2 border-dashed border-gray-600">
                    <div>
                      <p className="text-gray-400 font-medium text-sm">More Add-ons</p>
                      <p className="text-gray-500 text-xs">Explore features</p>
                    </div>
                    <Button variant="outline" size="sm" className="bg-blue-500/20 border-blue-500/30 text-blue-400">
                      <Plus className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Billing History */}
            <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
              <CardHeader>
                <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                  <FileText className="w-5 h-5 text-green-400" />
                  Billing History
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {profile.subscription.billingHistory.map((bill, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                          <CheckCircle className="w-4 h-4 text-green-400" />
                        </div>
                        <div>
                          <p className="text-white font-medium text-sm">{bill.amount}</p>
                          <p className="text-gray-400 text-xs">{bill.date}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="default" className="bg-green-500/20 text-green-400 border-green-500/30">
                          {bill.status}
                        </Badge>
                        <Button variant="outline" size="sm" className="bg-gray-700/50 border-gray-600 hover:bg-gray-600">
                          <Download className="w-3 h-3 mr-1" />
                          PDF
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-4 pt-4 border-t border-gray-800/50">
                  <div className="flex gap-2">
                    <Button variant="outline" className="flex-1 bg-gray-700/50 border-gray-600 hover:bg-gray-600">
                      <FileText className="w-4 h-4 mr-2" />
                      View All Invoices
                    </Button>
                    <Dialog open={showTaxExport} onOpenChange={setShowTaxExport}>
                      <DialogTrigger asChild>
                        <Button variant="outline" className="bg-green-500/20 border-green-500/30 text-green-400 hover:bg-green-500/30">
                          <Calculator className="w-4 h-4 mr-2" />
                          Tax Export
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="bg-black/90 border-gray-800/50 text-white max-w-2xl">
                        <DialogHeader>
                          <DialogTitle className="text-white font-space-grotesk flex items-center gap-2">
                            <Receipt className="w-5 h-5 text-green-400" />
                            Educational Expense Tax Export
                          </DialogTitle>
                        </DialogHeader>
                        <div className="space-y-6">
                          {/* Tax Eligibility Status */}
                          <div className="p-4 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg border border-green-500/20">
                            <div className="flex items-center gap-3 mb-3">
                              <div className="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center">
                                <CheckCircle className="w-5 h-5 text-green-400" />
                              </div>
                              <div>
                                <h4 className="text-white font-medium">Tax Deduction Eligible</h4>
                                <p className="text-gray-400 text-sm">Your educational expenses qualify for tax deductions</p>
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <div className="p-3 bg-gray-800/30 rounded-lg">
                                <div className="text-2xl font-bold text-green-400">${profile.taxExport.educationalExpenses.yearToDate}</div>
                                <div className="text-gray-400 text-sm">Year to Date Expenses</div>
                              </div>
                              <div className="p-3 bg-gray-800/30 rounded-lg">
                                <div className="text-2xl font-bold text-blue-400">${profile.taxExport.educationalExpenses.lastYear}</div>
                                <div className="text-gray-400 text-sm">Previous Year Total</div>
                              </div>
                            </div>
                          </div>

                          {/* Educational Category */}
                          <div className="p-4 bg-gray-800/30 rounded-lg">
                            <h5 className="text-white font-medium mb-3 flex items-center gap-2">
                              <School className="w-4 h-4 text-blue-400" />
                              Tax Category Information
                            </h5>
                            <div className="space-y-3">
                              <div className="flex justify-between items-center">
                                <span className="text-gray-400">Category:</span>
                                <Badge variant="outline" className="text-blue-400 border-blue-500/30">
                                  {profile.taxExport.educationalExpenses.category}
                                </Badge>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-gray-400">Homeschool Compliant:</span>
                                <Badge variant="default" className="bg-green-500/20 text-green-400 border-green-500/30">
                                  <CheckCircle className="w-3 h-3 mr-1" />
                                  {profile.taxExport.homeschoolCompliant ? 'Yes' : 'No'}
                                </Badge>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-gray-400">Available Receipts:</span>
                                <span className="text-white">{profile.taxExport.receiptsAvailable} documents</span>
                              </div>
                            </div>
                          </div>

                          {/* Export Options */}
                          <div className="space-y-3">
                            <h5 className="text-white font-medium flex items-center gap-2">
                              <Download className="w-4 h-4 text-purple-400" />
                              Export Options
                            </h5>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              <Button variant="outline" className="justify-start bg-blue-500/20 border-blue-500/30 text-blue-400 hover:bg-blue-500/30">
                                <FileText className="w-4 h-4 mr-2" />
                                Tax Summary Report (PDF)
                              </Button>
                              <Button variant="outline" className="justify-start bg-green-500/20 border-green-500/30 text-green-400 hover:bg-green-500/30">
                                <Receipt className="w-4 h-4 mr-2" />
                                All Receipts (ZIP)
                              </Button>
                              <Button variant="outline" className="justify-start bg-purple-500/20 border-purple-500/30 text-purple-400 hover:bg-purple-500/30">
                                <Calculator className="w-4 h-4 mr-2" />
                                TurboTax Import File
                              </Button>
                              <Button variant="outline" className="justify-start bg-yellow-500/20 border-yellow-500/30 text-yellow-400 hover:bg-yellow-500/30">
                                <Building className="w-4 h-4 mr-2" />
                                CPA-Ready Package
                              </Button>
                            </div>
                          </div>

                          {/* Tax Tips */}
                          <div className="p-4 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                            <h5 className="text-yellow-400 font-medium mb-2 flex items-center gap-2">
                              <Lightbulb className="w-4 h-4" />
                              Tax Deduction Tips
                            </h5>
                            <div className="space-y-1 text-sm text-gray-300">
                              <div>• Educational software and online learning platforms are typically deductible</div>
                              <div>• Keep all receipts and documentation for audit purposes</div>
                              <div>• Consult with a tax professional for specific advice</div>
                              <div>• Some states offer additional educational tax credits</div>
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex gap-3">
                            <Button className="flex-1 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600">
                              <Download className="w-4 h-4 mr-2" />
                              Download Complete Tax Package
                            </Button>
                            <Button variant="outline" onClick={() => setShowTaxExport(false)} className="bg-gray-700/50 border-gray-600 hover:bg-gray-600">
                              Close
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Settings & Preferences Tab */}
          <TabsContent value="settings" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Notification Settings */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Bell className="w-5 h-5 text-yellow-400" />
                    Notification Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {Object.entries(profile.notifications).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg">
                      <div>
                        <p className="text-white font-medium text-sm capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </p>
                        <p className="text-gray-400 text-xs">
                          {key === 'email' && 'Receive updates via email'}
                          {key === 'push' && 'Real-time browser notifications'}
                          {key === 'sms' && 'Text message alerts'}
                          {key === 'weekly' && 'Weekly progress summaries'}
                          {key === 'lessonCompletion' && 'When lessons are completed'}
                          {key === 'reminders' && 'Study time reminders'}
                          {key === 'safety' && 'Safety and security alerts'}
                        </p>
                      </div>
                      <Switch
                        checked={value}
                        onCheckedChange={(checked) => {
                          setProfile({
                            ...profile,
                            notifications: { ...profile.notifications, [key]: checked }
                          })
                        }}
                      />
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* AI Personalization */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Settings className="w-5 h-5 text-purple-400" />
                    AI Personalization
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {Object.entries(profile.aiPersonalization).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg">
                      <div>
                        <p className="text-white font-medium text-sm capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </p>
                        <p className="text-gray-400 text-xs">
                          {key === 'adaptiveDifficulty' && 'AI adjusts lesson difficulty automatically'}
                          {key === 'aiMentoring' && 'Enable AI-powered learning guidance'}
                          {key === 'personalizedContent' && 'Customize content based on interests'}
                        </p>
                      </div>
                      <Switch
                        checked={value}
                        onCheckedChange={(checked) => {
                          setProfile({
                            ...profile,
                            aiPersonalization: { ...profile.aiPersonalization, [key]: checked }
                          })
                        }}
                      />
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Accessibility Options */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Eye className="w-5 h-5 text-blue-400" />
                    Accessibility Options
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg">
                    <div>
                      <p className="text-white font-medium text-sm">High Contrast Mode</p>
                      <p className="text-gray-400 text-xs">Enhanced visibility for better readability</p>
                    </div>
                    <Switch
                      checked={profile.accessibility.highContrast}
                      onCheckedChange={(checked) => {
                        setProfile({
                          ...profile,
                          accessibility: { ...profile.accessibility, highContrast: checked }
                        })
                      }}
                    />
                  </div>

                  <div>
                    <Label className="text-gray-400 text-sm">Text Scaling</Label>
                    <Select
                      value={profile.accessibility.textScaling}
                      onValueChange={(value) => setProfile({
                        ...profile,
                        accessibility: { ...profile.accessibility, textScaling: value }
                      })}
                    >
                      <SelectTrigger className="mt-1 bg-gray-800/50 border-gray-700 text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="small">Small</SelectItem>
                        <SelectItem value="normal">Normal</SelectItem>
                        <SelectItem value="large">Large</SelectItem>
                        <SelectItem value="extra-large">Extra Large</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg">
                    <div>
                      <p className="text-white font-medium text-sm">Screen Reader Support</p>
                      <p className="text-gray-400 text-xs">Enhanced compatibility with screen readers</p>
                    </div>
                    <Switch
                      checked={profile.accessibility.screenReader}
                      onCheckedChange={(checked) => {
                        setProfile({
                          ...profile,
                          accessibility: { ...profile.accessibility, screenReader: checked }
                        })
                      }}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Content Filters */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Shield className="w-5 h-5 text-green-400" />
                    Content Filters
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label className="text-gray-400 text-sm">Content Language</Label>
                    <Select
                      value={profile.contentFilters.language}
                      onValueChange={(value) => setProfile({
                        ...profile,
                        contentFilters: { ...profile.contentFilters, language: value }
                      })}
                    >
                      <SelectTrigger className="mt-1 bg-gray-800/50 border-gray-700 text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="English">English</SelectItem>
                        <SelectItem value="Spanish">Spanish</SelectItem>
                        <SelectItem value="French">French</SelectItem>
                        <SelectItem value="German">German</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg">
                    <div>
                      <p className="text-white font-medium text-sm">Restrict Mature Content</p>
                      <p className="text-gray-400 text-xs">Filter age-inappropriate material</p>
                    </div>
                    <Switch
                      checked={profile.contentFilters.restrictMature}
                      onCheckedChange={(checked) => {
                        setProfile({
                          ...profile,
                          contentFilters: { ...profile.contentFilters, restrictMature: checked }
                        })
                      }}
                    />
                  </div>

                  <div className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg">
                    <div>
                      <p className="text-white font-medium text-sm">Restrict Violent Content</p>
                      <p className="text-gray-400 text-xs">Filter violent or aggressive content</p>
                    </div>
                    <Switch
                      checked={profile.contentFilters.restrictViolent}
                      onCheckedChange={(checked) => {
                        setProfile({
                          ...profile,
                          contentFilters: { ...profile.contentFilters, restrictViolent: checked }
                        })
                      }}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Reward Systems & Data Export */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Gift className="w-5 h-5 text-cyan-400" />
                    Reward Systems
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {Object.entries(profile.rewardSystems).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg">
                      <div>
                        <p className="text-white font-medium text-sm capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </p>
                        <p className="text-gray-400 text-xs">
                          {key === 'xpRewards' && 'Experience points for achievements'}
                          {key === 'cosmeticRewards' && 'Avatar skins and customizations'}
                          {key === 'badges' && 'Achievement badges and trophies'}
                        </p>
                      </div>
                      <Switch
                        checked={value}
                        onCheckedChange={(checked) => {
                          setProfile({
                            ...profile,
                            rewardSystems: { ...profile.rewardSystems, [key]: checked }
                          })
                        }}
                      />
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Download className="w-5 h-5 text-orange-400" />
                    Data Export
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-4 bg-gray-800/30 rounded-lg">
                    <h5 className="text-white font-medium mb-2">Export Your Data</h5>
                    <p className="text-gray-400 text-sm mb-4">
                      Download all your account data in compliance with GDPR and CCPA regulations.
                    </p>
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full bg-blue-500/20 border-blue-500/30 text-blue-400 hover:bg-blue-500/30">
                        <Download className="w-4 h-4 mr-2" />
                        Download Profile Data
                      </Button>
                      <Button variant="outline" className="w-full bg-green-500/20 border-green-500/30 text-green-400 hover:bg-green-500/30">
                        <Download className="w-4 h-4 mr-2" />
                        Download Children&apos;s Data
                      </Button>
                      <Button variant="outline" className="w-full bg-purple-500/20 border-purple-500/30 text-purple-400 hover:bg-purple-500/30">
                        <Download className="w-4 h-4 mr-2" />
                        Download Learning History
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Privacy & Permissions Tab */}
          <TabsContent value="privacy" className="space-y-6">
            {/* Compliance Status Overview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="bg-gradient-to-r from-green-500/10 to-blue-500/10 border-green-500/20">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center">
                      <Shield className="w-5 h-5 text-green-400" />
                    </div>
                    <div>
                      <p className="text-white font-medium">GDPR Compliant</p>
                      <p className="text-gray-400 text-sm">EU Data Protection</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border-blue-500/20">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center">
                      <Lock className="w-5 h-5 text-blue-400" />
                    </div>
                    <div>
                      <p className="text-white font-medium">CCPA Compliant</p>
                      <p className="text-gray-400 text-sm">California Privacy</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 border-purple-500/20">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-purple-500/20 rounded-full flex items-center justify-center">
                      <Users className="w-5 h-5 text-purple-400" />
                    </div>
                    <div>
                      <p className="text-white font-medium">COPPA Verified</p>
                      <p className="text-gray-400 text-sm">Child Protection</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Enhanced Consent Management */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Shield className="w-5 h-5 text-green-400" />
                    Data Consent Management
                  </CardTitle>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="outline" className="text-green-400 border-green-500/30 text-xs">
                      Version {profile.securityCompliance.gdprStatus.consentVersion}
                    </Badge>
                    <Badge variant="outline" className="text-blue-400 border-blue-500/30 text-xs">
                      Updated: {profile.securityCompliance.gdprStatus.lastConsentUpdate}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {Object.entries(profile.consent).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg">
                      <div>
                        <p className="text-white font-medium text-sm capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </p>
                        <p className="text-gray-400 text-xs">
                          {key === 'dataAnalytics' && 'Allow data collection for platform improvement'}
                          {key === 'aiFeedback' && 'Use learning data to improve AI recommendations'}
                          {key === 'marketingEmails' && 'Receive promotional emails and updates'}
                          {key === 'thirdPartySharing' && 'Share anonymized data with educational partners'}
                          {key === 'coppaCompliance' && 'Comply with Children\'s Online Privacy Protection Act'}
                          {key === 'gdprCompliance' && 'Comply with General Data Protection Regulation'}
                          {key === 'ccpaCompliance' && 'Comply with California Consumer Privacy Act'}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        {(key === 'coppaCompliance' || key === 'gdprCompliance' || key === 'ccpaCompliance') && (
                          <Badge variant="default" className="bg-green-500/20 text-green-400 border-green-500/30 text-xs">
                            Required
                          </Badge>
                        )}
                        <Switch
                          checked={value}
                          disabled={key === 'coppaCompliance' || key === 'gdprCompliance' || key === 'ccpaCompliance'}
                          onCheckedChange={(checked) => {
                            setProfile({
                              ...profile,
                              consent: { ...profile.consent, [key]: checked }
                            })
                          }}
                        />
                      </div>
                    </div>
                  ))}

                  <div className="pt-4 border-t border-gray-800/50">
                    <Button
                      variant="outline"
                      className="w-full bg-blue-500/20 border-blue-500/30 text-blue-400 hover:bg-blue-500/30"
                      onClick={() => setShowComplianceDetails(true)}
                    >
                      <Info className="w-4 h-4 mr-2" />
                      View Compliance Details
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Data Rights & Controls */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Key className="w-5 h-5 text-blue-400" />
                    Your Data Rights
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* GDPR Rights */}
                  <div className="p-3 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg border border-green-500/20">
                    <h5 className="text-white font-medium mb-2 flex items-center gap-2">
                      <Globe className="w-4 h-4 text-green-400" />
                      GDPR Rights (EU)
                    </h5>
                    <div className="space-y-2">
                      <Button variant="outline" size="sm" className="w-full justify-start bg-gray-800/30 border-gray-700 hover:bg-gray-700">
                        <Download className="w-4 h-4 mr-2" />
                        Request Data Export
                      </Button>
                      <Button variant="outline" size="sm" className="w-full justify-start bg-gray-800/30 border-gray-700 hover:bg-gray-700">
                        <Edit className="w-4 h-4 mr-2" />
                        Request Data Correction
                      </Button>
                      <Button variant="outline" size="sm" className="w-full justify-start bg-red-500/20 border-red-500/30 text-red-400 hover:bg-red-500/30">
                        <Trash2 className="w-4 h-4 mr-2" />
                        Right to be Forgotten
                      </Button>
                    </div>
                  </div>

                  {/* CCPA Rights */}
                  <div className="p-3 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg border border-blue-500/20">
                    <h5 className="text-white font-medium mb-2 flex items-center gap-2">
                      <Flag className="w-4 h-4 text-blue-400" />
                      CCPA Rights (California)
                    </h5>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between p-2 bg-gray-800/30 rounded text-sm">
                        <span className="text-gray-400">Do Not Sell Personal Info</span>
                        <Badge variant="default" className="bg-green-500/20 text-green-400 border-green-500/30">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Enabled
                        </Badge>
                      </div>
                      <Button variant="outline" size="sm" className="w-full justify-start bg-gray-800/30 border-gray-700 hover:bg-gray-700">
                        <Eye className="w-4 h-4 mr-2" />
                        View Personal Info Categories
                      </Button>
                    </div>
                  </div>

                  {/* COPPA Compliance */}
                  <div className="p-3 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg border border-purple-500/20">
                    <h5 className="text-white font-medium mb-2 flex items-center gap-2">
                      <Users className="w-4 h-4 text-purple-400" />
                      COPPA Compliance (Children)
                    </h5>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Parental Consent:</span>
                        <Badge variant="default" className="bg-green-500/20 text-green-400 border-green-500/30">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Verified
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Consent Method:</span>
                        <span className="text-white">{profile.securityCompliance.coppaStatus.consentMethod.replace(/_/g, ' ')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Children Under 13:</span>
                        <span className="text-cyan-400">{profile.securityCompliance.coppaStatus.childrenUnder13.length} child(ren)</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Enhanced Family Sharing with Co-Parent Roles */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Users className="w-5 h-5 text-blue-400" />
                    Family Sharing & Co-Parent Roles
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Primary Co-Parent */}
                  <div className="p-4 bg-gradient-to-r from-blue-500/10 to-green-500/10 rounded-lg border border-blue-500/20">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <h5 className="text-white font-medium">Primary Co-Parent</h5>
                        <Badge variant="default" className="bg-green-500/20 text-green-400 border-green-500/30">
                          <Crown className="w-3 h-3 mr-1" />
                          Active
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-blue-400 border-blue-500/30 text-xs">
                          Last active: {profile.familySharing.spouse.lastActive}
                        </Badge>
                        <Button variant="outline" size="sm" className="bg-gray-700/50 border-gray-600 hover:bg-gray-600">
                          <Edit className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-blue-400" />
                      </div>
                      <div className="flex-1">
                        <p className="text-white font-medium">{profile.familySharing.spouse.email}</p>
                        <p className="text-gray-400 text-sm">Joined: {profile.familySharing.spouse.joinedDate}</p>
                      </div>
                    </div>

                    {/* Permissions Grid */}
                    <div className="grid grid-cols-2 gap-2 mt-3">
                      {Object.entries(profile.familySharing.spouse.permissions).map(([key, value]) => (
                        <div key={key} className="flex items-center justify-between p-2 bg-gray-800/30 rounded text-xs">
                          <span className="text-gray-400 capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</span>
                          {value ? (
                            <UserCheck className="w-3 h-3 text-green-400" />
                          ) : (
                            <UserX className="w-3 h-3 text-red-400" />
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Other Family Members */}
                  {profile.familySharing.others.map((member) => (
                    <div key={member.id} className="p-3 bg-gray-800/30 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <h6 className="text-white font-medium text-sm">{member.name}</h6>
                          <Badge variant="outline" className={`text-xs ${
                            member.role === 'tutor' ? 'text-green-400 border-green-500/30' :
                            member.role === 'guardian' ? 'text-yellow-400 border-yellow-500/30' :
                            'text-purple-400 border-purple-500/30'
                          }`}>
                            {member.role}
                          </Badge>
                          <Badge variant={member.status === 'active' ? 'default' : 'secondary'} className="text-xs">
                            {member.status}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button variant="outline" size="sm" className="bg-gray-700/50 border-gray-600 hover:bg-gray-600 p-1">
                            <Edit className="w-3 h-3" />
                          </Button>
                          <Button variant="outline" size="sm" className="bg-red-500/20 border-red-500/30 text-red-400 hover:bg-red-500/30 p-1">
                            <UserX className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>

                      <div className="flex items-center gap-3 mb-2">
                        <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                          <GraduationCap className="w-4 h-4 text-green-400" />
                        </div>
                        <div className="flex-1">
                          <p className="text-white text-sm">{member.email}</p>
                          <p className="text-gray-400 text-xs">
                            Subjects: {member.subjects?.join(', ')} • Last active: {member.lastActive}
                          </p>
                        </div>
                      </div>

                      {/* Quick Permissions Overview */}
                      <div className="flex gap-1 mt-2">
                        {member.permissions.viewProgress && (
                          <Badge variant="outline" className="text-blue-400 border-blue-500/30 text-xs">
                            <Eye className="w-3 h-3 mr-1" />
                            View
                          </Badge>
                        )}
                        {member.permissions.editCurriculum && (
                          <Badge variant="outline" className="text-green-400 border-green-500/30 text-xs">
                            <Edit className="w-3 h-3 mr-1" />
                            Edit
                          </Badge>
                        )}
                        {member.permissions.receiveNotifications && (
                          <Badge variant="outline" className="text-yellow-400 border-yellow-500/30 text-xs">
                            <Bell className="w-3 h-3 mr-1" />
                            Notify
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}

                  {/* Invite New Member */}
                  <Dialog open={showInviteModal} onOpenChange={setShowInviteModal}>
                    <DialogTrigger asChild>
                      <div className="p-4 bg-gray-800/30 rounded-lg border-2 border-dashed border-gray-600 hover:border-blue-500/30 transition-colors cursor-pointer">
                        <div className="text-center">
                          <UserPlus className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                          <p className="text-gray-400 text-sm mb-2">Invite Family Member or Tutor</p>
                          <Button variant="outline" size="sm" className="bg-blue-500/20 border-blue-500/30 text-blue-400">
                            <Send className="w-4 h-4 mr-1" />
                            Send Invitation
                          </Button>
                        </div>
                      </div>
                    </DialogTrigger>
                    <DialogContent className="bg-black/90 border-gray-800/50 text-white">
                      <DialogHeader>
                        <DialogTitle className="text-white font-space-grotesk">Invite Family Member</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label className="text-gray-400 text-sm">Email Address</Label>
                          <Input
                            placeholder="Enter email address"
                            className="mt-1 bg-gray-800/50 border-gray-700 text-white"
                          />
                        </div>
                        <div>
                          <Label className="text-gray-400 text-sm">Role</Label>
                          <Select>
                            <SelectTrigger className="mt-1 bg-gray-800/50 border-gray-700 text-white">
                              <SelectValue placeholder="Select role" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="co-parent">Co-Parent (Full Access)</SelectItem>
                              <SelectItem value="tutor">Tutor (Limited Access)</SelectItem>
                              <SelectItem value="guardian">Guardian (View Only)</SelectItem>
                              <SelectItem value="mentor">Mentor (Subject Specific)</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label className="text-gray-400 text-sm">Personal Message (Optional)</Label>
                          <Textarea
                            placeholder="Add a personal message to the invitation..."
                            className="mt-1 bg-gray-800/50 border-gray-700 text-white"
                          />
                        </div>
                        <div className="flex gap-2">
                          <Button className="flex-1 bg-gradient-to-r from-blue-500 to-green-500 hover:from-blue-600 hover:to-green-600">
                            <Send className="w-4 h-4 mr-2" />
                            Send Invitation
                          </Button>
                          <Button variant="outline" onClick={() => setShowInviteModal(false)} className="bg-gray-700/50 border-gray-600 hover:bg-gray-600">
                            Cancel
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>

                  {/* Enhanced Permission Levels Guide */}
                  <div className="pt-4 border-t border-gray-800/50">
                    <h5 className="text-white font-medium text-sm mb-3 flex items-center gap-2">
                      <Info className="w-4 h-4 text-blue-400" />
                      Role Permissions Guide
                    </h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs">
                      <div className="p-2 bg-blue-500/10 rounded border border-blue-500/20">
                        <div className="flex items-center gap-2 mb-1">
                          <Crown className="w-3 h-3 text-blue-400" />
                          <span className="text-blue-400 font-medium">Co-Parent</span>
                        </div>
                        <div className="text-gray-400 space-y-1">
                          <div>• Full access to all children&apos;s data</div>
                          <div>• Edit curriculum and settings</div>
                          <div>• Manage billing and subscriptions</div>
                          <div>• Invite other family members</div>
                        </div>
                      </div>
                      <div className="p-2 bg-green-500/10 rounded border border-green-500/20">
                        <div className="flex items-center gap-2 mb-1">
                          <GraduationCap className="w-3 h-3 text-green-400" />
                          <span className="text-green-400 font-medium">Tutor</span>
                        </div>
                        <div className="text-gray-400 space-y-1">
                          <div>• View progress in assigned subjects</div>
                          <div>• Limited curriculum editing</div>
                          <div>• Receive lesson notifications</div>
                          <div>• No billing access</div>
                        </div>
                      </div>
                      <div className="p-2 bg-yellow-500/10 rounded border border-yellow-500/20">
                        <div className="flex items-center gap-2 mb-1">
                          <Shield className="w-3 h-3 text-yellow-400" />
                          <span className="text-yellow-400 font-medium">Guardian</span>
                        </div>
                        <div className="text-gray-400 space-y-1">
                          <div>• View-only access to safety reports</div>
                          <div>• Basic progress summaries</div>
                          <div>• Emergency contact notifications</div>
                          <div>• No editing permissions</div>
                        </div>
                      </div>
                      <div className="p-2 bg-purple-500/10 rounded border border-purple-500/20">
                        <div className="flex items-center gap-2 mb-1">
                          <Lightbulb className="w-3 h-3 text-purple-400" />
                          <span className="text-purple-400 font-medium">Mentor</span>
                        </div>
                        <div className="text-gray-400 space-y-1">
                          <div>• Subject-specific guidance</div>
                          <div>• Progress tracking in expertise area</div>
                          <div>• Lesson recommendations</div>
                          <div>• Limited time access</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Enhanced Security Monitoring & Audit Trail */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Eye className="w-5 h-5 text-cyan-400" />
                    Security Audit Trail
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    {profile.securityCompliance.auditTrail.slice(0, 3).map((audit) => (
                      <div key={audit.id} className="p-3 bg-gray-800/30 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <div className={`w-2 h-2 rounded-full ${audit.verified ? 'bg-green-400' : 'bg-yellow-400'}`} />
                            <span className="text-white font-medium text-sm capitalize">
                              {audit.action.replace(/_/g, ' ')}
                            </span>
                          </div>
                          <Badge variant="outline" className="text-cyan-400 border-cyan-500/30 text-xs">
                            {new Date(audit.timestamp).toLocaleDateString()}
                          </Badge>
                        </div>
                        <p className="text-gray-400 text-xs mb-2">{audit.details}</p>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>IP: {audit.ipAddress}</span>
                          <span>{audit.location}</span>
                        </div>
                      </div>
                    ))}
                  </div>

                  <Dialog open={showAuditLog} onOpenChange={setShowAuditLog}>
                    <DialogTrigger asChild>
                      <Button variant="outline" className="w-full bg-cyan-500/20 border-cyan-500/30 text-cyan-400 hover:bg-cyan-500/30">
                        <Eye className="w-4 h-4 mr-2" />
                        View Complete Audit Log
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="bg-black/90 border-gray-800/50 text-white max-w-4xl max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle className="text-white font-space-grotesk flex items-center gap-2">
                          <Shield className="w-5 h-5 text-cyan-400" />
                          Complete Security Audit Trail
                        </DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        {profile.securityCompliance.auditTrail.map((audit) => (
                          <div key={audit.id} className="p-4 bg-gray-800/30 rounded-lg">
                            <div className="flex items-center justify-between mb-3">
                              <div className="flex items-center gap-3">
                                <div className={`w-3 h-3 rounded-full ${audit.verified ? 'bg-green-400' : 'bg-yellow-400'}`} />
                                <div>
                                  <h5 className="text-white font-medium capitalize">
                                    {audit.action.replace(/_/g, ' ')}
                                  </h5>
                                  <p className="text-gray-400 text-sm">{audit.details}</p>
                                </div>
                              </div>
                              <div className="text-right">
                                <Badge variant={audit.verified ? 'default' : 'secondary'} className="mb-1">
                                  {audit.verified ? 'Verified' : 'Pending'}
                                </Badge>
                                <p className="text-gray-400 text-xs">
                                  {new Date(audit.timestamp).toLocaleString()}
                                </p>
                              </div>
                            </div>
                            <div className="grid grid-cols-2 gap-4 text-xs">
                              <div>
                                <span className="text-gray-500">IP Address:</span>
                                <span className="text-white ml-2">{audit.ipAddress}</span>
                              </div>
                              <div>
                                <span className="text-gray-500">Location:</span>
                                <span className="text-white ml-2">{audit.location}</span>
                              </div>
                              <div className="col-span-2">
                                <span className="text-gray-500">User Agent:</span>
                                <span className="text-white ml-2 text-xs break-all">{audit.userAgent}</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </DialogContent>
                  </Dialog>
                </CardContent>
              </Card>

              {/* Data Retention & Encryption */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Lock className="w-5 h-5 text-purple-400" />
                    Data Security & Retention
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Encryption Status */}
                  <div className="p-3 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg border border-purple-500/20">
                    <h5 className="text-white font-medium mb-3 flex items-center gap-2">
                      <Lock className="w-4 h-4 text-purple-400" />
                      Encryption Status
                    </h5>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Data at Rest:</span>
                        <Badge variant="default" className="bg-green-500/20 text-green-400 border-green-500/30">
                          {profile.securityCompliance.encryptionStatus.dataAtRest}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Data in Transit:</span>
                        <Badge variant="default" className="bg-green-500/20 text-green-400 border-green-500/30">
                          {profile.securityCompliance.encryptionStatus.dataInTransit}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Key Rotation:</span>
                        <Badge variant="default" className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                          {profile.securityCompliance.encryptionStatus.keyRotationEnabled ? 'Enabled' : 'Disabled'}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* Data Retention */}
                  <div className="p-3 bg-gray-800/30 rounded-lg">
                    <h5 className="text-white font-medium mb-3 flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-blue-400" />
                      Data Retention Periods
                    </h5>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Personal Data:</span>
                        <span className="text-white">{profile.securityCompliance.dataRetention.personalDataRetentionPeriod}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Learning Data:</span>
                        <span className="text-white">{profile.securityCompliance.dataRetention.learningDataRetentionPeriod}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Audit Logs:</span>
                        <span className="text-white">{profile.securityCompliance.dataRetention.auditLogRetentionPeriod}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Next Review:</span>
                        <span className="text-cyan-400">{profile.securityCompliance.dataRetention.nextReviewDate}</span>
                      </div>
                    </div>
                  </div>

                  {/* Auto-deletion */}
                  <div className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg">
                    <div>
                      <p className="text-white font-medium text-sm">Automatic Data Deletion</p>
                      <p className="text-gray-400 text-xs">Automatically delete data after retention period</p>
                    </div>
                    <Badge variant="default" className="bg-green-500/20 text-green-400 border-green-500/30">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Enabled
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Enhanced Account Deletion with Double Confirmation */}
            <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
              <CardHeader>
                <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5 text-red-400" />
                  Account Security & Deletion
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-gray-800/30 rounded-lg">
                    <h5 className="text-white font-medium mb-2 flex items-center gap-2">
                      <FileText className="w-4 h-4 text-green-400" />
                      Curriculum Override Log
                    </h5>
                    <p className="text-gray-400 text-sm mb-3">Review all manual lesson changes and modifications</p>
                    <Button variant="outline" className="w-full bg-green-500/20 border-green-500/30 text-green-400 hover:bg-green-500/30">
                      <FileText className="w-4 h-4 mr-2" />
                      View Override History
                    </Button>
                  </div>

                  <div className="p-4 bg-gray-800/30 rounded-lg">
                    <h5 className="text-white font-medium mb-2 flex items-center gap-2">
                      <Download className="w-4 h-4 text-blue-400" />
                      Data Export Request
                    </h5>
                    <p className="text-gray-400 text-sm mb-3">Download all your data before deletion</p>
                    <Dialog open={showDataExport} onOpenChange={setShowDataExport}>
                      <DialogTrigger asChild>
                        <Button variant="outline" className="w-full bg-blue-500/20 border-blue-500/30 text-blue-400 hover:bg-blue-500/30">
                          <Download className="w-4 h-4 mr-2" />
                          Request Data Export
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="bg-black/90 border-gray-800/50 text-white">
                        <DialogHeader>
                          <DialogTitle className="text-white font-space-grotesk">Data Export Request</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <p className="text-gray-400">
                            We&apos;ll prepare a complete export of your data including profile information,
                            children&apos;s learning data, and all associated records.
                          </p>
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <CheckCircle className="w-4 h-4 text-green-400" />
                              <span className="text-sm">Profile and account data</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <CheckCircle className="w-4 h-4 text-green-400" />
                              <span className="text-sm">Children&apos;s learning progress</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <CheckCircle className="w-4 h-4 text-green-400" />
                              <span className="text-sm">Curriculum customizations</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <CheckCircle className="w-4 h-4 text-green-400" />
                              <span className="text-sm">Billing and subscription history</span>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button className="flex-1 bg-gradient-to-r from-blue-500 to-green-500 hover:from-blue-600 hover:to-green-600">
                              <Download className="w-4 h-4 mr-2" />
                              Request Export
                            </Button>
                            <Button variant="outline" onClick={() => setShowDataExport(false)} className="bg-gray-700/50 border-gray-600 hover:bg-gray-600">
                              Cancel
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>

                {/* Enhanced Account Deletion */}
                <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                  <h5 className="text-red-400 font-medium mb-2 flex items-center gap-2">
                    <AlertTriangle className="w-4 h-4" />
                    Permanent Account Deletion
                  </h5>
                  <p className="text-gray-400 text-sm mb-4">
                    This will permanently delete your account and all associated data including children&apos;s learning progress.
                    This action cannot be undone and complies with GDPR &quot;Right to be Forgotten&quot; requirements.
                  </p>

                  <Dialog open={showDeleteConfirmation} onOpenChange={setShowDeleteConfirmation}>
                    <DialogTrigger asChild>
                      <Button variant="outline" className="bg-red-500/20 border-red-500/30 text-red-400 hover:bg-red-500/30">
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete Account
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="bg-black/90 border-gray-800/50 text-white">
                      <DialogHeader>
                        <DialogTitle className="text-red-400 font-space-grotesk flex items-center gap-2">
                          <AlertTriangle className="w-5 h-5" />
                          Confirm Account Deletion
                        </DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                          <h6 className="text-red-400 font-medium mb-2">This action will permanently delete:</h6>
                          <ul className="space-y-1 text-sm text-gray-300">
                            <li>• Your profile and account information</li>
                            <li>• All children&apos;s learning data and progress</li>
                            <li>• Curriculum customizations and settings</li>
                            <li>• Billing history and subscription data</li>
                            <li>• Family sharing connections</li>
                          </ul>
                        </div>

                        <div>
                          <Label className="text-gray-400 text-sm">
                            Type &quot;DELETE MY ACCOUNT&quot; to confirm:
                          </Label>
                          <Input
                            value={deleteConfirmationText}
                            onChange={(e) => setDeleteConfirmationText(e.target.value)}
                            placeholder="DELETE MY ACCOUNT"
                            className="mt-1 bg-gray-800/50 border-gray-700 text-white"
                          />
                        </div>

                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            className="flex-1 bg-red-500/20 border-red-500/30 text-red-400 hover:bg-red-500/30"
                            disabled={deleteConfirmationText !== 'DELETE MY ACCOUNT'}
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Permanently Delete Account
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => {
                              setShowDeleteConfirmation(false)
                              setDeleteConfirmationText('')
                            }}
                            className="bg-gray-700/50 border-gray-600 hover:bg-gray-600"
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardContent>
            </Card>

            {/* Compliance Details Modal */}
            <Dialog open={showComplianceDetails} onOpenChange={setShowComplianceDetails}>
              <DialogContent className="bg-black/90 border-gray-800/50 text-white max-w-4xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Shield className="w-5 h-5 text-green-400" />
                    Compliance & Privacy Details
                  </DialogTitle>
                </DialogHeader>
                <div className="space-y-6">
                  {/* GDPR Details */}
                  <div className="p-4 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg border border-green-500/20">
                    <h4 className="text-white font-medium mb-3 flex items-center gap-2">
                      <Globe className="w-4 h-4 text-green-400" />
                      GDPR Compliance (EU)
                    </h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-400">Lawful Basis:</span>
                        <span className="text-white ml-2">{profile.securityCompliance.gdprStatus.lawfulBasis}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Consent Version:</span>
                        <span className="text-white ml-2">{profile.securityCompliance.gdprStatus.consentVersion}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Last Updated:</span>
                        <span className="text-white ml-2">{profile.securityCompliance.gdprStatus.lastConsentUpdate}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Data Processing:</span>
                        <Badge variant="default" className="bg-green-500/20 text-green-400 border-green-500/30 ml-2">
                          Consented
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* CCPA Details */}
                  <div className="p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg border border-blue-500/20">
                    <h4 className="text-white font-medium mb-3 flex items-center gap-2">
                      <Flag className="w-4 h-4 text-blue-400" />
                      CCPA Compliance (California)
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Do Not Sell Personal Info:</span>
                        <Badge variant="default" className="bg-green-500/20 text-green-400 border-green-500/30">
                          Enabled
                        </Badge>
                      </div>
                      <div>
                        <span className="text-gray-400">Personal Info Categories:</span>
                        <div className="flex gap-1 mt-1">
                          {profile.securityCompliance.ccpaStatus.personalInfoCategories.map((category, index) => (
                            <Badge key={index} variant="outline" className="text-blue-400 border-blue-500/30 text-xs">
                              {category.replace(/_/g, ' ')}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* COPPA Details */}
                  <div className="p-4 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg border border-purple-500/20">
                    <h4 className="text-white font-medium mb-3 flex items-center gap-2">
                      <Users className="w-4 h-4 text-purple-400" />
                      COPPA Compliance (Children)
                    </h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-400">Consent Method:</span>
                        <span className="text-white ml-2">{profile.securityCompliance.coppaStatus.consentMethod.replace(/_/g, ' ')}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Consent Date:</span>
                        <span className="text-white ml-2">{profile.securityCompliance.coppaStatus.consentDate}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Parent Email:</span>
                        <span className="text-white ml-2">{profile.securityCompliance.coppaStatus.parentEmail}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Children Under 13:</span>
                        <span className="text-cyan-400 ml-2">{profile.securityCompliance.coppaStatus.childrenUnder13.length} child(ren)</span>
                      </div>
                    </div>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </TabsContent>

          {/* Dashboard Snapshot Tab */}
          <TabsContent value="dashboard" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Children Summary */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-green-500 rounded-full flex items-center justify-center">
                      <Users className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-white">
                        {profile.children.reduce((acc, child) => acc + child.skillGrowth, 0)}%
                      </p>
                      <p className="text-gray-400 text-sm">XP Gained This Week</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Planner Usage */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                      <Calendar className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-white">65%</p>
                      <p className="text-gray-400 text-sm">Custom Lessons</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Skills Progress */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full flex items-center justify-center">
                      <BarChart3 className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-white">
                        {Math.round(profile.children.reduce((acc, child) => acc + child.completionRate, 0) / profile.children.length)}%
                      </p>
                      <p className="text-gray-400 text-sm">Avg Completion</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Weekly Trends */}
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center">
                      <Clock className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-white">12.5h</p>
                      <p className="text-gray-400 text-sm">Study Time</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Enhanced Curriculum Insights */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Brain className="w-5 h-5 text-purple-400" />
                    &quot;What Your Child is Learning&quot; Digest
                  </CardTitle>
                  <div className="flex gap-2 mt-2">
                    <Button
                      variant={selectedInsightPeriod === 'weekly' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedInsightPeriod('weekly')}
                      className="text-xs"
                    >
                      Weekly
                    </Button>
                    <Button
                      variant={selectedInsightPeriod === 'monthly' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedInsightPeriod('monthly')}
                      className="text-xs"
                    >
                      Monthly
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  {selectedInsightPeriod === 'weekly' ? (
                    <div className="space-y-4">
                      {/* Weekly Digest */}
                      <div className="p-3 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg border border-purple-500/20">
                        <h5 className="text-white font-medium mb-2 flex items-center gap-2">
                          <Calendar className="w-4 h-4 text-purple-400" />
                          This Week&apos;s Learning Journey
                        </h5>
                        <div className="grid grid-cols-2 gap-3 mb-3">
                          <div className="text-center p-2 bg-gray-800/30 rounded">
                            <div className="text-lg font-bold text-blue-400">{profile.curriculumInsights.weeklyDigest.totalLessonsCompleted}</div>
                            <div className="text-xs text-gray-400">Lessons Completed</div>
                          </div>
                          <div className="text-center p-2 bg-gray-800/30 rounded">
                            <div className="text-lg font-bold text-green-400">{profile.curriculumInsights.weeklyDigest.averageEngagement}%</div>
                            <div className="text-xs text-gray-400">Avg Engagement</div>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div>
                            <p className="text-gray-400 text-xs mb-1">Top Performing Subjects:</p>
                            <div className="flex gap-1">
                              {profile.curriculumInsights.weeklyDigest.topPerformingSubjects.map((subject, index) => (
                                <Badge key={index} variant="default" className="bg-green-500/20 text-green-400 border-green-500/30 text-xs">
                                  <Star className="w-3 h-3 mr-1" />
                                  {subject}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          <div>
                            <p className="text-gray-400 text-xs mb-1">Needs Attention:</p>
                            <div className="flex gap-1">
                              {profile.curriculumInsights.weeklyDigest.needsAttentionSubjects.map((subject, index) => (
                                <Badge key={index} variant="outline" className="text-yellow-400 border-yellow-500/30 text-xs">
                                  <AlertTriangle className="w-3 h-3 mr-1" />
                                  {subject}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Learning Patterns */}
                      <div className="p-3 bg-gray-800/30 rounded-lg">
                        <h6 className="text-white font-medium text-sm mb-2 flex items-center gap-2">
                          <Clock4 className="w-4 h-4 text-cyan-400" />
                          Learning Patterns Discovered
                        </h6>
                        <div className="space-y-2 text-xs">
                          <div className="flex justify-between">
                            <span className="text-gray-400">Best Performance Time:</span>
                            <span className="text-cyan-400">{profile.curriculumInsights.weeklyDigest.learningPatterns.bestPerformanceTime}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-400">Preferred Difficulty:</span>
                            <span className="text-green-400">{profile.curriculumInsights.weeklyDigest.learningPatterns.preferredDifficulty}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-400">Avg Session Length:</span>
                            <span className="text-blue-400">{profile.curriculumInsights.weeklyDigest.learningPatterns.averageSessionLength} min</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {/* Monthly Report */}
                      <div className="p-3 bg-gradient-to-r from-blue-500/10 to-green-500/10 rounded-lg border border-blue-500/20">
                        <h5 className="text-white font-medium mb-3 flex items-center gap-2">
                          <TrendingUp className="w-4 h-4 text-blue-400" />
                          Monthly Skill Development
                        </h5>
                        <div className="space-y-2">
                          {profile.curriculumInsights.monthlyReport.skillDevelopment.map((skill, index) => (
                            <div key={index} className="flex items-center gap-3 p-2 bg-gray-800/30 rounded">
                              <div className="flex-1">
                                <div className="flex justify-between items-center mb-1">
                                  <span className="text-white text-sm">{skill.skill}</span>
                                  <div className="flex items-center gap-1">
                                    <span className={`text-sm font-medium ${
                                      skill.trend === 'up' ? 'text-green-400' :
                                      skill.trend === 'down' ? 'text-red-400' : 'text-yellow-400'
                                    }`}>
                                      +{skill.growth}%
                                    </span>
                                    {skill.trend === 'up' ? (
                                      <TrendingUp className="w-3 h-3 text-green-400" />
                                    ) : skill.trend === 'down' ? (
                                      <TrendingDown className="w-3 h-3 text-red-400" />
                                    ) : (
                                      <div className="w-3 h-3 bg-yellow-400 rounded-full" />
                                    )}
                                  </div>
                                </div>
                                <div className="w-full bg-gray-700 rounded-full h-1">
                                  <div
                                    className={`h-1 rounded-full transition-all duration-300 ${
                                      skill.trend === 'up' ? 'bg-gradient-to-r from-green-500 to-blue-500' :
                                      skill.trend === 'down' ? 'bg-gradient-to-r from-red-500 to-orange-500' :
                                      'bg-gradient-to-r from-yellow-500 to-orange-500'
                                    }`}
                                    style={{ width: `${skill.growth * 3}%` }}
                                  />
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Achievements */}
                      <div className="p-3 bg-gray-800/30 rounded-lg">
                        <h6 className="text-white font-medium text-sm mb-2 flex items-center gap-2">
                          <Award className="w-4 h-4 text-yellow-400" />
                          Monthly Achievements
                        </h6>
                        <div className="space-y-1">
                          {profile.curriculumInsights.monthlyReport.achievements.map((achievement, index) => (
                            <div key={index} className="flex items-center gap-2 text-xs">
                              <CheckCircle className="w-3 h-3 text-green-400" />
                              <span className="text-gray-300">{achievement}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* AI Recommendations */}
                      <div className="p-3 bg-gradient-to-r from-cyan-500/10 to-purple-500/10 rounded-lg border border-cyan-500/20">
                        <h6 className="text-white font-medium text-sm mb-2 flex items-center gap-2">
                          <Lightbulb className="w-4 h-4 text-cyan-400" />
                          AI Learning Recommendations
                        </h6>
                        <div className="space-y-1">
                          {profile.curriculumInsights.monthlyReport.recommendations.map((rec, index) => (
                            <div key={index} className="flex items-start gap-2 text-xs">
                              <Target className="w-3 h-3 text-cyan-400 mt-0.5" />
                              <span className="text-gray-300">{rec}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="mt-4 pt-3 border-t border-gray-800/50">
                    <Button variant="outline" className="w-full bg-purple-500/20 border-purple-500/30 text-purple-400 hover:bg-purple-500/30">
                      <BookOpen className="w-4 h-4 mr-2" />
                      View Detailed Learning Report
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
                <CardHeader>
                  <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                    <Settings className="w-5 h-5 text-purple-400" />
                    Plan Switch History
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-3 bg-gray-800/30 rounded-lg">
                      <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                        <CheckCircle className="w-4 h-4 text-green-400" />
                      </div>
                      <div className="flex-1">
                        <p className="text-white font-medium text-sm">Switched to Family Plan</p>
                        <p className="text-gray-400 text-xs">January 15, 2024</p>
                      </div>
                      <Badge variant="default" className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                        Upgrade
                      </Badge>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-gray-800/30 rounded-lg">
                      <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                        <CheckCircle className="w-4 h-4 text-blue-400" />
                      </div>
                      <div className="flex-1">
                        <p className="text-white font-medium text-sm">Started with Standard Plan</p>
                        <p className="text-gray-400 text-xs">December 1, 2023</p>
                      </div>
                      <Badge variant="outline" className="text-gray-400 border-gray-500/30">
                        Initial
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

        </Tabs>
      </motion.div>
    </div>
    </>
  )
}
