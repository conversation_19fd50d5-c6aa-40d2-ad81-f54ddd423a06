import {
  BookOpen,
  Wand2,
  <PERSON>,
  Trophy,
  Shield,
  Gamepad2,
  Users,
  Video,
  Brain,
  Lock,
  Award,
  UserCheck,
} from "lucide-react"

export const byteMentorMessages = [
  "Welcome, future digital hero! 🚀",
  "Ready to unlock your tech superpowers?",
  "Let's explore the amazing world of coding!",
  "Your journey to becoming a tech wizard starts here!",
]

export const testimonials = [
  {
    name: "<PERSON>",
    role: "Parent",
    avatar: "/placeholder.svg?height=40&width=40",
    rating: 5,
    text: "My daughter has learned more about cybersecurity in 2 weeks than I thought possible. The gamified approach keeps her engaged!",
  },
  {
    name: "<PERSON>",
    role: "Student, Age 14",
    avatar: "/placeholder.svg?height=40&width=40",
    rating: 5,
    text: "I built my first Discord bot and learned Python! The tutorials are actually fun and the community helps when I'm stuck.",
  },
  {
    name: "<PERSON><PERSON>",
    role: "Computer Science Teacher",
    avatar: "/placeholder.svg?height=40&width=40",
    rating: 5,
    text: "ByteHero bridges the gap between gaming and learning perfectly. My students are more motivated than ever to learn coding.",
  },
  {
    name: "<PERSON>",
    role: "Parent & Software Engineer",
    avatar: "/placeholder.svg?height=40&width=40",
    rating: 5,
    text: "Finally, a platform that teaches real skills safely. The parental controls give me peace of mind while my son explores tech.",
  },
]

export const features = [
  {
    icon: BookOpen,
    title: "Tutorial Zone",
    description: "Interactive learning with step-by-step tutorials",
    stats: "500+ Tutorials",
    color: "from-green-500 to-emerald-500",
  },
  {
    icon: Shield,
    title: "Hack Lab",
    description: "Safe cybersecurity challenges and ethical hacking",
    stats: "50+ Challenges",
    color: "from-red-500 to-orange-500",
  },
  {
    icon: Gamepad2,
    title: "Gamer Zone",
    description: "Gaming tutorials, mods, and competitive challenges",
    stats: "200+ Games",
    color: "from-purple-500 to-pink-500",
  },
  {
    icon: Video,
    title: "Creator Studio",
    description: "Build your profile, share content, and express creativity",
    stats: "10K+ Creators",
    color: "from-yellow-500 to-amber-500",
  },
  {
    icon: Users,
    title: "Community Lounge",
    description: "Connect, collaborate, and learn together safely",
    stats: "25K+ Members",
    color: "from-indigo-500 to-purple-500",
  },
  {
    icon: Brain,
    title: "Education Zone",
    description: "Life skills through interactive comic adventures",
    stats: "30+ Adventures",
    color: "from-cyan-500 to-blue-500",
  },
]

export const communityHighlights = [
  {
    user: "CyberNinja",
    achievement: "Created 'SQL Injection Defense' tutorial",
    views: "12.5K views",
    likes: 847,
    avatar: "/placeholder.svg?height=32&width=32",
    time: "2 days ago",
    type: "tutorial",
    badge: "Security Expert",
    level: "Hacker",
  },
  {
    user: "GameMaster99",
    achievement: "Won Weekly Minecraft Redstone Tournament",
    views: "8.2K views",
    likes: 623,
    avatar: "/placeholder.svg?height=32&width=32",
    time: "1 week ago",
    type: "tournament",
    badge: "Tournament Champion",
    level: "Creator",
  },
  {
    user: "CodeWizard",
    achievement: "Earned 'Python Master' badge",
    views: "5.7K views",
    likes: 412,
    avatar: "/placeholder.svg?height=32&width=32",
    time: "3 days ago",
    type: "badge",
    badge: "Python Master",
    level: "Learner",
  },
  {
    user: "TechMentor",
    achievement: "Helped 50+ students with coding challenges",
    views: "15.1K views",
    likes: 1205,
    avatar: "/placeholder.svg?height=32&width=32",
    time: "5 days ago",
    type: "mentoring",
    badge: "Community Hero",
    level: "Mentor",
  },
]

export const journeyStages = [
  {
    level: "Level 1",
    title: "Learner",
    icon: BookOpen,
    description: "Start with tutorials and basic challenges",
    color: "from-purple-500 to-purple-600",
    achievements: ["First Tutorial", "10 Challenges", "Basic Badge"],
  },
  {
    level: "Level 2",
    title: "Creator",
    icon: Wand2,
    description: "Build projects and share your creations",
    color: "from-blue-500 to-blue-600",
    achievements: ["First Project", "Community Post", "Creator Badge"],
  },
  {
    level: "Level 3",
    title: "Hacker",
    icon: Code,
    description: "Master cybersecurity and advanced coding",
    color: "from-cyan-500 to-cyan-600",
    achievements: ["CTF Winner", "Security Expert", "Hacker Badge"],
  },
  {
    level: "Level 4",
    title: "Mentor",
    icon: Trophy,
    description: "Guide others and lead the community",
    color: "from-yellow-500 to-orange-500",
    achievements: ["Help 10 Students", "Top Contributor", "Mentor Badge"],
  },
]

export const safetyFeatures = [
  {
    icon: Shield,
    title: "AI-Powered Moderation",
    description: "Advanced content filtering and real-time safety monitoring",
  },
  {
    icon: UserCheck,
    title: "Verified Mentors",
    description: "Background-checked educators and industry professionals",
  },
  {
    icon: Lock,
    title: "COPPA Compliant",
    description: "Full privacy protection and parental control features",
  },
  {
    icon: Award,
    title: "Educational Standards",
    description: "Curriculum aligned with STEM learning objectives",
  },
]

export const interests = [
  "Web Development",
  "Game Development",
  "Cybersecurity",
  "AI & Machine Learning",
  "Mobile Apps",
  "Robotics",
  "Data Science",
  "Digital Art",
]
