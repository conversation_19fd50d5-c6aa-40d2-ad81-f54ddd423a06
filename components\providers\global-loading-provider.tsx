"use client"

import React, { createContext, useContext, useState, ReactNode } from 'react'
import { GlobalLoading } from '@/components/ui/global-loading'

interface LoadingState {
  isVisible: boolean
  message?: string
  progress?: number
  variant?: 'default' | 'minimal' | 'full'
}

interface GlobalLoadingContextType {
  // State
  isLoading: boolean
  message?: string
  progress?: number
  variant?: 'default' | 'minimal' | 'full'
  
  // Actions
  showLoading: (message?: string, progress?: number, variant?: 'default' | 'minimal' | 'full') => void
  hideLoading: () => void
  updateProgress: (progress: number, message?: string) => void
  updateMessage: (message: string) => void
  setVariant: (variant: 'default' | 'minimal' | 'full') => void
}

const GlobalLoadingContext = createContext<GlobalLoadingContextType | undefined>(undefined)

interface GlobalLoadingProviderProps {
  children: ReactNode
}

export function GlobalLoadingProvider({ children }: GlobalLoadingProviderProps) {
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isVisible: false,
    variant: 'default'
  })

  const showLoading = (
    message?: string, 
    progress?: number, 
    variant: 'default' | 'minimal' | 'full' = 'default'
  ) => {
    setLoadingState({
      isVisible: true,
      message,
      progress,
      variant
    })
  }

  const hideLoading = () => {
    setLoadingState(prev => ({
      ...prev,
      isVisible: false
    }))
  }

  const updateProgress = (progress: number, message?: string) => {
    setLoadingState(prev => ({
      ...prev,
      progress,
      ...(message && { message })
    }))
  }

  const updateMessage = (message: string) => {
    setLoadingState(prev => ({
      ...prev,
      message
    }))
  }

  const setVariant = (variant: 'default' | 'minimal' | 'full') => {
    setLoadingState(prev => ({
      ...prev,
      variant
    }))
  }

  const contextValue: GlobalLoadingContextType = {
    isLoading: loadingState.isVisible,
    message: loadingState.message,
    progress: loadingState.progress,
    variant: loadingState.variant,
    showLoading,
    hideLoading,
    updateProgress,
    updateMessage,
    setVariant
  }

  // Map global loading variants to landing page loading variants
  const mapVariant = (variant?: 'default' | 'minimal' | 'full'): 'section' | 'full-page' | 'minimal' => {
    switch (variant) {
      case 'default':
        return 'section'
      case 'full':
        return 'full-page'
      case 'minimal':
      default:
        return 'minimal'
    }
  }

  return (
    <GlobalLoadingContext.Provider value={contextValue}>
      {children}
      <GlobalLoading
        isVisible={loadingState.isVisible}
        currentSection={loadingState.message || 'loading'}
        progress={loadingState.progress}
        variant={mapVariant(loadingState.variant)}
      />
    </GlobalLoadingContext.Provider>
  )
}

// Hook to use the global loading context
export function useGlobalLoading() {
  const context = useContext(GlobalLoadingContext)
  
  if (context === undefined) {
    throw new Error('useGlobalLoading must be used within a GlobalLoadingProvider')
  }
  
  return context
}

// Custom hooks for common loading scenarios
export function useLoadingUtils() {
  const { showLoading, hideLoading, updateProgress } = useGlobalLoading()

  return {
    // Show loading for async operations
    withLoading: async <T,>(
      operation: () => Promise<T>,
      message?: string,
      variant?: 'default' | 'minimal' | 'full'
    ): Promise<T> => {
      try {
        showLoading(message, undefined, variant)
        const result = await operation()
        return result
      } finally {
        hideLoading()
      }
    },

    // Show loading with progress tracking
    withProgressLoading: async <T,>(
      operation: (updateProgress: (progress: number, message?: string) => void) => Promise<T>,
      initialMessage?: string,
      variant?: 'default' | 'minimal' | 'full'
    ): Promise<T> => {
      try {
        showLoading(initialMessage, 0, variant)
        const result = await operation(updateProgress)
        return result
      } finally {
        hideLoading()
      }
    },

    // Show loading for navigation
    showNavigationLoading: (destination?: string) => {
      const message = destination
        ? `Navigating to ${destination}...`
        : 'Loading page...'
      showLoading(message, undefined, 'minimal')
    },

    // Show loading for data fetching
    showDataLoading: (dataType?: string) => {
      const message = dataType
        ? `Loading ${dataType}...`
        : 'Fetching data...'
      showLoading(message, undefined, 'minimal')
    },

    // Show loading for authentication
    showAuthLoading: (action?: 'login' | 'logout' | 'signup') => {
      const messages = {
        login: 'Signing in...',
        logout: 'Signing out...',
        signup: 'Creating account...'
      }
      const message = action ? messages[action] : 'Authenticating...'
      showLoading(message, undefined, 'default')
    },

    // Show loading for file operations
    showFileLoading: (operation?: 'upload' | 'download' | 'process') => {
      const messages = {
        upload: 'Uploading file...',
        download: 'Downloading file...',
        process: 'Processing file...'
      }
      const message = operation ? messages[operation] : 'File operation in progress...'
      showLoading(message, 0, 'default')
    }
  }
}

// Higher-order component for automatic loading states
export function withGlobalLoading<P extends object>(
  Component: React.ComponentType<P>,
  loadingMessage?: string,
  variant?: 'default' | 'minimal' | 'full'
) {
  return function WrappedComponent(props: P) {
    const [isComponentLoading, setIsComponentLoading] = useState(true)
    const { showLoading, hideLoading } = useGlobalLoading()

    React.useEffect(() => {
      showLoading(loadingMessage, undefined, variant)
      
      // Simulate component loading time
      const timer = setTimeout(() => {
        setIsComponentLoading(false)
        hideLoading()
      }, 1000)

      return () => {
        clearTimeout(timer)
        hideLoading()
      }
    }, [showLoading, hideLoading])

    if (isComponentLoading) {
      return null
    }

    return <Component {...props} />
  }
}

// Hook for component-level loading states that integrate with global loading
export function useComponentLoading(componentName?: string) {
  const { showLoading, hideLoading, updateProgress } = useGlobalLoading()
  const [isLoading, setIsLoading] = useState(false)

  const startLoading = (message?: string) => {
    const loadingMessage = message || (componentName ? `Loading ${componentName}...` : 'Loading...')
    setIsLoading(true)
    showLoading(loadingMessage, undefined, 'minimal')
  }

  const stopLoading = () => {
    setIsLoading(false)
    hideLoading()
  }

  const updateLoadingProgress = (progress: number, message?: string) => {
    updateProgress(progress, message)
  }

  return {
    isLoading,
    startLoading,
    stopLoading,
    updateProgress: updateLoadingProgress
  }
}
