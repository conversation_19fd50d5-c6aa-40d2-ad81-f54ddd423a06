'use client'

import { motion } from 'framer-motion'
import { Dna, Atom } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import DNA3DHelix from '../DNA3DHelix'
import { DNAAnalysisProps } from './types'

export default function DNAFragmentDisplay({ 
  dnaFragment, 
  playerData, 
  systemStatus, 
  evolutionTheme 
}: DNAAnalysisProps) {
  return (
    <Card className="bg-black/40 border-cyan-500/30 backdrop-blur-xl mb-6">
      <CardContent className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 3D DNA Helix Visualization */}
          <div className="relative">
            <h3 className="text-lg font-bold text-cyan-400 flex items-center gap-2 mb-4">
              <Dna className="w-5 h-5" />
              3D DNA Structure
            </h3>
            <div className="h-64 rounded-lg border border-cyan-500/30 overflow-hidden bg-gradient-to-br from-cyan-900/10 to-purple-900/10">
              <DNA3DHelix
                height={3}
                radius={0.5}
                turns={1.5}
                baseCount={16}
                evolutionStage={playerData?.level || 1}
                ceLevel={(systemStatus?.consciousnessLevel || 0)}
                qsLevel={(systemStatus?.systemHealth || 0)}
                isAnimated={true}
              />
            </div>
            <div className="absolute bottom-2 left-2 bg-black/70 px-2 py-1 rounded text-xs text-cyan-400">
              Real-time CE/QS flux visualization
            </div>
          </div>

          {/* DNA Fragment Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-bold text-cyan-400 flex items-center gap-2">
              <Atom className="w-5 h-5" />
              Your DNA Fragment
            </h3>

            <motion.div
              className="font-mono text-2xl font-bold tracking-wider text-center p-4 bg-gradient-to-r from-cyan-500/10 to-purple-500/10 rounded-lg border border-cyan-500/30"
              style={{ color: evolutionTheme.primary }}
              animate={{ scale: [1, 1.05, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              {dnaFragment}
            </motion.div>

            <p className="text-sm text-white/60 text-center italic">
              &ldquo;Your DNA evolves as you evolve others.&rdquo;
            </p>

            {/* Enhanced Metrics Display */}
            <div className="grid grid-cols-2 gap-4">
              <div className="p-3 bg-cyan-500/10 border border-cyan-500/30 rounded-lg text-center">
                <div className="text-2xl font-bold text-cyan-400">
                  {Math.round((systemStatus?.consciousnessLevel || 0) * 100)}
                </div>
                <div className="text-xs text-cyan-400/80">Consciousness Energy</div>
                <div className="w-full bg-gray-700 rounded-full h-1 mt-2">
                  <motion.div
                    className="bg-cyan-400 h-1 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${Math.round((systemStatus?.consciousnessLevel || 0) * 100)}%` }}
                    transition={{ duration: 1.5 }}
                  />
                </div>
              </div>

              <div className="p-3 bg-purple-500/10 border border-purple-500/30 rounded-lg text-center">
                <div className="text-2xl font-bold text-purple-400">
                  {Math.round((systemStatus?.systemHealth || 0) * 100)}
                </div>
                <div className="text-xs text-purple-400/80">Quantum Stability</div>
                <div className="w-full bg-gray-700 rounded-full h-1 mt-2">
                  <motion.div
                    className="bg-purple-400 h-1 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${Math.round((systemStatus?.systemHealth || 0) * 100)}%` }}
                    transition={{ duration: 1.5, delay: 0.3 }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
