// Advanced Skill Progression System for QuantumLink

export interface SkillTree {
  id: string
  name: string
  description: string
  icon: string
  color: string
  branches: SkillBranch[]
  prerequisites?: string[]
  maxLevel: number
}

export interface SkillBranch {
  id: string
  name: string
  description: string
  skills: Skill[]
  unlockLevel: number
}

export interface Skill {
  id: string
  name: string
  description: string
  level: number
  maxLevel: number
  experience: number
  nextLevelXP: number
  benefits: SkillBenefit[]
  activities: SkillActivity[]
  milestones: SkillMilestone[]
}

export interface SkillBenefit {
  level: number
  type: 'unlock' | 'bonus' | 'ability'
  description: string
  value?: number
}

export interface SkillActivity {
  id: string
  name: string
  description: string
  xpReward: number
  difficulty: 'easy' | 'medium' | 'hard'
  category: string
}

export interface SkillMilestone {
  level: number
  title: string
  description: string
  reward: {
    ce: number
    cubits: number
    badge?: string
    avatarUpgrade?: string
  }
}

// Define the complete skill trees for QuantumLink
export const COMMUNICATION_SKILL_TREE: SkillTree = {
  id: 'communication',
  name: 'Quantum Communication',
  description: 'Master the art of clear, kind, and effective communication',
  icon: '💬',
  color: 'from-cyan-500 to-blue-500',
  maxLevel: 50,
  branches: [
    {
      id: 'basic_communication',
      name: 'Foundation',
      description: 'Basic communication skills',
      unlockLevel: 1,
      skills: [
        {
          id: 'clear_expression',
          name: 'Clear Expression',
          description: 'Express ideas clearly and concisely',
          level: 1,
          maxLevel: 10,
          experience: 0,
          nextLevelXP: 100,
          benefits: [
            { level: 3, type: 'bonus', description: '+10% message clarity score' },
            { level: 5, type: 'unlock', description: 'Advanced expression tools' },
            { level: 8, type: 'ability', description: 'Mentor mode for helping others' }
          ],
          activities: [
            {
              id: 'explain_concept',
              name: 'Explain a Concept',
              description: 'Help someone understand a difficult topic',
              xpReward: 25,
              difficulty: 'medium',
              category: 'teaching'
            }
          ],
          milestones: [
            {
              level: 5,
              title: 'Clear Communicator',
              description: 'You can express complex ideas simply',
              reward: { ce: 50, cubits: 10, badge: 'clear_speaker' }
            }
          ]
        },
        {
          id: 'active_listening',
          name: 'Active Listening',
          description: 'Listen carefully and respond thoughtfully',
          level: 1,
          maxLevel: 10,
          experience: 0,
          nextLevelXP: 120,
          benefits: [
            { level: 2, type: 'bonus', description: '+15% empathy score' },
            { level: 4, type: 'unlock', description: 'Emotion recognition tools' },
            { level: 7, type: 'ability', description: 'Conflict mediation skills' }
          ],
          activities: [
            {
              id: 'thoughtful_response',
              name: 'Thoughtful Response',
              description: 'Respond to someone showing you understood them',
              xpReward: 20,
              difficulty: 'easy',
              category: 'listening'
            }
          ],
          milestones: [
            {
              level: 4,
              title: 'Great Listener',
              description: 'Others feel heard when talking to you',
              reward: { ce: 40, cubits: 8, badge: 'active_listener' }
            }
          ]
        }
      ]
    },
    {
      id: 'advanced_communication',
      name: 'Mastery',
      description: 'Advanced communication techniques',
      unlockLevel: 5,
      skills: [
        {
          id: 'persuasive_communication',
          name: 'Persuasive Communication',
          description: 'Influence others through compelling arguments',
          level: 1,
          maxLevel: 8,
          experience: 0,
          nextLevelXP: 200,
          benefits: [
            { level: 3, type: 'ability', description: 'Debate moderator role' },
            { level: 6, type: 'unlock', description: 'Leadership opportunities' }
          ],
          activities: [
            {
              id: 'convince_team',
              name: 'Team Persuasion',
              description: 'Convince your team to try a new approach',
              xpReward: 40,
              difficulty: 'hard',
              category: 'leadership'
            }
          ],
          milestones: [
            {
              level: 5,
              title: 'Persuasion Master',
              description: 'You can change minds with kindness',
              reward: { ce: 80, cubits: 16, avatarUpgrade: 'charisma_aura' }
            }
          ]
        }
      ]
    }
  ]
}

export const EMPATHY_SKILL_TREE: SkillTree = {
  id: 'empathy',
  name: 'Emotional Intelligence',
  description: 'Understand and connect with others emotionally',
  icon: '❤️',
  color: 'from-pink-500 to-rose-500',
  maxLevel: 40,
  branches: [
    {
      id: 'emotion_recognition',
      name: 'Recognition',
      description: 'Identify emotions in yourself and others',
      unlockLevel: 1,
      skills: [
        {
          id: 'self_awareness',
          name: 'Self-Awareness',
          description: 'Understand your own emotions',
          level: 1,
          maxLevel: 8,
          experience: 0,
          nextLevelXP: 80,
          benefits: [
            { level: 3, type: 'bonus', description: '+20% reflection quality' },
            { level: 6, type: 'ability', description: 'Emotion coaching for others' }
          ],
          activities: [
            {
              id: 'emotion_check',
              name: 'Emotion Check-in',
              description: 'Share how you\'re feeling and why',
              xpReward: 15,
              difficulty: 'easy',
              category: 'self_reflection'
            }
          ],
          milestones: [
            {
              level: 4,
              title: 'Self-Aware',
              description: 'You understand your emotional patterns',
              reward: { ce: 35, cubits: 7, badge: 'self_aware' }
            }
          ]
        }
      ]
    }
  ]
}

export const COLLABORATION_SKILL_TREE: SkillTree = {
  id: 'collaboration',
  name: 'Team Synergy',
  description: 'Work effectively with others to achieve common goals',
  icon: '🤝',
  color: 'from-green-500 to-emerald-500',
  maxLevel: 45,
  branches: [
    {
      id: 'teamwork_basics',
      name: 'Teamwork',
      description: 'Basic collaboration skills',
      unlockLevel: 1,
      skills: [
        {
          id: 'team_player',
          name: 'Team Player',
          description: 'Support team goals over individual achievements',
          level: 1,
          maxLevel: 10,
          experience: 0,
          nextLevelXP: 150,
          benefits: [
            { level: 3, type: 'bonus', description: '+25% team quest rewards' },
            { level: 6, type: 'unlock', description: 'Team formation abilities' }
          ],
          activities: [
            {
              id: 'support_teammate',
              name: 'Support Teammate',
              description: 'Help a teammate achieve their goal',
              xpReward: 30,
              difficulty: 'medium',
              category: 'support'
            }
          ],
          milestones: [
            {
              level: 5,
              title: 'Team Champion',
              description: 'Teams work better when you\'re involved',
              reward: { ce: 60, cubits: 12, badge: 'team_champion' }
            }
          ]
        }
      ]
    }
  ]
}

// Skill progression calculation functions
export function calculateSkillXP(
  activity: string,
  quality: number,
  difficulty: 'easy' | 'medium' | 'hard'
): number {
  const baseXP = {
    easy: 10,
    medium: 20,
    hard: 35
  }
  
  const qualityMultiplier = Math.max(0.5, Math.min(2.0, quality))
  return Math.floor(baseXP[difficulty] * qualityMultiplier)
}

export function updateSkillProgress(
  skill: Skill,
  xpGained: number
): { updatedSkill: Skill; leveledUp: boolean; newBenefits: SkillBenefit[] } {
  const updatedSkill = { ...skill }
  updatedSkill.experience += xpGained
  
  let leveledUp = false
  const newBenefits: SkillBenefit[] = []
  
  // Check for level ups
  while (updatedSkill.experience >= updatedSkill.nextLevelXP && updatedSkill.level < updatedSkill.maxLevel) {
    updatedSkill.experience -= updatedSkill.nextLevelXP
    updatedSkill.level += 1
    leveledUp = true
    
    // Calculate next level XP requirement
    updatedSkill.nextLevelXP = Math.floor(updatedSkill.nextLevelXP * 1.2)
    
    // Check for new benefits
    const levelBenefits = updatedSkill.benefits.filter(b => b.level === updatedSkill.level)
    newBenefits.push(...levelBenefits)
  }
  
  return { updatedSkill, leveledUp, newBenefits }
}

export function getSkillRecommendations(
  userSkills: Record<string, Skill>,
  userLevel: number,
  recentActivity: string[]
): {
  skillToFocus: string
  activities: SkillActivity[]
  reasoning: string
} {
  // Find the skill with the lowest level that's available
  const availableSkills = Object.entries(userSkills).filter(([_, skill]) => 
    skill.level < skill.maxLevel
  )
  
  if (availableSkills.length === 0) {
    return {
      skillToFocus: 'communication',
      activities: [],
      reasoning: 'All skills are maxed out! You\'re a master communicator!'
    }
  }
  
  // Sort by level (lowest first) and recent activity
  availableSkills.sort(([aId, aSkill], [bId, bSkill]) => {
    const aRecentActivity = recentActivity.filter(activity => activity.includes(aId)).length
    const bRecentActivity = recentActivity.filter(activity => activity.includes(bId)).length
    
    // Prefer skills that haven't been practiced recently
    if (aRecentActivity !== bRecentActivity) {
      return aRecentActivity - bRecentActivity
    }
    
    return aSkill.level - bSkill.level
  })
  
  const [focusSkillId, focusSkill] = availableSkills[0]
  
  return {
    skillToFocus: focusSkillId,
    activities: focusSkill.activities.slice(0, 3),
    reasoning: `Focus on ${focusSkill.name} to improve your overall communication abilities. This skill will unlock new opportunities for growth.`
  }
}

export function calculateOverallProgress(userSkills: Record<string, Skill>): {
  totalLevel: number
  totalXP: number
  completionPercentage: number
  nextMajorMilestone: string
} {
  const skills = Object.values(userSkills)
  const totalLevel = skills.reduce((sum, skill) => sum + skill.level, 0)
  const totalXP = skills.reduce((sum, skill) => sum + skill.experience, 0)
  
  const maxPossibleLevel = skills.reduce((sum, skill) => sum + skill.maxLevel, 0)
  const completionPercentage = (totalLevel / maxPossibleLevel) * 100
  
  // Find next major milestone
  let nextMajorMilestone = 'Complete all current skill activities'
  for (const skill of skills) {
    const nextMilestone = skill.milestones.find(m => m.level > skill.level)
    if (nextMilestone) {
      nextMajorMilestone = `Reach level ${nextMilestone.level} in ${skill.name}`
      break
    }
  }
  
  return {
    totalLevel,
    totalXP,
    completionPercentage,
    nextMajorMilestone
  }
}
