/**
 * Asset Loading Optimization System for NanoHero Platform
 * Implements lazy loading, compression, and efficient asset management
 */

import React, { useRef, useEffect, useState } from 'react'

export type AssetType = 'image' | 'model' | 'texture' | 'audio' | 'video' | 'font'

export interface AssetLoadingConfig {
  lazy: boolean
  priority: 'low' | 'medium' | 'high' | 'critical'
  compression: boolean
  caching: boolean
  preload: boolean
  fallback?: string
}

export interface LoadedAsset {
  url: string
  type: AssetType
  size: number
  loadTime: number
  cached: boolean
  compressed: boolean
}

/**
 * Asset loading manager with optimization features
 */
export class AssetLoadingManager {
  private static instance: AssetLoadingManager
  private loadedAssets = new Map<string, LoadedAsset>()
  private loadingPromises = new Map<string, Promise<any>>()
  private compressionWorker: Worker | null = null
  private loadingQueue: Array<{ url: string; priority: number; resolve: Function; reject: Function }> = []
  private isProcessingQueue = false
  private maxConcurrentLoads = 6

  static getInstance(): AssetLoadingManager {
    if (!AssetLoadingManager.instance) {
      AssetLoadingManager.instance = new AssetLoadingManager()
    }
    return AssetLoadingManager.instance
  }

  constructor() {
    this.initializeCompressionWorker()
  }

  private initializeCompressionWorker() {
    try {
      const workerScript = `
        self.onmessage = function(e) {
          const { imageData, quality } = e.data;
          
          // Simple image compression simulation
          // In a real implementation, you'd use libraries like imagemin
          const compressedData = {
            ...imageData,
            size: Math.floor(imageData.size * (quality / 100))
          };
          
          self.postMessage({
            success: true,
            data: compressedData
          });
        };
      `
      
      const blob = new Blob([workerScript], { type: 'application/javascript' })
      this.compressionWorker = new Worker(URL.createObjectURL(blob))
    } catch (error) {
      console.warn('Compression worker not available:', error)
    }
  }

  /**
   * Load asset with optimization
   */
  async loadAsset(
    url: string, 
    type: AssetType, 
    config: Partial<AssetLoadingConfig> = {}
  ): Promise<any> {
    const finalConfig: AssetLoadingConfig = {
      lazy: true,
      priority: 'medium',
      compression: true,
      caching: true,
      preload: false,
      ...config
    }

    // Check cache first
    if (finalConfig.caching && this.loadedAssets.has(url)) {
      const cached = this.loadedAssets.get(url)!
      console.log(`📦 Asset loaded from cache: ${url}`)
      return cached
    }

    // Check if already loading
    if (this.loadingPromises.has(url)) {
      return this.loadingPromises.get(url)!
    }

    // Create loading promise
    const loadingPromise = this.createLoadingPromise(url, type, finalConfig)
    this.loadingPromises.set(url, loadingPromise)

    try {
      const result = await loadingPromise
      this.loadingPromises.delete(url)
      return result
    } catch (error) {
      this.loadingPromises.delete(url)
      throw error
    }
  }

  private async createLoadingPromise(
    url: string, 
    type: AssetType, 
    config: AssetLoadingConfig
  ): Promise<any> {
    const startTime = performance.now()
    
    try {
      let asset: any
      
      switch (type) {
        case 'image':
          asset = await this.loadImage(url, config)
          break
        case 'model':
          asset = await this.loadModel(url, config)
          break
        case 'texture':
          asset = await this.loadTexture(url, config)
          break
        case 'audio':
          asset = await this.loadAudio(url, config)
          break
        case 'video':
          asset = await this.loadVideo(url, config)
          break
        case 'font':
          asset = await this.loadFont(url, config)
          break
        default:
          throw new Error(`Unsupported asset type: ${type}`)
      }

      const loadTime = performance.now() - startTime
      const loadedAsset: LoadedAsset = {
        url,
        type,
        size: this.getAssetSize(asset),
        loadTime,
        cached: config.caching,
        compressed: config.compression
      }

      if (config.caching) {
        this.loadedAssets.set(url, loadedAsset)
      }

      console.log(`✅ Asset loaded: ${url} (${loadTime.toFixed(2)}ms)`)
      return asset

    } catch (error) {
      console.error(`❌ Failed to load asset: ${url}`, error)
      
      // Try fallback if available
      if (config.fallback) {
        console.log(`🔄 Loading fallback: ${config.fallback}`)
        return this.loadAsset(config.fallback, type, { ...config, fallback: undefined })
      }
      
      throw error
    }
  }

  private async loadImage(url: string, config: AssetLoadingConfig): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image()
      
      // Enable CORS if needed
      img.crossOrigin = 'anonymous'
      
      // Optimize loading
      img.loading = config.lazy ? 'lazy' : 'eager'
      img.decoding = 'async'
      
      img.onload = () => {
        if (config.compression && this.compressionWorker) {
          this.compressImage(img).then(resolve).catch(() => resolve(img))
        } else {
          resolve(img)
        }
      }
      
      img.onerror = () => reject(new Error(`Failed to load image: ${url}`))
      img.src = url
    })
  }

  private async loadModel(url: string, config: AssetLoadingConfig): Promise<any> {
    // Placeholder for 3D model loading
    // In a real implementation, you'd use Three.js loaders
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`Failed to load model: ${response.statusText}`)
    }
    return response.arrayBuffer()
  }

  private async loadTexture(url: string, config: AssetLoadingConfig): Promise<any> {
    // Load as image first, then convert to texture
    const image = await this.loadImage(url, config)
    
    // In a real Three.js implementation:
    // const texture = new THREE.Texture(image)
    // texture.needsUpdate = true
    // return texture
    
    return image
  }

  private async loadAudio(url: string, config: AssetLoadingConfig): Promise<HTMLAudioElement> {
    return new Promise((resolve, reject) => {
      const audio = new Audio()
      audio.preload = config.preload ? 'auto' : 'metadata'
      
      audio.oncanplaythrough = () => resolve(audio)
      audio.onerror = () => reject(new Error(`Failed to load audio: ${url}`))
      
      audio.src = url
    })
  }

  private async loadVideo(url: string, config: AssetLoadingConfig): Promise<HTMLVideoElement> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video')
      video.preload = config.preload ? 'auto' : 'metadata'
      
      video.oncanplaythrough = () => resolve(video)
      video.onerror = () => reject(new Error(`Failed to load video: ${url}`))
      
      video.src = url
    })
  }

  private async loadFont(url: string, config: AssetLoadingConfig): Promise<FontFace> {
    const fontFace = new FontFace('CustomFont', `url(${url})`)
    await fontFace.load()
    document.fonts.add(fontFace)
    return fontFace
  }

  private async compressImage(img: HTMLImageElement): Promise<HTMLImageElement> {
    if (!this.compressionWorker) return img

    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      
      canvas.width = img.width
      canvas.height = img.height
      ctx.drawImage(img, 0, 0)
      
      // Simple compression by reducing quality
      canvas.toBlob((blob) => {
        if (blob) {
          const compressedImg = new Image()
          compressedImg.onload = () => resolve(compressedImg)
          compressedImg.src = URL.createObjectURL(blob)
        } else {
          resolve(img)
        }
      }, 'image/jpeg', 0.8)
    })
  }

  private getAssetSize(asset: any): number {
    if (asset instanceof HTMLImageElement) {
      return asset.width * asset.height * 4 // Rough estimate
    }
    if (asset instanceof ArrayBuffer) {
      return asset.byteLength
    }
    return 0
  }

  /**
   * Preload critical assets
   */
  async preloadAssets(urls: Array<{ url: string; type: AssetType }>): Promise<void> {
    const preloadPromises = urls.map(({ url, type }) =>
      this.loadAsset(url, type, { priority: 'critical', preload: true })
    )
    
    await Promise.allSettled(preloadPromises)
    console.log(`🚀 Preloaded ${urls.length} critical assets`)
  }

  /**
   * Get loading statistics
   */
  getStats() {
    const assets = Array.from(this.loadedAssets.values())
    const totalSize = assets.reduce((sum, asset) => sum + asset.size, 0)
    const averageLoadTime = assets.reduce((sum, asset) => sum + asset.loadTime, 0) / assets.length
    
    return {
      totalAssets: assets.length,
      totalSize: totalSize,
      averageLoadTime: averageLoadTime || 0,
      cachedAssets: assets.filter(a => a.cached).length,
      compressedAssets: assets.filter(a => a.compressed).length,
      activeLoads: this.loadingPromises.size
    }
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.loadedAssets.clear()
    console.log('🗑️ Asset cache cleared')
  }

  /**
   * Dispose resources
   */
  dispose() {
    this.clearCache()
    this.loadingPromises.clear()
    if (this.compressionWorker) {
      this.compressionWorker.terminate()
      this.compressionWorker = null
    }
  }
}

export const assetManager = AssetLoadingManager.getInstance()

/**
 * React hook for optimized image loading
 */
export const useOptimizedImage = (
  src: string, 
  config: Partial<AssetLoadingConfig> = {}
) => {
  const [image, setImage] = useState<HTMLImageElement | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!src) return

    setLoading(true)
    setError(null)

    assetManager.loadAsset(src, 'image', config)
      .then((img) => {
        setImage(img)
        setLoading(false)
      })
      .catch((err) => {
        setError(err.message)
        setLoading(false)
      })
  }, [src, config])

  return { image, loading, error }
}

/**
 * React hook for intersection observer-based lazy loading
 */
export const useLazyLoading = (threshold: number = 0.1) => {
  const [isVisible, setIsVisible] = useState(false)
  const elementRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.unobserve(element)
        }
      },
      { threshold }
    )

    observer.observe(element)

    return () => {
      observer.unobserve(element)
    }
  }, [threshold])

  return { elementRef, isVisible }
}

/**
 * Optimized image component with lazy loading
 */
interface OptimizedImageProps {
  src: string
  alt: string
  className?: string
  lazy?: boolean
  compression?: boolean
  fallback?: string
  onLoad?: () => void
  onError?: (error: string) => void
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className = '',
  lazy = true,
  compression = true,
  fallback,
  onLoad,
  onError
}) => {
  const { elementRef, isVisible } = useLazyLoading()
  const shouldLoad = !lazy || isVisible

  const { image, loading, error } = useOptimizedImage(
    shouldLoad ? src : '',
    { lazy, compression, fallback }
  )

  useEffect(() => {
    if (image && onLoad) {
      onLoad()
    }
  }, [image, onLoad])

  useEffect(() => {
    if (error && onError) {
      onError(error)
    }
  }, [error, onError])

  return (
    <div ref={elementRef} className={className}>
      {loading && (
        <div className="animate-pulse bg-gray-300 w-full h-full rounded" />
      )}
      {image && (
        <img
          src={image.src}
          alt={alt}
          className="w-full h-full object-cover"
          loading="lazy"
          decoding="async"
        />
      )}
      {error && (
        <div className="bg-gray-200 w-full h-full flex items-center justify-center text-gray-500">
          Failed to load image
        </div>
      )}
    </div>
  )
}

/**
 * Asset preloading utilities
 */
export const assetPreloader = {
  /**
   * Preload critical assets for landing page
   */
  preloadLandingAssets: async () => {
    const criticalAssets = [
      { url: '/images/hero-bg.jpg', type: 'image' as AssetType },
      { url: '/images/logo.svg', type: 'image' as AssetType },
      // Add more critical assets
    ]
    
    await assetManager.preloadAssets(criticalAssets)
  },

  /**
   * Preload dashboard assets
   */
  preloadDashboardAssets: async () => {
    const dashboardAssets = [
      { url: '/images/dashboard-bg.jpg', type: 'image' as AssetType },
      { url: '/models/nanocore.glb', type: 'model' as AssetType },
      // Add more dashboard assets
    ]
    
    await assetManager.preloadAssets(dashboardAssets)
  },

  /**
   * Preload 3D assets for NanoVerse
   */
  preload3DAssets: async () => {
    const assets3D = [
      { url: '/textures/particle.png', type: 'texture' as AssetType },
      { url: '/textures/skybox.jpg', type: 'texture' as AssetType },
      // Add more 3D assets
    ]
    
    await assetManager.preloadAssets(assets3D)
  }
}
