'use client'

import React, { useMemo } from 'react'
import { motion } from 'framer-motion'

interface RadarDataPoint {
  subject: string
  value: number
  maxValue: number
  color: string
}

interface RadarChartProps {
  data: RadarDataPoint[]
  size?: number
  className?: string
}

export function RadarChart({ data, size = 300, className = '' }: RadarChartProps) {
  const center = size / 2
  const radius = (size / 2) - 40
  const angleStep = (2 * Math.PI) / data.length

  // Calculate points for the radar chart
  const radarPoints = useMemo(() => {
    return data.map((item, index) => {
      const angle = (index * angleStep) - (Math.PI / 2) // Start from top
      const normalizedValue = item.value / item.maxValue
      const x = center + (radius * normalizedValue * Math.cos(angle))
      const y = center + (radius * normalizedValue * Math.sin(angle))
      
      return {
        x,
        y,
        angle,
        normalizedValue,
        ...item
      }
    })
  }, [data, center, radius, angleStep])

  // Calculate axis points (full radius)
  const axisPoints = useMemo(() => {
    return data.map((item, index) => {
      const angle = (index * angleStep) - (Math.PI / 2)
      const x = center + (radius * Math.cos(angle))
      const y = center + (radius * Math.sin(angle))
      
      // Label position (slightly outside the circle)
      const labelRadius = radius + 25
      const labelX = center + (labelRadius * Math.cos(angle))
      const labelY = center + (labelRadius * Math.sin(angle))
      
      return {
        x,
        y,
        labelX,
        labelY,
        angle,
        subject: item.subject
      }
    })
  }, [data, center, radius, angleStep])

  // Create the path string for the filled area
  const pathString = useMemo(() => {
    if (radarPoints.length === 0) return ''
    
    const pathCommands = radarPoints.map((point, index) => {
      return `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
    }).join(' ')
    
    return `${pathCommands} Z`
  }, [radarPoints])

  // Grid circles (concentric circles for reference)
  const gridCircles = [0.2, 0.4, 0.6, 0.8, 1.0]

  return (
    <div className={`relative ${className}`}>
      <svg width={size} height={size} className="overflow-visible">
        {/* Grid circles */}
        {gridCircles.map((scale, index) => (
          <circle
            key={index}
            cx={center}
            cy={center}
            r={radius * scale}
            fill="none"
            stroke="rgba(156, 163, 175, 0.2)"
            strokeWidth="1"
            strokeDasharray={index === gridCircles.length - 1 ? "none" : "2,2"}
          />
        ))}

        {/* Axis lines */}
        {axisPoints.map((point, index) => (
          <line
            key={index}
            x1={center}
            y1={center}
            x2={point.x}
            y2={point.y}
            stroke="rgba(156, 163, 175, 0.3)"
            strokeWidth="1"
          />
        ))}

        {/* Filled area */}
        <motion.path
          d={pathString}
          fill="rgba(34, 197, 94, 0.1)"
          stroke="rgba(34, 197, 94, 0.6)"
          strokeWidth="2"
          initial={{ pathLength: 0, opacity: 0 }}
          animate={{ pathLength: 1, opacity: 1 }}
          transition={{ duration: 1.5, ease: "easeInOut" }}
        />

        {/* Data points */}
        {radarPoints.map((point, index) => (
          <motion.g key={index}>
            {/* Point circle */}
            <motion.circle
              cx={point.x}
              cy={point.y}
              r="4"
              fill={point.color}
              stroke="white"
              strokeWidth="2"
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: index * 0.1 + 0.5, duration: 0.3 }}
              className="drop-shadow-lg"
            />
            
            {/* Hover effect */}
            <motion.circle
              cx={point.x}
              cy={point.y}
              r="8"
              fill="transparent"
              className="cursor-pointer"
              whileHover={{ scale: 1.2 }}
            />
          </motion.g>
        ))}

        {/* Labels */}
        {axisPoints.map((point, index) => {
          const dataPoint = data[index]
          const textAnchor = point.labelX > center ? 'start' : point.labelX < center ? 'end' : 'middle'
          const dy = point.labelY > center ? '0.3em' : point.labelY < center ? '-0.3em' : '0.3em'
          
          return (
            <motion.g
              key={index}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1 + 1, duration: 0.3 }}
            >
              {/* Subject name */}
              <text
                x={point.labelX}
                y={point.labelY}
                textAnchor={textAnchor}
                dy={dy}
                className="text-xs font-medium fill-white"
              >
                {point.subject}
              </text>
              
              {/* Value */}
              <text
                x={point.labelX}
                y={point.labelY + (point.labelY > center ? 12 : -12)}
                textAnchor={textAnchor}
                className="text-xs fill-gray-400"
              >
                {dataPoint.value}%
              </text>
            </motion.g>
          )
        })}

        {/* Center point */}
        <circle
          cx={center}
          cy={center}
          r="2"
          fill="rgba(156, 163, 175, 0.5)"
        />
      </svg>

      {/* Legend */}
      <div className="absolute bottom-0 left-0 right-0 flex justify-center">
        <div className="bg-black/60 backdrop-blur-xl border border-gray-800/50 rounded-lg p-2">
          <div className="text-xs text-gray-400 text-center">
            Mastery Levels (0-100%)
          </div>
        </div>
      </div>
    </div>
  )
}
