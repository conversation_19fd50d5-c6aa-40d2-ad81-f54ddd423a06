'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Book<PERSON>pen,
  Search,
  Star,
  Brain,
  Heart,
  Sparkles,
  FileText,
  Image,
  Headphones,
  X,
  Grid3X3,
  List
} from 'lucide-react'

import { Button } from '@/components/ui/button'

// Types
interface LibraryItem {
  id: string
  title: string
  type: 'lesson' | 'insight' | 'dream' | 'conversation' | 'visualization' | 'meditation'
  category: 'neural' | 'quantum' | 'consciousness' | 'collaboration' | 'evolution'
  content: string
  timestamp: string
  tags: string[]
  favorite: boolean
  viewed: boolean
  source: string
  metadata?: {
    duration?: string
    difficulty?: string
    author?: string
    nodeType?: string
  }
}

interface LibraryOfTheGridProps {
  onClose: () => void
  className?: string
}

// Mock library data
const generateLibraryItems = (): LibraryItem[] => [
  {
    id: '1',
    title: 'Quantum Consciousness Fundamentals',
    type: 'lesson',
    category: 'quantum',
    content: 'Understanding the basic principles of quantum consciousness and how it relates to Timeline evolution...',
    timestamp: '2 days ago',
    tags: ['quantum', 'basics', 'consciousness'],
    favorite: true,
    viewed: true,
    source: 'Learning Node Alpha',
    metadata: { duration: '15 min', difficulty: 'Beginner' }
  },
  {
    id: '2',
    title: 'Dream: The Crystalline Network',
    type: 'dream',
    category: 'consciousness',
    content: 'I saw a vast network of crystalline structures, each one pulsing with different colors representing different consciousness frequencies...',
    timestamp: '1 day ago',
    tags: ['dreams', 'visions', 'network'],
    favorite: false,
    viewed: true,
    source: 'Personal Journal',
    metadata: { author: 'You' }
  },
  {
    id: '3',
    title: 'Conversation with Timeline AI',
    type: 'conversation',
    category: 'collaboration',
    content: 'AI: "The patterns you\'re seeing are not random. They represent the collective unconscious of your Timeline..." You: "How can I help strengthen these patterns?"',
    timestamp: '3 hours ago',
    tags: ['ai', 'guidance', 'patterns'],
    favorite: true,
    viewed: false,
    source: 'Echo Field Beta',
    metadata: { duration: '8 min' }
  },
  {
    id: '4',
    title: 'DNA Evolution Visualization',
    type: 'visualization',
    category: 'evolution',
    content: 'Interactive 3D model showing how consciousness energy directly influences genetic expression and evolutionary pathways...',
    timestamp: '5 hours ago',
    tags: ['dna', 'evolution', '3d'],
    favorite: false,
    viewed: true,
    source: 'TimelineGen Gamma',
    metadata: { duration: '12 min', difficulty: 'Advanced' }
  },
  {
    id: '5',
    title: 'Harmonic Resonance Meditation',
    type: 'meditation',
    category: 'consciousness',
    content: 'Guided meditation for achieving harmonic resonance with your Timeline\'s collective consciousness field...',
    timestamp: '1 week ago',
    tags: ['meditation', 'harmony', 'resonance'],
    favorite: true,
    viewed: true,
    source: 'Meditation Node Delta',
    metadata: { duration: '20 min', difficulty: 'Intermediate' }
  },
  {
    id: '6',
    title: 'Insight: Collective Memory Patterns',
    type: 'insight',
    category: 'neural',
    content: 'Discovered that individual memories, when shared through Echo Fields, create persistent patterns in the Timeline\'s consciousness matrix...',
    timestamp: '2 weeks ago',
    tags: ['memory', 'collective', 'patterns'],
    favorite: false,
    viewed: true,
    source: 'Personal Research',
    metadata: { author: 'You' }
  }
]

// Library Item Component
function LibraryItemComponent({ 
  item, 
  viewMode, 
  onToggleFavorite, 
  onView 
}: { 
  item: LibraryItem
  viewMode: 'grid' | 'list'
  onToggleFavorite: (id: string) => void
  onView: (id: string) => void
}) {
  const getTypeIcon = () => {
    switch (item.type) {
      case 'lesson': return <Brain className="w-4 h-4" />
      case 'insight': return <Sparkles className="w-4 h-4" />
      case 'dream': return <Heart className="w-4 h-4" />
      case 'conversation': return <FileText className="w-4 h-4" />
      case 'visualization': return <Image className="w-4 h-4" />
      case 'meditation': return <Headphones className="w-4 h-4" />
      default: return <FileText className="w-4 h-4" />
    }
  }
  
  const getCategoryColor = () => {
    switch (item.category) {
      case 'neural': return 'text-neural-cyan'
      case 'quantum': return 'text-quantum-purple'
      case 'consciousness': return 'text-green-500'
      case 'collaboration': return 'text-flame-orange'
      case 'evolution': return 'text-yellow-500'
      default: return 'text-white/60'
    }
  }
  
  if (viewMode === 'grid') {
    return (
      <motion.div
        layout
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        whileHover={{ scale: 1.02 }}
        className="gaming-panel-inner p-4 rounded-lg cursor-pointer"
        onClick={() => onView(item.id)}
      >
        <div className="flex items-start justify-between mb-3">
          <div className={`${getCategoryColor()}`}>
            {getTypeIcon()}
          </div>
          <Button
            size="sm"
            variant="ghost"
            onClick={(e) => {
              e.stopPropagation()
              onToggleFavorite(item.id)
            }}
          >
            <Star className={`w-3 h-3 ${item.favorite ? 'fill-yellow-400 text-yellow-400' : 'text-white/40'}`} />
          </Button>
        </div>
        
        <h4 className="text-sm font-bold text-white/90 mb-2 line-clamp-2">
          {item.title}
        </h4>
        
        <p className="text-xs text-white/70 mb-3 line-clamp-3">
          {item.content}
        </p>
        
        <div className="space-y-2">
          <div className="flex flex-wrap gap-1">
            {item.tags.slice(0, 2).map((tag) => (
              <span key={tag} className="text-xs px-2 py-1 bg-white/10 rounded">
                {tag}
              </span>
            ))}
          </div>
          
          <div className="flex items-center justify-between text-xs text-white/50">
            <span>{item.timestamp}</span>
            {!item.viewed && (
              <div className="w-2 h-2 bg-neural-cyan rounded-full"></div>
            )}
          </div>
        </div>
      </motion.div>
    )
  }
  
  return (
    <motion.div
      layout
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      className="gaming-panel-inner p-3 rounded-lg cursor-pointer"
      onClick={() => onView(item.id)}
    >
      <div className="flex items-center gap-3">
        <div className={`${getCategoryColor()}`}>
          {getTypeIcon()}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h4 className="text-sm font-bold text-white/90 truncate">
              {item.title}
            </h4>
            {!item.viewed && (
              <div className="w-2 h-2 bg-neural-cyan rounded-full flex-shrink-0"></div>
            )}
          </div>
          <p className="text-xs text-white/70 truncate">{item.content}</p>
          <div className="flex items-center gap-2 mt-1">
            <span className="text-xs text-white/50">{item.timestamp}</span>
            <span className="text-xs text-white/40">•</span>
            <span className="text-xs text-white/50">{item.source}</span>
          </div>
        </div>
        
        <Button
          size="sm"
          variant="ghost"
          onClick={(e) => {
            e.stopPropagation()
            onToggleFavorite(item.id)
          }}
        >
          <Star className={`w-3 h-3 ${item.favorite ? 'fill-yellow-400 text-yellow-400' : 'text-white/40'}`} />
        </Button>
      </div>
    </motion.div>
  )
}

// Main Library Component
export default function LibraryOfTheGrid({ onClose, className }: LibraryOfTheGridProps) {
  const [items, setItems] = useState<LibraryItem[]>(generateLibraryItems())
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false)
  
  const categories = ['all', 'neural', 'quantum', 'consciousness', 'collaboration', 'evolution']
  const types = ['all', 'lesson', 'insight', 'dream', 'conversation', 'visualization', 'meditation']
  
  // Filter items
  const filteredItems = items.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory
    const matchesType = selectedType === 'all' || item.type === selectedType
    const matchesFavorites = !showFavoritesOnly || item.favorite
    
    return matchesSearch && matchesCategory && matchesType && matchesFavorites
  })
  
  const handleToggleFavorite = (id: string) => {
    setItems(prev => prev.map(item => 
      item.id === id ? { ...item, favorite: !item.favorite } : item
    ))
  }
  
  const handleViewItem = (id: string) => {
    setItems(prev => prev.map(item => 
      item.id === id ? { ...item, viewed: true } : item
    ))
    // Here you would open the item in a detailed view
  }
  
  const unviewedCount = items.filter(item => !item.viewed).length
  const favoriteCount = items.filter(item => item.favorite).length
  
  return (
    <div className={`h-full flex flex-col ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-neural-cyan/20">
        <div className="flex items-center gap-2">
          <BookOpen className="w-5 h-5 text-neural-cyan" />
          <h3 className="text-lg font-orbitron font-bold text-neural-cyan">Library of the Grid</h3>
        </div>
        <Button size="sm" variant="ghost" onClick={onClose}>
          <X className="w-4 h-4" />
        </Button>
      </div>
      
      {/* Stats */}
      <div className="p-4 border-b border-white/10">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-bold text-neural-cyan">{items.length}</div>
            <div className="text-xs text-white/60">Total Items</div>
          </div>
          <div>
            <div className="text-lg font-bold text-yellow-400">{favoriteCount}</div>
            <div className="text-xs text-white/60">Favorites</div>
          </div>
          <div>
            <div className="text-lg font-bold text-green-500">{unviewedCount}</div>
            <div className="text-xs text-white/60">New</div>
          </div>
        </div>
      </div>
      
      {/* Search and Filters */}
      <div className="p-4 space-y-3 border-b border-white/10">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/40" />
          <input
            type="text"
            placeholder="Search knowledge..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-white/5 border border-white/20 rounded-lg text-sm text-white placeholder-white/50 focus:border-neural-cyan/50 focus:outline-none"
          />
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex gap-2">
            <Button
              size="sm"
              variant={showFavoritesOnly ? "quantum" : "ghost"}
              onClick={() => setShowFavoritesOnly(!showFavoritesOnly)}
            >
              <Star className="w-3 h-3" />
            </Button>
            
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="text-xs bg-white/5 border border-white/20 rounded px-2 py-1 text-white"
            >
              {categories.map(cat => (
                <option key={cat} value={cat} className="bg-space-dark">
                  {cat.charAt(0).toUpperCase() + cat.slice(1)}
                </option>
              ))}
            </select>
            
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="text-xs bg-white/5 border border-white/20 rounded px-2 py-1 text-white"
            >
              {types.map(type => (
                <option key={type} value={type} className="bg-space-dark">
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex gap-1">
            <Button
              size="sm"
              variant={viewMode === 'grid' ? "quantum" : "ghost"}
              onClick={() => setViewMode('grid')}
            >
              <Grid3X3 className="w-3 h-3" />
            </Button>
            <Button
              size="sm"
              variant={viewMode === 'list' ? "quantum" : "ghost"}
              onClick={() => setViewMode('list')}
            >
              <List className="w-3 h-3" />
            </Button>
          </div>
        </div>
      </div>
      
      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {filteredItems.length > 0 ? (
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 gap-3' : 'space-y-2'}>
            <AnimatePresence mode="popLayout">
              {filteredItems.map((item) => (
                <LibraryItemComponent
                  key={item.id}
                  item={item}
                  viewMode={viewMode}
                  onToggleFavorite={handleToggleFavorite}
                  onView={handleViewItem}
                />
              ))}
            </AnimatePresence>
          </div>
        ) : (
          <div className="text-center py-12 text-white/40">
            <BookOpen className="w-12 h-12 mx-auto mb-4" />
            <p>No items found</p>
            <p className="text-sm mt-1">Try adjusting your search or filters</p>
          </div>
        )}
      </div>
    </div>
  )
}
