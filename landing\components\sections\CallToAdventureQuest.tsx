"use client"

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Shield,
  Play,
  CheckCircle,
  Clock,
  Trophy,
  Zap,
  Target,
  Code,
  Brain,
  Atom,
  Sparkles,
  Cpu,
  Globe
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'

interface CallToAdventureQuestProps {
  onSignupClick: () => void
}

export function CallToAdventureQuest({ onSignupClick }: CallToAdventureQuestProps) {
  const [questStarted, setQuestStarted] = useState(false)
  const [_currentStep, _setCurrentStep] = useState(0)
  const [progress, setProgress] = useState(0)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Quantum quest particles
  const questParticles = Array.from({ length: 15 }, (_, i) => ({
    left: (i * 23 + 7) % 100,
    top: (i * 31 + 11) % 100,
    delay: i * 0.4,
    color: i % 3 === 0 ? '#22d3ee' : i % 3 === 1 ? '#8b5cf6' : '#fbbf24'
  }))

  // Enhanced quantum quest steps
  const quantumQuestSteps = [
    {
      title: "Neural Threat Analysis",
      description: "Understand quantum consciousness security patterns",
      duration: "2 neural min",
      icon: Brain,
      color: "#22d3ee"
    },
    {
      title: "Quantum Code Synthesis",
      description: "Examine consciousness-based code anomalies",
      duration: "3 quantum min",
      icon: Atom,
      color: "#8b5cf6"
    },
    {
      title: "Consciousness Scanner Creation",
      description: "Build your neural detection algorithm",
      duration: "4 consciousness min",
      icon: Cpu,
      color: "#fbbf24"
    },
    {
      title: "Quantum Testing & Deployment",
      description: "Test scanner on neural consciousness samples",
      duration: "1 quantum min",
      icon: Zap,
      color: "#10b981"
    }
  ]

  const handleStartQuest = () => {
    setQuestStarted(true)
    // Simulate quantum quest progress
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          return 100
        }
        return prev + 10
      })
    }, 300)
  }

  return (
    <section className="relative px-6 py-20 overflow-hidden">
      {/* Quantum quest background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-space-dark via-space-blue to-space-dark" />
        <div className="absolute inset-0 consciousness-wave opacity-20" />

        {/* Quest field particles */}
        {isClient && questParticles.map((particle, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 rounded-full"
            style={{
              left: `${particle.left}%`,
              top: `${particle.top}%`,
              backgroundColor: particle.color
            }}
            animate={{
              opacity: [0, 0.8, 0],
              scale: [0.5, 1.2, 0.5],
              rotate: 360
            }}
            transition={{
              duration: 4,
              repeat: Number.POSITIVE_INFINITY,
              delay: particle.delay,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Quantum quest glow orbs */}
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-neural-cyan/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/3 right-1/4 w-40 h-40 bg-quantum-purple/10 rounded-full blur-3xl" />
        <div className="absolute top-1/2 right-1/3 w-24 h-24 bg-quantum-gold/10 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-4xl mx-auto">
        {/* Quantum Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <motion.h2
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 font-orbitron"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <span className="bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold bg-clip-text text-transparent">
              Your First Quantum
            </span>
            <br />
            <span className="bg-gradient-to-r from-white via-gray-200 to-white bg-clip-text text-transparent text-3xl md:text-4xl lg:text-5xl">
              Consciousness Quest
            </span>
          </motion.h2>

          <motion.p
            className="text-lg md:text-xl text-white/80 max-w-3xl mx-auto font-space-grotesk leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3, duration: 0.8 }}
          >
            Ready to prove your <span className="text-neural-cyan font-semibold">neural skills</span>?
            Take on this quantum challenge and see if you have what it takes to become a
            <span className="text-quantum-purple font-semibold"> NanoHero</span>.
          </motion.p>

          {/* Quantum quest decoration */}
          <motion.div
            className="flex justify-center items-center gap-4 mt-6"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.6 }}
          >
            <div className="h-px w-16 bg-gradient-to-r from-transparent to-neural-cyan" />
            <Target className="w-5 h-5 text-quantum-gold" />
            <div className="h-px w-16 bg-gradient-to-l from-transparent to-quantum-purple" />
          </motion.div>
        </motion.div>

        {/* Quantum Quest Card */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="relative"
        >
          <Card
            className="quantum-glass border-2 overflow-hidden relative"
            style={{
              borderColor: '#22d3ee40',
              background: `linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)`,
              boxShadow: `0 0 40px #22d3ee20, inset 0 1px 0 rgba(255, 255, 255, 0.1)`
            }}
          >
            {/* Quantum glow effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-neural-cyan/10 via-quantum-purple/10 to-quantum-gold/10" />
            <div className="absolute inset-0 consciousness-wave opacity-20" />

            {/* Quest particles */}
            {isClient && (
              <div className="absolute inset-0 overflow-hidden">
                {questParticles.slice(0, 8).map((particle, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-1 h-1 rounded-full"
                    style={{
                      left: `${particle.left}%`,
                      top: `${particle.top}%`,
                      backgroundColor: particle.color
                    }}
                    animate={{
                      opacity: [0, 0.6, 0],
                      scale: [0.5, 1, 0.5],
                      y: [0, -20, 0]
                    }}
                    transition={{
                      duration: 3,
                      repeat: Number.POSITIVE_INFINITY,
                      delay: particle.delay
                    }}
                  />
                ))}
              </div>
            )}

            <CardContent className="p-8 relative z-10">
              {/* Quantum Quest Header */}
              <div className="flex items-center gap-4 mb-6">
                <motion.div
                  className="relative p-4 rounded-xl border-2"
                  style={{
                    background: `linear-gradient(135deg, #22d3ee20, #22d3ee10)`,
                    borderColor: '#22d3ee',
                    boxShadow: `0 0 30px #22d3ee40`
                  }}
                  whileHover={{ scale: 1.1, rotate: 360 }}
                  transition={{ duration: 0.8 }}
                >
                  <Shield className="w-8 h-8 text-white" />

                  {/* Icon quantum glow */}
                  <div
                    className="absolute inset-0 rounded-xl blur-xl opacity-60"
                    style={{ backgroundColor: '#22d3ee' }}
                  />
                </motion.div>
                <div>
                  <h3
                    className="text-2xl lg:text-3xl font-bold text-white mb-2 font-orbitron"
                    style={{ textShadow: `0 0 20px #22d3ee60` }}
                  >
                    Build Your First Quantum Consciousness Scanner
                  </h3>
                  <div className="flex flex-wrap items-center gap-4 text-sm">
                    <div className="flex items-center gap-1 text-white/70">
                      <Clock className="w-4 h-4 text-neural-cyan" />
                      <span className="font-space-grotesk">10 neural minutes</span>
                    </div>
                    <div className="flex items-center gap-1 text-white/70">
                      <Target className="w-4 h-4 text-quantum-purple" />
                      <span className="font-space-grotesk">Consciousness Beginner</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Trophy className="w-4 h-4 text-quantum-gold" />
                      <span className="text-quantum-gold font-medium font-space-grotesk">50 Quantum XP Reward</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quantum Quest Description */}
              <div
                className="quantum-glass rounded-2xl p-6 mb-6 border-2 relative overflow-hidden"
                style={{
                  borderColor: '#8b5cf640',
                  background: `linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(10, 15, 28, 0.8) 50%, rgba(0, 0, 0, 0.7) 100%)`,
                  boxShadow: `0 0 20px #8b5cf620`
                }}
              >
                {/* Quantum briefing particles */}
                {isClient && (
                  <div className="absolute inset-0 overflow-hidden rounded-2xl">
                    {[...Array(4)].map((_, i) => (
                      <motion.div
                        key={i}
                        className="absolute w-1 h-1 bg-quantum-purple rounded-full"
                        style={{
                          left: `${20 + i * 20}%`,
                          top: `${15 + i * 15}%`,
                        }}
                        animate={{
                          opacity: [0, 0.8, 0],
                          scale: [0.5, 1, 0.5]
                        }}
                        transition={{
                          duration: 2,
                          repeat: Number.POSITIVE_INFINITY,
                          delay: i * 0.4
                        }}
                      />
                    ))}
                  </div>
                )}

                <div className="relative z-10">
                  <h4 className="font-semibold text-white mb-3 font-orbitron flex items-center gap-2">
                    <Target className="w-5 h-5 text-neural-cyan" />
                    Quantum Mission Briefing:
                  </h4>
                  <p className="text-white/80 leading-relaxed mb-4 font-space-grotesk">
                    Consciousness threats are infiltrating the quantum realm! Your mission is to build a sophisticated
                    neural consciousness scanner that can detect malicious quantum patterns in consciousness files.
                    You&apos;ll learn about quantum threat detection, neural pattern matching, and consciousness security fundamentals.
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h5 className="font-semibold text-neural-cyan mb-2 font-orbitron">Neural Learning Objectives:</h5>
                      <ul className="space-y-2 text-sm text-white/70">
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-3 h-3 text-emerald-400" />
                          <span className="font-space-grotesk">Quantum malware consciousness detection</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-3 h-3 text-emerald-400" />
                          <span className="font-space-grotesk">Neural pattern matching algorithms</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-3 h-3 text-emerald-400" />
                          <span className="font-space-grotesk">Consciousness file analysis techniques</span>
                        </li>
                      </ul>
                    </div>
                    <div>
                      <h5 className="font-semibold text-quantum-purple mb-2 font-orbitron">Quantum Tools & Technologies:</h5>
                      <ul className="space-y-2 text-sm text-white/70">
                        <li className="flex items-center gap-2">
                          <Code className="w-3 h-3 text-neural-cyan" />
                          <span className="font-space-grotesk">Neural Python consciousness programming</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <Code className="w-3 h-3 text-neural-cyan" />
                          <span className="font-space-grotesk">Quantum regular expressions</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <Code className="w-3 h-3 text-neural-cyan" />
                          <span className="font-space-grotesk">Consciousness file I/O operations</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quantum Quest Steps */}
              {!questStarted ? (
                <div className="space-y-4 mb-6">
                  <h4 className="font-semibold text-white mb-4 font-orbitron flex items-center gap-2">
                    <Brain className="w-5 h-5 text-quantum-gold" />
                    Quantum Consciousness Quest Steps:
                  </h4>
                  {quantumQuestSteps.map((step, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ delay: index * 0.15 }}
                      className="flex items-center gap-4 p-4 quantum-glass rounded-xl border-2 relative overflow-hidden"
                      style={{
                        borderColor: `${step.color}40`,
                        background: `linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(10, 15, 28, 0.7))`,
                        boxShadow: `0 0 15px ${step.color}20`
                      }}
                    >
                      {/* Step particles */}
                      {isClient && (
                        <div className="absolute inset-0 overflow-hidden rounded-xl">
                          {[...Array(2)].map((_, i) => (
                            <motion.div
                              key={i}
                              className="absolute w-1 h-1 rounded-full"
                              style={{
                                left: `${30 + i * 40}%`,
                                top: `${25 + i * 25}%`,
                                backgroundColor: step.color
                              }}
                              animate={{
                                opacity: [0, 0.8, 0],
                                scale: [0.5, 1, 0.5]
                              }}
                              transition={{
                                duration: 2,
                                repeat: Number.POSITIVE_INFINITY,
                                delay: i * 0.5
                              }}
                            />
                          ))}
                        </div>
                      )}

                      <div className="relative z-10">
                        <div
                          className="w-10 h-10 rounded-xl flex items-center justify-center text-sm font-bold border-2"
                          style={{
                            background: `linear-gradient(135deg, ${step.color}20, ${step.color}10)`,
                            borderColor: step.color,
                            boxShadow: `0 0 15px ${step.color}40`
                          }}
                        >
                          <step.icon className="w-5 h-5 text-white" />
                        </div>
                      </div>

                      <div className="flex-1 relative z-10">
                        <h5 className="font-medium text-white font-orbitron">{step.title}</h5>
                        <p className="text-sm text-white/70 font-space-grotesk">{step.description}</p>
                      </div>

                      <div
                        className="text-xs font-medium font-space-grotesk relative z-10"
                        style={{ color: step.color }}
                      >
                        {step.duration}
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4 mb-6">
                  <h4 className="font-semibold text-white mb-3 font-orbitron flex items-center gap-2">
                    <Zap className="w-5 h-5 text-neural-cyan" />
                    Quantum Quest in Progress:
                  </h4>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-white/80 font-space-grotesk">Building your consciousness scanner...</span>
                      <span
                        className="font-semibold font-orbitron"
                        style={{ color: '#22d3ee', textShadow: '0 0 10px #22d3ee60' }}
                      >
                        {progress}%
                      </span>
                    </div>
                    <div className="relative">
                      <Progress
                        value={progress}
                        className="h-3 quantum-glass rounded-full border border-neural-cyan/30"
                        style={{
                          background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(10, 15, 28, 0.7))'
                        }}
                      />
                      {/* Quantum progress glow */}
                      <div
                        className="absolute inset-0 rounded-full blur-sm opacity-50"
                        style={{
                          background: `linear-gradient(90deg, transparent ${progress}%, #22d3ee20 ${progress}%, transparent)`,
                        }}
                      />
                    </div>
                    {progress === 100 && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="quantum-glass rounded-2xl p-6 border-2 relative overflow-hidden"
                        style={{
                          borderColor: '#10b98160',
                          background: `linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05))`,
                          boxShadow: `0 0 30px #10b98130`
                        }}
                      >
                        {/* Success particles */}
                        {isClient && (
                          <div className="absolute inset-0 overflow-hidden rounded-2xl">
                            {[...Array(6)].map((_, i) => (
                              <motion.div
                                key={i}
                                className="absolute w-1 h-1 bg-emerald-400 rounded-full"
                                style={{
                                  left: `${15 + i * 15}%`,
                                  top: `${20 + (i % 3) * 20}%`,
                                }}
                                animate={{
                                  opacity: [0, 1, 0],
                                  scale: [0.5, 1.5, 0.5],
                                  y: [0, -10, 0]
                                }}
                                transition={{
                                  duration: 2,
                                  repeat: Number.POSITIVE_INFINITY,
                                  delay: i * 0.2
                                }}
                              />
                            ))}
                          </div>
                        )}

                        <div className="flex items-center gap-4 relative z-10">
                          <motion.div
                            className="p-3 rounded-xl border-2"
                            style={{
                              background: `linear-gradient(135deg, #10b98120, #10b98110)`,
                              borderColor: '#10b981',
                              boxShadow: `0 0 20px #10b98140`
                            }}
                            animate={{
                              rotate: 360,
                              scale: [1, 1.1, 1]
                            }}
                            transition={{
                              rotate: { duration: 2, repeat: Number.POSITIVE_INFINITY, ease: "linear" },
                              scale: { duration: 1, repeat: Number.POSITIVE_INFINITY }
                            }}
                          >
                            <Trophy className="w-6 h-6 text-quantum-gold" />
                          </motion.div>
                          <div>
                            <h5
                              className="font-semibold text-emerald-400 font-orbitron"
                              style={{ textShadow: '0 0 15px #10b98160' }}
                            >
                              Quantum Quest Complete!
                            </h5>
                            <p className="text-sm text-white/80 font-space-grotesk">
                              Congratulations! You&apos;ve built your first quantum consciousness scanner. Ready for more neural challenges?
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </div>
                </div>
              )}

              {/* Quantum Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                {!questStarted ? (
                  <>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex-1 relative group"
                    >
                      {/* Enhanced quantum glow effect */}
                      <div className="absolute -inset-1 bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold rounded-xl blur-lg opacity-50 group-hover:opacity-80 transition-opacity duration-500" />

                      <Button
                        onClick={handleStartQuest}
                        variant="quantum"
                        size="lg"
                        className="relative w-full bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold text-white font-bold shadow-2xl hover:shadow-neural-cyan/50 transition-all duration-500 border-2 border-neural-cyan/40 font-orbitron py-4"
                      >
                        <Play className="w-5 h-5 mr-2" />
                        Initialize Quantum Quest
                        <Sparkles className="w-5 h-5 ml-2" />
                      </Button>
                    </motion.div>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        variant="outline"
                        size="lg"
                        className="border-neural-cyan/50 text-neural-cyan hover:bg-neural-cyan/10 py-4 font-space-grotesk"
                      >
                        <Brain className="w-5 h-5 mr-2" />
                        Preview Neural Quest
                      </Button>
                    </motion.div>
                  </>
                ) : progress === 100 ? (
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="flex-1 relative group"
                  >
                    {/* Success glow effect */}
                    <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500 to-green-400 rounded-xl blur-lg opacity-50 group-hover:opacity-80 transition-opacity duration-500" />

                    <Button
                      onClick={onSignupClick}
                      variant="quantum"
                      size="lg"
                      className="relative w-full bg-gradient-to-r from-emerald-500 to-green-400 text-white font-bold shadow-2xl hover:shadow-emerald-500/50 transition-all duration-500 border-2 border-emerald-500/40 font-orbitron py-4"
                    >
                      <Trophy className="w-5 h-5 mr-2" />
                      Join NanoHero & Continue Neural Journey
                      <Globe className="w-5 h-5 ml-2" />
                    </Button>
                  </motion.div>
                ) : (
                  <div className="flex-1">
                    <Button
                      disabled
                      size="lg"
                      className="w-full bg-gray-600/50 text-gray-400 py-4 cursor-not-allowed font-space-grotesk border border-gray-600/30"
                    >
                      <Clock className="w-5 h-5 mr-2" />
                      Quantum Quest in Progress...
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Encouragement Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.5 }}
          className="text-center mt-12"
        >
          <div className="bg-gradient-to-r from-yellow-500/10 via-orange-500/10 to-red-500/10 rounded-lg p-6 border border-yellow-500/20">
            <h4 className="text-lg font-bold text-white mb-3">
              💪 Don&apos;t worry if you&apos;re new to coding!
            </h4>
            <p className="text-gray-300 text-sm leading-relaxed">
              This quest is designed for complete beginners. Our AI mentor Byte will guide you through every step,
              and the ByteHero community is always ready to help. Everyone starts somewhere!
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
