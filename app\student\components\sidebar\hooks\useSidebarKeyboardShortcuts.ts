import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MenuItem } from '../types'

interface UseSidebarKeyboardShortcutsProps {
  menuItems: MenuItem[]
  setActiveItem: (item: string) => void
  setShowCommandPalette: (show: boolean | ((prev: boolean) => boolean)) => void
  setCompactMode: (compact: boolean | ((prev: boolean) => boolean)) => void
}

export function useSidebarKeyboardShortcuts({
  menuItems,
  setActiveItem,
  setShowCommandPalette,
  setCompactMode
}: UseSidebarKeyboardShortcutsProps) {
  const router = useRouter()

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Command palette toggle (Ctrl/Cmd + K)
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault()
        setShowCommandPalette(prev => !prev)
      }

      // Quick navigation hotkeys (Ctrl/Cmd + number)
      if ((event.ctrlKey || event.metaKey) && event.key >= '1' && event.key <= '9') {
        event.preventDefault()
        const index = parseInt(event.key) - 1
        const menuItem = menuItems[index]
        if (menuItem) {
          setActiveItem(menuItem.id)
          if (menuItem.route) {
            router.push(menuItem.route)
          } else if (menuItem.action) {
            menuItem.action()
          }
        }
      }

      // Compact mode toggle (Ctrl/Cmd + Shift + C)
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'C') {
        event.preventDefault()
        setCompactMode(prev => !prev)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [menuItems, setActiveItem, setShowCommandPalette, setCompactMode, router])
}
