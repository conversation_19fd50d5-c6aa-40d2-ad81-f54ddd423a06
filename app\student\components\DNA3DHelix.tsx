"use client"

import { useRef, useMemo } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { <PERSON><PERSON>, Cylinder } from '@react-three/drei'
import { Vector3, Color } from 'three'
import * as THREE from 'three'

interface DNAHelixProps {
  height?: number
  radius?: number
  turns?: number
  baseCount?: number
  evolutionStage?: number
  ceLevel?: number
  qsLevel?: number
  isAnimated?: boolean
}

// DNA Base pair component
function BasePair({ 
  position, 
  rotation, 
  baseType, 
  evolutionStage = 1,
  ceLevel = 0.5,
  qsLevel = 0.5 
}: { 
  position: [number, number, number]
  rotation: number
  baseType: 'AT' | 'GC' | 'NANO' | 'QUANTUM'
  evolutionStage: number
  ceLevel: number
  qsLevel: number
}) {
  const groupRef = useRef<THREE.Group>(null)
  
  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = rotation + state.clock.elapsedTime * 0.5
    }
  })

  // Color based on base type and evolution stage
  const getBaseColors = () => {
    switch (baseType) {
      case 'AT':
        return {
          left: new Color(0x22D3EE).lerp(new Color(0x8B5CF6), evolutionStage / 10),
          right: new Color(0xF59E0B).lerp(new Color(0xEF4444), evolutionStage / 10)
        }
      case 'GC':
        return {
          left: new Color(0x10B981).lerp(new Color(0x8B5CF6), evolutionStage / 10),
          right: new Color(0xF59E0B).lerp(new Color(0xFFD700), evolutionStage / 10)
        }
      case 'NANO':
        return {
          left: new Color(0x22D3EE).multiplyScalar(1 + ceLevel * 0.5),
          right: new Color(0x8B5CF6).multiplyScalar(1 + ceLevel * 0.5)
        }
      case 'QUANTUM':
        return {
          left: new Color(0x8B5CF6).multiplyScalar(1 + qsLevel * 0.5),
          right: new Color(0xFFD700).multiplyScalar(1 + qsLevel * 0.5)
        }
    }
  }

  const colors = getBaseColors()
  const intensity = 0.3 + (ceLevel + qsLevel) * 0.4

  return (
    <group ref={groupRef} position={position}>
      {/* Left base */}
      <Sphere args={[0.08, 8, 8]} position={[-0.4, 0, 0]}>
        <meshStandardMaterial 
          color={colors.left} 
          emissive={colors.left}
          emissiveIntensity={intensity}
          transparent
          opacity={0.9}
        />
      </Sphere>
      
      {/* Right base */}
      <Sphere args={[0.08, 8, 8]} position={[0.4, 0, 0]}>
        <meshStandardMaterial 
          color={colors.right} 
          emissive={colors.right}
          emissiveIntensity={intensity}
          transparent
          opacity={0.9}
        />
      </Sphere>
      
      {/* Connecting bond */}
      <Cylinder args={[0.02, 0.02, 0.8]} rotation={[0, 0, Math.PI / 2]}>
        <meshStandardMaterial 
          color={new Color().lerpColors(colors.left, colors.right, 0.5)}
          emissive={new Color().lerpColors(colors.left, colors.right, 0.5)}
          emissiveIntensity={intensity * 0.5}
          transparent
          opacity={0.7}
        />
      </Cylinder>
    </group>
  )
}

// DNA Backbone component
function DNABackbone({ 
  points, 
  color, 
  evolutionStage = 1,
  energyLevel = 0.5 
}: { 
  points: Vector3[]
  color: Color
  evolutionStage: number
  energyLevel: number
}) {
  const tubeRef = useRef<THREE.Mesh>(null)
  
  useFrame((state) => {
    if (tubeRef.current) {
      const material = tubeRef.current.material as THREE.MeshStandardMaterial
      material.emissiveIntensity = 0.2 + Math.sin(state.clock.elapsedTime * 2) * 0.1 + energyLevel * 0.3
    }
  })

  const curve = useMemo(() => {
    return new THREE.CatmullRomCurve3(points)
  }, [points])

  const tubeGeometry = useMemo(() => {
    return new THREE.TubeGeometry(curve, 64, 0.03, 8, false)
  }, [curve])

  return (
    <mesh ref={tubeRef} geometry={tubeGeometry}>
      <meshStandardMaterial 
        color={color}
        emissive={color}
        emissiveIntensity={0.2 + energyLevel * 0.3}
        transparent
        opacity={0.8}
      />
    </mesh>
  )
}

// Main DNA Helix component
function DNAHelix3D({ 
  height = 4, 
  radius = 0.6, 
  turns = 2, 
  baseCount = 20,
  evolutionStage = 1,
  ceLevel = 0.5,
  qsLevel = 0.5,
  isAnimated = true
}: DNAHelixProps) {
  const groupRef = useRef<THREE.Group>(null)
  
  useFrame((state) => {
    if (groupRef.current && isAnimated) {
      groupRef.current.rotation.y = state.clock.elapsedTime * 0.3
    }
  })

  // Generate helix points and base pairs
  const { leftBackbone, rightBackbone, basePairs } = useMemo(() => {
    const leftPoints: Vector3[] = []
    const rightPoints: Vector3[] = []
    const pairs: Array<{
      position: [number, number, number]
      rotation: number
      baseType: 'AT' | 'GC' | 'NANO' | 'QUANTUM'
    }> = []

    for (let i = 0; i < baseCount; i++) {
      const t = i / (baseCount - 1)
      const y = (t - 0.5) * height
      const angle = t * turns * Math.PI * 2
      
      // Left strand
      const leftX = Math.cos(angle) * radius
      const leftZ = Math.sin(angle) * radius
      leftPoints.push(new Vector3(leftX, y, leftZ))
      
      // Right strand (opposite side)
      const rightX = Math.cos(angle + Math.PI) * radius
      const rightZ = Math.sin(angle + Math.PI) * radius
      rightPoints.push(new Vector3(rightX, y, rightZ))
      
      // Base pair
      const centerX = (leftX + rightX) / 2
      const centerZ = (leftZ + rightZ) / 2
      
      // Determine base type based on evolution stage and position
      let baseType: 'AT' | 'GC' | 'NANO' | 'QUANTUM'
      if (evolutionStage >= 8) {
        baseType = i % 4 === 0 ? 'QUANTUM' : i % 3 === 0 ? 'NANO' : i % 2 === 0 ? 'GC' : 'AT'
      } else if (evolutionStage >= 5) {
        baseType = i % 3 === 0 ? 'NANO' : i % 2 === 0 ? 'GC' : 'AT'
      } else {
        baseType = i % 2 === 0 ? 'AT' : 'GC'
      }
      
      pairs.push({
        position: [centerX, y, centerZ],
        rotation: angle,
        baseType
      })
    }
    
    return { leftBackbone: leftPoints, rightBackbone: rightPoints, basePairs: pairs }
  }, [height, radius, turns, baseCount, evolutionStage])

  // Colors based on evolution stage
  const leftColor = useMemo(() => {
    return new Color(0x22D3EE).lerp(new Color(0x8B5CF6), evolutionStage / 10)
  }, [evolutionStage])
  
  const rightColor = useMemo(() => {
    return new Color(0xF59E0B).lerp(new Color(0xFFD700), evolutionStage / 10)
  }, [evolutionStage])

  return (
    <group ref={groupRef} rotation={[0, 0, -Math.PI / 5]}>
      {/* Left backbone */}
      <DNABackbone
        points={leftBackbone}
        color={leftColor}
        evolutionStage={evolutionStage}
        energyLevel={ceLevel}
      />

      {/* Right backbone */}
      <DNABackbone
        points={rightBackbone}
        color={rightColor}
        evolutionStage={evolutionStage}
        energyLevel={qsLevel}
      />

      {/* Base pairs */}
      {basePairs.map((pair, index) => (
        <BasePair
          key={index}
          position={pair.position}
          rotation={pair.rotation}
          baseType={pair.baseType}
          evolutionStage={evolutionStage}
          ceLevel={ceLevel}
          qsLevel={qsLevel}
        />
      ))}

      {/* Energy field effect */}
      {/* <Sphere args={[radius * 1.5, 32, 32]} position={[0, 0, 0]}>
        <meshStandardMaterial
          color={new Color().lerpColors(leftColor, rightColor, 0.5)}
          transparent
          opacity={0.05 + (ceLevel + qsLevel) * 0.1}
          emissive={new Color().lerpColors(leftColor, rightColor, 0.5)}
          emissiveIntensity={0.1 + (ceLevel + qsLevel) * 0.2}
        />
      </Sphere> */}
    </group>
  )
}

// Wrapper component for the Canvas
export default function DNA3DHelix({
  height = 4,
  radius = 0.6,
  turns = 2,
  baseCount = 20,
  evolutionStage = 1,
  ceLevel = 0.5,
  qsLevel = 0.5,
  isAnimated = true,
  className = ""
}: DNAHelixProps & { className?: string }) {
  // Add error boundary and fallback
  try {
    return (
      <div className={`w-full h-full ${className}`}>
        <Canvas
          camera={{ position: [3, 0, 3], fov: 50 }}
          onCreated={({ gl }) => {
            gl.setClearColor('#000000', 0)
          }}
        >
          <ambientLight intensity={0.3} />
          <pointLight position={[10, 10, 10]} intensity={0.5} />
          <pointLight position={[-10, -10, -10]} intensity={0.3} color="#8B5CF6" />
          <pointLight position={[0, 10, 0]} intensity={0.4} color="#22D3EE" />

          <DNAHelix3D
            height={height}
            radius={radius}
            turns={turns}
            baseCount={baseCount}
            evolutionStage={evolutionStage}
            ceLevel={ceLevel}
            qsLevel={qsLevel}
            isAnimated={isAnimated}
          />
        </Canvas>
      </div>
    )
  } catch (error) {
    console.error('DNA3DHelix error:', error)
    // Fallback 2D visualization
    return (
      <div className={`w-full h-full flex items-center justify-center ${className}`}>
        <div className="text-center">
          <div className="text-cyan-400 text-lg font-bold mb-2">🧬 DNA Structure</div>
          <div className="text-white/60 text-sm">3D visualization loading...</div>
          <div className="text-xs text-white/40 mt-2">Stage {evolutionStage} | CE: {Math.round(ceLevel * 100)}% | QS: {Math.round(qsLevel * 100)}%</div>
        </div>
      </div>
    )
  }
}
