"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { 
  ChevronRight, 
  Home, 
  Globe, 
  Building, 
  Users, 
  Sparkles,
  Map,
  Calendar,
  Trophy,
  Heart,
  Brain,
  Hammer,
  Search
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

interface BreadcrumbItem {
  id: string
  label: string
  icon?: React.ComponentType<{ className?: string }>
  path?: string
  isActive?: boolean
  badge?: string | number
}

interface EpochTownsBreadcrumbProps {
  items: BreadcrumbItem[]
  onNavigate?: (path: string) => void
  showTownInfo?: boolean
  townData?: {
    generation: string
    citizens: number
    level: number
    dna: string
    stats: {
      empathy: number
      wisdom: number
      builder: number
      curiosity: number
    }
  }
  className?: string
}

export function EpochTownsBreadcrumb({
  items,
  onNavigate,
  showTownInfo = false,
  townData,
  className = ""
}: EpochTownsBreadcrumbProps) {
  const handleItemClick = (item: BreadcrumbItem) => {
    if (item.path && onNavigate) {
      onNavigate(item.path)
    }
  }

  const getStatIcon = (statName: string) => {
    switch (statName) {
      case 'empathy': return Heart
      case 'wisdom': return Brain
      case 'builder': return Hammer
      case 'curiosity': return Search
      default: return Sparkles
    }
  }

  const getStatColor = (statName: string) => {
    switch (statName) {
      case 'empathy': return 'text-red-400'
      case 'wisdom': return 'text-blue-400'
      case 'builder': return 'text-yellow-400'
      case 'curiosity': return 'text-green-400'
      default: return 'text-gray-400'
    }
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Breadcrumb */}
      <nav className="flex items-center space-x-2 text-sm">
        {items.map((item, index) => (
          <React.Fragment key={item.id}>
            <motion.div
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center"
            >
              {item.isActive ? (
                <div className="flex items-center gap-2 px-3 py-2 bg-cyan-600/20 border border-cyan-600/30 rounded-lg">
                  {item.icon && <item.icon className="w-4 h-4 text-cyan-400" />}
                  <span className="font-medium text-cyan-400">{item.label}</span>
                  {item.badge && (
                    <Badge variant="outline" className="text-xs border-cyan-400 text-cyan-400">
                      {item.badge}
                    </Badge>
                  )}
                </div>
              ) : (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleItemClick(item)}
                  className="flex items-center gap-2 text-gray-400 hover:text-white hover:bg-gray-800"
                  disabled={!item.path}
                >
                  {item.icon && <item.icon className="w-4 h-4" />}
                  <span>{item.label}</span>
                  {item.badge && (
                    <Badge variant="outline" className="text-xs">
                      {item.badge}
                    </Badge>
                  )}
                </Button>
              )}
            </motion.div>
            
            {index < items.length - 1 && (
              <ChevronRight className="w-4 h-4 text-gray-500" />
            )}
          </React.Fragment>
        ))}
      </nav>

      {/* Town Info Bar */}
      {showTownInfo && townData && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between p-4 bg-gray-800/50 border border-gray-700 rounded-lg"
        >
          <div className="flex items-center gap-6">
            {/* Generation Info */}
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-cyan-400" />
              <div>
                <p className="text-xs text-gray-400">Generation</p>
                <p className="text-sm font-medium text-white">{townData.generation}</p>
              </div>
            </div>

            {/* Citizens Count */}
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4 text-green-400" />
              <div>
                <p className="text-xs text-gray-400">Citizens</p>
                <p className="text-sm font-medium text-white">{townData.citizens}</p>
              </div>
            </div>

            {/* Town Level */}
            <div className="flex items-center gap-2">
              <Trophy className="w-4 h-4 text-yellow-400" />
              <div>
                <p className="text-xs text-gray-400">Level</p>
                <p className="text-sm font-medium text-white">{townData.level}</p>
              </div>
            </div>

            {/* DNA String */}
            <div className="flex items-center gap-2">
              <Sparkles className="w-4 h-4 text-purple-400" />
              <div>
                <p className="text-xs text-gray-400">DNA</p>
                <p className="text-sm font-medium text-purple-400 font-mono">{townData.dna}</p>
              </div>
            </div>
          </div>

          {/* Town Stats */}
          <div className="flex items-center gap-4">
            {Object.entries(townData.stats).map(([statName, value]) => {
              const Icon = getStatIcon(statName)
              const colorClass = getStatColor(statName)
              
              return (
                <div key={statName} className="flex items-center gap-1">
                  <Icon className={`w-4 h-4 ${colorClass}`} />
                  <span className="text-sm font-medium text-white">{value}%</span>
                </div>
              )
            })}
          </div>
        </motion.div>
      )}
    </div>
  )
}

// Predefined breadcrumb configurations for common Epoch Towns pages
export const EPOCH_TOWNS_BREADCRUMBS = {
  dashboard: [
    { id: 'home', label: 'Dashboard', icon: Home, path: '/dashboard' },
    { id: 'epochtowns', label: 'Epoch Towns', icon: Globe, isActive: true }
  ],
  
  townOverview: [
    { id: 'home', label: 'Dashboard', icon: Home, path: '/dashboard' },
    { id: 'epochtowns', label: 'Epoch Towns', icon: Globe, path: '/dashboard?section=epochtowns' },
    { id: 'overview', label: 'Overview', icon: Map, isActive: true }
  ],
  
  townWorld: [
    { id: 'home', label: 'Dashboard', icon: Home, path: '/dashboard' },
    { id: 'epochtowns', label: 'Epoch Towns', icon: Globe, path: '/dashboard?section=epochtowns' },
    { id: 'world', label: '3D World', icon: Building, isActive: true }
  ],
  
  townExplore: [
    { id: 'home', label: 'Dashboard', icon: Home, path: '/dashboard' },
    { id: 'epochtowns', label: 'Epoch Towns', icon: Globe, path: '/dashboard?section=epochtowns' },
    { id: 'explore', label: 'Explore Towns', icon: Map, isActive: true }
  ],
  
  fullEpochTowns: [
    { id: 'home', label: 'Dashboard', icon: Home, path: '/dashboard' },
    { id: 'epochtowns', label: 'Epoch Towns', icon: Globe, path: '/dashboard?section=epochtowns' },
    { id: 'full', label: 'Full Experience', icon: Sparkles, isActive: true }
  ],
  
  socialHub: [
    { id: 'home', label: 'Dashboard', icon: Home, path: '/dashboard' },
    { id: 'epochtowns', label: 'Epoch Towns', icon: Globe, path: '/dashboard?section=epochtowns' },
    { id: 'social', label: 'Social Hub', icon: Users, isActive: true }
  ]
}

export default EpochTownsBreadcrumb
