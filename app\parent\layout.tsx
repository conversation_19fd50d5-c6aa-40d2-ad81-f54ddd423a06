"use client"

import React, { useState } from "react"

// Force dynamic rendering for parent dashboard
export const dynamic = 'force-dynamic'
import { motion } from "framer-motion"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/AuthContext"
import { AuthProvider } from "@/contexts/AuthContext"
import { ParentSidebar } from "./components/ParentSidebar"
import { ParentTopNav } from "./components/ParentTopNav"
import { ParentBackground } from "./components/ParentBackground"
// import { ProtectedRoute } from "@/components/auth/ProtectedRoute" // Disabled for development

interface ParentDashboardLayoutProps {
  children: React.ReactNode
}

function ParentDashboardLayoutContent({ children }: ParentDashboardLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const { user: _user, userProfile: _userProfile, loading: _loading } = useAuth()
  const _router = useRouter()

  // DEVELOPMENT: Removed role-based redirections for testing
  // TODO: Re-enable authentication checks for production

  // // Redirect non-parent users
  // useEffect(() => {
  //   if (!loading && user && userProfile) {
  //     // Check if user is a parent, if not redirect to appropriate dashboard
  //     if (userProfile.account_type !== 'parent') {
  //       if (userProfile.account_type === 'student') {
  //         router.push('/dashboard')
  //       } else {
  //         router.push('/login')
  //       }
  //     }
  //   }
  // }, [user, userProfile, loading, router])

  // // Show loading while checking user role
  // if (loading || (user && userProfile?.account_type !== 'parent')) {
  //   return (
  //     <div className="min-h-screen bg-gradient-to-br from-blue-900 via-black to-green-900 flex items-center justify-center">
  //       <motion.div
  //         initial={{ opacity: 0, scale: 0.8 }}
  //         animate={{ opacity: 1, scale: 1 }}
  //         className="flex flex-col items-center gap-4 text-white"
  //       >
  //         <div className="w-8 h-8 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />
  //         <span className="text-lg font-space-grotesk">Verifying parent access...</span>
  //       </motion.div>
  //     </div>
  //   )
  // }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-black to-green-900 relative overflow-hidden">
      {/* Parent Sidebar Navigation */}
      <ParentSidebar onCollapseChange={setSidebarCollapsed} />

      {/* Parent Top Navigation */}
      <ParentTopNav sidebarCollapsed={sidebarCollapsed} />

      {/* Parent Background */}
      <ParentBackground />

      {/* Main Content Area */}
      <div
        className="relative z-20 transition-all duration-300"
        style={{
          marginLeft: sidebarCollapsed ? '80px' : '280px',
          marginTop: '64px',
          height: 'calc(100vh - 64px)',
          width: sidebarCollapsed ? 'calc(100% - 80px)' : 'calc(100% - 280px)'
        }}
      >
        <div className="h-full overflow-y-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="p-6"
          >
            {children}
          </motion.div>
        </div>
      </div>
    </div>
  )
}

export default function ParentDashboardLayout({ children }: ParentDashboardLayoutProps) {
  return (
    <AuthProvider>
      {/* DEVELOPMENT: Removed ProtectedRoute for testing */}
      {/* TODO: Re-enable ProtectedRoute for production */}
      {/* <ProtectedRoute redirectTo="/login"> */}
        <ParentDashboardLayoutContent>
          {children}
        </ParentDashboardLayoutContent>
      {/* </ProtectedRoute> */}
    </AuthProvider>
  )
}
