// Content filtering and moderation utilities

export interface ContentAnalysis {
  isClean: boolean
  flags: string[]
  severity: 'low' | 'medium' | 'high' | 'critical'
  confidence: number
  suggestions: string[]
}

export interface ModerationRule {
  id: string
  name: string
  pattern: RegExp | string
  severity: 'low' | 'medium' | 'high' | 'critical'
  action: 'flag' | 'block' | 'review'
  description: string
}

// Profanity and inappropriate content patterns
const _PROFANITY_PATTERNS = [
  // Basic profanity (using mild examples for demonstration)
  /\b(damn|hell|crap)\b/gi,
  // Add more patterns as needed
]

// Spam detection patterns
const _SPAM_PATTERNS = [
  /(.)\1{4,}/g, // Repeated characters (aaaaa)
  /[A-Z]{5,}/g, // Excessive caps
  /(https?:\/\/[^\s]+){3,}/g, // Multiple links
  /\b(buy now|click here|limited time|act fast)\b/gi, // Spam phrases
  /(.{1,20})\1{2,}/g, // Repeated phrases
]

// Harassment and bullying patterns
const _HARASSMENT_PATTERNS = [
  /\b(kill yourself|kys|die|hate you)\b/gi,
  /\b(stupid|idiot|loser|worthless)\b/gi,
  /\b(shut up|go away|nobody likes you)\b/gi,
]

// Suspicious link patterns
const _SUSPICIOUS_LINK_PATTERNS = [
  /bit\.ly|tinyurl|t\.co/gi, // Shortened URLs
  /\.(tk|ml|ga|cf)\b/gi, // Suspicious TLDs
  /[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}/g, // IP addresses
]

// Personal information patterns
const _PII_PATTERNS = [
  /\b\d{3}-\d{2}-\d{4}\b/g, // SSN format
  /\b\d{3}-\d{3}-\d{4}\b/g, // Phone number
  /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, // Email
  /\b\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\b/g, // Credit card format
]

// Moderation rules configuration
export const MODERATION_RULES: ModerationRule[] = [
  {
    id: 'profanity',
    name: 'Profanity Detection',
    pattern: /\b(damn|hell|crap|stupid|idiot)\b/gi,
    severity: 'medium',
    action: 'flag',
    description: 'Detects inappropriate language and profanity'
  },
  {
    id: 'excessive_caps',
    name: 'Excessive Capitals',
    pattern: /[A-Z]{8,}/g,
    severity: 'low',
    action: 'flag',
    description: 'Detects excessive use of capital letters'
  },
  {
    id: 'repeated_chars',
    name: 'Repeated Characters',
    pattern: /(.)\1{6,}/g,
    severity: 'low',
    action: 'flag',
    description: 'Detects spam-like repeated characters'
  },
  {
    id: 'multiple_links',
    name: 'Multiple Links',
    pattern: /(https?:\/\/[^\s]+.*){3,}/g,
    severity: 'high',
    action: 'review',
    description: 'Detects content with multiple external links'
  },
  {
    id: 'harassment',
    name: 'Harassment Language',
    pattern: /\b(kill yourself|kys|hate you|worthless|nobody likes you)\b/gi,
    severity: 'critical',
    action: 'block',
    description: 'Detects harassment and bullying language'
  },
  {
    id: 'personal_info',
    name: 'Personal Information',
    pattern: /\b\d{3}-\d{2}-\d{4}\b|\b\d{3}-\d{3}-\d{4}\b/g,
    severity: 'high',
    action: 'block',
    description: 'Detects potential personal information sharing'
  },
  {
    id: 'suspicious_links',
    name: 'Suspicious Links',
    pattern: /bit\.ly|tinyurl|t\.co|\.(tk|ml|ga|cf)\b/gi,
    severity: 'medium',
    action: 'review',
    description: 'Detects potentially suspicious or shortened links'
  },
  {
    id: 'spam_phrases',
    name: 'Spam Phrases',
    pattern: /\b(buy now|click here|limited time|act fast|make money|work from home)\b/gi,
    severity: 'medium',
    action: 'flag',
    description: 'Detects common spam and promotional phrases'
  }
]

/**
 * Analyzes content for potential issues using moderation rules
 */
export function analyzeContent(content: string): ContentAnalysis {
  const flags: string[] = []
  let maxSeverity: 'low' | 'medium' | 'high' | 'critical' = 'low'
  const suggestions: string[] = []
  let totalMatches = 0

  // Apply each moderation rule
  for (const rule of MODERATION_RULES) {
    const matches = content.match(rule.pattern)
    if (matches && matches.length > 0) {
      flags.push(rule.id)
      totalMatches += matches.length

      // Update max severity
      const severityLevels = { low: 1, medium: 2, high: 3, critical: 4 }
      if (severityLevels[rule.severity] > severityLevels[maxSeverity]) {
        maxSeverity = rule.severity
      }

      // Add suggestions based on rule type
      switch (rule.id) {
        case 'profanity':
          suggestions.push('Consider using more appropriate language')
          break
        case 'excessive_caps':
          suggestions.push('Avoid using excessive capital letters')
          break
        case 'repeated_chars':
          suggestions.push('Remove repeated characters for better readability')
          break
        case 'multiple_links':
          suggestions.push('Limit the number of external links')
          break
        case 'harassment':
          suggestions.push('This content may be harmful to others')
          break
        case 'personal_info':
          suggestions.push('Avoid sharing personal information publicly')
          break
        case 'suspicious_links':
          suggestions.push('Use trusted, full URLs instead of shortened links')
          break
        case 'spam_phrases':
          suggestions.push('Focus on providing value rather than promotional content')
          break
      }
    }
  }

  // Calculate confidence based on number of matches and content length
  const contentLength = content.length
  const matchDensity = totalMatches / Math.max(contentLength / 100, 1)
  const confidence = Math.min(matchDensity * 0.3 + (flags.length * 0.2), 1)

  return {
    isClean: flags.length === 0,
    flags,
    severity: maxSeverity,
    confidence,
    suggestions: [...new Set(suggestions)] // Remove duplicates
  }
}

/**
 * Checks if content should be auto-blocked
 */
export function shouldAutoBlock(analysis: ContentAnalysis): boolean {
  return analysis.severity === 'critical' || 
         (analysis.severity === 'high' && analysis.confidence > 0.7)
}

/**
 * Checks if content should be flagged for review
 */
export function shouldFlagForReview(analysis: ContentAnalysis): boolean {
  return analysis.severity === 'high' || 
         (analysis.severity === 'medium' && analysis.confidence > 0.5) ||
         analysis.flags.includes('multiple_links') ||
         analysis.flags.includes('suspicious_links')
}

/**
 * Sanitizes content by removing or replacing problematic parts
 */
export function sanitizeContent(content: string): string {
  let sanitized = content

  // Remove excessive caps (convert to normal case)
  sanitized = sanitized.replace(/[A-Z]{8,}/g, (match) => 
    match.charAt(0) + match.slice(1).toLowerCase()
  )

  // Remove excessive repeated characters
  sanitized = sanitized.replace(/(.)\1{6,}/g, '$1$1$1')

  // Remove suspicious patterns but keep the content readable
  sanitized = sanitized.replace(/\b(buy now|click here|act fast)\b/gi, '[promotional content]')

  return sanitized
}

/**
 * Generates a moderation priority score
 */
export function getModerationPriority(analysis: ContentAnalysis): 'low' | 'medium' | 'high' | 'urgent' {
  if (analysis.severity === 'critical') return 'urgent'
  if (analysis.severity === 'high') return 'high'
  if (analysis.severity === 'medium' && analysis.confidence > 0.7) return 'high'
  if (analysis.severity === 'medium') return 'medium'
  return 'low'
}

/**
 * Checks username for appropriateness
 */
export function validateUsername(username: string): ContentAnalysis {
  // Apply basic content analysis to username
  const analysis = analyzeContent(username)
  
  // Additional username-specific checks
  const additionalFlags: string[] = []
  
  // Check for admin-like names
  if (/\b(admin|moderator|support|official)\b/gi.test(username)) {
    additionalFlags.push('impersonation_risk')
  }
  
  // Check for excessive numbers or special characters
  if (/[0-9]{5,}/.test(username) || /[^a-zA-Z0-9_-]{3,}/.test(username)) {
    additionalFlags.push('suspicious_format')
  }

  return {
    ...analysis,
    flags: [...analysis.flags, ...additionalFlags]
  }
}

/**
 * Batch analyze multiple content items
 */
export function batchAnalyzeContent(contents: string[]): ContentAnalysis[] {
  return contents.map(content => analyzeContent(content))
}

/**
 * Get moderation statistics for a set of analyses
 */
export function getModerationStats(analyses: ContentAnalysis[]) {
  const stats = {
    total: analyses.length,
    clean: 0,
    flagged: 0,
    blocked: 0,
    needsReview: 0,
    severityBreakdown: {
      low: 0,
      medium: 0,
      high: 0,
      critical: 0
    }
  }

  analyses.forEach(analysis => {
    if (analysis.isClean) {
      stats.clean++
    } else {
      stats.flagged++
      if (shouldAutoBlock(analysis)) {
        stats.blocked++
      } else if (shouldFlagForReview(analysis)) {
        stats.needsReview++
      }
    }
    stats.severityBreakdown[analysis.severity]++
  })

  return stats
}
