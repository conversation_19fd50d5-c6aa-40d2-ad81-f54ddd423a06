'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { MenuItem, EvolutionTheme } from './types'
import { getBottomMenuItemStyle, getIconStyle } from './styles'

interface SidebarBottomMenuProps {
  bottomMenuItems: MenuItem[]
  activeItem: string
  isCollapsed: boolean
  evolutionTheme: EvolutionTheme
  onItemClick: (item: MenuItem) => void
}

export function SidebarBottomMenu({
  bottomMenuItems,
  activeItem,
  isCollapsed,
  evolutionTheme,
  onItemClick
}: SidebarBottomMenuProps) {
  if (bottomMenuItems.length === 0) {
    return null
  }

  return (
    <div
      className="border-t-2 p-4 space-y-3 relative overflow-hidden"
      style={{
        borderTopColor: `${evolutionTheme.primary}30`,
        background: 'linear-gradient(135deg, rgba(34, 211, 238, 0.03) 0%, rgba(139, 92, 246, 0.03) 100%)'
      }}
    >
      {bottomMenuItems.map((item) => (
        <motion.button
          key={item.id}
          style={getBottomMenuItemStyle(
            activeItem === item.id,
            item.id === 'logout'
          )}
          onClick={() => onItemClick(item)}
          whileHover={{
            scale: 1.02,
            boxShadow: activeItem === item.id
              ? "0 8px 32px rgba(34, 211, 238, 0.3)"
              : item.id === 'logout'
              ? "0 4px 20px rgba(239, 68, 68, 0.2)"
              : "0 4px 20px rgba(255, 255, 255, 0.1)"
          }}
          whileTap={{ scale: 0.98 }}
          onMouseEnter={(e) => {
            const isActive = activeItem === item.id;
            const isLogout = item.id === 'logout';
            Object.assign(e.currentTarget.style, getBottomMenuItemStyle(isActive, isLogout, true));
          }}
          onMouseLeave={(e) => {
            const isActive = activeItem === item.id;
            const isLogout = item.id === 'logout';
            Object.assign(e.currentTarget.style, getBottomMenuItemStyle(isActive, isLogout, false));
          }}
        >
          {/* Enhanced Bottom Menu Quantum Effects */}
          <div className="absolute inset-0 pointer-events-none">
            {/* Quantum sweep effect */}
            <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
              <div
                className="absolute inset-0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"
                style={{
                  background: item.id === 'logout'
                    ? 'linear-gradient(90deg, transparent, rgba(239, 68, 68, 0.3), transparent)'
                    : `linear-gradient(90deg, transparent, ${evolutionTheme.primary}30, transparent)`
                }}
              />
            </div>

            {/* Active quantum field */}
            {activeItem === item.id && (
              <motion.div
                className="absolute inset-0"
                style={{
                  background: `linear-gradient(45deg, ${evolutionTheme.primary}08, ${evolutionTheme.secondary}08, ${evolutionTheme.primary}08)`
                }}
                animate={{
                  opacity: [0.3, 0.6, 0.3]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
            )}
          </div>

          <item.icon
            style={getIconStyle(
              activeItem === item.id,
              item.id === 'logout',
              false,
              evolutionTheme
            )}
          />
          
          <AnimatePresence>
            {!isCollapsed && (
              <motion.span
                initial={{ opacity: 0, width: 0 }}
                animate={{ opacity: 1, width: 'auto' }}
                exit={{ opacity: 0, width: 0 }}
                style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  position: 'relative',
                  zIndex: 10,
                  transition: 'all 0.3s ease',
                }}
              >
                {item.label}
              </motion.span>
            )}
          </AnimatePresence>
        </motion.button>
      ))}
    </div>
  )
}
