'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Dna,
  TrendingUp,
  Activity,
  Star,
  Atom,
  Eye,
  Target,
  Map,
  Users,
  Compass,
  BookOpen,
  Focus,
  MessageCircle,
  BarChart3,
  Globe,
  Layers,
  Lightbulb,
  Circle
} from 'lucide-react'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useDashboardStore, usePlayerData, useSystemStatus, useTimelineData } from '../store/dashboardStore'
import DNA3DHelix from './DNA3DHelix'
import DNASequenceVisualizer from './DNASequenceVisualizer'

interface DNACoreDashboardProps {
  className?: string
}

// Enhanced node types for the NanoGenesis system
interface NanoNode {
  id: string
  type: 'learning' | 'echo' | 'meditation' | 'timelinegen'
  title: string
  description: string
  status: 'active' | 'dormant' | 'completed'
  position: { x: number; y: number; z: number }
  color: string
  participants: number
  lastActivity: string
  completionRate: number
  rewards: { ce: number; qs: number }
}

// Timeline evolution stages
type EvolutionStage = 'dormant' | 'awakening' | 'harmonic' | 'syntropic' | 'ascended'

export default function DNACoreDashboard({ className }: DNACoreDashboardProps) {
  const { setActivePanel: _setActivePanel, setViewMode: _setViewMode, setSelectedNode: _setSelectedNode } = useDashboardStore()
  const playerData = usePlayerData()
  const _timelineData = useTimelineData()
  const systemStatus = useSystemStatus()

  const [_animationTrigger, setAnimationTrigger] = useState(0)
  const [selectedNode, setSelectedNodeLocal] = useState<NanoNode | null>(null)
  const [viewMode, setViewModeLocal] = useState<'explore' | 'focus' | 'assist' | 'syntropy'>('explore')
  const [dnaFragment, setDnaFragment] = useState('NANO-ARCH-EVOL-SYNC')
  const [_timelineStage, _setTimelineStage] = useState<EvolutionStage>('awakening')
  const _canvasRef = useRef<HTMLCanvasElement>(null)

  // Trigger animations periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setAnimationTrigger(prev => prev + 1)
    }, 3000)
    return () => clearInterval(interval)
  }, [])

  // Generate dynamic DNA fragment based on player data
  useEffect(() => {
    const generateDNAFragment = () => {
      const segments = [
        ['NANO', 'ARCH', 'EVOL', 'SYNC'],
        ['QNTM', 'FLUX', 'WAVE', 'CORE'],
        ['MIND', 'SOUL', 'BODY', 'SPRT'],
        ['LOVE', 'WISE', 'POWR', 'UNIT']
      ]
      const level = playerData?.level || 1
      const selectedSegments = segments.map((segment, index) =>
        segment[Math.min(Math.floor(level / 5) + index, segment.length - 1)]
      )
      setDnaFragment(selectedSegments.join('-'))
    }
    generateDNAFragment()
  }, [playerData?.level])

  // Evolution stage color theme based on player level
  const getEvolutionTheme = () => {
    const stage = playerData?.level || 1
    if (stage <= 2) return {
      primary: '#22D3EE', // Neural cyan
      secondary: '#8B5CF6', // Quantum purple
      glow: 'rgba(34, 211, 238, 0.3)',
      gradient: 'from-cyan-500 to-purple-600'
    }
    if (stage <= 5) return {
      primary: '#8B5CF6', // Quantum purple
      secondary: '#F59E0B', // Flame orange
      glow: 'rgba(139, 92, 246, 0.3)',
      gradient: 'from-purple-500 to-orange-500'
    }
    if (stage <= 8) return {
      primary: '#F59E0B', // Flame orange
      secondary: '#EF4444', // Red
      glow: 'rgba(245, 158, 11, 0.3)',
      gradient: 'from-orange-500 to-red-500'
    }
    return {
      primary: '#FFD700', // Gold
      secondary: '#FFFFFF', // White
      glow: 'rgba(255, 215, 0, 0.4)',
      gradient: 'from-yellow-400 to-white'
    }
  }

  const evolutionTheme = getEvolutionTheme()

  // Mock nano nodes data for the enhanced system
  const nanoNodes: NanoNode[] = [
    {
      id: 'learning-1',
      type: 'learning',
      title: 'Neural Pathways',
      description: 'Enhance cognitive connections',
      status: 'active',
      position: { x: 100, y: 150, z: 0 },
      color: '#22D3EE',
      participants: 12,
      lastActivity: '2 min ago',
      completionRate: 85,
      rewards: { ce: 50, qs: 25 }
    },
    {
      id: 'echo-1',
      type: 'echo',
      title: 'Wisdom Echo',
      description: 'Share insights with community',
      status: 'active',
      position: { x: 250, y: 100, z: 0 },
      color: '#8B5CF6',
      participants: 8,
      lastActivity: '5 min ago',
      completionRate: 92,
      rewards: { ce: 30, qs: 40 }
    },
    {
      id: 'meditation-1',
      type: 'meditation',
      title: 'Quantum Balance',
      description: 'Stabilize consciousness energy',
      status: 'dormant',
      position: { x: 180, y: 220, z: 0 },
      color: '#F59E0B',
      participants: 15,
      lastActivity: '1 hour ago',
      completionRate: 67,
      rewards: { ce: 20, qs: 60 }
    },
    {
      id: 'timelinegen-1',
      type: 'timelinegen',
      title: 'Matter Synthesis',
      description: 'Create quantum constructs',
      status: 'active',
      position: { x: 320, y: 180, z: 0 },
      color: '#EF4444',
      participants: 6,
      lastActivity: '10 min ago',
      completionRate: 78,
      rewards: { ce: 75, qs: 35 }
    }
  ]

  // Timeline evolution progress
  const getTimelineProgress = () => {
    const stages: EvolutionStage[] = ['dormant', 'awakening', 'harmonic', 'syntropic', 'ascended']
    const currentIndex = stages.indexOf(_timelineStage)
    return {
      current: _timelineStage,
      progress: ((currentIndex + 1) / stages.length) * 100,
      nextStage: stages[currentIndex + 1] || 'ascended'
    }
  }

  return (
    <div className={`p-6 space-y-6 ${className}`}>
      {/* Enhanced Header with DNA Fragment Display */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative"
      >
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <motion.div
              className="relative p-4 rounded-xl bg-gradient-to-br from-space-dark/80 to-space-dark/60 border-2"
              style={{
                borderColor: evolutionTheme.primary + '40',
                boxShadow: `0 0 20px ${evolutionTheme.glow}`
              }}
              animate={{
                boxShadow: [
                  `0 0 20px ${evolutionTheme.glow}`,
                  `0 0 30px ${evolutionTheme.glow}`,
                  `0 0 20px ${evolutionTheme.glow}`
                ]
              }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <Dna className="w-8 h-8" style={{ color: evolutionTheme.primary }} />
              {/* Dynamic particle effects */}
              <div className="absolute inset-0 pointer-events-none">
                {[...Array(6)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-1 h-1 rounded-full"
                    style={{ backgroundColor: evolutionTheme.secondary }}
                    animate={{
                      x: [0, 20, -20, 0],
                      y: [0, -15, 15, 0],
                      opacity: [0.3, 1, 0.3]
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      delay: i * 0.5
                    }}
                  />
                ))}
              </div>
            </motion.div>
            <div>
              <h1 className="text-3xl font-bold text-white font-orbitron">NanoCore</h1>
              <p className="text-white/60">Mission Control & Consciousness Mirror</p>
            </div>
          </div>

          <div className="text-right space-y-2">
            <div className="text-sm text-white/60">Evolution Stage</div>
            <div className="text-2xl font-bold" style={{ color: evolutionTheme.primary }}>
              {playerData?.level || 1}/20
            </div>
            <Badge className="bg-gradient-to-r from-cyan-500/20 to-purple-500/20 text-cyan-400 border-cyan-500/30">
              NanoArchitect
            </Badge>
          </div>
        </div>

        {/* Enhanced DNA Fragment Display with 3D Helix */}
        <Card className="bg-black/40 border-cyan-500/30 backdrop-blur-xl mb-6">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 3D DNA Helix Visualization */}
              <div className="relative">
                <h3 className="text-lg font-bold text-cyan-400 flex items-center gap-2 mb-4">
                  <Dna className="w-5 h-5" />
                  3D DNA Structure
                </h3>
                <div className="h-64 rounded-lg border border-cyan-500/30 overflow-hidden bg-gradient-to-br from-cyan-900/10 to-purple-900/10">
                  <DNA3DHelix
                    height={3}
                    radius={0.5}
                    turns={1.5}
                    baseCount={16}
                    evolutionStage={playerData?.level || 1}
                    ceLevel={(systemStatus?.consciousnessLevel || 0)}
                    qsLevel={(systemStatus?.systemHealth || 0)}
                    isAnimated={true}
                  />
                </div>
                <div className="absolute bottom-2 left-2 bg-black/70 px-2 py-1 rounded text-xs text-cyan-400">
                  Real-time CE/QS flux visualization
                </div>
              </div>

              {/* DNA Fragment Info */}
              <div className="space-y-4">
                <h3 className="text-lg font-bold text-cyan-400 flex items-center gap-2">
                  <Atom className="w-5 h-5" />
                  Your DNA Fragment
                </h3>

                <motion.div
                  className="font-mono text-2xl font-bold tracking-wider text-center p-4 bg-gradient-to-r from-cyan-500/10 to-purple-500/10 rounded-lg border border-cyan-500/30"
                  style={{ color: evolutionTheme.primary }}
                  animate={{ scale: [1, 1.05, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  {dnaFragment}
                </motion.div>

                <p className="text-sm text-white/60 text-center italic">
                  &ldquo;Your DNA evolves as you evolve others.&rdquo;
                </p>

                {/* Enhanced Metrics Display */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-3 bg-cyan-500/10 border border-cyan-500/30 rounded-lg text-center">
                    <div className="text-2xl font-bold text-cyan-400">
                      {Math.round((systemStatus?.consciousnessLevel || 0) * 100)}
                    </div>
                    <div className="text-xs text-cyan-400/80">Consciousness Energy</div>
                    <div className="w-full bg-gray-700 rounded-full h-1 mt-2">
                      <motion.div
                        className="bg-cyan-400 h-1 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${Math.round((systemStatus?.consciousnessLevel || 0) * 100)}%` }}
                        transition={{ duration: 1.5 }}
                      />
                    </div>
                  </div>

                  <div className="p-3 bg-purple-500/10 border border-purple-500/30 rounded-lg text-center">
                    <div className="text-2xl font-bold text-purple-400">
                      {Math.round((systemStatus?.systemHealth || 0) * 100)}
                    </div>
                    <div className="text-xs text-purple-400/80">Quantum Stability</div>
                    <div className="w-full bg-gray-700 rounded-full h-1 mt-2">
                      <motion.div
                        className="bg-purple-400 h-1 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${Math.round((systemStatus?.systemHealth || 0) * 100)}%` }}
                        transition={{ duration: 1.5, delay: 0.3 }}
                      />
                    </div>
                  </div>
                </div>

                {/* Total Power Level */}
                <div className="p-4 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/30 rounded-lg text-center">
                  <div className="text-sm text-yellow-400/80 mb-1">Total Power Level</div>
                  <div className="text-3xl font-bold text-yellow-400">
                    {Math.round(((systemStatus?.consciousnessLevel || 0) + (systemStatus?.systemHealth || 0)) * 100)}
                  </div>
                  <div className="text-xs text-yellow-400/60 mt-1">
                    Evolution Stage {playerData?.level || 1}/20
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Main NanoGenesis Interface */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-7 bg-black/40 border border-gray-800/50">
          <TabsTrigger value="overview" className="data-[state=active]:bg-cyan-500/20 data-[state=active]:text-cyan-400">
            <Compass className="w-4 h-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="dna-analysis" className="data-[state=active]:bg-cyan-500/20 data-[state=active]:text-cyan-400">
            <Dna className="w-4 h-4 mr-2" />
            DNA Analysis
          </TabsTrigger>
          <TabsTrigger value="timeline-map" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400">
            <Map className="w-4 h-4 mr-2" />
            Timeline Map
          </TabsTrigger>
          <TabsTrigger value="consciousness" className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-400">
            <Users className="w-4 h-4 mr-2" />
            Consciousness Feed
          </TabsTrigger>
          <TabsTrigger value="quantum-tasks" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400">
            <Target className="w-4 h-4 mr-2" />
            Quantum Tasks
          </TabsTrigger>
          <TabsTrigger value="library" className="data-[state=active]:bg-yellow-500/20 data-[state=active]:text-yellow-400">
            <BookOpen className="w-4 h-4 mr-2" />
            Library
          </TabsTrigger>
          <TabsTrigger value="metrics" className="data-[state=active]:bg-pink-500/20 data-[state=active]:text-pink-400">
            <BarChart3 className="w-4 h-4 mr-2" />
            Metrics
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Timeline Evolution Bar */}
            <Card className="lg:col-span-3 bg-black/40 border-indigo-500/30 backdrop-blur-xl">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-indigo-400">
                  <Layers className="w-5 h-5" />
                  Timeline Evolution Progress
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-white/80">Current Stage: <span className="text-indigo-400 font-bold capitalize">{_timelineStage}</span></span>
                    <span className="text-white/60">Next: <span className="text-cyan-400 capitalize">{getTimelineProgress().nextStage}</span></span>
                  </div>
                  <div className="relative h-4 bg-space-dark/60 rounded-full overflow-hidden">
                    <motion.div
                      className="absolute inset-y-0 left-0 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: `${getTimelineProgress().progress}%` }}
                      transition={{ duration: 2, ease: 'easeOut' }}
                    />
                  </div>
                  <div className="flex justify-between text-xs text-white/60">
                    <span>Dormant</span>
                    <span>Awakening</span>
                    <span>Harmonic</span>
                    <span>Syntropic</span>
                    <span>Ascended</span>
                  </div>
                  <div className="text-center pt-4">
                    <Button className="bg-indigo-500/20 text-indigo-400 border border-indigo-500/30 hover:bg-indigo-500/30">
                      🤝 Assist Your Timeline
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* CE/QS Metrics */}
            <Card className="bg-black/40 border-cyan-500/30 backdrop-blur-xl">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-cyan-400">
                  <Activity className="w-5 h-5" />
                  Consciousness Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Consciousness Energy */}
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-white/80">Consciousness Energy</span>
                      <span className="font-bold text-cyan-400">{Math.round((systemStatus?.consciousnessLevel || 0) * 100)}</span>
                    </div>
                    <div className="relative h-3 bg-space-dark/60 rounded-full overflow-hidden">
                      <motion.div
                        className="absolute inset-y-0 left-0 bg-gradient-to-r from-cyan-500 to-cyan-400 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${Math.round((systemStatus?.consciousnessLevel || 0) * 100)}%` }}
                        transition={{ duration: 1.5, ease: 'easeOut' }}
                      />
                    </div>
                  </div>

                  {/* Quantum Stability */}
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-white/80">Quantum Stability</span>
                      <span className="font-bold text-purple-400">{Math.round((systemStatus?.systemHealth || 0) * 100)}</span>
                    </div>
                    <div className="relative h-3 bg-space-dark/60 rounded-full overflow-hidden">
                      <motion.div
                        className="absolute inset-y-0 left-0 bg-gradient-to-r from-purple-500 to-purple-400 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${Math.round((systemStatus?.systemHealth || 0) * 100)}%` }}
                        transition={{ duration: 1.5, ease: 'easeOut', delay: 0.3 }}
                      />
                    </div>
                  </div>

                  {/* Total Power */}
                  <div className="pt-4 border-t border-white/10">
                    <div className="flex justify-between items-center">
                      <span className="text-white/80">Total Power Level</span>
                      <span className="font-bold text-xl" style={{ color: evolutionTheme.primary }}>
                        {Math.round(((systemStatus?.consciousnessLevel || 0) + (systemStatus?.systemHealth || 0)) * 100)}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Avatar Evolution Preview with DNA */}
            <Card className="bg-black/40 border-yellow-500/30 backdrop-blur-xl">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-yellow-400">
                  <Star className="w-5 h-5" />
                  Avatar Evolution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Evolution DNA Helix */}
                  <div className="relative h-32 rounded-lg border border-yellow-500/30 overflow-hidden bg-gradient-to-br from-yellow-900/10 to-orange-900/10">
                    <DNA3DHelix
                      height={2}
                      radius={0.3}
                      turns={1}
                      baseCount={8}
                      evolutionStage={playerData?.level || 1}
                      ceLevel={0.65} // 65% progress to next level
                      qsLevel={(systemStatus?.systemHealth || 0)}
                      isAnimated={true}
                    />
                    <div className="absolute top-2 left-2 bg-black/70 px-2 py-1 rounded text-xs text-yellow-400">
                      Evolution DNA
                    </div>
                  </div>

                  {/* Evolution Progress */}
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-white/60">Level {playerData?.level || 1} → {(playerData?.level || 1) + 1}</span>
                      <span className="text-sm font-bold text-yellow-400">65%</span>
                    </div>
                    <div className="relative h-3 bg-space-dark/60 rounded-full overflow-hidden">
                      <motion.div
                        className={`absolute inset-y-0 left-0 bg-gradient-to-r ${evolutionTheme.gradient} rounded-full`}
                        initial={{ width: 0 }}
                        animate={{ width: '65%' }}
                        transition={{ duration: 2, ease: 'easeOut' }}
                      />
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
                    </div>
                  </div>

                  {/* Evolution Benefits */}
                  <div className="grid grid-cols-1 gap-2 text-xs">
                    <div className="p-2 bg-yellow-500/10 border border-yellow-500/30 rounded">
                      <div className="text-yellow-400 font-bold">Next Unlock:</div>
                      <div className="text-white/60">Enhanced Neural Pathways</div>
                    </div>
                    <div className="p-2 bg-orange-500/10 border border-orange-500/30 rounded">
                      <div className="text-orange-400 font-bold">New Ability:</div>
                      <div className="text-white/60">Quantum Meditation</div>
                    </div>
                  </div>

                  {/* Evolution Stats */}
                  <div className="grid grid-cols-2 gap-3 text-xs">
                    <div className="text-center">
                      <div className="text-white/60">DNA Complexity</div>
                      <div className="font-bold text-cyan-400">Level {playerData?.level || 1}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-white/60">Uniqueness</div>
                      <div className="font-bold text-purple-400">99.{97 - (playerData?.level || 1)}%</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Active Nodes Quick Access */}
            <Card className="bg-black/40 border-green-500/30 backdrop-blur-xl">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-400">
                  <Globe className="w-5 h-5" />
                  Active Nodes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {nanoNodes.filter(node => node.status === 'active').map((node, index) => (
                    <motion.div
                      key={node.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="p-3 rounded-lg bg-gray-800/30 border border-gray-700/50 hover:border-green-500/30 transition-colors cursor-pointer"
                      onClick={() => setSelectedNodeLocal(node)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: node.color }}
                          />
                          <span className="text-white text-sm font-medium">{node.title}</span>
                        </div>
                        <div className="flex items-center gap-2 text-xs">
                          <Users className="w-3 h-3 text-gray-400" />
                          <span className="text-gray-400">{node.participants}</span>
                        </div>
                      </div>
                      <div className="mt-2 text-xs text-gray-400">
                        {node.description}
                      </div>
                      <div className="mt-2 flex justify-between text-xs">
                        <span className="text-cyan-400">+{node.rewards.ce} CE</span>
                        <span className="text-purple-400">+{node.rewards.qs} QS</span>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* DNA Analysis Tab */}
        <TabsContent value="dna-analysis" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Enhanced 3D DNA Helix */}
            <Card className="bg-black/40 border-cyan-500/30 backdrop-blur-xl">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-cyan-400">
                  <Dna className="w-5 h-5" />
                  3D DNA Structure Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="h-80 rounded-lg border border-cyan-500/30 overflow-hidden bg-gradient-to-br from-cyan-900/10 to-purple-900/10">
                    <DNA3DHelix
                      height={4}
                      radius={0.6}
                      turns={2}
                      baseCount={24}
                      evolutionStage={playerData?.level || 1}
                      ceLevel={(systemStatus?.consciousnessLevel || 0)}
                      qsLevel={(systemStatus?.systemHealth || 0)}
                      isAnimated={true}
                    />
                  </div>

                  {/* DNA Structure Stats */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="p-3 bg-cyan-500/10 border border-cyan-500/30 rounded-lg">
                      <div className="text-cyan-400 font-bold">Base Pairs</div>
                      <div className="text-white/80">24 active</div>
                      <div className="text-xs text-cyan-400/60">2 helical turns</div>
                    </div>
                    <div className="p-3 bg-purple-500/10 border border-purple-500/30 rounded-lg">
                      <div className="text-purple-400 font-bold">Complexity</div>
                      <div className="text-white/80">Level {playerData?.level || 1}</div>
                      <div className="text-xs text-purple-400/60">Quantum enhanced</div>
                    </div>
                  </div>

                  {/* Real-time Flux Indicators */}
                  <div className="space-y-2">
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-white/60">CE Flux Intensity</span>
                      <span className="text-cyan-400">{Math.round((systemStatus?.consciousnessLevel || 0) * 100)}%</span>
                    </div>
                    <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                      <motion.div
                        className="h-full bg-gradient-to-r from-cyan-500 to-cyan-400 rounded-full"
                        animate={{ width: `${Math.round((systemStatus?.consciousnessLevel || 0) * 100)}%` }}
                        transition={{ duration: 1 }}
                      />
                    </div>

                    <div className="flex justify-between items-center text-sm">
                      <span className="text-white/60">QS Stability</span>
                      <span className="text-purple-400">{Math.round((systemStatus?.systemHealth || 0) * 100)}%</span>
                    </div>
                    <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                      <motion.div
                        className="h-full bg-gradient-to-r from-purple-500 to-purple-400 rounded-full"
                        animate={{ width: `${Math.round((systemStatus?.systemHealth || 0) * 100)}%` }}
                        transition={{ duration: 1, delay: 0.2 }}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* DNA Sequence Breakdown */}
            <Card className="bg-black/40 border-purple-500/30 backdrop-blur-xl">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-purple-400">
                  <Atom className="w-5 h-5" />
                  Sequence Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <DNASequenceVisualizer
                  sequence={dnaFragment}
                  evolutionStage={playerData?.level || 1}
                  ceLevel={(systemStatus?.consciousnessLevel || 0)}
                  qsLevel={(systemStatus?.systemHealth || 0)}
                />
              </CardContent>
            </Card>
          </div>

          {/* Evolution Prediction */}
          <Card className="bg-black/40 border-yellow-500/30 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-yellow-400">
                <TrendingUp className="w-5 h-5" />
                Evolution Prediction Model
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Current State */}
                <div className="space-y-3">
                  <h4 className="text-white font-bold">Current State</h4>
                  <div className="space-y-2">
                    <div className="p-3 bg-cyan-500/10 border border-cyan-500/30 rounded-lg">
                      <div className="text-cyan-400 text-sm">DNA Complexity</div>
                      <div className="text-white font-bold">Level {playerData?.level || 1}</div>
                    </div>
                    <div className="p-3 bg-purple-500/10 border border-purple-500/30 rounded-lg">
                      <div className="text-purple-400 text-sm">Quantum Coherence</div>
                      <div className="text-white font-bold">{Math.round((systemStatus?.systemHealth || 0) * 100)}%</div>
                    </div>
                  </div>
                </div>

                {/* Predicted Evolution */}
                <div className="space-y-3">
                  <h4 className="text-white font-bold">Next Evolution</h4>
                  <div className="space-y-2">
                    <div className="p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                      <div className="text-yellow-400 text-sm">Estimated Time</div>
                      <div className="text-white font-bold">2.3 days</div>
                    </div>
                    <div className="p-3 bg-orange-500/10 border border-orange-500/30 rounded-lg">
                      <div className="text-orange-400 text-sm">New Capabilities</div>
                      <div className="text-white font-bold">Neural++</div>
                    </div>
                  </div>
                </div>

                {/* Optimization Suggestions */}
                <div className="space-y-3">
                  <h4 className="text-white font-bold">Optimization</h4>
                  <div className="space-y-2 text-sm">
                    <div className="p-2 bg-green-500/10 border border-green-500/30 rounded">
                      <div className="text-green-400">✓ Complete 3 Learning Nodes</div>
                    </div>
                    <div className="p-2 bg-blue-500/10 border border-blue-500/30 rounded">
                      <div className="text-blue-400">→ Meditate for QS boost</div>
                    </div>
                    <div className="p-2 bg-purple-500/10 border border-purple-500/30 rounded">
                      <div className="text-purple-400">→ Assist 2 Timeline members</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Timeline Map Tab */}
        <TabsContent value="timeline-map" className="space-y-6">
          <Card className="bg-black/40 border-blue-500/30 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-blue-400">
                <Map className="w-5 h-5" />
                3D Timeline World View
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative h-96 bg-gradient-to-br from-blue-900/20 to-purple-900/20 rounded-lg border border-blue-500/30 overflow-hidden">
                {/* 3D Map Placeholder - Would integrate Three.js here */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center space-y-4">
                    <Globe className="w-16 h-16 text-blue-400 mx-auto animate-spin" style={{ animationDuration: '10s' }} />
                    <div className="text-white/80">3D Timeline Map</div>
                    <div className="text-sm text-white/60">Interactive world view with node positions</div>
                  </div>
                </div>

                {/* Node Markers Overlay */}
                <div className="absolute inset-0">
                  {nanoNodes.map((node, index) => (
                    <motion.div
                      key={node.id}
                      className="absolute cursor-pointer"
                      style={{
                        left: `${(node.position.x / 400) * 100}%`,
                        top: `${(node.position.y / 300) * 100}%`
                      }}
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: index * 0.2 }}
                      whileHover={{ scale: 1.2 }}
                      onClick={() => setSelectedNodeLocal(node)}
                    >
                      <div
                        className="w-4 h-4 rounded-full border-2 border-white/50"
                        style={{ backgroundColor: node.color }}
                      />
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-xs text-white/80 whitespace-nowrap">
                        {node.title}
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Navigation Controls */}
                <div className="absolute bottom-4 right-4 flex gap-2">
                  <Button size="sm" variant="outline" className="border-blue-500/30 text-blue-400">
                    <Eye className="w-4 h-4" />
                  </Button>
                  <Button size="sm" variant="outline" className="border-green-500/30 text-green-400">
                    <Focus className="w-4 h-4" />
                  </Button>
                  <Button size="sm" variant="outline" className="border-purple-500/30 text-purple-400">
                    <Users className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* View Mode Controls */}
              <div className="flex gap-2 mt-4">
                {(['explore', 'focus', 'assist', 'syntropy'] as const).map((mode) => (
                  <Button
                    key={mode}
                    size="sm"
                    variant={viewMode === mode ? "default" : "outline"}
                    onClick={() => setViewModeLocal(mode)}
                    className={`capitalize ${
                      viewMode === mode
                        ? 'bg-blue-500/20 text-blue-400 border-blue-500/30'
                        : 'border-gray-600 text-gray-400'
                    }`}
                  >
                    {mode}
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Consciousness Feed Tab */}
        <TabsContent value="consciousness" className="space-y-6">
          <Card className="bg-black/40 border-purple-500/30 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-purple-400">
                <Users className="w-5 h-5" />
                Live Consciousness Feed
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {[
                  { user: 'Luna', action: 'completed Neural Pathways node', time: '2 min ago', type: 'achievement' },
                  { user: 'Nova', action: 'shared wisdom in Echo Field', time: '5 min ago', type: 'collaboration' },
                  { user: 'Zara', action: 'achieved quantum balance', time: '8 min ago', type: 'meditation' },
                  { user: 'Kai', action: 'synthesized new matter construct', time: '12 min ago', type: 'creation' },
                  { user: 'Timeline Consciousness', action: 'Evolution stage increased to Harmonic', time: '15 min ago', type: 'evolution' }
                ].map((activity, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-4 rounded-lg bg-gray-800/30 border border-gray-700/50"
                  >
                    <div className="flex items-start gap-3">
                      <div className={`w-2 h-2 rounded-full mt-2 ${
                        activity.type === 'achievement' ? 'bg-yellow-400' :
                        activity.type === 'collaboration' ? 'bg-purple-400' :
                        activity.type === 'meditation' ? 'bg-green-400' :
                        activity.type === 'creation' ? 'bg-red-400' :
                        'bg-cyan-400'
                      }`} />
                      <div className="flex-1">
                        <div className="text-white/80">
                          <span className="font-bold text-cyan-400">{activity.user}</span> {activity.action}
                        </div>
                        <div className="text-xs text-white/40 mt-1">{activity.time}</div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* AI Insights */}
              <div className="mt-6 p-4 bg-purple-500/10 border border-purple-500/30 rounded-lg">
                <h4 className="text-purple-400 font-bold mb-2">🧠 Timeline Consciousness Insight</h4>
                <p className="text-white/80 text-sm italic">
                  &ldquo;The collective energy flows like rivers of light, each contribution rippling through the quantum field,
                  strengthening the bonds that weave us into one unified consciousness.&rdquo;
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Quantum Tasks Tab */}
        <TabsContent value="quantum-tasks" className="space-y-6">
          <Card className="bg-black/40 border-green-500/30 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-400">
                <Target className="w-5 h-5" />
                Daily Quantum Tasks
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { task: 'Complete 3 Learning Nodes', progress: 67, reward: '50 CE', streak: 5 },
                  { task: 'Assist 2 Timeline Members', progress: 50, reward: '30 QS', streak: 3 },
                  { task: 'Meditate for 10 minutes', progress: 100, reward: '40 CE', streak: 12 },
                  { task: 'Create 1 Matter Construct', progress: 0, reward: '75 CE', streak: 0 }
                ].map((task, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-4 rounded-lg bg-gray-800/30 border border-gray-700/50"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-white/80">{task.task}</span>
                      <div className="flex items-center gap-2">
                        <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                          {task.reward}
                        </Badge>
                        <div className="text-xs text-yellow-400">🔥 {task.streak}</div>
                      </div>
                    </div>
                    <div className="relative h-2 bg-gray-700 rounded-full overflow-hidden">
                      <motion.div
                        className="absolute inset-y-0 left-0 bg-gradient-to-r from-green-500 to-green-400 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${task.progress}%` }}
                        transition={{ duration: 1, delay: index * 0.2 }}
                      />
                    </div>
                    <div className="text-xs text-white/40 mt-2">{task.progress}% complete</div>
                  </motion.div>
                ))}
              </div>

              <div className="mt-6 text-center">
                <Button className="bg-green-500/20 text-green-400 border border-green-500/30 hover:bg-green-500/30">
                  <Focus className="w-4 h-4 mr-2" />
                  Activate Focus Stream
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Library Tab */}
        <TabsContent value="library" className="space-y-6">
          <Card className="bg-black/40 border-yellow-500/30 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-yellow-400">
                <BookOpen className="w-5 h-5" />
                Library of the Grid
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {[
                  { category: 'Unlocked Knowledge', count: 47, icon: Lightbulb, color: 'cyan' },
                  { category: 'Saved Dreams', count: 12, icon: Circle, color: 'purple' },
                  { category: 'AI Conversations', count: 23, icon: MessageCircle, color: 'green' }
                ].map((item, index) => (
                  <motion.div
                    key={item.category}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`p-4 rounded-lg bg-${item.color}-500/10 border border-${item.color}-500/30 hover:bg-${item.color}-500/20 transition-colors cursor-pointer`}
                  >
                    <div className="text-center space-y-3">
                      <item.icon className={`w-8 h-8 text-${item.color}-400 mx-auto`} />
                      <div>
                        <div className={`text-2xl font-bold text-${item.color}-400`}>{item.count}</div>
                        <div className="text-sm text-white/60">{item.category}</div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              <div className="mt-6 p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                <h4 className="text-yellow-400 font-bold mb-2">📚 Recent Additions</h4>
                <div className="space-y-2 text-sm">
                  <div className="text-white/80">• Quantum Entanglement Principles</div>
                  <div className="text-white/80">• Neural Network Optimization</div>
                  <div className="text-white/80">• Consciousness Field Theory</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Metrics Tab */}
        <TabsContent value="metrics" className="space-y-6">
          <Card className="bg-black/40 border-pink-500/30 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-pink-400">
                <BarChart3 className="w-5 h-5" />
                Success Metrics Dashboard
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {[
                  { metric: 'Session Duration', value: '18 min', target: '> 15 min', status: 'good' },
                  { metric: 'CE/QS Activity', value: '72%', target: '60%', status: 'excellent' },
                  { metric: 'Collaboration Rate', value: '58%', target: '> 50%', status: 'good' },
                  { metric: 'Task Completion', value: '89%', target: '> 80%', status: 'excellent' }
                ].map((metric, index) => (
                  <motion.div
                    key={metric.metric}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    className={`p-4 rounded-lg border ${
                      metric.status === 'excellent'
                        ? 'bg-green-500/10 border-green-500/30'
                        : 'bg-yellow-500/10 border-yellow-500/30'
                    }`}
                  >
                    <div className="text-center space-y-2">
                      <div className="text-sm text-white/60">{metric.metric}</div>
                      <div className={`text-2xl font-bold ${
                        metric.status === 'excellent' ? 'text-green-400' : 'text-yellow-400'
                      }`}>
                        {metric.value}
                      </div>
                      <div className="text-xs text-white/40">Target: {metric.target}</div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Node Interaction Modal */}
      <AnimatePresence>
        {selectedNode && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedNodeLocal(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-black/80 border border-gray-700 rounded-xl p-6 max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-bold text-white">{selectedNode.title}</h3>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setSelectedNodeLocal(null)}
                  className="border-gray-600 text-gray-400"
                >
                  ✕
                </Button>
              </div>

              <div className="space-y-4">
                <p className="text-white/80">{selectedNode.description}</p>

                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <Users className="w-4 h-4 text-gray-400" />
                    <span className="text-white/60">{selectedNode.participants} active</span>
                  </div>
                  <div className="text-white/60">{selectedNode.lastActivity}</div>
                </div>

                <div className="flex gap-2">
                  <Badge className="bg-cyan-500/20 text-cyan-400 border-cyan-500/30">
                    +{selectedNode.rewards.ce} CE
                  </Badge>
                  <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30">
                    +{selectedNode.rewards.qs} QS
                  </Badge>
                </div>

                <Button
                  className="w-full bg-gradient-to-r from-cyan-500/20 to-purple-500/20 text-cyan-400 border border-cyan-500/30 hover:bg-cyan-500/30"
                >
                  Enter Node
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
