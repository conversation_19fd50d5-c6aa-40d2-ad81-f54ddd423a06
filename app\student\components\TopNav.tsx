'use client'

import React, { useState, useEffect, memo } from 'react'
import { motion } from 'framer-motion'
import {
  Brain,
  Activity,
  Sparkles,
  Users
} from 'lucide-react'


import { useDashboardStore, usePlayerData, useSystemStatus } from '../store/dashboardStore'
import UserAvatarWidget from './UserAvatarWidget'
import { LevelProgressWidget, StreakWidget, QuickStatsWidget } from './widgets'

interface TopNavProps {
  sidebarCollapsed?: boolean
  className?: string
}

const TopNav = memo(function TopNav({ sidebarCollapsed = false, className }: TopNavProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)
  const [isOnline, setIsOnline] = useState(true)
  const [currentTime, setCurrentTime] = useState(new Date())

  const { isFullscreen, toggleFullscreen, viewMode } = useDashboardStore()
  const playerData = usePlayerData()
  const systemStatus = useSystemStatus()

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const notifications = [
    {
      id: '1',
      type: 'achievement',
      title: 'Evolution Milestone',
      message: 'You reached Stage 3 consciousness!',
      time: '2 min ago',
      unread: true
    },
    {
      id: '2',
      type: 'collaboration',
      title: 'Timeline Assist',
      message: 'Nova needs help with quantum stabilization',
      time: '5 min ago',
      unread: true
    },
    {
      id: '3',
      type: 'system',
      title: 'System Update',
      message: 'New meditation nodes available',
      time: '1 hour ago',
      unread: false
    }
  ]

  const unreadCount = notifications.filter(n => n.unread).length

  const getViewModeColor = () => {
    switch (viewMode) {
      case 'explore': return 'text-neural-cyan'
      case 'focus': return 'text-neural-cyan'
      case 'assist': return 'text-green-500'
      case 'syntropy': return 'text-flame-orange'
      default: return 'text-white/80'
    }
  }

  const getViewModeIcon = () => {
    switch (viewMode) {
      case 'explore': return <Activity className="w-4 h-4" />
      case 'focus': return <Brain className="w-4 h-4" />
      case 'assist': return <Users className="w-4 h-4" />
      case 'syntropy': return <Sparkles className="w-4 h-4" />
      default: return <Activity className="w-4 h-4" />
    }
  }

  return (
    <motion.div
      className={`fixed z-30 ${className}`}
      style={{
        top: '5px',
        left: sidebarCollapsed ? '85px' : '285px', // 80px + 16px margin or 280px + 16px margin
        right: '',
        width: sidebarCollapsed ? 'calc(100% - 112px)' : 'calc(100% - 312px)' // Account for both sides + margins
      }}
      animate={{
        left: sidebarCollapsed ? '96px' : '296px',
        width: sidebarCollapsed ? 'calc(100% - 112px)' : 'calc(100% - 312px)'
      }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
    >
      {/* Enhanced TopNav Container */}
      <div className="h-20 flex flex-row gaming-panel-inner justify-between items-center rounded-2xl overflow-hidden px-6" style={{
        background: 'linear-gradient(135deg, rgba(10, 15, 28, 0.98) 0%, rgba(26, 35, 50, 0.95) 50%, rgba(10, 15, 28, 0.98) 100%)',
        backdropFilter: 'blur(20px)',
        border: '2px solid rgba(34, 211, 238, 0.3)',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.4), 0 0 40px rgba(34, 211, 238, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
      }}>
        {/* Student Stats Widgets */}
        <div className="flex items-center gap-2 md:gap-4 flex-1 overflow-hidden">
          {/* Level Progress Widget - Always visible */}
          <LevelProgressWidget
            currentLevel={playerData?.level || 1}
            currentXP={playerData?.xp || 0}
            xpToNext={((playerData?.level || 1) + 1) * 1000} // Calculate XP needed for next level
            totalXP={playerData?.totalXP || 0}
            size="sm"
            variant="compact"
            className="min-w-[120px] md:min-w-[140px] flex-shrink-0"
          />

          {/* Streak Widget - Hidden on mobile */}
          <div className="hidden sm:block flex-shrink-0">
            <StreakWidget
              current={playerData?.stats?.learningStreak || 0}
              best={Math.max(playerData?.stats?.learningStreak || 0, 15)} // Mock best streak
              size="sm"
              variant="compact"
              className="min-w-[100px]"
            />
          </div>

          {/* Quick Stats Widget - Hidden on small screens, compact on medium */}
          <div className="hidden md:block flex-shrink-0">
            <QuickStatsWidget
              stats={{
                level: playerData?.level || 1,
                xp: playerData?.xp || 0,
                xpToNextLevel: ((playerData?.level || 1) + 1) * 1000,
                totalXP: playerData?.totalXP || 0,
                streak: playerData?.stats?.learningStreak || 0,
                lessonsCompleted: playerData?.stats?.totalLessons || 0,
                averageScore: 85, // Mock average score
                timeSpent: playerData?.stats?.mentorshipHours ? playerData.stats.mentorshipHours * 60 : 0, // Convert hours to minutes
                rank: 'Quantum Explorer', // Mock rank
                badges: playerData?.badges?.length || 0
              }}
              size="sm"
              variant="compact"
              showAnimation={false}
              className="min-w-[180px] lg:min-w-[220px]"
            />
          </div>
        </div>

        {/* User Avatar Widget */}
        <UserAvatarWidget
          onClick={() => setShowUserMenu(!showUserMenu)}
          showName={true}
          size="md"
        />
      </div>
      
    </motion.div>
  )
})

export default TopNav
