'use client'

import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { 
  CurriculumPlan, 
  ChildProfile, 
  Lesson, 
  LessonAssignment, 
  ParentPreferences,
  PlanningMode,
  ScheduleView,
  LessonFilters,
  CurriculumNotification
} from '../types/curriculum'
import { LESSON_LIBRARY } from '../data/lessonLibrary'

// State Interface
interface CurriculumPlannerState {
  // Core Data
  children: ChildProfile[]
  selectedChildId: string | null
  currentPlan: CurriculumPlan | null
  lessons: Lesson[]
  assignments: LessonAssignment[]
  
  // UI State
  planningMode: PlanningMode
  scheduleView: ScheduleView
  selectedDate: Date
  filters: LessonFilters
  
  // Preferences & Settings
  parentPreferences: ParentPreferences | null
  
  // Notifications
  notifications: CurriculumNotification[]
  
  // Loading States
  isLoading: boolean
  isSaving: boolean
  error: string | null
}

// Action Types
type CurriculumPlannerAction =
  | { type: 'SET_CHILDREN'; payload: Child<PERSON><PERSON><PERSON>le[] }
  | { type: 'SELECT_CHILD'; payload: string }
  | { type: 'SET_CURRENT_PLAN'; payload: CurriculumPlan }
  | { type: 'SET_PLANNING_MODE'; payload: PlanningMode }
  | { type: 'SET_SCHEDULE_VIEW'; payload: ScheduleView }
  | { type: 'SET_SELECTED_DATE'; payload: Date }
  | { type: 'SET_FILTERS'; payload: Partial<LessonFilters> }
  | { type: 'ADD_ASSIGNMENT'; payload: LessonAssignment }
  | { type: 'UPDATE_ASSIGNMENT'; payload: { id: string; updates: Partial<LessonAssignment> } }
  | { type: 'REMOVE_ASSIGNMENT'; payload: string }
  | { type: 'SET_PARENT_PREFERENCES'; payload: ParentPreferences }
  | { type: 'ADD_NOTIFICATION'; payload: CurriculumNotification }
  | { type: 'MARK_NOTIFICATION_READ'; payload: string }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_SAVING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }

// Initial State
const initialState: CurriculumPlannerState = {
  children: [],
  selectedChildId: null,
  currentPlan: null,
  lessons: LESSON_LIBRARY,
  assignments: [],
  planningMode: 'auto',
  scheduleView: 'weekly',
  selectedDate: new Date(),
  filters: {
    subjects: [],
    categories: [],
    difficulty: [],
    duration: [0, 120],
    ageRange: [5, 16],
    tags: [],
    type: [],
    searchQuery: ''
  },
  parentPreferences: null,
  notifications: [],
  isLoading: false,
  isSaving: false,
  error: null
}

// Reducer
function curriculumPlannerReducer(
  state: CurriculumPlannerState,
  action: CurriculumPlannerAction
): CurriculumPlannerState {
  switch (action.type) {
    case 'SET_CHILDREN':
      return { ...state, children: action.payload }
    
    case 'SELECT_CHILD':
      return { ...state, selectedChildId: action.payload }
    
    case 'SET_CURRENT_PLAN':
      return { ...state, currentPlan: action.payload }
    
    case 'SET_PLANNING_MODE':
      return { ...state, planningMode: action.payload }
    
    case 'SET_SCHEDULE_VIEW':
      return { ...state, scheduleView: action.payload }
    
    case 'SET_SELECTED_DATE':
      return { ...state, selectedDate: action.payload }
    
    case 'SET_FILTERS':
      return { 
        ...state, 
        filters: { ...state.filters, ...action.payload }
      }
    
    case 'ADD_ASSIGNMENT':
      return {
        ...state,
        assignments: [...state.assignments, action.payload]
      }
    
    case 'UPDATE_ASSIGNMENT':
      return {
        ...state,
        assignments: state.assignments.map(assignment =>
          assignment.id === action.payload.id
            ? { ...assignment, ...action.payload.updates }
            : assignment
        )
      }
    
    case 'REMOVE_ASSIGNMENT':
      return {
        ...state,
        assignments: state.assignments.filter(assignment => assignment.id !== action.payload)
      }
    
    case 'SET_PARENT_PREFERENCES':
      return { ...state, parentPreferences: action.payload }
    
    case 'ADD_NOTIFICATION':
      return {
        ...state,
        notifications: [action.payload, ...state.notifications]
      }
    
    case 'MARK_NOTIFICATION_READ':
      return {
        ...state,
        notifications: state.notifications.map(notification =>
          notification.id === action.payload
            ? { ...notification, read: true }
            : notification
        )
      }
    
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload }
    
    case 'SET_SAVING':
      return { ...state, isSaving: action.payload }
    
    case 'SET_ERROR':
      return { ...state, error: action.payload }
    
    default:
      return state
  }
}

// Context
interface CurriculumPlannerContextType {
  state: CurriculumPlannerState
  dispatch: React.Dispatch<CurriculumPlannerAction>

  // Helper Functions
  getSelectedChild: () => ChildProfile | null
  getFilteredLessons: () => Lesson[]
  getAssignmentsForDate: (date: Date) => LessonAssignment[]
  getAssignmentsForChild: (childId: string) => LessonAssignment[]
  scheduleLesson: (lessonId: string, date: Date, childId: string, status?: string) => void
  approveLesson: (assignmentId: string) => void
  rejectLesson: (assignmentId: string, reason?: string) => void
  updateAssignment: (assignmentId: string, updates: Partial<LessonAssignment>) => void
  generateAutoSchedule: (childId: string, weeks: number) => void
  copyPlanToChild: (fromChildId: string, toChildId: string) => void
}

const CurriculumPlannerContext = createContext<CurriculumPlannerContextType | null>(null)

// Provider Component
export function CurriculumPlannerProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(curriculumPlannerReducer, initialState)

  // Initialize with mock data
  useEffect(() => {
    // Mock children data
    const mockChildren: ChildProfile[] = [
      {
        id: 'child-1',
        name: 'Emma',
        age: 12,
        gradeLevel: '6th',
        avatar: '👧',
        interests: ['quantum-physics', 'space-science', 'coding'],
        learningStyle: ['visual', 'hands-on'],
        skillLevels: {
          'critical-thinking': 85,
          'problem-solving': 78,
          'creativity': 92,
          'communication': 88
        },
        preferences: {
          dailyTimeLimit: 90,
          preferredSubjects: ['quantum-physics', 'space-science'],
          avoidSubjects: [],
          bestLearningTimes: ['morning', 'afternoon']
        },
        progress: {
          completedLessons: ['qp-001', 'bio-001'],
          currentStreak: 15,
          totalHours: 156.5,
          skillProgress: {
            'critical-thinking': 85,
            'problem-solving': 78
          }
        }
      },
      {
        id: 'child-2',
        name: 'Alex',
        age: 9,
        gradeLevel: '4th',
        avatar: '👦',
        interests: ['biology', 'animals', 'nature'],
        learningStyle: ['kinesthetic', 'auditory'],
        skillLevels: {
          'critical-thinking': 65,
          'creativity': 88,
          'empathy': 95
        },
        preferences: {
          dailyTimeLimit: 60,
          preferredSubjects: ['dna-biology', 'emotional-intelligence'],
          avoidSubjects: ['cybersecurity'],
          bestLearningTimes: ['morning']
        },
        progress: {
          completedLessons: ['bio-001', 'ei-001'],
          currentStreak: 8,
          totalHours: 89.2,
          skillProgress: {
            'empathy': 95,
            'creativity': 88
          }
        }
      }
    ]

    dispatch({ type: 'SET_CHILDREN', payload: mockChildren })
    dispatch({ type: 'SELECT_CHILD', payload: 'child-1' })

    // Comprehensive mock curriculum flowchart data for Emma
    const mockAssignments: LessonAssignment[] = [
      // === WEEK 1: Foundation Building ===
      // Quantum Physics - Introduction
      {
        id: 'assignment-1',
        lessonId: 'qp-001',
        childId: 'child-1',
        scheduledDate: new Date('2024-01-08'),
        status: 'completed',
        completedAt: new Date('2024-01-08'),
        timeSpent: 18,
        feedback: {
          rating: 5,
          comments: 'Emma showed great curiosity about quantum concepts! Loved the wave-particle duality demo.'
        },
        parentNotes: 'Excellent start! Emma was fascinated by the quantum world.'
      },
      // Critical Thinking - Foundation
      {
        id: 'assignment-2',
        lessonId: 'ct-001',
        childId: 'child-1',
        scheduledDate: new Date('2024-01-10'),
        status: 'completed',
        completedAt: new Date('2024-01-10'),
        timeSpent: 28,
        feedback: {
          rating: 4,
          comments: 'Good logical reasoning skills. Needs more practice with complex patterns.'
        },
        parentNotes: 'Focus on pattern recognition exercises'
      },
      // Emotional Intelligence - Self Awareness
      {
        id: 'assignment-3',
        lessonId: 'ei-001',
        childId: 'child-1',
        scheduledDate: new Date('2024-01-12'),
        status: 'completed',
        completedAt: new Date('2024-01-12'),
        timeSpent: 22,
        feedback: {
          rating: 5,
          comments: 'Emma demonstrated excellent emotional awareness and empathy.'
        }
      },

      // === WEEK 2: Building on Foundations ===
      // Quantum Physics - Advanced Concepts
      {
        id: 'assignment-4',
        lessonId: 'qp-002',
        childId: 'child-1',
        scheduledDate: new Date('2024-01-15'),
        status: 'completed',
        completedAt: new Date('2024-01-15'),
        timeSpent: 35,
        feedback: {
          rating: 4,
          comments: 'Quantum superposition lab was challenging but Emma persevered. Great problem-solving!'
        },
        parentNotes: 'Loved the hands-on quantum simulator'
      },
      // DNA Biology - Introduction
      {
        id: 'assignment-5',
        lessonId: 'bio-001',
        childId: 'child-1',
        scheduledDate: new Date('2024-01-17'),
        status: 'completed',
        completedAt: new Date('2024-01-17'),
        timeSpent: 25,
        feedback: {
          rating: 5,
          comments: 'Fascinated by DNA structure! Built an amazing 3D model.'
        }
      },
      // Cybersecurity - Digital Safety
      {
        id: 'assignment-6',
        lessonId: 'cs-001',
        childId: 'child-1',
        scheduledDate: new Date('2024-01-19'),
        status: 'completed',
        completedAt: new Date('2024-01-19'),
        timeSpent: 30,
        feedback: {
          rating: 4,
          comments: 'Good understanding of digital footprints. Created a strong privacy plan.'
        },
        parentNotes: 'Important lesson - Emma now understands online safety better'
      },

      // === WEEK 3: Current Progress ===
      // Quantum Physics - Quantum Computing Basics
      {
        id: 'assignment-7',
        lessonId: 'qp-003',
        childId: 'child-1',
        scheduledDate: new Date('2024-01-22'),
        status: 'in-progress',
        parentNotes: 'This builds on previous quantum lessons. Emma is excited about quantum computers!'
      },
      // Critical Thinking - Advanced Logic
      {
        id: 'assignment-8',
        lessonId: 'ct-002',
        childId: 'child-1',
        scheduledDate: new Date('2024-01-24'),
        status: 'pending',
        parentNotes: 'Ready for more complex logical reasoning. Please review the prerequisite concepts.'
      },

      // === WEEK 4: Upcoming Lessons ===
      // DNA Biology - Gene Expression
      {
        id: 'assignment-9',
        lessonId: 'bio-002',
        childId: 'child-1',
        scheduledDate: new Date('2024-01-26'),
        status: 'approved',
        parentNotes: 'Continuation of DNA studies. Emma loves biology!'
      },
      // Emotional Intelligence - Communication Skills
      {
        id: 'assignment-10',
        lessonId: 'ei-002',
        childId: 'child-1',
        scheduledDate: new Date('2024-01-29'),
        status: 'approved'
      },
      // Cybersecurity - Password Security
      {
        id: 'assignment-11',
        lessonId: 'cs-002',
        childId: 'child-1',
        scheduledDate: new Date('2024-01-31'),
        status: 'approved',
        parentNotes: 'Practical lesson on creating strong passwords'
      },

      // === FEBRUARY: Advanced Topics ===
      // Quantum Physics - Quantum Entanglement
      {
        id: 'assignment-12',
        lessonId: 'qp-004',
        childId: 'child-1',
        scheduledDate: new Date('2024-02-02'),
        status: 'pending',
        parentNotes: 'Advanced quantum concept - ensure Emma has mastered previous lessons'
      },
      // Critical Thinking - Problem Solving Strategies
      {
        id: 'assignment-13',
        lessonId: 'ct-003',
        childId: 'child-1',
        scheduledDate: new Date('2024-02-05'),
        status: 'pending'
      },
      // DNA Biology - Biotechnology Applications
      {
        id: 'assignment-14',
        lessonId: 'bio-003',
        childId: 'child-1',
        scheduledDate: new Date('2024-02-07'),
        status: 'pending',
        parentNotes: 'Real-world applications of genetic engineering'
      },
      // Cybersecurity - Network Security
      {
        id: 'assignment-15',
        lessonId: 'cs-003',
        childId: 'child-1',
        scheduledDate: new Date('2024-02-09'),
        status: 'pending'
      },

      // === FEBRUARY WEEK 2: Integration Projects ===
      // Quantum Physics - Quantum Applications
      {
        id: 'assignment-16',
        lessonId: 'qp-005',
        childId: 'child-1',
        scheduledDate: new Date('2024-02-12'),
        status: 'pending',
        parentNotes: 'Capstone project combining all quantum concepts learned'
      },
      // Critical Thinking - Systems Thinking
      {
        id: 'assignment-17',
        lessonId: 'ct-004',
        childId: 'child-1',
        scheduledDate: new Date('2024-02-14'),
        status: 'pending'
      },
      // Emotional Intelligence - Leadership Skills
      {
        id: 'assignment-18',
        lessonId: 'ei-003',
        childId: 'child-1',
        scheduledDate: new Date('2024-02-16'),
        status: 'pending',
        parentNotes: 'Building on communication skills to develop leadership'
      },

      // === FEBRUARY WEEK 3: Cross-Subject Integration ===
      // Interdisciplinary Project 1: Quantum Biology
      {
        id: 'assignment-19',
        lessonId: 'interdisciplinary-001',
        childId: 'child-1',
        scheduledDate: new Date('2024-02-19'),
        status: 'pending',
        parentNotes: 'Exciting project combining quantum physics and biology concepts!'
      },
      // Cybersecurity - Ethical Hacking Basics
      {
        id: 'assignment-20',
        lessonId: 'cs-004',
        childId: 'child-1',
        scheduledDate: new Date('2024-02-21'),
        status: 'pending',
        parentNotes: 'Advanced cybersecurity - ensure Emma understands ethical implications'
      },

      // === FEBRUARY WEEK 4: Assessment & Reflection ===
      // Portfolio Review & Reflection
      {
        id: 'assignment-21',
        lessonId: 'portfolio-review-001',
        childId: 'child-1',
        scheduledDate: new Date('2024-02-23'),
        status: 'pending',
        parentNotes: 'Time to review all learning and create a portfolio of achievements'
      },
      // Future Learning Path Planning
      {
        id: 'assignment-22',
        lessonId: 'planning-001',
        childId: 'child-1',
        scheduledDate: new Date('2024-02-26'),
        status: 'pending',
        parentNotes: 'Plan next month\'s learning journey based on Emma\'s interests and progress'
      },
      // Child 2 assignments
      {
        id: 'assignment-7',
        lessonId: 'bio-001',
        childId: 'child-2',
        scheduledDate: new Date('2024-01-16'),
        status: 'completed',
        completedAt: new Date('2024-01-16'),
        timeSpent: 22
      },
      {
        id: 'assignment-8',
        lessonId: 'ei-001',
        childId: 'child-2',
        scheduledDate: new Date('2024-01-19'),
        status: 'completed',
        completedAt: new Date('2024-01-19'),
        timeSpent: 18
      },
      {
        id: 'assignment-9',
        lessonId: 'ct-001',
        childId: 'child-2',
        scheduledDate: new Date('2024-01-23'),
        status: 'pending'
      }
    ]

    // Add assignments to state
    mockAssignments.forEach(assignment => {
      dispatch({ type: 'ADD_ASSIGNMENT', payload: assignment })
    })
  }, [])

  // Helper Functions
  const getSelectedChild = (): ChildProfile | null => {
    if (!state.selectedChildId) return null
    return state.children.find(child => child.id === state.selectedChildId) || null
  }

  const getFilteredLessons = (): Lesson[] => {
    return state.lessons.filter(lesson => {
      const { filters } = state
      
      // Search query
      if (filters.searchQuery && !lesson.title.toLowerCase().includes(filters.searchQuery.toLowerCase())) {
        return false
      }
      
      // Subjects
      if (filters.subjects.length > 0 && !filters.subjects.includes(lesson.subject)) {
        return false
      }
      
      // Categories
      if (filters.categories.length > 0 && !filters.categories.includes(lesson.category)) {
        return false
      }
      
      // Difficulty
      if (filters.difficulty.length > 0 && !filters.difficulty.includes(lesson.difficulty)) {
        return false
      }
      
      // Duration
      if (lesson.duration < filters.duration[0] || lesson.duration > filters.duration[1]) {
        return false
      }
      
      // Age Range
      const selectedChild = getSelectedChild()
      if (selectedChild) {
        if (selectedChild.age < lesson.ageRange[0] || selectedChild.age > lesson.ageRange[1]) {
          return false
        }
      }
      
      return true
    })
  }

  const getAssignmentsForDate = (date: Date): LessonAssignment[] => {
    return state.assignments.filter(assignment => {
      const assignmentDate = new Date(assignment.scheduledDate)
      return assignmentDate.toDateString() === date.toDateString()
    })
  }

  const getAssignmentsForChild = (childId: string): LessonAssignment[] => {
    return state.assignments.filter(assignment => assignment.childId === childId)
  }

  const scheduleLesson = (lessonId: string, date: Date, childId: string, status?: string): void => {
    const newAssignment: LessonAssignment = {
      id: `assignment-${Date.now()}`,
      lessonId,
      childId,
      scheduledDate: date,
      status: (status as any) || (state.planningMode === 'auto' ? 'approved' : 'pending')
    }

    dispatch({ type: 'ADD_ASSIGNMENT', payload: newAssignment })
  }

  const approveLesson = (assignmentId: string): void => {
    dispatch({
      type: 'UPDATE_ASSIGNMENT',
      payload: { id: assignmentId, updates: { status: 'approved' } }
    })
  }

  const rejectLesson = (assignmentId: string, reason?: string): void => {
    dispatch({
      type: 'UPDATE_ASSIGNMENT',
      payload: {
        id: assignmentId,
        updates: {
          status: 'rejected',
          parentNotes: reason
        }
      }
    })
  }

  const updateAssignment = (assignmentId: string, updates: Partial<LessonAssignment>): void => {
    dispatch({
      type: 'UPDATE_ASSIGNMENT',
      payload: { id: assignmentId, updates }
    })
  }

  const generateAutoSchedule = (childId: string, weeks: number): void => {
    // Auto-scheduling logic would go here
    console.log(`Generating auto schedule for child ${childId} for ${weeks} weeks`)
  }

  const copyPlanToChild = (fromChildId: string, toChildId: string): void => {
    // Plan copying logic would go here
    console.log(`Copying plan from ${fromChildId} to ${toChildId}`)
  }

  const contextValue: CurriculumPlannerContextType = {
    state,
    dispatch,
    getSelectedChild,
    getFilteredLessons,
    getAssignmentsForDate,
    getAssignmentsForChild,
    scheduleLesson,
    approveLesson,
    rejectLesson,
    updateAssignment,
    generateAutoSchedule,
    copyPlanToChild
  }

  return (
    <CurriculumPlannerContext.Provider value={contextValue}>
      {children}
    </CurriculumPlannerContext.Provider>
  )
}

// Hook to use the context
export function useCurriculumPlanner() {
  const context = useContext(CurriculumPlannerContext)
  if (!context) {
    throw new Error('useCurriculumPlanner must be used within a CurriculumPlannerProvider')
  }
  return context
}
