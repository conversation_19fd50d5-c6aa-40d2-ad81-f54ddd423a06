'use client'

import { motion } from 'framer-motion'
import { Star, TrendingUp, Zap } from 'lucide-react'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { LevelProgressWidgetProps } from './types'

export default function LevelProgressWidget({
  currentLevel,
  currentXP,
  xpToNext,
  totalXP,
  className = '',
  size = 'md',
  variant = 'detailed'
}: LevelProgressWidgetProps) {
  const progressPercentage = (currentXP / xpToNext) * 100
  const xpNeeded = xpToNext - currentXP

  const sizeClasses = {
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6'
  }

  const textSizes = {
    sm: { title: 'text-sm', value: 'text-lg', label: 'text-xs' },
    md: { title: 'text-base', value: 'text-xl', label: 'text-sm' },
    lg: { title: 'text-lg', value: 'text-2xl', label: 'text-base' }
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`bg-black/40 border border-cyan-500/30 backdrop-blur-xl rounded-lg ${sizeClasses[size]} ${className}`}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Star className="w-5 h-5 text-yellow-400" />
          <span className={`font-medium text-white ${textSizes[size].title}`}>
            Level Progress
          </span>
        </div>
        <Badge className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-yellow-400 border-yellow-500/30">
          Level {currentLevel}
        </Badge>
      </div>

      {variant === 'detailed' && (
        <>
          <div className="space-y-2 mb-4">
            <div className="flex justify-between items-center">
              <span className="text-white/80 text-sm">
                {currentXP.toLocaleString()} / {xpToNext.toLocaleString()} XP
              </span>
              <span className="text-cyan-400 font-bold text-sm">
                {Math.round(progressPercentage)}%
              </span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
            <div className="flex justify-between text-xs text-white/60">
              <span>{xpNeeded.toLocaleString()} XP needed</span>
              <span>Total: {totalXP.toLocaleString()}</span>
            </div>
          </div>

          <div className="flex items-center gap-2 text-xs text-green-400">
            <TrendingUp className="w-3 h-3" />
            <span>Next level unlocks new abilities</span>
          </div>
        </>
      )}

      {variant === 'compact' && (
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className={`text-cyan-400 font-bold ${textSizes[size].value}`}>
              {Math.round(progressPercentage)}%
            </span>
            <span className="text-white/60 text-xs">
              {xpNeeded.toLocaleString()} XP to go
            </span>
          </div>
          <Progress value={progressPercentage} className="h-1" />
        </div>
      )}
    </motion.div>
  )
}
