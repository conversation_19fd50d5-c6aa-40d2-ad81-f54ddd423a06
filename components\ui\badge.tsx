import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-neural-cyan focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-neural-cyan/40 bg-neural-cyan/20 text-neural-cyan hover:bg-neural-cyan/30",
        secondary:
          "border-white/20 bg-black/40 text-white/80 hover:bg-black/60",
        destructive:
          "border-red-500/40 bg-red-500/20 text-red-400 hover:bg-red-500/30",
        outline: "border-white/30 text-white/80 hover:bg-white/10",
        quantum: "border-quantum-purple/40 bg-quantum-purple/20 text-quantum-purple hover:bg-quantum-purple/30",
        success: "border-emerald-500/40 bg-emerald-500/20 text-emerald-400 hover:bg-emerald-500/30",
        warning: "border-quantum-gold/40 bg-quantum-gold/20 text-quantum-gold hover:bg-quantum-gold/30",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
