"use client"

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { <PERSON>, Heart } from 'lucide-react'
import { testimonials } from '../../data/constants'

interface TestimonialsSectionProps {
  currentTestimonial: number
  setCurrentTestimonial: (index: number) => void
}

export function TestimonialsSection({ currentTestimonial, setCurrentTestimonial }: TestimonialsSectionProps) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Quantum testimonial particles
  const testimonialParticles = Array.from({ length: 15 }, (_, i) => ({
    left: (i * 23 + 7) % 100,
    top: (i * 31 + 11) % 100,
    delay: i * 0.3,
    color: i % 3 === 0 ? '#22d3ee' : i % 3 === 1 ? '#8b5cf6' : '#fbbf24'
  }))

  // Get quantum color based on testimonial type/role
  const getQuantumColor = (role: string) => {
    if (role.toLowerCase().includes('student') || role.toLowerCase().includes('learner')) {
      return '#22d3ee' // Neural cyan for students
    } else if (role.toLowerCase().includes('parent') || role.toLowerCase().includes('guardian')) {
      return '#10b981' // Emerald for parents
    } else if (role.toLowerCase().includes('teacher') || role.toLowerCase().includes('educator')) {
      return '#8b5cf6' // Quantum purple for educators
    } else {
      return '#fbbf24' // Quantum gold for others
    }
  }
  return (
    <section className="relative px-6 py-20 overflow-hidden">
      {/* Quantum testimonial background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-space-dark via-space-blue to-space-dark" />
        <div className="absolute inset-0 consciousness-wave opacity-20" />

        {/* Testimonial consciousness particles */}
        {isClient && testimonialParticles.map((particle, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 rounded-full"
            style={{
              left: `${particle.left}%`,
              top: `${particle.top}%`,
              backgroundColor: particle.color
            }}
            animate={{
              opacity: [0, 0.8, 0],
              scale: [0.5, 1.2, 0.5],
              y: [0, -15, 0]
            }}
            transition={{
              duration: 4,
              repeat: Number.POSITIVE_INFINITY,
              delay: particle.delay,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Quantum testimonial glow orbs */}
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-neural-cyan/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/3 right-1/4 w-40 h-40 bg-quantum-purple/10 rounded-full blur-3xl" />
        <div className="absolute top-1/2 right-1/3 w-24 h-24 bg-quantum-gold/10 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-4xl mx-auto">
        {/* Quantum Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.h2
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 font-orbitron"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <span className="bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold bg-clip-text text-transparent">
              Loved by Neural
            </span>
            <br />
            <span className="bg-gradient-to-r from-white via-gray-200 to-white bg-clip-text text-transparent text-3xl md:text-4xl lg:text-5xl">
              Consciousness Community
            </span>
          </motion.h2>

          <motion.p
            className="text-lg md:text-xl text-white/80 max-w-3xl mx-auto font-space-grotesk leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3, duration: 0.8 }}
          >
            Real quantum feedback from our amazing <span className="text-neural-cyan font-semibold">NanoHero</span> consciousness community
          </motion.p>

          {/* Quantum testimonial decoration */}
          <motion.div
            className="flex justify-center items-center gap-4 mt-6"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.6 }}
          >
            <div className="h-px w-16 bg-gradient-to-r from-transparent to-neural-cyan" />
            <Heart className="w-5 h-5 text-quantum-gold" />
            <div className="h-px w-16 bg-gradient-to-l from-transparent to-quantum-purple" />
          </motion.div>
        </motion.div>

        <div className="relative">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentTestimonial}
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -50 }}
              transition={{ duration: 0.6 }}
            >
              {(() => {
                const currentQuantumColor = getQuantumColor(testimonials[currentTestimonial].role)

                return (
                  <Card
                    className="quantum-glass border-2 relative overflow-hidden"
                    style={{
                      borderColor: `${currentQuantumColor}40`,
                      background: `linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)`,
                      boxShadow: `0 0 40px ${currentQuantumColor}20, inset 0 1px 0 rgba(255, 255, 255, 0.1)`
                    }}
                  >
                    {/* Quantum testimonial background effects */}
                    <div className="absolute inset-0 consciousness-wave opacity-20" />

                    {/* Testimonial quantum particles */}
                    {isClient && (
                      <div className="absolute inset-0 overflow-hidden">
                        {testimonialParticles.slice(0, 6).map((particle, i) => (
                          <motion.div
                            key={i}
                            className="absolute w-1 h-1 rounded-full"
                            style={{
                              left: `${particle.left}%`,
                              top: `${particle.top}%`,
                              backgroundColor: currentQuantumColor
                            }}
                            animate={{
                              opacity: [0, 0.6, 0],
                              scale: [0.5, 1, 0.5],
                              rotate: 360
                            }}
                            transition={{
                              duration: 3,
                              repeat: Number.POSITIVE_INFINITY,
                              delay: particle.delay
                            }}
                          />
                        ))}
                      </div>
                    )}

                    <CardContent className="p-8 lg:p-12 text-center relative z-10">
                      {/* Quantum star rating */}
                      <div className="flex justify-center mb-6">
                        {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                          <motion.div
                            key={i}
                            initial={{ opacity: 0, scale: 0 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: i * 0.1, duration: 0.3 }}
                            whileHover={{ scale: 1.2, rotate: 360 }}
                          >
                            <Star
                              className="w-6 h-6 lg:w-7 lg:h-7 fill-current mx-1"
                              style={{
                                color: currentQuantumColor,
                                filter: `drop-shadow(0 0 8px ${currentQuantumColor}60)`
                              }}
                            />
                          </motion.div>
                        ))}
                      </div>

                      {/* Enhanced quantum testimonial text */}
                      <motion.blockquote
                        className="text-xl lg:text-2xl text-white/90 mb-8 leading-relaxed italic font-space-grotesk"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                        style={{
                          textShadow: `0 0 20px ${currentQuantumColor}30`
                        }}
                      >
                        &ldquo;{testimonials[currentTestimonial].text}&rdquo;
                      </motion.blockquote>

                      {/* Enhanced quantum avatar and info */}
                      <motion.div
                        className="flex items-center justify-center gap-4 lg:gap-6"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.5 }}
                      >
                        <motion.div
                          whileHover={{ scale: 1.1 }}
                          transition={{ type: "spring", stiffness: 300 }}
                        >
                          <Avatar
                            className="w-16 h-16 lg:w-20 lg:h-20 border-4"
                            style={{
                              borderColor: currentQuantumColor,
                              boxShadow: `0 0 20px ${currentQuantumColor}40`
                            }}
                          >
                            <AvatarImage src={testimonials[currentTestimonial].avatar || "/placeholder.svg"} />
                            <AvatarFallback
                              className="text-white font-orbitron text-lg lg:text-xl"
                              style={{
                                background: `linear-gradient(135deg, ${currentQuantumColor}, ${currentQuantumColor}80)`,
                                boxShadow: `0 0 15px ${currentQuantumColor}40`
                              }}
                            >
                              {testimonials[currentTestimonial].name.slice(0, 2)}
                            </AvatarFallback>
                          </Avatar>
                        </motion.div>

                        <div className="text-left">
                          <div
                            className="font-semibold text-white text-lg lg:text-xl font-orbitron"
                            style={{ textShadow: `0 0 15px ${currentQuantumColor}60` }}
                          >
                            {testimonials[currentTestimonial].name}
                          </div>
                          <div
                            className="text-sm lg:text-base font-medium font-space-grotesk"
                            style={{ color: currentQuantumColor }}
                          >
                            {testimonials[currentTestimonial].role}
                          </div>
                        </div>
                      </motion.div>
                    </CardContent>
                  </Card>
                )
              })()}
            </motion.div>
          </AnimatePresence>

          {/* Quantum Testimonial Indicators */}
          <div className="flex justify-center gap-3 lg:gap-4 mt-8">
            {testimonials.map((testimonial, index) => {
              const indicatorColor = getQuantumColor(testimonial.role)
              const isActive = index === currentTestimonial

              return (
                <motion.button
                  key={index}
                  onClick={() => setCurrentTestimonial(index)}
                  className="relative group"
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                >
                  {/* Quantum glow effect for active indicator */}
                  {isActive && (
                    <div
                      className="absolute inset-0 rounded-full blur-md opacity-60"
                      style={{ backgroundColor: indicatorColor }}
                    />
                  )}

                  <div
                    className={`w-4 h-4 lg:w-5 lg:h-5 rounded-full border-2 transition-all duration-300 relative z-10 ${
                      isActive ? "scale-125" : "hover:scale-110"
                    }`}
                    style={{
                      backgroundColor: isActive ? indicatorColor : 'transparent',
                      borderColor: isActive ? indicatorColor : '#ffffff40',
                      boxShadow: isActive ? `0 0 15px ${indicatorColor}60` : 'none'
                    }}
                  >
                    {/* Quantum pulse effect for active indicator */}
                    {isActive && isClient && (
                      <motion.div
                        className="absolute inset-0 rounded-full border-2"
                        style={{ borderColor: indicatorColor }}
                        animate={{
                          scale: [1, 1.5, 1],
                          opacity: [0.8, 0, 0.8]
                        }}
                        transition={{
                          duration: 2,
                          repeat: Number.POSITIVE_INFINITY,
                          ease: "easeInOut"
                        }}
                      />
                    )}
                  </div>

                  {/* Quantum indicator particles */}
                  {isActive && isClient && (
                    <div className="absolute inset-0">
                      {[...Array(4)].map((_, i) => (
                        <motion.div
                          key={i}
                          className="absolute w-1 h-1 rounded-full"
                          style={{
                            backgroundColor: indicatorColor,
                            left: `${25 + Math.cos(i * Math.PI / 2) * 150}%`,
                            top: `${25 + Math.sin(i * Math.PI / 2) * 150}%`,
                          }}
                          animate={{
                            scale: [0, 1, 0],
                            opacity: [0, 0.8, 0]
                          }}
                          transition={{
                            duration: 1.5,
                            repeat: Number.POSITIVE_INFINITY,
                            delay: i * 0.2
                          }}
                        />
                      ))}
                    </div>
                  )}
                </motion.button>
              )
            })}
          </div>
        </div>
      </div>
    </section>
  )
}
