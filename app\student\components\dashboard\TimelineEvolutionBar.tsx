'use client'

import { motion } from 'framer-motion'
import { Layers } from 'lucide-react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { TimelineComponentProps, EvolutionStage, TimelineProgress } from './types'

export default function TimelineEvolutionBar({ 
  timelineStage, 
  playerData, 
  systemStatus 
}: TimelineComponentProps) {
  
  // Timeline evolution progress
  const getTimelineProgress = (): TimelineProgress => {
    const stages: EvolutionStage[] = ['dormant', 'awakening', 'harmonic', 'syntropic', 'ascended']
    const currentIndex = stages.indexOf(timelineStage)
    return {
      current: timelineStage,
      progress: ((currentIndex + 1) / stages.length) * 100,
      nextStage: stages[currentIndex + 1] || 'ascended'
    }
  }

  return (
    <Card className="lg:col-span-3 bg-black/40 border-indigo-500/30 backdrop-blur-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-indigo-400">
          <Layers className="w-5 h-5" />
          Timeline Evolution Progress
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-white/80">
              Current Stage: <span className="text-indigo-400 font-bold capitalize">{timelineStage}</span>
            </span>
            <span className="text-white/60">
              Next: <span className="text-cyan-400 capitalize">{getTimelineProgress().nextStage}</span>
            </span>
          </div>
          <div className="relative h-4 bg-space-dark/60 rounded-full overflow-hidden">
            <motion.div
              className="absolute inset-y-0 left-0 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${getTimelineProgress().progress}%` }}
              transition={{ duration: 2, ease: 'easeOut' }}
            />
          </div>
          <div className="flex justify-between text-xs text-white/60">
            <span>Dormant</span>
            <span>Awakening</span>
            <span>Harmonic</span>
            <span>Syntropic</span>
            <span>Ascended</span>
          </div>
          <div className="text-center pt-4">
            <Button className="bg-indigo-500/20 text-indigo-400 border border-indigo-500/30 hover:bg-indigo-500/30">
              🤝 Assist Your Timeline
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
