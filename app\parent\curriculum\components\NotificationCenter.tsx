'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Bell,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  Eye,
  Trash2
} from 'lucide-react'
import { useCurriculumPlanner } from './CurriculumPlannerProvider'

export function NotificationCenter() {
  const { state, dispatch } = useCurriculumPlanner()

  const handleMarkAsRead = (notificationId: string) => {
    dispatch({ type: 'MARK_NOTIFICATION_READ', payload: notificationId })
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'approval_needed': return AlertCircle
      case 'lesson_reminder': return Clock
      case 'progress_update': return CheckCircle
      case 'schedule_conflict': return XCircle
      default: return Bell
    }
  }

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'approval_needed': return 'text-yellow-400'
      case 'lesson_reminder': return 'text-blue-400'
      case 'progress_update': return 'text-green-400'
      case 'schedule_conflict': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  return (
    <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
      <CardHeader>
        <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
          <Bell className="w-5 h-5 text-cyan-400" />
          Notifications
          {state.notifications.filter(n => !n.read).length > 0 && (
            <Badge className="bg-red-500 text-white">
              {state.notifications.filter(n => !n.read).length}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {state.notifications.length > 0 ? (
          <div className="space-y-3">
            {state.notifications.map((notification) => {
              const Icon = getNotificationIcon(notification.type)
              const iconColor = getNotificationColor(notification.type)
              
              return (
                <motion.div
                  key={notification.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`p-3 rounded-lg border transition-all ${
                    notification.read 
                      ? 'bg-gray-800/30 border-gray-700/30' 
                      : 'bg-gray-800/50 border-gray-600/50'
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <Icon className={`w-5 h-5 mt-0.5 ${iconColor}`} />
                    <div className="flex-1 min-w-0">
                      <h4 className={`font-medium text-sm ${
                        notification.read ? 'text-gray-300' : 'text-white'
                      }`}>
                        {notification.title}
                      </h4>
                      <p className={`text-xs mt-1 ${
                        notification.read ? 'text-gray-500' : 'text-gray-400'
                      }`}>
                        {notification.message}
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        <span className="text-xs text-gray-500">
                          {notification.createdAt.toLocaleDateString()}
                        </span>
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${
                            notification.priority === 'high' ? 'border-red-500/30 text-red-400' :
                            notification.priority === 'medium' ? 'border-yellow-500/30 text-yellow-400' :
                            'border-gray-500/30 text-gray-400'
                          }`}
                        >
                          {notification.priority}
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      {!notification.read && (
                        <Button
                          size="sm"
                          variant="ghost"
                          className="text-gray-400 hover:text-white p-1"
                          onClick={() => handleMarkAsRead(notification.id)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                      )}
                      <Button
                        size="sm"
                        variant="ghost"
                        className="text-gray-400 hover:text-red-400 p-1"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  
                  {notification.actions && notification.actions.length > 0 && (
                    <div className="flex gap-2 mt-3 pt-2 border-t border-gray-700/30">
                      {notification.actions.map((action, index) => (
                        <Button
                          key={index}
                          size="sm"
                          variant="outline"
                          className="text-xs border-gray-600 text-gray-300 hover:bg-gray-700/50"
                        >
                          {action.label}
                        </Button>
                      ))}
                    </div>
                  )}
                </motion.div>
              )
            })}
          </div>
        ) : (
          <div className="text-center py-8">
            <Bell className="w-12 h-12 text-gray-600 mx-auto mb-3" />
            <h3 className="text-lg font-semibold text-gray-400 mb-2">No notifications</h3>
            <p className="text-gray-500 text-sm">You&apos;re all caught up!</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
