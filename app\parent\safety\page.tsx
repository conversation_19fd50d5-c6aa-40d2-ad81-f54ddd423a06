'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import {
  Shield,
  Eye,
  Clock,
  MessageSquare,
  AlertTriangle,
  CheckCircle,
  Settings,
  Lock,
  Filter
} from 'lucide-react'

export default function SafetyPage() {
  const [safetySettings, setSafetySettings] = useState({
    contentFiltering: true,
    chatMonitoring: true,
    timeRestrictions: true,
    activityReports: true,
    emergencyAlerts: true
  })

  const updateSetting = (key: string, value: boolean) => {
    setSafetySettings(prev => ({ ...prev, [key]: value }))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-white font-space-grotesk">
            Safety & Controls
          </h1>
          <p className="text-gray-400 mt-1">
            Manage your child&apos;s safety settings and monitoring preferences
          </p>
        </div>
        <Badge variant="outline" className="text-green-400 border-green-500/30">
          <CheckCircle className="w-3 h-3 mr-1" />
          All Systems Active
        </Badge>
      </motion.div>

      {/* Safety Settings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardHeader>
            <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
              <Settings className="w-5 h-5 text-blue-400" />
              Safety Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {[
              {
                key: 'contentFiltering',
                title: 'Content Filtering',
                description: 'Filter inappropriate content and ensure age-appropriate material',
                icon: Filter,
                color: 'text-blue-400'
              },
              {
                key: 'chatMonitoring',
                title: 'Chat Monitoring',
                description: 'Monitor chat interactions for safety and appropriate behavior',
                icon: MessageSquare,
                color: 'text-green-400'
              },
              {
                key: 'timeRestrictions',
                title: 'Time Restrictions',
                description: 'Set daily time limits and bedtime restrictions',
                icon: Clock,
                color: 'text-yellow-400'
              },
              {
                key: 'activityReports',
                title: 'Activity Reports',
                description: 'Receive regular reports on your child\'s activities',
                icon: Eye,
                color: 'text-purple-400'
              },
              {
                key: 'emergencyAlerts',
                title: 'Emergency Alerts',
                description: 'Get immediate notifications for safety concerns',
                icon: AlertTriangle,
                color: 'text-red-400'
              }
            ].map((setting, index) => (
              <motion.div
                key={setting.key}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 + index * 0.05 }}
                className="flex items-center justify-between p-4 bg-gray-800/30 rounded-lg"
              >
                <div className="flex items-center gap-4">
                  <setting.icon className={`w-6 h-6 ${setting.color}`} />
                  <div>
                    <h3 className="text-white font-medium">{setting.title}</h3>
                    <p className="text-gray-400 text-sm">{setting.description}</p>
                  </div>
                </div>
                <Switch
                  checked={safetySettings[setting.key as keyof typeof safetySettings]}
                  onCheckedChange={(checked) => updateSetting(setting.key, checked)}
                />
              </motion.div>
            ))}
          </CardContent>
        </Card>
      </motion.div>

      {/* Safety Status */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardHeader>
            <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
              <Shield className="w-5 h-5 text-green-400" />
              Safety Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-green-500/10 rounded-lg border border-green-500/20">
                <CheckCircle className="w-8 h-8 text-green-400 mx-auto mb-2" />
                <h3 className="text-white font-medium">Content Safe</h3>
                <p className="text-gray-400 text-sm">All content filtered</p>
              </div>
              <div className="text-center p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
                <Eye className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                <h3 className="text-white font-medium">Monitored</h3>
                <p className="text-gray-400 text-sm">Activity tracked</p>
              </div>
              <div className="text-center p-4 bg-purple-500/10 rounded-lg border border-purple-500/20">
                <Lock className="w-8 h-8 text-purple-400 mx-auto mb-2" />
                <h3 className="text-white font-medium">Secure</h3>
                <p className="text-gray-400 text-sm">Privacy protected</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
