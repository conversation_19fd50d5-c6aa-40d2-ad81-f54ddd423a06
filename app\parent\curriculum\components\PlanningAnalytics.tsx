'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  BarChart3,
  TrendingUp,
  Target,
  Clock,
  Star,
  Calendar,
  Award,
  Brain,
  Zap
} from 'lucide-react'
import { useCurriculumPlanner } from './CurriculumPlannerProvider'

export function PlanningAnalytics() {
  const { state: _state, getSelectedChild } = useCurriculumPlanner()
  const _selectedChild = getSelectedChild()

  // Mock analytics data
  const analyticsData = {
    weeklyProgress: {
      planned: 12,
      completed: 8,
      pending: 3,
      modified: 1
    },
    subjectDistribution: [
      { subject: 'Quantum Physics', percentage: 30, color: 'bg-blue-500' },
      { subject: 'Critical Thinking', percentage: 25, color: 'bg-purple-500' },
      { subject: 'DNA Biology', percentage: 20, color: 'bg-green-500' },
      { subject: 'Cybersecurity', percentage: 15, color: 'bg-red-500' },
      { subject: 'Emotional Intelligence', percentage: 10, color: 'bg-pink-500' }
    ],
    skillProgress: [
      { skill: 'Critical Thinking', current: 85, target: 90, growth: '+5' },
      { skill: 'Problem Solving', current: 78, target: 85, growth: '+8' },
      { skill: 'Creativity', current: 92, target: 95, growth: '+3' },
      { skill: 'Communication', current: 88, target: 90, growth: '+6' }
    ],
    engagementMetrics: {
      averageRating: 4.6,
      completionRate: 87,
      timeOnTask: 145, // minutes per week
      streakDays: 15
    }
  }

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-cyan-500/20 rounded-lg">
                <Target className="w-5 h-5 text-cyan-400" />
              </div>
              <div>
                <div className="text-2xl font-bold text-white">
                  {analyticsData.weeklyProgress.completed}
                </div>
                <div className="text-xs text-gray-400">Lessons Completed</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-500/20 rounded-lg">
                <TrendingUp className="w-5 h-5 text-green-400" />
              </div>
              <div>
                <div className="text-2xl font-bold text-white">
                  {analyticsData.engagementMetrics.completionRate}%
                </div>
                <div className="text-xs text-gray-400">Completion Rate</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-500/20 rounded-lg">
                <Clock className="w-5 h-5 text-yellow-400" />
              </div>
              <div>
                <div className="text-2xl font-bold text-white">
                  {Math.round(analyticsData.engagementMetrics.timeOnTask / 60)}h
                </div>
                <div className="text-xs text-gray-400">Weekly Time</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-500/20 rounded-lg">
                <Star className="w-5 h-5 text-purple-400" />
              </div>
              <div>
                <div className="text-2xl font-bold text-white">
                  {analyticsData.engagementMetrics.averageRating}
                </div>
                <div className="text-xs text-gray-400">Avg Rating</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Weekly Progress */}
        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardHeader>
            <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
              <Calendar className="w-5 h-5 text-cyan-400" />
              Weekly Progress
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-gray-800/30 rounded-lg">
                <div className="text-2xl font-bold text-cyan-400">
                  {analyticsData.weeklyProgress.planned}
                </div>
                <div className="text-xs text-gray-400">Planned</div>
              </div>
              <div className="text-center p-3 bg-gray-800/30 rounded-lg">
                <div className="text-2xl font-bold text-green-400">
                  {analyticsData.weeklyProgress.completed}
                </div>
                <div className="text-xs text-gray-400">Completed</div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Progress</span>
                <span className="text-white">
                  {Math.round((analyticsData.weeklyProgress.completed / analyticsData.weeklyProgress.planned) * 100)}%
                </span>
              </div>
              <Progress 
                value={(analyticsData.weeklyProgress.completed / analyticsData.weeklyProgress.planned) * 100}
                className="h-2"
              />
            </div>

            <div className="flex gap-2 text-xs">
              <Badge variant="outline" className="border-yellow-500/30 text-yellow-400">
                {analyticsData.weeklyProgress.pending} Pending
              </Badge>
              <Badge variant="outline" className="border-blue-500/30 text-blue-400">
                {analyticsData.weeklyProgress.modified} Modified
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Subject Distribution */}
        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardHeader>
            <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-purple-400" />
              Subject Distribution
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {analyticsData.subjectDistribution.map((item, index) => (
              <div key={index} className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">{item.subject}</span>
                  <span className="text-white">{item.percentage}%</span>
                </div>
                <div className="w-full bg-gray-800 rounded-full h-2">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${item.percentage}%` }}
                    transition={{ delay: index * 0.1, duration: 0.8 }}
                    className={`h-2 rounded-full ${item.color}`}
                  />
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* Skill Progress */}
      <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
        <CardHeader>
          <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
            <Brain className="w-5 h-5 text-green-400" />
            Skill Development Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {analyticsData.skillProgress.map((skill, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="space-y-3"
              >
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium">{skill.skill}</span>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="border-green-500/30 text-green-400 text-xs">
                      {skill.growth}
                    </Badge>
                    <span className="text-sm text-gray-400">
                      {skill.current}/{skill.target}
                    </span>
                  </div>
                </div>
                
                <div className="space-y-1">
                  <Progress value={skill.current} className="h-2" />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Current: {skill.current}%</span>
                    <span>Target: {skill.target}%</span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Engagement Insights */}
      <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
        <CardHeader>
          <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
            <Zap className="w-5 h-5 text-yellow-400" />
            Engagement Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-lg border border-yellow-500/20">
              <Award className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white mb-1">
                {analyticsData.engagementMetrics.streakDays}
              </div>
              <div className="text-sm text-gray-400">Day Learning Streak</div>
            </div>

            <div className="text-center p-4 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg border border-green-500/20">
              <TrendingUp className="w-8 h-8 text-green-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white mb-1">87%</div>
              <div className="text-sm text-gray-400">Above Peer Average</div>
            </div>

            <div className="text-center p-4 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg border border-purple-500/20">
              <Star className="w-8 h-8 text-purple-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white mb-1">4.6/5</div>
              <div className="text-sm text-gray-400">Lesson Satisfaction</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
