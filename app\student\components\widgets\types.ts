// Widget component types and interfaces

export interface StudentStats {
  level: number
  xp: number
  xpToNextLevel: number
  totalXP: number
  streak: number
  lessonsCompleted: number
  averageScore: number
  timeSpent: number // in minutes
  rank: string
  badges: number
}

export interface PerformanceMetrics {
  weeklyProgress: number
  monthlyProgress: number
  accuracyRate: number
  completionRate: number
  engagementScore: number
  learningVelocity: number
}

export interface ActivityData {
  date: string
  xpEarned: number
  lessonsCompleted: number
  timeSpent: number
  score: number
}

export interface WidgetProps {
  className?: string
  size?: 'sm' | 'md' | 'lg'
  variant?: 'compact' | 'detailed'
}

export interface StatsWidgetProps extends WidgetProps {
  stats: StudentStats
  showAnimation?: boolean
}

export interface ProgressWidgetProps extends WidgetProps {
  current: number
  target: number
  label: string
  color?: string
  showPercentage?: boolean
}

export interface IndicatorWidgetProps extends WidgetProps {
  value: number
  label: string
  trend?: 'up' | 'down' | 'stable'
  color?: string
  icon?: React.ReactNode
}

export interface ActivityWidgetProps extends WidgetProps {
  data: ActivityData[]
  timeframe: 'week' | 'month'
}

export interface StreakWidgetProps extends WidgetProps {
  current: number
  best: number
  target?: number
}

export interface LevelProgressWidgetProps extends WidgetProps {
  currentLevel: number
  currentXP: number
  xpToNext: number
  totalXP: number
}

export interface PerformanceIndicatorProps extends WidgetProps {
  metrics: PerformanceMetrics
}
