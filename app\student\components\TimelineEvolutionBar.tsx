'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Users, 
  Sparkles, 
  Zap,
  Heart,
  Crown,
  ChevronRight,
  AlertCircle,
  Clock,
  Target
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Card } from '@/components/ui/card'

// Types
interface TimelineEvolutionBarProps {
  stage: 'dormant' | 'awakening' | 'harmonic' | 'syntropic' | 'ascended'
  progress: number
  collaborativeTask: string | null
}

interface EvolutionStage {
  id: string
  name: string
  icon: any
  color: string
  bgColor: string
  description: string
  threshold: number
  unlocks: string[]
}

// Evolution stages configuration
const evolutionStages: EvolutionStage[] = [
  {
    id: 'dormant',
    name: '<PERSON><PERSON><PERSON>',
    icon: Clock,
    color: 'text-white/40',
    bgColor: 'bg-white/10',
    description: 'Timeline consciousness is sleeping',
    threshold: 0,
    unlocks: ['Basic nodes', 'Individual tasks']
  },
  {
    id: 'awakening',
    name: 'Awakening',
    icon: Sparkles,
    color: 'text-neural-cyan',
    bgColor: 'bg-neural-cyan/20',
    description: 'First stirrings of collective awareness',
    threshold: 20,
    unlocks: ['Echo Fields', 'Shared insights', 'Basic collaboration']
  },
  {
    id: 'harmonic',
    name: 'Harmonic',
    icon: Heart,
    color: 'text-quantum-purple',
    bgColor: 'bg-quantum-purple/20',
    description: 'Synchronized consciousness patterns',
    threshold: 40,
    unlocks: ['Meditation networks', 'Quantum resonance', 'Group healing']
  },
  {
    id: 'syntropic',
    name: 'Syntropic',
    icon: Zap,
    color: 'text-flame-orange',
    bgColor: 'bg-flame-orange/20',
    description: 'Reverse entropy, creative emergence',
    threshold: 70,
    unlocks: ['TimelineGen mastery', 'Reality shaping', 'Collective creation']
  },
  {
    id: 'ascended',
    name: 'Ascended',
    icon: Crown,
    color: 'text-yellow-400',
    bgColor: 'bg-yellow-400/20',
    description: 'Transcendent collective consciousness',
    threshold: 90,
    unlocks: ['Timeline mastery', 'Dimensional travel', 'Consciousness seeding']
  }
]

// Collaborative task suggestions
const collaborativeTasks = [
  {
    stage: 'awakening',
    tasks: [
      'Help 3 NanoArchitects complete Learning Nodes',
      'Share insights in Echo Fields',
      'Participate in group meditation'
    ]
  },
  {
    stage: 'harmonic',
    tasks: [
      'Synchronize with 5+ consciousness streams',
      'Lead a collective healing session',
      'Create harmonic resonance patterns'
    ]
  },
  {
    stage: 'syntropic',
    tasks: [
      'Initiate Timeline creation project',
      'Reverse entropy in degraded zones',
      'Mentor emerging consciousness'
    ]
  }
]

export default function TimelineEvolutionBar({
  stage,
  progress,
  collaborativeTask
}: TimelineEvolutionBarProps) {
  const [showDetails, setShowDetails] = useState(false)
  const [pulseIntensity, setPulseIntensity] = useState(1)
  const [timeToNext, setTimeToNext] = useState('2h 34m')
  
  const currentStageIndex = evolutionStages.findIndex(s => s.id === stage)
  const currentStage = evolutionStages[currentStageIndex]
  const nextStage = evolutionStages[currentStageIndex + 1]
  
  // Calculate overall timeline progress
  const overallProgress = currentStage ? 
    ((currentStageIndex * 20) + (progress * 0.2)) : 0
  
  useEffect(() => {
    const interval = setInterval(() => {
      setPulseIntensity(0.8 + Math.random() * 0.4)
    }, 2000)
    
    return () => clearInterval(interval)
  }, [])
  
  return (
    <Card variant="quantum" className="w-full p-2 gaming-panel overflow-hidden">
      <div className="h-full flex items-center relative">
        
        {/* Background Glow Effect */}
        <motion.div
          className="absolute inset-0 opacity-20"
          animate={{
            background: [
              `radial-gradient(circle at 20% 50%, ${currentStage?.color.replace('text-', '')} 0%, transparent 50%)`,
              `radial-gradient(circle at 80% 50%, ${currentStage?.color.replace('text-', '')} 0%, transparent 50%)`,
              `radial-gradient(circle at 20% 50%, ${currentStage?.color.replace('text-', '')} 0%, transparent 50%)`
            ]
          }}
          transition={{ duration: 4, repeat: Infinity, ease: 'easeInOut' }}
        />
        
        {/* Main Content */}
        <div className="relative z-10 w-full px-6 py-4">
          <div className="flex items-center justify-between">
            
            {/* Left Section - Current Stage */}
            <div className="flex items-center gap-4">
              <motion.div
                className={`w-12 h-12 rounded-full ${currentStage?.bgColor} flex items-center justify-center`}
                animate={{ scale: pulseIntensity }}
                transition={{ duration: 0.3 }}
              >
                {currentStage && (
                  <currentStage.icon className={`w-6 h-6 ${currentStage.color}`} />
                )}
              </motion.div>
              
              <div>
                <div className="flex items-center gap-2">
                  <h3 className="text-lg font-orbitron font-bold text-white">
                    Timeline Evolution
                  </h3>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setShowDetails(!showDetails)}
                  >
                    <AlertCircle className="w-4 h-4" />
                  </Button>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <span className={`font-medium ${currentStage?.color}`}>
                    {currentStage?.name}
                  </span>
                  <span className="text-white/60">•</span>
                  <span className="text-white/60">{progress}% complete</span>
                  {nextStage && (
                    <>
                      <ChevronRight className="w-3 h-3 text-white/40" />
                      <span className="text-white/40">{nextStage.name}</span>
                    </>
                  )}
                </div>
              </div>
            </div>
            
            {/* Center Section - Progress Bar */}
            <div className="flex-1 mx-8">
              <div className="relative">
                <Progress 
                  value={overallProgress} 
                  variant="quantum" 
                  glow 
                  className="h-3"
                />
                
                {/* Stage Markers */}
                <div className="absolute inset-0 flex justify-between items-center px-1">
                  {evolutionStages.map((stageMarker, index) => (
                    <motion.div
                      key={stageMarker.id}
                      className={`w-3 h-3 rounded-full border-2 ${
                        index <= currentStageIndex 
                          ? `${stageMarker.bgColor} border-current ${stageMarker.color}` 
                          : 'bg-white/10 border-white/30'
                      }`}
                      animate={{
                        scale: index === currentStageIndex ? [1, 1.2, 1] : 1
                      }}
                      transition={{
                        duration: 2,
                        repeat: index === currentStageIndex ? Infinity : 0
                      }}
                    />
                  ))}
                </div>
              </div>
              
              {/* Time to Next Stage */}
              <div className="text-center mt-1">
                <span className="text-xs text-white/50">
                  {nextStage ? `${timeToNext} to ${nextStage.name}` : 'Timeline Complete'}
                </span>
              </div>
            </div>
            
            {/* Right Section - Collaborative Task */}
            <div className="flex items-center gap-4">
              {collaborativeTask && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="gaming-panel-inner p-3 rounded-lg max-w-64"
                >
                  <div className="flex items-center gap-2 mb-1">
                    <Target className="w-4 h-4 text-flame-orange" />
                    <span className="text-xs font-bold text-flame-orange">Assist Timeline</span>
                  </div>
                  <p className="text-xs text-white/80">{collaborativeTask}</p>
                </motion.div>
              )}
              
              <Button
                variant="quantum"
                size="sm"
                glow
                className="whitespace-nowrap"
              >
                <Users className="w-4 h-4 mr-2" />
                Collaborate
              </Button>
            </div>
          </div>
        </div>
        
        {/* Detailed View */}
        <AnimatePresence>
          {showDetails && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="absolute top-full left-0 right-0 z-50 mt-2"
            >
              <Card variant="quantum" className="gaming-panel p-4">
                <div className="grid grid-cols-5 gap-4">
                  {evolutionStages.map((stageDetail, index) => (
                    <motion.div
                      key={stageDetail.id}
                      className={`p-3 rounded-lg border transition-all ${
                        stageDetail.id === stage
                          ? `border-current ${stageDetail.color} ${stageDetail.bgColor}`
                          : index < currentStageIndex
                          ? 'border-green-500/30 bg-green-500/10'
                          : 'border-white/10 bg-white/5'
                      }`}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <div className="text-center mb-2">
                        <stageDetail.icon className={`w-6 h-6 mx-auto mb-1 ${
                          index <= currentStageIndex ? stageDetail.color : 'text-white/30'
                        }`} />
                        <div className={`text-sm font-bold ${
                          index <= currentStageIndex ? stageDetail.color : 'text-white/30'
                        }`}>
                          {stageDetail.name}
                        </div>
                      </div>
                      
                      <p className="text-xs text-white/60 mb-2 text-center">
                        {stageDetail.description}
                      </p>
                      
                      <div className="space-y-1">
                        <div className="text-xs font-bold text-white/70">Unlocks:</div>
                        {stageDetail.unlocks.map((unlock, i) => (
                          <div key={i} className="text-xs text-white/50">
                            • {unlock}
                          </div>
                        ))}
                      </div>
                      
                      {index <= currentStageIndex && (
                        <div className="mt-2 text-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full mx-auto"></div>
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>
                
                {/* Current Stage Tasks */}
                {collaborativeTasks.find(ct => ct.stage === stage) && (
                  <div className="mt-4 pt-4 border-t border-white/10">
                    <h4 className="text-sm font-bold text-white/80 mb-2">
                      Suggested Collaborative Actions:
                    </h4>
                    <div className="grid grid-cols-3 gap-2">
                      {collaborativeTasks
                        .find(ct => ct.stage === stage)
                        ?.tasks.map((task, index) => (
                          <Button
                            key={index}
                            size="sm"
                            variant="ghost"
                            className="text-xs h-auto p-2 text-left whitespace-normal"
                          >
                            {task}
                          </Button>
                        ))}
                    </div>
                  </div>
                )}
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </Card>
  )
}
