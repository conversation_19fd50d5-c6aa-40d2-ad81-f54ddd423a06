/**
 * Web Worker Management System for NanoHero Platform
 * Moves heavy computations off the main thread for better performance
 */

import { useMemo } from 'react'

export type WorkerTaskType = 
  | 'geometry-generation'
  | 'particle-calculation'
  | 'noise-generation'
  | 'data-processing'
  | 'search-filter'
  | 'image-processing'
  | 'math-computation'

export interface WorkerTask {
  id: string
  type: WorkerTaskType
  data: any
  priority: 'low' | 'medium' | 'high'
  timeout?: number
}

export interface WorkerResult {
  id: string
  success: boolean
  data?: any
  error?: string
  executionTime: number
}

/**
 * Web Worker Pool Manager
 */
export class WebWorkerPool {
  private workers: Worker[] = []
  private availableWorkers: Worker[] = []
  private taskQueue: WorkerTask[] = []
  private activeTasks = new Map<string, { worker: Worker; resolve: Function; reject: Function }>()
  private maxWorkers: number
  private workerScript: string

  constructor(maxWorkers: number = navigator.hardwareConcurrency || 4) {
    this.maxWorkers = Math.min(maxWorkers, 8) // Cap at 8 workers
    this.workerScript = this.createWorkerScript()
    this.initializeWorkers()
  }

  private createWorkerScript(): string {
    return `
      // Web Worker script for heavy computations
      self.onmessage = function(e) {
        const { id, type, data } = e.data;
        const startTime = performance.now();
        
        try {
          let result;
          
          switch (type) {
            case 'geometry-generation':
              result = generateGeometry(data);
              break;
            case 'particle-calculation':
              result = calculateParticles(data);
              break;
            case 'noise-generation':
              result = generateNoise(data);
              break;
            case 'data-processing':
              result = processData(data);
              break;
            case 'search-filter':
              result = searchAndFilter(data);
              break;
            case 'image-processing':
              result = processImage(data);
              break;
            case 'math-computation':
              result = performMathComputation(data);
              break;
            default:
              throw new Error('Unknown task type: ' + type);
          }
          
          const executionTime = performance.now() - startTime;
          
          self.postMessage({
            id,
            success: true,
            data: result,
            executionTime
          });
        } catch (error) {
          const executionTime = performance.now() - startTime;
          
          self.postMessage({
            id,
            success: false,
            error: error.message,
            executionTime
          });
        }
      };
      
      // Geometry generation functions
      function generateGeometry(params) {
        const { type, resolution, seed } = params;
        
        switch (type) {
          case 'terrain':
            return generateTerrain(resolution, seed);
          case 'particles':
            return generateParticlePositions(params);
          default:
            throw new Error('Unknown geometry type');
        }
      }
      
      function generateTerrain(resolution, seed) {
        const positions = new Float32Array(resolution * resolution * 3);
        const indices = [];
        
        // Simple noise function
        function noise(x, y, seed) {
          const n = Math.sin(x * 0.1 + seed) * Math.cos(y * 0.1 + seed);
          return (n + 1) / 2; // Normalize to 0-1
        }
        
        // Generate vertices
        for (let x = 0; x < resolution; x++) {
          for (let z = 0; z < resolution; z++) {
            const i = (x * resolution + z) * 3;
            const height = noise(x, z, seed) * 5;
            
            positions[i] = x - resolution / 2;
            positions[i + 1] = height;
            positions[i + 2] = z - resolution / 2;
          }
        }
        
        // Generate indices
        for (let x = 0; x < resolution - 1; x++) {
          for (let z = 0; z < resolution - 1; z++) {
            const a = x * resolution + z;
            const b = x * resolution + z + 1;
            const c = (x + 1) * resolution + z;
            const d = (x + 1) * resolution + z + 1;
            
            indices.push(a, b, c, b, d, c);
          }
        }
        
        return { positions, indices };
      }
      
      function generateParticlePositions(params) {
        const { count, bounds, distribution } = params;
        const positions = new Float32Array(count * 3);
        
        for (let i = 0; i < count; i++) {
          const i3 = i * 3;
          
          switch (distribution) {
            case 'random':
              positions[i3] = (Math.random() - 0.5) * bounds.x;
              positions[i3 + 1] = (Math.random() - 0.5) * bounds.y;
              positions[i3 + 2] = (Math.random() - 0.5) * bounds.z;
              break;
            case 'sphere':
              const phi = Math.random() * Math.PI * 2;
              const costheta = Math.random() * 2 - 1;
              const u = Math.random();
              const theta = Math.acos(costheta);
              const r = bounds.radius * Math.cbrt(u);
              
              positions[i3] = r * Math.sin(theta) * Math.cos(phi);
              positions[i3 + 1] = r * Math.sin(theta) * Math.sin(phi);
              positions[i3 + 2] = r * Math.cos(theta);
              break;
          }
        }
        
        return positions;
      }
      
      // Particle calculation functions
      function calculateParticles(params) {
        const { positions, velocities, forces, deltaTime } = params;
        const newPositions = new Float32Array(positions);
        const newVelocities = new Float32Array(velocities);
        
        for (let i = 0; i < positions.length; i += 3) {
          // Apply forces
          newVelocities[i] += forces[i] * deltaTime;
          newVelocities[i + 1] += forces[i + 1] * deltaTime;
          newVelocities[i + 2] += forces[i + 2] * deltaTime;
          
          // Update positions
          newPositions[i] += newVelocities[i] * deltaTime;
          newPositions[i + 1] += newVelocities[i + 1] * deltaTime;
          newPositions[i + 2] += newVelocities[i + 2] * deltaTime;
        }
        
        return { positions: newPositions, velocities: newVelocities };
      }
      
      // Noise generation functions
      function generateNoise(params) {
        const { width, height, scale, octaves, seed } = params;
        const noise = new Float32Array(width * height);
        
        for (let x = 0; x < width; x++) {
          for (let y = 0; y < height; y++) {
            const i = x * height + y;
            noise[i] = perlinNoise(x * scale, y * scale, octaves, seed);
          }
        }
        
        return noise;
      }
      
      function perlinNoise(x, y, octaves, seed) {
        let value = 0;
        let amplitude = 1;
        let frequency = 1;
        
        for (let i = 0; i < octaves; i++) {
          value += simpleNoise(x * frequency + seed, y * frequency + seed) * amplitude;
          amplitude *= 0.5;
          frequency *= 2;
        }
        
        return value;
      }
      
      function simpleNoise(x, y) {
        const n = Math.sin(x * 12.9898 + y * 78.233) * 43758.5453;
        return (n - Math.floor(n)) * 2 - 1;
      }
      
      // Data processing functions
      function processData(params) {
        const { data, operation } = params;
        
        switch (operation) {
          case 'sort':
            return data.sort((a, b) => a - b);
          case 'filter':
            return data.filter(item => item > params.threshold);
          case 'map':
            return data.map(item => item * params.multiplier);
          case 'reduce':
            return data.reduce((acc, item) => acc + item, 0);
          default:
            return data;
        }
      }
      
      // Search and filter functions
      function searchAndFilter(params) {
        const { items, query, fields } = params;
        const lowerQuery = query.toLowerCase();
        
        return items.filter(item => {
          return fields.some(field => {
            const value = item[field];
            return value && value.toString().toLowerCase().includes(lowerQuery);
          });
        });
      }
      
      // Image processing functions
      function processImage(params) {
        const { imageData, operation } = params;
        const { data, width, height } = imageData;
        const result = new Uint8ClampedArray(data);
        
        switch (operation) {
          case 'grayscale':
            for (let i = 0; i < data.length; i += 4) {
              const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
              result[i] = gray;
              result[i + 1] = gray;
              result[i + 2] = gray;
            }
            break;
          case 'blur':
            // Simple box blur
            const radius = params.radius || 1;
            for (let y = 0; y < height; y++) {
              for (let x = 0; x < width; x++) {
                let r = 0, g = 0, b = 0, count = 0;
                
                for (let dy = -radius; dy <= radius; dy++) {
                  for (let dx = -radius; dx <= radius; dx++) {
                    const nx = x + dx;
                    const ny = y + dy;
                    
                    if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                      const i = (ny * width + nx) * 4;
                      r += data[i];
                      g += data[i + 1];
                      b += data[i + 2];
                      count++;
                    }
                  }
                }
                
                const i = (y * width + x) * 4;
                result[i] = r / count;
                result[i + 1] = g / count;
                result[i + 2] = b / count;
              }
            }
            break;
        }
        
        return { data: result, width, height };
      }
      
      // Math computation functions
      function performMathComputation(params) {
        const { operation, data } = params;
        
        switch (operation) {
          case 'matrix-multiply':
            return multiplyMatrices(data.a, data.b);
          case 'fft':
            return fastFourierTransform(data.signal);
          case 'statistics':
            return calculateStatistics(data.values);
          default:
            throw new Error('Unknown math operation');
        }
      }
      
      function multiplyMatrices(a, b) {
        const result = [];
        for (let i = 0; i < a.length; i++) {
          result[i] = [];
          for (let j = 0; j < b[0].length; j++) {
            let sum = 0;
            for (let k = 0; k < b.length; k++) {
              sum += a[i][k] * b[k][j];
            }
            result[i][j] = sum;
          }
        }
        return result;
      }
      
      function calculateStatistics(values) {
        const n = values.length;
        const sum = values.reduce((a, b) => a + b, 0);
        const mean = sum / n;
        const variance = values.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / n;
        const stdDev = Math.sqrt(variance);
        
        return { mean, variance, stdDev, min: Math.min(...values), max: Math.max(...values) };
      }
    `;
  }

  private initializeWorkers() {
    const blob = new Blob([this.workerScript], { type: 'application/javascript' })
    const workerUrl = URL.createObjectURL(blob)

    for (let i = 0; i < this.maxWorkers; i++) {
      const worker = new Worker(workerUrl)
      worker.onmessage = this.handleWorkerMessage.bind(this)
      worker.onerror = this.handleWorkerError.bind(this)
      
      this.workers.push(worker)
      this.availableWorkers.push(worker)
    }

    URL.revokeObjectURL(workerUrl)
  }

  private handleWorkerMessage(event: MessageEvent) {
    const result: WorkerResult = event.data
    const task = this.activeTasks.get(result.id)
    
    if (task) {
      this.activeTasks.delete(result.id)
      this.availableWorkers.push(task.worker)
      
      if (result.success) {
        task.resolve(result)
      } else {
        task.reject(new Error(result.error))
      }
      
      // Process next task in queue
      this.processNextTask()
    }
  }

  private handleWorkerError(error: ErrorEvent) {
    console.error('Worker error:', error)
  }

  private processNextTask() {
    if (this.taskQueue.length === 0 || this.availableWorkers.length === 0) {
      return
    }

    // Sort queue by priority
    this.taskQueue.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })

    const task = this.taskQueue.shift()!
    const worker = this.availableWorkers.pop()!

    const promise = new Promise<WorkerResult>((resolve, reject) => {
      this.activeTasks.set(task.id, { worker, resolve, reject })
      
      // Set timeout if specified
      if (task.timeout) {
        setTimeout(() => {
          if (this.activeTasks.has(task.id)) {
            this.activeTasks.delete(task.id)
            this.availableWorkers.push(worker)
            reject(new Error('Task timeout'))
          }
        }, task.timeout)
      }
    })

    worker.postMessage(task)
    return promise
  }

  public executeTask(task: Omit<WorkerTask, 'id'>): Promise<WorkerResult> {
    const fullTask: WorkerTask = {
      ...task,
      id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }

    return new Promise((resolve, reject) => {
      this.taskQueue.push(fullTask)
      
      // Store resolve/reject for when task completes
      const processTask = () => {
        if (this.availableWorkers.length > 0) {
          this.processNextTask()?.then(resolve).catch(reject)
        } else {
          // Task will be processed when a worker becomes available
          const checkInterval = setInterval(() => {
            if (this.activeTasks.has(fullTask.id)) {
              clearInterval(checkInterval)
            }
          }, 100)
        }
      }

      processTask()
    })
  }

  public getStats() {
    return {
      totalWorkers: this.workers.length,
      availableWorkers: this.availableWorkers.length,
      activeTasks: this.activeTasks.size,
      queuedTasks: this.taskQueue.length
    }
  }

  public dispose() {
    this.workers.forEach(worker => worker.terminate())
    this.workers = []
    this.availableWorkers = []
    this.taskQueue = []
    this.activeTasks.clear()
  }
}

// Global worker pool instance
export const workerPool = new WebWorkerPool()

/**
 * Convenience functions for common tasks
 */
export const webWorkerTasks = {
  generateTerrain: (resolution: number, seed: number) =>
    workerPool.executeTask({
      type: 'geometry-generation',
      data: { type: 'terrain', resolution, seed },
      priority: 'medium'
    }),

  generateParticles: (count: number, bounds: any, distribution: string = 'random') =>
    workerPool.executeTask({
      type: 'geometry-generation',
      data: { type: 'particles', count, bounds, distribution },
      priority: 'high'
    }),

  calculateParticlePhysics: (positions: Float32Array, velocities: Float32Array, forces: Float32Array, deltaTime: number) =>
    workerPool.executeTask({
      type: 'particle-calculation',
      data: { positions, velocities, forces, deltaTime },
      priority: 'high'
    }),

  generateNoise: (width: number, height: number, scale: number = 0.1, octaves: number = 4, seed: number = 0) =>
    workerPool.executeTask({
      type: 'noise-generation',
      data: { width, height, scale, octaves, seed },
      priority: 'medium'
    }),

  searchData: (items: any[], query: string, fields: string[]) =>
    workerPool.executeTask({
      type: 'search-filter',
      data: { items, query, fields },
      priority: 'high'
    }),

  processImage: (imageData: ImageData, operation: string, params?: any) =>
    workerPool.executeTask({
      type: 'image-processing',
      data: { imageData, operation, ...params },
      priority: 'medium'
    })
}

/**
 * Debouncing utilities for user interactions
 */
export class DebouncedInteractionManager {
  private debounceTimers = new Map<string, NodeJS.Timeout>()
  private throttleTimers = new Map<string, number>()

  /**
   * Debounce a function call
   */
  debounce<T extends (...args: any[]) => any>(
    key: string,
    fn: T,
    delay: number = 300
  ): (...args: Parameters<T>) => void {
    return (...args: Parameters<T>) => {
      const existingTimer = this.debounceTimers.get(key)
      if (existingTimer) {
        clearTimeout(existingTimer)
      }

      const timer = setTimeout(() => {
        fn(...args)
        this.debounceTimers.delete(key)
      }, delay)

      this.debounceTimers.set(key, timer)
    }
  }

  /**
   * Throttle a function call
   */
  throttle<T extends (...args: any[]) => any>(
    key: string,
    fn: T,
    delay: number = 100
  ): (...args: Parameters<T>) => void {
    return (...args: Parameters<T>) => {
      const lastCall = this.throttleTimers.get(key) || 0
      const now = Date.now()

      if (now - lastCall >= delay) {
        fn(...args)
        this.throttleTimers.set(key, now)
      }
    }
  }

  /**
   * Clear all timers
   */
  clear() {
    this.debounceTimers.forEach(timer => clearTimeout(timer))
    this.debounceTimers.clear()
    this.throttleTimers.clear()
  }

  /**
   * Clear specific timer
   */
  clearKey(key: string) {
    const timer = this.debounceTimers.get(key)
    if (timer) {
      clearTimeout(timer)
      this.debounceTimers.delete(key)
    }
    this.throttleTimers.delete(key)
  }
}

export const interactionManager = new DebouncedInteractionManager()

/**
 * React hooks for debounced interactions
 */
export const useDebouncedCallback = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number = 300,
  deps: any[] = []
) => {
  const key = `callback_${Math.random().toString(36).substr(2, 9)}`

  return useMemo(() => {
    return interactionManager.debounce(key, callback, delay)
  }, [key, delay, ...deps])
}

export const useThrottledCallback = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number = 100,
  deps: any[] = []
) => {
  const key = `throttle_${Math.random().toString(36).substr(2, 9)}`

  return useMemo(() => {
    return interactionManager.throttle(key, callback, delay)
  }, [key, delay, ...deps])
}
