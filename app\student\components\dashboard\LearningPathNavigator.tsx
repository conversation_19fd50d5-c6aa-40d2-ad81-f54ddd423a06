'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Map, 
  Play, 
  Lock, 
  CheckCircle, 
  Clock, 
  Star, 
  Alert<PERSON>riangle,
  BookOpen,
  Code,
  Shield,
  Brain,
  Zap,
  Trophy,
  ChevronRight,
  Target,
  Sparkles
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { MetricsComponentProps, EvolutionTheme } from './types'

interface LearningPathNavigatorProps extends MetricsComponentProps {
  evolutionTheme: EvolutionTheme
}

interface LearningNode {
  id: string
  title: string
  description: string
  type: 'foundation' | 'core' | 'advanced' | 'specialization' | 'mastery'
  category: 'logic' | 'tech' | 'security' | 'emotional' | 'creative'
  status: 'locked' | 'available' | 'in-progress' | 'completed' | 'mastered'
  progress: number
  estimatedTime: string
  xpReward: number
  prerequisites: string[]
  position: { x: number; y: number }
  icon: React.ReactNode
  color: string
  difficulty: 1 | 2 | 3 | 4 | 5
  hasQuiz: boolean
  needsReview: boolean
  children?: string[]
}

interface LearningTrack {
  id: string
  name: string
  description: string
  color: string
  totalNodes: number
  completedNodes: number
  estimatedHours: number
}

export default function LearningPathNavigator({ 
  systemStatus, 
  playerData,
  evolutionTheme 
}: LearningPathNavigatorProps) {
  const [selectedNode, setSelectedNode] = useState<LearningNode | null>(null)
  const [selectedTrack, setSelectedTrack] = useState<string>('foundation')
  const [showNodeDetails, setShowNodeDetails] = useState(false)

  // Mock learning tracks
  const learningTracks: LearningTrack[] = [
    {
      id: 'foundation',
      name: 'Foundation Path',
      description: 'Essential skills and core concepts',
      color: '#22D3EE',
      totalNodes: 8,
      completedNodes: 6,
      estimatedHours: 12
    },
    {
      id: 'technology',
      name: 'Technology Mastery',
      description: 'Advanced technical skills',
      color: '#8B5CF6',
      totalNodes: 12,
      completedNodes: 4,
      estimatedHours: 24
    },
    {
      id: 'security',
      name: 'CyberSafe Guardian',
      description: 'Security and protection expertise',
      color: '#EF4444',
      totalNodes: 10,
      completedNodes: 3,
      estimatedHours: 18
    }
  ]

  // Mock learning nodes for skill tree
  const learningNodes: LearningNode[] = [
    // Foundation Path
    {
      id: 'basics-1',
      title: 'Digital Literacy',
      description: 'Understanding digital tools and concepts',
      type: 'foundation',
      category: 'tech',
      status: 'completed',
      progress: 100,
      estimatedTime: '2h',
      xpReward: 100,
      prerequisites: [],
      position: { x: 100, y: 50 },
      icon: <BookOpen className="w-4 h-4" />,
      color: '#22D3EE',
      difficulty: 1,
      hasQuiz: true,
      needsReview: false,
      children: ['basics-2', 'logic-1']
    },
    {
      id: 'basics-2',
      title: 'Problem Solving 101',
      description: 'Core analytical thinking skills',
      type: 'foundation',
      category: 'logic',
      status: 'completed',
      progress: 100,
      estimatedTime: '3h',
      xpReward: 150,
      prerequisites: ['basics-1'],
      position: { x: 200, y: 50 },
      icon: <Brain className="w-4 h-4" />,
      color: '#10B981',
      difficulty: 2,
      hasQuiz: true,
      needsReview: false,
      children: ['logic-2', 'tech-1']
    },
    {
      id: 'logic-1',
      title: 'Logic Fundamentals',
      description: 'Boolean logic and reasoning',
      type: 'core',
      category: 'logic',
      status: 'in-progress',
      progress: 65,
      estimatedTime: '4h',
      xpReward: 200,
      prerequisites: ['basics-1'],
      position: { x: 150, y: 120 },
      icon: <Target className="w-4 h-4" />,
      color: '#F59E0B',
      difficulty: 2,
      hasQuiz: true,
      needsReview: false,
      children: ['logic-2']
    },
    {
      id: 'tech-1',
      title: 'Coding Basics',
      description: 'Introduction to programming',
      type: 'core',
      category: 'tech',
      status: 'available',
      progress: 0,
      estimatedTime: '6h',
      xpReward: 300,
      prerequisites: ['basics-2'],
      position: { x: 300, y: 120 },
      icon: <Code className="w-4 h-4" />,
      color: '#8B5CF6',
      difficulty: 3,
      hasQuiz: true,
      needsReview: false,
      children: ['tech-2', 'security-1']
    },
    {
      id: 'logic-2',
      title: 'Advanced Logic',
      description: 'Complex reasoning and algorithms',
      type: 'advanced',
      category: 'logic',
      status: 'locked',
      progress: 0,
      estimatedTime: '5h',
      xpReward: 250,
      prerequisites: ['logic-1', 'basics-2'],
      position: { x: 200, y: 190 },
      icon: <Zap className="w-4 h-4" />,
      color: '#F59E0B',
      difficulty: 4,
      hasQuiz: true,
      needsReview: false,
      children: ['mastery-1']
    },
    {
      id: 'security-1',
      title: 'CyberSafe Basics',
      description: 'Digital security fundamentals',
      type: 'core',
      category: 'security',
      status: 'locked',
      progress: 0,
      estimatedTime: '4h',
      xpReward: 200,
      prerequisites: ['tech-1'],
      position: { x: 350, y: 190 },
      icon: <Shield className="w-4 h-4" />,
      color: '#EF4444',
      difficulty: 3,
      hasQuiz: true,
      needsReview: false,
      children: ['security-2']
    }
  ]

  const getStatusIcon = (status: string, hasQuiz: boolean, needsReview: boolean) => {
    if (needsReview) return <AlertTriangle className="w-3 h-3 text-yellow-400" />
    if (status === 'completed') return <CheckCircle className="w-3 h-3 text-green-400" />
    if (status === 'in-progress') return <Clock className="w-3 h-3 text-blue-400" />
    if (status === 'locked') return <Lock className="w-3 h-3 text-gray-400" />
    if (hasQuiz) return <Star className="w-3 h-3 text-yellow-400" />
    return <Play className="w-3 h-3 text-cyan-400" />
  }

  const getNodeStyle = (node: LearningNode) => {
    const baseStyle = "absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
    
    switch (node.status) {
      case 'completed':
        return `${baseStyle} bg-green-500/20 border-2 border-green-500/50 shadow-lg shadow-green-500/20`
      case 'in-progress':
        return `${baseStyle} bg-blue-500/20 border-2 border-blue-500/50 shadow-lg shadow-blue-500/20`
      case 'available':
        return `${baseStyle} bg-cyan-500/20 border-2 border-cyan-500/50 shadow-lg shadow-cyan-500/20 hover:shadow-cyan-500/40`
      case 'locked':
        return `${baseStyle} bg-gray-500/10 border-2 border-gray-500/30 opacity-50`
      default:
        return `${baseStyle} bg-gray-500/20 border-2 border-gray-500/50`
    }
  }

  const selectedTrackData = learningTracks.find(track => track.id === selectedTrack)
  const trackProgress = selectedTrackData ? (selectedTrackData.completedNodes / selectedTrackData.totalNodes) * 100 : 0

  return (
    <Card className="bg-black/40 border-cyan-500/30 backdrop-blur-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-cyan-400">
          <Map className="w-5 h-5" />
          Learning Path Navigator
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Track Selection */}
          <div className="space-y-4">
            <h3 className="text-lg font-bold text-white">Choose Your Path</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {learningTracks.map((track) => (
                <motion.div
                  key={track.id}
                  className={`p-4 rounded-lg border cursor-pointer transition-all ${
                    selectedTrack === track.id
                      ? 'border-cyan-500/50 bg-cyan-500/10'
                      : 'border-gray-700/50 bg-gray-800/30 hover:border-gray-600/50'
                  }`}
                  onClick={() => setSelectedTrack(track.id)}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-bold text-white">{track.name}</h4>
                    <div className="text-sm" style={{ color: track.color }}>
                      {track.completedNodes}/{track.totalNodes}
                    </div>
                  </div>
                  <p className="text-sm text-white/60 mb-3">{track.description}</p>
                  <div className="space-y-2">
                    <Progress 
                      value={(track.completedNodes / track.totalNodes) * 100} 
                      className="h-2"
                    />
                    <div className="flex justify-between text-xs text-white/60">
                      <span>{Math.round((track.completedNodes / track.totalNodes) * 100)}% Complete</span>
                      <span>{track.estimatedHours}h total</span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Skill Tree Visualization */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-bold text-white">Skill Tree</h3>
              <div className="flex items-center gap-2 text-sm text-white/60">
                <span>Progress: {Math.round(trackProgress)}%</span>
                <Progress value={trackProgress} className="w-20 h-2" />
              </div>
            </div>

            {/* Interactive Skill Tree */}
            <div className="relative bg-gradient-to-br from-space-dark/60 to-space-blue/20 rounded-lg border border-cyan-500/30 overflow-hidden">
              <div className="relative w-full h-80 p-4">
                {/* Connection Lines */}
                <svg className="absolute inset-0 w-full h-full pointer-events-none">
                  {learningNodes.map((node) => 
                    node.children?.map((childId) => {
                      const childNode = learningNodes.find(n => n.id === childId)
                      if (!childNode) return null
                      
                      return (
                        <motion.line
                          key={`${node.id}-${childId}`}
                          x1={node.position.x}
                          y1={node.position.y}
                          x2={childNode.position.x}
                          y2={childNode.position.y}
                          stroke="rgba(34, 211, 238, 0.3)"
                          strokeWidth="2"
                          strokeDasharray={node.status === 'completed' ? "0" : "5,5"}
                          initial={{ pathLength: 0 }}
                          animate={{ pathLength: 1 }}
                          transition={{ duration: 1, delay: 0.5 }}
                        />
                      )
                    })
                  )}
                </svg>

                {/* Learning Nodes */}
                {learningNodes.map((node, index) => (
                  <motion.div
                    key={node.id}
                    className={getNodeStyle(node)}
                    style={{
                      left: `${node.position.x}px`,
                      top: `${node.position.y}px`
                    }}
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: index * 0.1 }}
                    onClick={() => {
                      setSelectedNode(node)
                      setShowNodeDetails(true)
                    }}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <div className="w-16 h-16 rounded-full flex flex-col items-center justify-center p-2">
                      <div style={{ color: node.color }}>
                        {node.icon}
                      </div>
                      <div className="text-xs font-bold text-white mt-1 text-center leading-tight">
                        {node.title.split(' ')[0]}
                      </div>
                      
                      {/* Progress Ring for in-progress nodes */}
                      {node.status === 'in-progress' && (
                        <div className="absolute inset-0 rounded-full">
                          <svg className="w-full h-full transform -rotate-90">
                            <circle
                              cx="50%"
                              cy="50%"
                              r="30"
                              fill="none"
                              stroke="rgba(59, 130, 246, 0.3)"
                              strokeWidth="2"
                            />
                            <circle
                              cx="50%"
                              cy="50%"
                              r="30"
                              fill="none"
                              stroke="#3B82F6"
                              strokeWidth="2"
                              strokeDasharray={`${2 * Math.PI * 30}`}
                              strokeDashoffset={`${2 * Math.PI * 30 * (1 - node.progress / 100)}`}
                              className="transition-all duration-500"
                            />
                          </svg>
                        </div>
                      )}
                      
                      {/* Status Icon */}
                      <div className="absolute -top-1 -right-1 bg-black/80 rounded-full p-1">
                        {getStatusIcon(node.status, node.hasQuiz, node.needsReview)}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="flex gap-3">
            <Button className="flex-1 bg-cyan-500/20 text-cyan-400 border border-cyan-500/30 hover:bg-cyan-500/30">
              <Play className="w-4 h-4 mr-2" />
              Continue Learning
            </Button>
            <Button className="flex-1 bg-purple-500/20 text-purple-400 border border-purple-500/30 hover:bg-purple-500/30">
              <BookOpen className="w-4 h-4 mr-2" />
              Review Progress
            </Button>
          </div>
        </div>

        {/* Node Details Modal */}
        <AnimatePresence>
          {showNodeDetails && selectedNode && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
              onClick={() => setShowNodeDetails(false)}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="bg-black/90 border border-cyan-500/30 rounded-lg p-6 max-w-md mx-4 backdrop-blur-xl"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="flex items-center gap-3 mb-4">
                  <div className="p-2 rounded-lg" style={{ backgroundColor: `${selectedNode.color}20`, border: `1px solid ${selectedNode.color}50` }}>
                    <div style={{ color: selectedNode.color }}>
                      {selectedNode.icon}
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-white">{selectedNode.title}</h3>
                    <p className="text-sm text-white/60">{selectedNode.description}</p>
                  </div>
                </div>

                <div className="space-y-3 mb-6">
                  <div className="flex justify-between items-center">
                    <span className="text-white/80">Progress</span>
                    <span className="text-cyan-400 font-bold">{selectedNode.progress}%</span>
                  </div>
                  <Progress value={selectedNode.progress} className="h-2" />
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-white/60">Time:</span>
                      <span className="text-white ml-2">{selectedNode.estimatedTime}</span>
                    </div>
                    <div>
                      <span className="text-white/60">XP:</span>
                      <span className="text-yellow-400 ml-2">+{selectedNode.xpReward}</span>
                    </div>
                    <div>
                      <span className="text-white/60">Difficulty:</span>
                      <div className="flex ml-2">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-3 h-3 ${
                              i < selectedNode.difficulty ? 'text-yellow-400 fill-current' : 'text-gray-600'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                    <div>
                      <span className="text-white/60">Type:</span>
                      <Badge className="ml-2 bg-purple-500/20 text-purple-400 border-purple-500/30">
                        {selectedNode.type}
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="flex gap-3">
                  {selectedNode.status === 'available' && (
                    <Button className="flex-1 bg-cyan-500/20 text-cyan-400 border border-cyan-500/30">
                      <Play className="w-4 h-4 mr-2" />
                      Start Lesson
                    </Button>
                  )}
                  {selectedNode.status === 'in-progress' && (
                    <Button className="flex-1 bg-blue-500/20 text-blue-400 border border-blue-500/30">
                      <ChevronRight className="w-4 h-4 mr-2" />
                      Continue
                    </Button>
                  )}
                  {selectedNode.status === 'completed' && (
                    <Button className="flex-1 bg-green-500/20 text-green-400 border border-green-500/30">
                      <Trophy className="w-4 h-4 mr-2" />
                      Review
                    </Button>
                  )}
                  <Button 
                    variant="outline" 
                    onClick={() => setShowNodeDetails(false)}
                    className="border-gray-600/30 text-gray-400"
                  >
                    Close
                  </Button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>
    </Card>
  )
}
