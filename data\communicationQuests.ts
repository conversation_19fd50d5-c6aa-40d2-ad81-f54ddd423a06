import { CommunicationQuest } from '@/types/chat'

export const DAILY_COMMUNICATION_QUESTS: CommunicationQuest[] = [
  {
    id: 'daily_help_1',
    title: 'Quantum Helper',
    description: 'Help 3 fellow NanoArchitects with their questions or challenges',
    type: 'help_someone',
    target: 3,
    progress: 0,
    reward: {
      ce: 50,
      cubits: 10,
      badge: 'helpful_hand'
    },
    isDaily: true
  },
  {
    id: 'daily_compliment_1',
    title: 'Kindness Catalyst',
    description: 'Give 5 genuine compliments or words of encouragement',
    type: 'give_compliment',
    target: 5,
    progress: 0,
    reward: {
      ce: 30,
      cubits: 8,
      badge: 'kindness_spark'
    },
    isDaily: true
  },
  {
    id: 'daily_explain_1',
    title: 'Knowledge Sharer',
    description: 'Explain a concept you understand to someone who needs help',
    type: 'explain_concept',
    target: 2,
    progress: 0,
    reward: {
      ce: 40,
      cubits: 12,
      badge: 'wisdom_keeper'
    },
    isDaily: true
  },
  {
    id: 'daily_kindness_1',
    title: 'Harmony Builder',
    description: 'Perform 4 acts of kindness in chat (thanking, encouraging, welcoming)',
    type: 'show_kindness',
    target: 4,
    progress: 0,
    reward: {
      ce: 35,
      cubits: 9,
      badge: 'harmony_weaver'
    },
    isDaily: true
  }
]

export const WEEKLY_COMMUNICATION_QUESTS: CommunicationQuest[] = [
  {
    id: 'weekly_mentor_1',
    title: 'Timeline Mentor',
    description: 'Help 15 different users throughout the week',
    type: 'help_someone',
    target: 15,
    progress: 0,
    reward: {
      ce: 200,
      cubits: 50,
      badge: 'quantum_mentor',
      avatarUpgrade: 'mentor_aura'
    },
    timeLimit: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    isDaily: false
  },
  {
    id: 'weekly_peacemaker_1',
    title: 'Quantum Peacemaker',
    description: 'Successfully help resolve 3 conflicts or misunderstandings',
    type: 'resolve_conflict',
    target: 3,
    progress: 0,
    reward: {
      ce: 150,
      cubits: 40,
      badge: 'peace_architect',
      avatarUpgrade: 'wisdom_glow'
    },
    timeLimit: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    isDaily: false
  },
  {
    id: 'weekly_teacher_1',
    title: 'Master Explainer',
    description: 'Teach 10 concepts to other users with clear, helpful explanations',
    type: 'explain_concept',
    target: 10,
    progress: 0,
    reward: {
      ce: 180,
      cubits: 45,
      badge: 'knowledge_beacon',
      avatarUpgrade: 'scholar_crown'
    },
    timeLimit: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    isDaily: false
  }
]

export const SPECIAL_COMMUNICATION_QUESTS: CommunicationQuest[] = [
  {
    id: 'special_welcome_1',
    title: 'New Timeline Guardian',
    description: 'Welcome 10 new users to the NanoVerse with helpful introductions',
    type: 'show_kindness',
    target: 10,
    progress: 0,
    reward: {
      ce: 100,
      cubits: 25,
      badge: 'welcome_guardian',
      avatarUpgrade: 'guardian_wings'
    },
    isDaily: false
  },
  {
    id: 'special_collaboration_1',
    title: 'Quantum Collaborator',
    description: 'Successfully complete 5 team challenges through effective communication',
    type: 'help_someone',
    target: 5,
    progress: 0,
    reward: {
      ce: 120,
      cubits: 30,
      badge: 'team_catalyst',
      avatarUpgrade: 'collaboration_aura'
    },
    isDaily: false
  },
  {
    id: 'special_inspiration_1',
    title: 'Inspiration Amplifier',
    description: 'Receive 20 "thanks" reactions from other users for your helpful messages',
    type: 'give_compliment',
    target: 20,
    progress: 0,
    reward: {
      ce: 250,
      cubits: 60,
      badge: 'inspiration_master',
      avatarUpgrade: 'radiant_core'
    },
    isDaily: false
  }
]

// Advanced Quest Types for Higher Level Users
export const ADVANCED_COMMUNICATION_QUESTS: CommunicationQuest[] = [
  {
    id: 'advanced_mentor_1',
    title: 'Quantum Mentor Master',
    description: 'Successfully mentor 3 new users through their first week, helping them complete their initial quests',
    type: 'help_someone',
    target: 3,
    progress: 0,
    reward: {
      ce: 300,
      cubits: 75,
      badge: 'quantum_mentor_master',
      avatarUpgrade: 'mentor_crown'
    },
    isDaily: false
  },
  {
    id: 'advanced_mediator_1',
    title: 'Harmony Mediator',
    description: 'Successfully mediate and resolve 5 conflicts between users using peaceful communication',
    type: 'resolve_conflict',
    target: 5,
    progress: 0,
    reward: {
      ce: 400,
      cubits: 100,
      badge: 'harmony_mediator',
      avatarUpgrade: 'peace_aura'
    },
    isDaily: false
  },
  {
    id: 'advanced_teacher_1',
    title: 'Knowledge Architect',
    description: 'Create and share 10 detailed explanations that receive high helpfulness ratings',
    type: 'explain_concept',
    target: 10,
    progress: 0,
    reward: {
      ce: 350,
      cubits: 85,
      badge: 'knowledge_architect',
      avatarUpgrade: 'wisdom_glow'
    },
    isDaily: false
  },
  {
    id: 'advanced_community_1',
    title: 'Community Catalyst',
    description: 'Organize and lead 3 community events that bring users together for learning',
    type: 'show_kindness',
    target: 3,
    progress: 0,
    reward: {
      ce: 500,
      cubits: 125,
      badge: 'community_catalyst',
      avatarUpgrade: 'leadership_crown'
    },
    isDaily: false
  }
]

// Skill-Specific Micro-Quests
export const MICRO_QUESTS: CommunicationQuest[] = [
  {
    id: 'micro_active_listening_1',
    title: 'Active Listener',
    description: 'Respond to 3 messages showing you understood what the person said',
    type: 'help_someone',
    target: 3,
    progress: 0,
    reward: {
      ce: 20,
      cubits: 4
    },
    isDaily: true
  },
  {
    id: 'micro_question_asker_1',
    title: 'Curious Mind',
    description: 'Ask 5 thoughtful questions that help others think deeper',
    type: 'explain_concept',
    target: 5,
    progress: 0,
    reward: {
      ce: 25,
      cubits: 5
    },
    isDaily: true
  },
  {
    id: 'micro_encourager_1',
    title: 'Encouragement Spark',
    description: 'Give 4 genuine compliments or words of encouragement',
    type: 'give_compliment',
    target: 4,
    progress: 0,
    reward: {
      ce: 18,
      cubits: 3
    },
    isDaily: true
  },
  {
    id: 'micro_helper_1',
    title: 'Quick Helper',
    description: 'Provide quick help or answers to 2 users who are stuck',
    type: 'help_someone',
    target: 2,
    progress: 0,
    reward: {
      ce: 15,
      cubits: 3
    },
    isDaily: true
  }
]

export const QUEST_REWARDS = {
  help_someone: {
    ce: 10,
    cubits: 2,
    harmonyBonus: 5
  },
  give_compliment: {
    ce: 8,
    cubits: 1,
    harmonyBonus: 3
  },
  explain_concept: {
    ce: 15,
    cubits: 3,
    harmonyBonus: 8
  },
  show_kindness: {
    ce: 12,
    cubits: 2,
    harmonyBonus: 6
  },
  resolve_conflict: {
    ce: 25,
    cubits: 5,
    harmonyBonus: 15
  }
}

export const COMMUNICATION_BADGES = {
  helpful_hand: {
    name: 'Helpful Hand',
    description: 'Awarded for consistently helping others',
    icon: '🤝',
    rarity: 'common'
  },
  kindness_spark: {
    name: 'Kindness Spark',
    description: 'Spreads positivity throughout the timeline',
    icon: '✨',
    rarity: 'common'
  },
  wisdom_keeper: {
    name: 'Wisdom Keeper',
    description: 'Shares knowledge generously with others',
    icon: '📚',
    rarity: 'common'
  },
  harmony_weaver: {
    name: 'Harmony Weaver',
    description: 'Builds positive connections in the community',
    icon: '🌟',
    rarity: 'common'
  },
  quantum_mentor: {
    name: 'Quantum Mentor',
    description: 'Guides many on their learning journey',
    icon: '🎓',
    rarity: 'rare'
  },
  peace_architect: {
    name: 'Peace Architect',
    description: 'Builds bridges and resolves conflicts',
    icon: '🕊️',
    rarity: 'rare'
  },
  knowledge_beacon: {
    name: 'Knowledge Beacon',
    description: 'Illuminates understanding for others',
    icon: '💡',
    rarity: 'rare'
  },
  welcome_guardian: {
    name: 'Welcome Guardian',
    description: 'Makes newcomers feel at home',
    icon: '🛡️',
    rarity: 'uncommon'
  },
  team_catalyst: {
    name: 'Team Catalyst',
    description: 'Sparks amazing collaborations',
    icon: '⚡',
    rarity: 'uncommon'
  },
  inspiration_master: {
    name: 'Inspiration Master',
    description: 'Inspires countless others to grow',
    icon: '🌈',
    rarity: 'legendary'
  }
}

export function generateDailyQuests(
  userLevel: number,
  userMetrics?: any,
  recentActivity?: string[]
): CommunicationQuest[] {
  const availableQuests = DAILY_COMMUNICATION_QUESTS.filter(quest => {
    // Adjust quest difficulty based on user level
    return true // For now, all quests are available
  })

  // Adaptive quest selection based on user weaknesses
  let selectedQuests = [...availableQuests]

  if (userMetrics) {
    // Prioritize quests that address weak areas
    selectedQuests.sort((a, b) => {
      const aScore = getQuestRelevanceScore(a, userMetrics)
      const bScore = getQuestRelevanceScore(b, userMetrics)
      return bScore - aScore // Higher score first
    })
  }

  // Avoid recently completed quest types
  if (recentActivity) {
    selectedQuests = selectedQuests.filter(quest =>
      !recentActivity.includes(quest.type)
    )
  }

  // Return 2-3 adaptive daily quests
  const shuffled = selectedQuests.slice(0, 5).sort(() => 0.5 - Math.random())
  return shuffled.slice(0, Math.min(3, shuffled.length)).map(quest => ({
    ...quest,
    id: `${quest.id}_${Date.now()}`, // Make unique for each day
    progress: 0,
    target: Math.max(1, Math.floor(quest.target * (0.5 + userLevel * 0.1))), // Scale with level
    reward: {
      ...quest.reward,
      ce: Math.floor(quest.reward.ce * (1 + userLevel * 0.1)),
      cubits: Math.floor(quest.reward.cubits * (1 + userLevel * 0.1))
    },
    timeLimit: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
  }))
}

function getQuestRelevanceScore(quest: CommunicationQuest, userMetrics: any): number {
  let score = 0

  // Higher score for quests that address weak areas
  switch (quest.type) {
    case 'help_someone':
      score = userMetrics.helpfulnessScore < 0.6 ? 3 : 1
      break
    case 'give_compliment':
      score = userMetrics.kindnessScore < 0.6 ? 3 : 1
      break
    case 'explain_concept':
      score = userMetrics.communicationSkillLevel < 5 ? 3 : 1
      break
    case 'show_kindness':
      score = userMetrics.empathyScore < 0.6 ? 3 : 1
      break
    case 'resolve_conflict':
      score = userMetrics.conflictResolutionSkill < 0.5 ? 3 : 1
      break
  }

  return score
}

export function checkQuestProgress(quest: CommunicationQuest, action: string): CommunicationQuest {
  if (quest.type === action && quest.progress < quest.target) {
    return {
      ...quest,
      progress: quest.progress + 1
    }
  }
  return quest
}

export function isQuestComplete(quest: CommunicationQuest): boolean {
  return quest.progress >= quest.target
}
