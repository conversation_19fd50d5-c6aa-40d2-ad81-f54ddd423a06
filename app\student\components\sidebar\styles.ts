import { EvolutionTheme } from './types'

export const getMenuItemStyle = (
  isActive: boolean, 
  isHovered: boolean = false, 
  evolutionTheme: EvolutionTheme
) => ({
  width: '100%',
  display: 'flex',
  alignItems: 'center',
  gap: '0.75rem',
  padding: '1rem',
  borderRadius: '1rem',
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative' as const,
  overflow: 'hidden' as const,
  border: '2px solid',
  backdropFilter: 'blur(16px)',
  background: isActive
    ? `linear-gradient(135deg, ${evolutionTheme.primary}25, ${evolutionTheme.secondary}15, ${evolutionTheme.primary}25)`
    : isHovered
    ? 'linear-gradient(135deg, rgba(34, 211, 238, 0.08), rgba(139, 92, 246, 0.08), rgba(34, 211, 238, 0.08))'
    : 'linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.2))',
  borderColor: isActive
    ? `${evolutionTheme.primary}80`
    : isHovered
    ? `${evolutionTheme.primary}40`
    : 'rgba(255, 255, 255, 0.1)',
  boxShadow: isActive
    ? `
      0 8px 32px ${evolutionTheme.glow},
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      0 0 20px ${evolutionTheme.primary}40
    `
    : isHovered
    ? `
      0 4px 20px rgba(34, 211, 238, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1)
    `
    : 'inset 0 1px 0 rgba(255, 255, 255, 0.05)',
  color: isActive ? evolutionTheme.primary : 'rgba(255, 255, 255, 0.8)',
  transform: isHovered ? 'scale(1.02) translateY(-1px)' : 'scale(1)',
})

export const getBottomMenuItemStyle = (
  isActive: boolean, 
  isLogout: boolean, 
  isHovered: boolean = false
) => ({
  width: '100%',
  display: 'flex',
  alignItems: 'center',
  gap: '0.75rem',
  padding: '0.75rem 1rem',
  borderRadius: '1rem',
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative' as const,
  overflow: 'hidden' as const,
  border: '2px solid',
  backdropFilter: 'blur(16px)',
  background: isActive
    ? 'linear-gradient(135deg, rgba(34, 211, 238, 0.25), rgba(139, 92, 246, 0.15), rgba(34, 211, 238, 0.25))'
    : isLogout && isHovered
    ? 'linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(248, 113, 113, 0.2), rgba(239, 68, 68, 0.15))'
    : isHovered
    ? 'linear-gradient(135deg, rgba(34, 211, 238, 0.08), rgba(139, 92, 246, 0.08), rgba(34, 211, 238, 0.08))'
    : 'linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.2))',
  borderColor: isActive
    ? 'rgba(34, 211, 238, 0.6)'
    : isLogout && isHovered
    ? 'rgba(239, 68, 68, 0.5)'
    : isHovered
    ? 'rgba(34, 211, 238, 0.3)'
    : 'rgba(255, 255, 255, 0.1)',
  boxShadow: isActive
    ? `
      0 8px 32px rgba(34, 211, 238, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      0 0 20px rgba(34, 211, 238, 0.4)
    `
    : isLogout && isHovered
    ? `
      0 4px 20px rgba(239, 68, 68, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1)
    `
    : isHovered
    ? `
      0 4px 20px rgba(34, 211, 238, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1)
    `
    : 'inset 0 1px 0 rgba(255, 255, 255, 0.05)',
  color: isActive
    ? 'rgba(34, 211, 238, 1)'
    : isLogout && isHovered
    ? 'rgba(239, 68, 68, 1)'
    : isLogout
    ? 'rgba(239, 68, 68, 0.8)'
    : 'rgba(255, 255, 255, 0.8)',
  transform: isHovered ? 'scale(1.02) translateY(-1px)' : 'scale(1)',
})

export const getIconStyle = (
  isActive: boolean, 
  isLogout: boolean = false, 
  isHovered: boolean = false,
  evolutionTheme: EvolutionTheme
) => ({
  width: '1.25rem',
  height: '1.25rem',
  flexShrink: 0,
  position: 'relative' as const,
  zIndex: 10,
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  filter: isActive
    ? `drop-shadow(0 0 8px ${evolutionTheme.primary}) drop-shadow(0 2px 4px ${evolutionTheme.primary}80)`
    : isLogout && isHovered
    ? 'drop-shadow(0 0 6px rgba(239, 68, 68, 0.8)) drop-shadow(0 2px 4px rgba(239, 68, 68, 0.5))'
    : isHovered
    ? 'drop-shadow(0 0 6px rgba(34, 211, 238, 0.6)) drop-shadow(0 2px 4px rgba(34, 211, 238, 0.3))'
    : 'none',
  transform: isActive ? 'scale(1.1)' : isHovered ? 'scale(1.05)' : 'scale(1)',
})
