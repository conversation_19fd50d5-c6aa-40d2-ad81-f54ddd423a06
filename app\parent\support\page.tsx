'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import {
  MessageSquare,
  Mail,
  Phone,
  HelpCircle,
  Book,
  Video,
  FileText,
  Send,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react'

export default function SupportPage() {
  const [contactForm, setContactForm] = useState({
    subject: '',
    message: '',
    priority: 'medium'
  })

  const faqs = [
    {
      question: 'How do I monitor my child\'s progress?',
      answer: 'You can view your child\'s progress on the main dashboard, which shows learning hours, completed modules, achievements, and current activities.',
      category: 'Progress Tracking'
    },
    {
      question: 'Is the content age-appropriate?',
      answer: 'Yes, all content is carefully curated and designed for different age groups. Our AI safety systems also monitor interactions to ensure a safe learning environment.',
      category: 'Safety'
    },
    {
      question: 'How can I set screen time limits?',
      answer: 'Screen time limits can be set in your child\'s profile settings. You can specify daily and weekly limits, as well as bedtime restrictions.',
      category: 'Parental Controls'
    },
    {
      question: 'What if my child needs help with a lesson?',
      answer: 'Children can ask questions through the AI mentor system, participate in peer discussions, or request help from instructors through the platform.',
      category: 'Learning Support'
    }
  ]

  const resources = [
    {
      title: 'Parent Guide',
      description: 'Complete guide to using the parent dashboard',
      type: 'PDF',
      icon: FileText,
      color: 'text-blue-400'
    },
    {
      title: 'Video Tutorials',
      description: 'Step-by-step video guides for common tasks',
      type: 'Video',
      icon: Video,
      color: 'text-green-400'
    },
    {
      title: 'Safety Guidelines',
      description: 'Understanding our safety measures and controls',
      type: 'Guide',
      icon: Book,
      color: 'text-purple-400'
    }
  ]

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
    console.log('Support request submitted:', contactForm)
    // Reset form
    setContactForm({ subject: '', message: '', priority: 'medium' })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-white font-space-grotesk">
            Support Center
          </h1>
          <p className="text-gray-400 mt-1">
            Get help and find answers to your questions
          </p>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Contact Support */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                <MessageSquare className="w-5 h-5 text-blue-400" />
                Contact Support
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label className="text-gray-400 text-sm">Subject</Label>
                  <Input
                    value={contactForm.subject}
                    onChange={(e) => setContactForm({...contactForm, subject: e.target.value})}
                    placeholder="Brief description of your issue"
                    className="mt-1"
                    required
                  />
                </div>

                <div>
                  <Label className="text-gray-400 text-sm">Priority</Label>
                  <div className="flex gap-2 mt-1">
                    {['low', 'medium', 'high'].map((priority) => (
                      <Button
                        key={priority}
                        type="button"
                        variant={contactForm.priority === priority ? "default" : "outline"}
                        size="sm"
                        onClick={() => setContactForm({...contactForm, priority})}
                        className="capitalize"
                      >
                        {priority}
                      </Button>
                    ))}
                  </div>
                </div>

                <div>
                  <Label className="text-gray-400 text-sm">Message</Label>
                  <Textarea
                    value={contactForm.message}
                    onChange={(e) => setContactForm({...contactForm, message: e.target.value})}
                    placeholder="Describe your issue or question in detail"
                    className="mt-1 min-h-[120px]"
                    required
                  />
                </div>

                <Button type="submit" className="w-full flex items-center gap-2">
                  <Send className="w-4 h-4" />
                  Send Message
                </Button>
              </form>

              {/* Contact Info */}
              <div className="mt-6 pt-6 border-t border-gray-800/50">
                <h3 className="text-white font-medium mb-3">Other Ways to Reach Us</h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-3 text-sm">
                    <Mail className="w-4 h-4 text-blue-400" />
                    <span className="text-gray-400"><EMAIL></span>
                  </div>
                  <div className="flex items-center gap-3 text-sm">
                    <Phone className="w-4 h-4 text-green-400" />
                    <span className="text-gray-400">+1 (555) 123-NANO</span>
                  </div>
                  <div className="flex items-center gap-3 text-sm">
                    <Clock className="w-4 h-4 text-purple-400" />
                    <span className="text-gray-400">Mon-Fri 9AM-6PM PST</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Help Resources */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                <Book className="w-5 h-5 text-green-400" />
                Help Resources
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {resources.map((resource, index) => (
                <motion.div
                  key={resource.title}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 + index * 0.05 }}
                  className="flex items-center gap-4 p-3 bg-gray-800/30 rounded-lg hover:bg-gray-800/50 transition-colors cursor-pointer"
                >
                  <resource.icon className={`w-6 h-6 ${resource.color}`} />
                  <div className="flex-1">
                    <h4 className="text-white font-medium">{resource.title}</h4>
                    <p className="text-gray-400 text-sm">{resource.description}</p>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {resource.type}
                  </Badge>
                </motion.div>
              ))}
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* FAQ Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardHeader>
            <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
              <HelpCircle className="w-5 h-5 text-yellow-400" />
              Frequently Asked Questions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {faqs.map((faq, index) => (
                <motion.div
                  key={faq.question}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 + index * 0.05 }}
                  className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/30"
                >
                  <div className="flex items-start gap-3 mb-2">
                    <Badge variant="outline" className="text-xs text-cyan-400 border-cyan-500/30">
                      {faq.category}
                    </Badge>
                  </div>
                  <h4 className="text-white font-medium mb-2">{faq.question}</h4>
                  <p className="text-gray-400 text-sm">{faq.answer}</p>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Status & Updates */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardHeader>
            <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-orange-400" />
              System Status & Updates
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                <CheckCircle className="w-5 h-5 text-green-400" />
                <div>
                  <p className="text-white font-medium">All Systems Operational</p>
                  <p className="text-gray-400 text-sm">Last updated: 2 minutes ago</p>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                <AlertCircle className="w-5 h-5 text-blue-400" />
                <div>
                  <p className="text-white font-medium">Scheduled Maintenance</p>
                  <p className="text-gray-400 text-sm">Sunday 2AM-4AM PST - Minor updates planned</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
