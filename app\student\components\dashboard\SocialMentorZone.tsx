'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  MessageCircle, 
  Users, 
  Heart, 
  HelpCircle, 
  Star, 
  Send, 
  UserCheck, 
  Clock, 
  Award,
  Sparkles,
  ChevronRight,
  Bot,
  Shield,
  ThumbsUp,
  MessageSquare,
  Zap,
  Target,
  BookOpen
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { MetricsComponentProps, EvolutionTheme } from './types'

interface SocialMentorZoneProps extends MetricsComponentProps {
  evolutionTheme: EvolutionTheme
}

interface ChatMessage {
  id: string
  sender: string
  avatar: string
  message: string
  timestamp: string
  type: 'help-request' | 'general' | 'celebration' | 'mentor-response'
  isModerated: boolean
  likes: number
  replies: number
  subject?: string
}

interface Mentor {
  id: string
  name: string
  avatar: string
  specialty: string
  rating: number
  responseTime: string
  isOnline: boolean
  helpedStudents: number
  badges: string[]
  type: 'ai' | 'human' | 'peer'
}

interface HelpRequest {
  id: string
  student: string
  avatar: string
  subject: string
  topic: string
  difficulty: 'easy' | 'medium' | 'hard'
  timePosted: string
  xpReward: number
  urgency: 'low' | 'medium' | 'high'
  studentsHelping: number
  maxHelpers: number
}

export default function SocialMentorZone({ 
  systemStatus, 
  playerData,
  evolutionTheme 
}: SocialMentorZoneProps) {
  const [selectedTab, setSelectedTab] = useState<'chat' | 'mentors' | 'help'>('chat')
  const [newMessage, setNewMessage] = useState('')

  // Mock chat messages
  const chatMessages: ChatMessage[] = [
    {
      id: '1',
      sender: 'QuantumLearner',
      avatar: '/avatars/student1.png',
      message: 'Just completed the Cybersecurity basics! The encryption module was amazing! 🔐',
      timestamp: '2 min ago',
      type: 'celebration',
      isModerated: true,
      likes: 12,
      replies: 3
    },
    {
      id: '2',
      sender: 'CodeNinja',
      avatar: '/avatars/student2.png',
      message: 'Can someone help me understand quantum entanglement? I\'m stuck on the Bell\'s theorem part.',
      timestamp: '5 min ago',
      type: 'help-request',
      isModerated: true,
      likes: 5,
      replies: 8,
      subject: 'Quantum Physics'
    },
    {
      id: '3',
      sender: 'ByteMentor',
      avatar: '/avatars/mentor1.png',
      message: 'Great question! Think of entanglement like two coins that always land on opposite sides, no matter how far apart they are. Let me share a visual explanation...',
      timestamp: '3 min ago',
      type: 'mentor-response',
      isModerated: true,
      likes: 18,
      replies: 2
    },
    {
      id: '4',
      sender: 'LogicMaster',
      avatar: '/avatars/student3.png',
      message: 'The new logic puzzles are really challenging! Anyone want to form a study group?',
      timestamp: '8 min ago',
      type: 'general',
      isModerated: true,
      likes: 7,
      replies: 5
    }
  ]

  // Mock mentors
  const mentors: Mentor[] = [
    {
      id: 'byte-mentor',
      name: 'ByteMentor',
      avatar: '/avatars/ai-mentor.png',
      specialty: 'Programming & Logic',
      rating: 4.9,
      responseTime: '< 2 min',
      isOnline: true,
      helpedStudents: 1247,
      badges: ['🤖', '⚡', '🧠'],
      type: 'ai'
    },
    {
      id: 'cyber-sage',
      name: 'CyberSage',
      avatar: '/avatars/human-mentor.png',
      specialty: 'Cybersecurity',
      rating: 4.8,
      responseTime: '~15 min',
      isOnline: true,
      helpedStudents: 892,
      badges: ['🛡️', '🔐', '⭐'],
      type: 'human'
    },
    {
      id: 'study-buddy',
      name: 'Your Study Buddy',
      avatar: '/avatars/peer-mentor.png',
      specialty: 'Peer Support',
      rating: 4.7,
      responseTime: '~5 min',
      isOnline: false,
      helpedStudents: 156,
      badges: ['🤝', '💡', '🎯'],
      type: 'peer'
    }
  ]

  // Mock help requests
  const helpRequests: HelpRequest[] = [
    {
      id: '1',
      student: 'NanoExplorer',
      avatar: '/avatars/student4.png',
      subject: 'Logic Puzzles',
      topic: 'Boolean algebra and truth tables',
      difficulty: 'medium',
      timePosted: '10 min ago',
      xpReward: 50,
      urgency: 'medium',
      studentsHelping: 1,
      maxHelpers: 3
    },
    {
      id: '2',
      student: 'TechSeeker',
      avatar: '/avatars/student5.png',
      subject: 'Coding Basics',
      topic: 'Understanding loops and conditionals',
      difficulty: 'easy',
      timePosted: '15 min ago',
      xpReward: 30,
      urgency: 'low',
      studentsHelping: 0,
      maxHelpers: 2
    },
    {
      id: '3',
      student: 'CryptoLearner',
      avatar: '/avatars/student6.png',
      subject: 'Cybersecurity',
      topic: 'RSA encryption implementation',
      difficulty: 'hard',
      timePosted: '5 min ago',
      xpReward: 100,
      urgency: 'high',
      studentsHelping: 2,
      maxHelpers: 4
    }
  ]

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-400 border-green-400/30 bg-green-500/10'
      case 'medium': return 'text-yellow-400 border-yellow-400/30 bg-yellow-500/10'
      case 'hard': return 'text-red-400 border-red-400/30 bg-red-500/10'
      default: return 'text-gray-400 border-gray-400/30 bg-gray-500/10'
    }
  }

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'high': return <Zap className="w-3 h-3 text-red-400" />
      case 'medium': return <Clock className="w-3 h-3 text-yellow-400" />
      case 'low': return <Target className="w-3 h-3 text-green-400" />
      default: return null
    }
  }

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case 'help-request': return <HelpCircle className="w-3 h-3 text-blue-400" />
      case 'celebration': return <Sparkles className="w-3 h-3 text-yellow-400" />
      case 'mentor-response': return <Bot className="w-3 h-3 text-purple-400" />
      default: return <MessageCircle className="w-3 h-3 text-gray-400" />
    }
  }

  return (
    <Card className="bg-black/40 border-green-500/30 backdrop-blur-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-green-400">
          <Users className="w-5 h-5" />
          Social & Mentor Zone
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Tab Navigation */}
          <div className="flex gap-2">
            {(['chat', 'mentors', 'help'] as const).map((tab) => (
              <Button
                key={tab}
                variant={selectedTab === tab ? "default" : "outline"}
                onClick={() => setSelectedTab(tab)}
                className={`capitalize ${
                  selectedTab === tab
                    ? 'bg-green-500/20 text-green-400 border-green-500/30'
                    : 'border-gray-600/30 text-gray-400 hover:border-green-500/30 hover:text-green-400'
                }`}
              >
                {tab === 'chat' && <MessageCircle className="w-4 h-4 mr-2" />}
                {tab === 'mentors' && <UserCheck className="w-4 h-4 mr-2" />}
                {tab === 'help' && <Heart className="w-4 h-4 mr-2" />}
                {tab === 'chat' ? 'Community' : tab === 'mentors' ? 'Mentors' : 'Help Others'}
              </Button>
            ))}
          </div>

          {/* Community Chat Tab */}
          {selectedTab === 'chat' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-bold text-white">Community Chat</h3>
                <Badge className="bg-green-500/20 text-green-400 border-green-500/30 flex items-center gap-1">
                  <Shield className="w-3 h-3" />
                  Moderated
                </Badge>
              </div>

              <div className="space-y-3 max-h-80 overflow-y-auto">
                {chatMessages.map((message, index) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-4 rounded-lg bg-gray-800/30 border border-gray-700/50 hover:border-green-500/30 transition-colors"
                  >
                    <div className="flex items-start gap-3">
                      <Avatar className="w-8 h-8">
                        <AvatarImage src={message.avatar} />
                        <AvatarFallback>{message.sender[0]}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium text-white text-sm">{message.sender}</span>
                          {getMessageTypeIcon(message.type)}
                          <span className="text-xs text-white/60">{message.timestamp}</span>
                          {message.subject && (
                            <Badge className="text-xs bg-blue-500/20 text-blue-400 border-blue-500/30">
                              {message.subject}
                            </Badge>
                          )}
                        </div>
                        <p className="text-white/80 text-sm mb-2">{message.message}</p>
                        <div className="flex items-center gap-4 text-xs text-white/60">
                          <button className="flex items-center gap-1 hover:text-green-400 transition-colors">
                            <ThumbsUp className="w-3 h-3" />
                            {message.likes}
                          </button>
                          <button className="flex items-center gap-1 hover:text-blue-400 transition-colors">
                            <MessageSquare className="w-3 h-3" />
                            {message.replies}
                          </button>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Message Input */}
              <div className="flex gap-2">
                <input
                  type="text"
                  placeholder="Share your thoughts or ask for help..."
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  className="flex-1 px-3 py-2 bg-gray-800/50 border border-gray-700/50 rounded-lg text-white placeholder-white/60 focus:border-green-500/50 focus:outline-none"
                />
                <Button 
                  size="sm" 
                  className="bg-green-500/20 text-green-400 border border-green-500/30 hover:bg-green-500/30"
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </motion.div>
          )}

          {/* Mentors Tab */}
          {selectedTab === 'mentors' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              <h3 className="text-lg font-bold text-white">Available Mentors</h3>
              <div className="space-y-3">
                {mentors.map((mentor, index) => (
                  <motion.div
                    key={mentor.id}
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-4 rounded-lg bg-gray-800/30 border border-gray-700/50 hover:border-green-500/30 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="relative">
                          <Avatar className="w-12 h-12">
                            <AvatarImage src={mentor.avatar} />
                            <AvatarFallback>{mentor.name[0]}</AvatarFallback>
                          </Avatar>
                          {mentor.isOnline && (
                            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-black"></div>
                          )}
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <h4 className="font-bold text-white">{mentor.name}</h4>
                            <div className="flex gap-1">
                              {mentor.badges.map((badge, i) => (
                                <span key={i} className="text-sm">{badge}</span>
                              ))}
                            </div>
                          </div>
                          <p className="text-sm text-white/60">{mentor.specialty}</p>
                          <div className="flex items-center gap-4 text-xs text-white/60 mt-1">
                            <div className="flex items-center gap-1">
                              <Star className="w-3 h-3 text-yellow-400" />
                              {mentor.rating}
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              {mentor.responseTime}
                            </div>
                            <div className="flex items-center gap-1">
                              <Users className="w-3 h-3" />
                              {mentor.helpedStudents} helped
                            </div>
                          </div>
                        </div>
                      </div>
                      <Button 
                        size="sm" 
                        className="bg-green-500/20 text-green-400 border border-green-500/30 hover:bg-green-500/30"
                        disabled={!mentor.isOnline}
                      >
                        <MessageCircle className="w-3 h-3 mr-1" />
                        {mentor.isOnline ? 'Ask for Help' : 'Offline'}
                      </Button>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Mentor Suggestions */}
              <div className="p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg border border-blue-500/30">
                <h4 className="font-bold text-white mb-2 flex items-center gap-2">
                  <Sparkles className="w-4 h-4 text-blue-400" />
                  Need Help?
                </h4>
                <p className="text-sm text-white/80 mb-3">
                  Ask ByteMentor for instant AI assistance, or connect with CyberSage for expert cybersecurity guidance!
                </p>
                <div className="flex gap-2">
                  <Button size="sm" className="bg-purple-500/20 text-purple-400 border border-purple-500/30">
                    <Bot className="w-3 h-3 mr-1" />
                    Ask ByteMentor
                  </Button>
                  <Button size="sm" className="bg-blue-500/20 text-blue-400 border border-blue-500/30">
                    <Shield className="w-3 h-3 mr-1" />
                    Contact CyberSage
                  </Button>
                </div>
              </div>
            </motion.div>
          )}

          {/* Help Others Tab */}
          {selectedTab === 'help' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-bold text-white">Help Others & Earn XP</h3>
                <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                  Altruism Rewards
                </Badge>
              </div>

              <div className="space-y-3">
                {helpRequests.map((request, index) => (
                  <motion.div
                    key={request.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-4 rounded-lg bg-gray-800/30 border border-gray-700/50 hover:border-green-500/30 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 flex-1">
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={request.avatar} />
                          <AvatarFallback>{request.student[0]}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium text-white text-sm">{request.student}</span>
                            <span className="text-xs text-white/60">needs help with</span>
                            <Badge className="text-xs bg-blue-500/20 text-blue-400 border-blue-500/30">
                              {request.subject}
                            </Badge>
                          </div>
                          <p className="text-white/80 text-sm mb-2">{request.topic}</p>
                          <div className="flex items-center gap-3 text-xs">
                            <Badge className={`text-xs ${getDifficultyColor(request.difficulty)}`}>
                              {request.difficulty}
                            </Badge>
                            <div className="flex items-center gap-1 text-white/60">
                              {getUrgencyIcon(request.urgency)}
                              <span>{request.timePosted}</span>
                            </div>
                            <div className="flex items-center gap-1 text-green-400">
                              <Award className="w-3 h-3" />
                              <span>+{request.xpReward} XP</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xs text-white/60 mb-2">
                          {request.studentsHelping}/{request.maxHelpers} helping
                        </div>
                        <Button 
                          size="sm" 
                          className="bg-green-500/20 text-green-400 border border-green-500/30 hover:bg-green-500/30"
                          disabled={request.studentsHelping >= request.maxHelpers}
                        >
                          <Heart className="w-3 h-3 mr-1" />
                          {request.studentsHelping >= request.maxHelpers ? 'Full' : 'Help'}
                        </Button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Altruism Encouragement */}
              <div className="p-4 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg border border-green-500/30">
                <h4 className="font-bold text-white mb-2 flex items-center gap-2">
                  <Heart className="w-4 h-4 text-green-400" />
                  Spread Knowledge, Earn Rewards
                </h4>
                <p className="text-sm text-white/80 mb-3">
                  Help fellow students and earn altruism-based XP! Teaching others reinforces your own learning while building a supportive community.
                </p>
                <div className="flex items-center justify-between">
                  <div className="text-sm text-white/60">
                    You've helped <span className="text-green-400 font-bold">12 students</span> this week
                  </div>
                  <Button size="sm" className="bg-green-500/20 text-green-400 border border-green-500/30">
                    <BookOpen className="w-3 h-3 mr-1" />
                    View All Requests
                    <ChevronRight className="w-3 h-3 ml-1" />
                  </Button>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
