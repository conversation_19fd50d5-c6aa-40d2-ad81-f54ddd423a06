'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  ChevronLeft,
  ChevronRight,
  Calendar,
  CalendarDays,
  RotateCcw,
  FastForward
} from 'lucide-react'

interface WeekNavigatorProps {
  currentWeek: Date
  onWeekChange: (date: Date) => void
}

export function WeekNavigator({ currentWeek, onWeekChange }: WeekNavigatorProps) {
  // Get start of week (Monday)
  const getStartOfWeek = (date: Date) => {
    const d = new Date(date)
    const day = d.getDay()
    const diff = d.getDate() - day + (day === 0 ? -6 : 1) // Adjust when day is Sunday
    return new Date(d.setDate(diff))
  }

  // Get end of week (Sunday)
  const getEndOfWeek = (date: Date) => {
    const startOfWeek = getStartOfWeek(date)
    const endOfWeek = new Date(startOfWeek)
    endOfWeek.setDate(startOfWeek.getDate() + 6)
    return endOfWeek
  }

  const startOfWeek = getStartOfWeek(currentWeek)
  const endOfWeek = getEndOfWeek(currentWeek)

  // Navigate to previous week
  const goToPreviousWeek = () => {
    const previousWeek = new Date(startOfWeek)
    previousWeek.setDate(startOfWeek.getDate() - 7)
    onWeekChange(previousWeek)
  }

  // Navigate to next week
  const goToNextWeek = () => {
    const nextWeek = new Date(startOfWeek)
    nextWeek.setDate(startOfWeek.getDate() + 7)
    onWeekChange(nextWeek)
  }

  // Go to current week
  const goToCurrentWeek = () => {
    onWeekChange(new Date())
  }

  // Check if it's current week
  const isCurrentWeek = () => {
    const today = new Date()
    const currentWeekStart = getStartOfWeek(today)
    return startOfWeek.toDateString() === currentWeekStart.toDateString()
  }

  // Format date range
  const formatDateRange = () => {
    const startMonth = startOfWeek.toLocaleDateString('en-US', { month: 'short' })
    const startDay = startOfWeek.getDate()
    const endMonth = endOfWeek.toLocaleDateString('en-US', { month: 'short' })
    const endDay = endOfWeek.getDate()
    const year = endOfWeek.getFullYear()

    if (startMonth === endMonth) {
      return `${startMonth} ${startDay} - ${endDay}, ${year}`
    } else {
      return `${startMonth} ${startDay} - ${endMonth} ${endDay}, ${year}`
    }
  }

  // Get week status
  const getWeekStatus = () => {
    const today = new Date()
    const currentWeekStart = getStartOfWeek(today)
    
    if (startOfWeek < currentWeekStart) {
      return { label: 'Past Week', color: 'bg-gray-500/20 text-gray-400' }
    } else if (startOfWeek.toDateString() === currentWeekStart.toDateString()) {
      return { label: 'Current Week', color: 'bg-green-500/20 text-green-400' }
    } else {
      return { label: 'Future Week', color: 'bg-blue-500/20 text-blue-400' }
    }
  }

  const weekStatus = getWeekStatus()

  return (
    <div className="space-y-4">
      {/* Week Navigation Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={goToPreviousWeek}
            className="border-gray-700/50 text-gray-300 hover:bg-gray-800/50"
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={goToNextWeek}
            className="border-gray-700/50 text-gray-300 hover:bg-gray-800/50"
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
          
          {!isCurrentWeek() && (
            <Button
              variant="outline"
              size="sm"
              onClick={goToCurrentWeek}
              className="border-cyan-500/30 text-cyan-400 hover:bg-cyan-500/10"
            >
              <RotateCcw className="w-4 h-4 mr-2" />
              Current Week
            </Button>
          )}
        </div>

        <Badge className={weekStatus.color}>
          {weekStatus.label}
        </Badge>
      </div>

      {/* Week Display */}
      <motion.div
        key={startOfWeek.toISOString()}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        className="space-y-2"
      >
        <div className="flex items-center gap-2">
          <Calendar className="w-5 h-5 text-cyan-400" />
          <h3 className="text-lg font-semibold text-white">
            {formatDateRange()}
          </h3>
        </div>

        {/* Week Days Preview */}
        <div className="grid grid-cols-7 gap-1">
          {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => {
            const dayDate = new Date(startOfWeek)
            dayDate.setDate(startOfWeek.getDate() + index)
            const isToday = dayDate.toDateString() === new Date().toDateString()
            const isWeekend = index >= 5

            return (
              <div
                key={day}
                className={`text-center p-2 rounded-lg text-xs transition-all ${
                  isToday
                    ? 'bg-cyan-500/20 text-cyan-400 border border-cyan-500/30'
                    : isWeekend
                    ? 'bg-purple-500/10 text-purple-400'
                    : 'bg-gray-800/30 text-gray-400'
                }`}
              >
                <div className="font-medium">{day}</div>
                <div className="text-xs opacity-75">{dayDate.getDate()}</div>
              </div>
            )
          })}
        </div>
      </motion.div>

      {/* Quick Actions */}
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          className="border-gray-700/50 text-gray-300 hover:bg-gray-800/50 text-xs"
        >
          <CalendarDays className="w-3 h-3 mr-1" />
          Copy Last Week
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          className="border-gray-700/50 text-gray-300 hover:bg-gray-800/50 text-xs"
        >
          <FastForward className="w-3 h-3 mr-1" />
          Auto Fill
        </Button>
      </div>
    </div>
  )
}
