'use client'

import { motion } from 'framer-motion'
import { Bar<PERSON>hart3, <PERSON>, BookO<PERSON>, Star } from 'lucide-react'
import { ActivityWidgetProps } from './types'

export default function ActivityWidget({
  data,
  timeframe,
  className = '',
  size = 'md',
  variant = 'detailed'
}: ActivityWidgetProps) {
  const sizeClasses = {
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6'
  }

  const textSizes = {
    sm: { title: 'text-sm', value: 'text-base', label: 'text-xs' },
    md: { title: 'text-base', value: 'text-lg', label: 'text-sm' },
    lg: { title: 'text-lg', value: 'text-xl', label: 'text-base' }
  }

  // Calculate totals
  const totalXP = data.reduce((sum, day) => sum + day.xpEarned, 0)
  const totalLessons = data.reduce((sum, day) => sum + day.lessonsCompleted, 0)
  const totalTime = data.reduce((sum, day) => sum + day.timeSpent, 0)
  const averageScore = data.length > 0 
    ? data.reduce((sum, day) => sum + day.score, 0) / data.length 
    : 0

  // Get max values for chart scaling
  const maxXP = Math.max(...data.map(d => d.xpEarned))
  const maxLessons = Math.max(...data.map(d => d.lessonsCompleted))

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) return `${hours}h ${mins}m`
    return `${mins}m`
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`bg-black/40 border border-blue-500/30 backdrop-blur-xl rounded-lg ${sizeClasses[size]} ${className}`}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <BarChart3 className="w-5 h-5 text-blue-400" />
          <span className={`font-medium text-white ${textSizes[size].title}`}>
            {timeframe === 'week' ? 'Weekly' : 'Monthly'} Activity
          </span>
        </div>
        <div className={`text-blue-400 font-bold ${textSizes[size].value}`}>
          {totalXP} XP
        </div>
      </div>

      {variant === 'detailed' && (
        <>
          {/* Activity Chart */}
          <div className="mb-4">
            <div className="flex items-end justify-between h-20 gap-1">
              {data.slice(-7).map((day, index) => {
                const xpHeight = maxXP > 0 ? (day.xpEarned / maxXP) * 100 : 0
                const lessonHeight = maxLessons > 0 ? (day.lessonsCompleted / maxLessons) * 100 : 0
                
                return (
                  <div key={index} className="flex-1 flex flex-col items-center gap-1">
                    <div className="w-full flex flex-col justify-end h-16">
                      {/* XP Bar */}
                      <motion.div
                        initial={{ height: 0 }}
                        animate={{ height: `${xpHeight}%` }}
                        transition={{ delay: index * 0.1 }}
                        className="w-full bg-gradient-to-t from-blue-500 to-cyan-400 rounded-sm mb-1"
                        style={{ minHeight: day.xpEarned > 0 ? '4px' : '0px' }}
                      />
                      {/* Lessons Bar */}
                      <motion.div
                        initial={{ height: 0 }}
                        animate={{ height: `${lessonHeight}%` }}
                        transition={{ delay: index * 0.1 + 0.2 }}
                        className="w-full bg-gradient-to-t from-green-500 to-emerald-400 rounded-sm"
                        style={{ minHeight: day.lessonsCompleted > 0 ? '4px' : '0px' }}
                      />
                    </div>
                    <div className="text-xs text-white/60">
                      {new Date(day.date).getDate()}
                    </div>
                  </div>
                )
              })}
            </div>
            <div className="flex justify-center gap-4 mt-2 text-xs">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <span className="text-white/60">XP</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-white/60">Lessons</span>
              </div>
            </div>
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-3 gap-3 pt-3 border-t border-white/10">
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 mb-1">
                <BookOpen className="w-3 h-3 text-green-400" />
              </div>
              <div className={`font-bold text-green-400 ${textSizes[size].value}`}>
                {totalLessons}
              </div>
              <div className={`text-white/60 ${textSizes[size].label}`}>
                Lessons
              </div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 mb-1">
                <Clock className="w-3 h-3 text-yellow-400" />
              </div>
              <div className={`font-bold text-yellow-400 ${textSizes[size].value}`}>
                {formatTime(totalTime)}
              </div>
              <div className={`text-white/60 ${textSizes[size].label}`}>
                Time
              </div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 mb-1">
                <Star className="w-3 h-3 text-purple-400" />
              </div>
              <div className={`font-bold text-purple-400 ${textSizes[size].value}`}>
                {Math.round(averageScore)}%
              </div>
              <div className={`text-white/60 ${textSizes[size].label}`}>
                Avg Score
              </div>
            </div>
          </div>
        </>
      )}

      {variant === 'compact' && (
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-3">
            <div className="text-center">
              <div className={`font-bold text-green-400 ${textSizes[size].value}`}>
                {totalLessons}
              </div>
              <div className={`text-white/60 ${textSizes[size].label}`}>
                Lessons
              </div>
            </div>
            <div className="text-center">
              <div className={`font-bold text-yellow-400 ${textSizes[size].value}`}>
                {formatTime(totalTime)}
              </div>
              <div className={`text-white/60 ${textSizes[size].label}`}>
                Time
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className={`font-bold text-purple-400 ${textSizes[size].value}`}>
              {Math.round(averageScore)}%
            </div>
            <div className={`text-white/60 ${textSizes[size].label}`}>
              Score
            </div>
          </div>
        </div>
      )}
    </motion.div>
  )
}
