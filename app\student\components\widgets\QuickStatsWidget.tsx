'use client'

import { motion } from 'framer-motion'
import { 
  Trophy, 
  Clock, 
  Target, 
  TrendingUp,
  Star,
  Zap,
  BookOpen,
  Users
} from 'lucide-react'
import { StatsWidgetProps } from './types'

export default function QuickStatsWidget({
  stats,
  showAnimation = true,
  className = '',
  size = 'md',
  variant = 'detailed'
}: StatsWidgetProps) {
  const sizeClasses = {
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6'
  }

  const textSizes = {
    sm: { title: 'text-sm', value: 'text-base', label: 'text-xs' },
    md: { title: 'text-base', value: 'text-lg', label: 'text-sm' },
    lg: { title: 'text-lg', value: 'text-xl', label: 'text-base' }
  }

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    if (hours > 0) return `${hours}h ${minutes % 60}m`
    return `${minutes}m`
  }

  const quickStats = [
    {
      label: 'Level',
      value: stats.level,
      icon: <Star className="w-4 h-4" />,
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-500/10',
      borderColor: 'border-yellow-500/30'
    },
    {
      label: 'Streak',
      value: `${stats.streak}d`,
      icon: <Zap className="w-4 h-4" />,
      color: 'text-orange-400',
      bgColor: 'bg-orange-500/10',
      borderColor: 'border-orange-500/30'
    },
    {
      label: 'Lessons',
      value: stats.lessonsCompleted,
      icon: <BookOpen className="w-4 h-4" />,
      color: 'text-green-400',
      bgColor: 'bg-green-500/10',
      borderColor: 'border-green-500/30'
    },
    {
      label: 'Score',
      value: `${stats.averageScore}%`,
      icon: <Target className="w-4 h-4" />,
      color: 'text-cyan-400',
      bgColor: 'bg-cyan-500/10',
      borderColor: 'border-cyan-500/30'
    }
  ]

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`bg-black/40 border border-gray-500/30 backdrop-blur-xl rounded-lg ${sizeClasses[size]} ${className}`}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Trophy className="w-5 h-5 text-purple-400" />
          <span className={`font-medium text-white ${textSizes[size].title}`}>
            Quick Stats
          </span>
        </div>
        <div className="flex items-center gap-1 text-xs text-white/60">
          <TrendingUp className="w-3 h-3" />
          <span>{stats.rank}</span>
        </div>
      </div>

      {variant === 'detailed' && (
        <div className="grid grid-cols-2 gap-3">
          {quickStats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={showAnimation ? { opacity: 0, y: 20 } : {}}
              animate={showAnimation ? { opacity: 1, y: 0 } : {}}
              transition={showAnimation ? { delay: index * 0.1 } : {}}
              className={`p-3 rounded-lg border ${stat.bgColor} ${stat.borderColor}`}
            >
              <div className="flex items-center justify-between mb-2">
                <span className={stat.color}>
                  {stat.icon}
                </span>
                <span className={`font-bold ${stat.color} ${textSizes[size].value}`}>
                  {stat.value}
                </span>
              </div>
              <div className={`text-white/60 ${textSizes[size].label}`}>
                {stat.label}
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {variant === 'compact' && (
        <div className="flex justify-between items-center">
          {quickStats.slice(0, 3).map((stat, index) => (
            <div key={stat.label} className="text-center">
              <div className={`${stat.color} mb-1`}>
                {stat.icon}
              </div>
              <div className={`font-bold ${stat.color} ${textSizes[size].value}`}>
                {stat.value}
              </div>
              <div className={`text-white/60 ${textSizes[size].label}`}>
                {stat.label}
              </div>
            </div>
          ))}
        </div>
      )}

      {variant === 'detailed' && (
        <div className="mt-4 pt-3 border-t border-white/10">
          <div className="flex justify-between items-center text-sm">
            <div className="flex items-center gap-2 text-white/60">
              <Clock className="w-3 h-3" />
              <span>Time Spent</span>
            </div>
            <span className="text-cyan-400 font-medium">
              {formatTime(stats.timeSpent)}
            </span>
          </div>
          <div className="flex justify-between items-center text-sm mt-2">
            <div className="flex items-center gap-2 text-white/60">
              <Trophy className="w-3 h-3" />
              <span>Badges</span>
            </div>
            <span className="text-purple-400 font-medium">
              {stats.badges}
            </span>
          </div>
        </div>
      )}
    </motion.div>
  )
}
