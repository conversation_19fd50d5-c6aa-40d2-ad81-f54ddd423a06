'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Calendar } from '@/components/ui/calendar'
import { 
  Calendar as CalendarIcon,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  Plus,
  Filter,
  Grid3X3,
  List,
  MoreHorizontal
} from 'lucide-react'
import { useCurriculumPlanner } from './CurriculumPlannerProvider'
import { ScheduleView } from '../types/curriculum'

export function CurriculumCalendar() {
  const { state, dispatch, getSelectedChild, getAssignmentsForDate } = useCurriculumPlanner()
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [viewMode, setViewMode] = useState<'calendar' | 'list'>('calendar')
  
  const selectedChild = getSelectedChild()

  const handleViewChange = (view: ScheduleView) => {
    dispatch({ type: 'SET_SCHEDULE_VIEW', payload: view })
  }

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date)
      dispatch({ type: 'SET_SELECTED_DATE', payload: date })
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500'
      case 'approved': return 'bg-cyan-500'
      case 'pending': return 'bg-yellow-500'
      case 'rejected': return 'bg-red-500'
      case 'in-progress': return 'bg-blue-500'
      default: return 'bg-gray-500'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return CheckCircle
      case 'approved': return CheckCircle
      case 'pending': return AlertCircle
      case 'rejected': return XCircle
      case 'in-progress': return Clock
      default: return AlertCircle
    }
  }

  const todayAssignments = getAssignmentsForDate(selectedDate)
  const selectedChildAssignments = selectedChild 
    ? todayAssignments.filter(a => a.childId === selectedChild.id)
    : todayAssignments

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Calendar View */}
      <div className="lg:col-span-2">
        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                <CalendarIcon className="w-5 h-5 text-cyan-400" />
                Learning Calendar
              </CardTitle>
              
              {/* View Controls */}
              <div className="flex items-center gap-2">
                {/* Schedule View Toggle */}
                <div className="flex bg-gray-800/50 rounded-lg p-1">
                  {(['daily', 'weekly', 'monthly'] as ScheduleView[]).map((view) => (
                    <Button
                      key={view}
                      size="sm"
                      variant="ghost"
                      className={`text-xs ${
                        state.scheduleView === view
                          ? 'bg-cyan-500/20 text-cyan-400'
                          : 'text-gray-400 hover:text-white'
                      }`}
                      onClick={() => handleViewChange(view)}
                    >
                      {view.charAt(0).toUpperCase() + view.slice(1)}
                    </Button>
                  ))}
                </div>

                {/* View Mode Toggle */}
                <div className="flex bg-gray-800/50 rounded-lg p-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    className={`${
                      viewMode === 'calendar'
                        ? 'bg-purple-500/20 text-purple-400'
                        : 'text-gray-400 hover:text-white'
                    }`}
                    onClick={() => setViewMode('calendar')}
                  >
                    <Grid3X3 className="w-4 h-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    className={`${
                      viewMode === 'list'
                        ? 'bg-purple-500/20 text-purple-400'
                        : 'text-gray-400 hover:text-white'
                    }`}
                    onClick={() => setViewMode('list')}
                  >
                    <List className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {viewMode === 'calendar' ? (
              <div className="space-y-4">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={handleDateSelect}
                  className="rounded-md border border-gray-700/50"
                  classNames={{
                    months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
                    month: "space-y-4",
                    caption: "flex justify-center pt-1 relative items-center text-white",
                    caption_label: "text-sm font-medium",
                    nav: "space-x-1 flex items-center",
                    nav_button: "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 text-white",
                    table: "w-full border-collapse space-y-1",
                    head_row: "flex",
                    head_cell: "text-gray-400 rounded-md w-9 font-normal text-[0.8rem]",
                    row: "flex w-full mt-2",
                    cell: "text-center text-sm p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
                    day: "h-9 w-9 p-0 font-normal text-white hover:bg-gray-700/50 rounded-md",
                    day_selected: "bg-cyan-500 text-white hover:bg-cyan-600",
                    day_today: "bg-gray-700 text-white",
                    day_outside: "text-gray-600 opacity-50",
                    day_disabled: "text-gray-600 opacity-50",
                  }}
                />

                {/* Calendar Legend */}
                <div className="flex flex-wrap gap-2 pt-4 border-t border-gray-700/30">
                  <div className="flex items-center gap-2 text-xs">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-gray-400">Completed</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs">
                    <div className="w-3 h-3 bg-cyan-500 rounded-full"></div>
                    <span className="text-gray-400">Approved</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <span className="text-gray-400">Pending</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <span className="text-gray-400">Rejected</span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                {/* List View */}
                <div className="text-center py-8">
                  <List className="w-12 h-12 text-gray-600 mx-auto mb-3" />
                  <h3 className="text-lg font-semibold text-gray-400 mb-2">List View</h3>
                  <p className="text-gray-500">Detailed list view coming soon!</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Daily Schedule Sidebar */}
      <div className="space-y-6">
        {/* Selected Date Info */}
        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardHeader>
            <CardTitle className="text-white font-space-grotesk text-lg">
              {selectedDate.toLocaleDateString('en-US', { 
                weekday: 'long',
                month: 'long', 
                day: 'numeric' 
              })}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {selectedChildAssignments.length > 0 ? (
              <div className="space-y-3">
                {selectedChildAssignments.map((assignment) => {
                  const lesson = state.lessons.find(l => l.id === assignment.lessonId)
                  const StatusIcon = getStatusIcon(assignment.status)
                  
                  return (
                    <motion.div
                      key={assignment.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="p-3 bg-gray-800/30 rounded-lg border border-gray-700/30"
                    >
                      <div className="flex items-start gap-3">
                        <div className={`w-2 h-2 rounded-full mt-2 ${getStatusColor(assignment.status)}`} />
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-white text-sm truncate">
                            {lesson?.title || 'Unknown Lesson'}
                          </h4>
                          <p className="text-xs text-gray-400 mt-1">
                            {lesson?.duration} min • {lesson?.subject}
                          </p>
                          
                          <div className="flex items-center gap-2 mt-2">
                            <Badge 
                              variant="outline" 
                              className={`text-xs ${
                                assignment.status === 'completed' ? 'border-green-500/30 text-green-400' :
                                assignment.status === 'approved' ? 'border-cyan-500/30 text-cyan-400' :
                                assignment.status === 'pending' ? 'border-yellow-500/30 text-yellow-400' :
                                assignment.status === 'rejected' ? 'border-red-500/30 text-red-400' :
                                'border-gray-500/30 text-gray-400'
                              }`}
                            >
                              <StatusIcon className="w-3 h-3 mr-1" />
                              {assignment.status}
                            </Badge>
                          </div>
                        </div>
                        
                        <Button
                          size="sm"
                          variant="ghost"
                          className="text-gray-400 hover:text-white p-1"
                        >
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </div>
                    </motion.div>
                  )
                })}
              </div>
            ) : (
              <div className="text-center py-6">
                <CalendarIcon className="w-8 h-8 text-gray-600 mx-auto mb-2" />
                <p className="text-gray-400 text-sm">No lessons scheduled</p>
                <Button
                  size="sm"
                  className="mt-3 bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Lesson
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardHeader>
            <CardTitle className="text-white font-space-grotesk text-lg">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button
              className="w-full justify-start bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
            >
              <Plus className="w-4 h-4 mr-2" />
              Schedule Lesson
            </Button>
            
            <Button
              variant="outline"
              className="w-full justify-start border-gray-600 text-gray-300 hover:bg-gray-700/50"
            >
              <Eye className="w-4 h-4 mr-2" />
              Preview Week
            </Button>
            
            <Button
              variant="outline"
              className="w-full justify-start border-gray-600 text-gray-300 hover:bg-gray-700/50"
            >
              <Filter className="w-4 h-4 mr-2" />
              Filter Lessons
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
