'use client'

import { useState, useRef, useEffect, useMemo } from 'react'
import { Canvas, use<PERSON>rame, useThree } from '@react-three/fiber'
import { OrbitControls, Plane, Html } from '@react-three/drei'
import * as THREE from 'three'
import { motion } from 'framer-motion'
import {
  Map,
  Navigation,
  Activity,
  Maximize2,
  Minimize2,
  Focus,
  User
} from 'lucide-react'

import { useDashboardStore } from '../store/dashboardStore'
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipTrigger, TooltipProvider } from '@/components/ui/tooltip'
import { Button } from '@/components/ui/button'

// Types
interface TimelineMapProps {
  className?: string
}

interface NodeData {
  id: string
  type: 'learning' | 'echo' | 'meditation' | 'timelinegen'
  position: [number, number, number]
  active: boolean
  title: string
  description: string
  color: string
  emoji: string
  participants: number
  energyLevel: number
  lastActivity: string
}

interface PlayerData {
  id: string
  name: string
  position: [number, number, number]
  color: string
  isCurrentUser: boolean
  streak: number
  status: 'active' | 'idle' | 'helping'
}

interface CollaborationPing {
  id: string
  fromPlayer: string
  nodeId: string
  position: [number, number, number]
  message: string
  timestamp: number
  urgency: 'low' | 'medium' | 'high'
}

interface MapCanvasProps {
  nodes: NodeData[]
  players: PlayerData[]
  pings: CollaborationPing[]
  selectedNode: string | null
  hoveredNode: string | null
  onNodeClick: (nodeId: string) => void
  onNodeHover: (nodeId: string | null) => void
  cameraMode: 'free' | 'follow' | 'focus'
  showTrails: boolean
}

interface NodeLegendProps {
  nodes: NodeData[]
  onNodeTypeFilter: (type: string | null) => void
  activeFilter: string | null
}

// Enhanced Interactive Node Component
function InteractiveNode({
  node,
  onClick,
  onHover,
  isSelected,
  isHovered,
  showTrails
}: {
  node: NodeData
  onClick: () => void
  onHover: (hovered: boolean) => void
  isSelected: boolean
  isHovered: boolean
  showTrails: boolean
}) {
  const meshRef = useRef<THREE.Mesh>(null)
  const ringRef = useRef<THREE.Mesh>(null)
  const particlesRef = useRef<THREE.Points>(null)

  // Node type configurations
  const nodeConfigs = {
    learning: {
      emoji: '🧠',
      baseColor: '#22D3EE',
      emissiveColor: '#22D3EE',
      geometry: 'sphere',
      scale: 1.0
    },
    echo: {
      emoji: '🌀',
      baseColor: '#8B5CF6',
      emissiveColor: '#8B5CF6',
      geometry: 'octahedron',
      scale: 1.2
    },
    meditation: {
      emoji: '🧘',
      baseColor: '#10B981',
      emissiveColor: '#10B981',
      geometry: 'tetrahedron',
      scale: 0.9
    },
    timelinegen: {
      emoji: '🧬',
      baseColor: '#F59E0B',
      emissiveColor: '#F59E0B',
      geometry: 'dodecahedron',
      scale: 1.1
    }
  }

  const config = nodeConfigs[node.type]

  // Particle system for node activity
  const particleCount = 30
  const particles = useMemo(() => {
    const positions = new Float32Array(particleCount * 3)

    for (let i = 0; i < particleCount; i++) {
      const radius = 0.5 + Math.random() * 1.0
      const theta = Math.random() * Math.PI * 2
      const phi = Math.random() * Math.PI

      positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta)
      positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta)
      positions[i * 3 + 2] = radius * Math.cos(phi)
    }

    return positions
  }, [])

  useFrame((state) => {
    if (!meshRef.current) return

    const time = state.clock.elapsedTime

    // Floating animation
    const floatY = Math.sin(time * 2 + node.position[0]) * 0.15
    meshRef.current.position.y = node.position[1] + floatY

    // Rotation based on activity and type
    if (node.active) {
      meshRef.current.rotation.y = time * (0.3 + node.energyLevel * 0.01)
      meshRef.current.rotation.x = Math.sin(time * 0.5) * 0.1
    }

    // Pulsing scale for selected/hovered nodes
    let scale = config.scale
    if (isSelected) {
      scale *= 1 + Math.sin(time * 4) * 0.15
    }
    if (isHovered) {
      scale *= 1.1
    }
    meshRef.current.scale.setScalar(scale)

    // Ring animation
    if (ringRef.current) {
      ringRef.current.rotation.z = time * 0.5
      const ringScale = 1 + Math.sin(time * 3) * 0.1 * (node.energyLevel / 100)
      ringRef.current.scale.setScalar(ringScale)
    }

    // Particle animation
    if (particlesRef.current && node.active) {
      const positions = particlesRef.current.geometry.attributes.position.array as Float32Array

      for (let i = 0; i < particleCount; i++) {
        const idx = i * 3
        positions[idx + 1] += Math.sin(time * 2 + i) * 0.01

        // Orbital motion
        const angle = time * 0.5 + (i / particleCount) * Math.PI * 2
        const radius = 0.8 + Math.sin(time + i) * 0.2
        positions[idx] = Math.cos(angle) * radius
        positions[idx + 2] = Math.sin(angle) * radius
      }

      particlesRef.current.geometry.attributes.position.needsUpdate = true
    }
  })

  // Dynamic colors based on state
  const nodeColor = isSelected ? '#F59E0B' : config.baseColor
  const emissiveIntensity = node.active ?
    (0.2 + (node.energyLevel / 100) * 0.4) :
    0.1

  return (
    <group position={node.position}>
      {/* Main Node Geometry */}
      <mesh
        ref={meshRef}
        onClick={onClick}
        onPointerOver={() => onHover(true)}
        onPointerOut={() => onHover(false)}
      >
        {config.geometry === 'sphere' && <sphereGeometry args={[0.4, 16, 16]} />}
        {config.geometry === 'octahedron' && <octahedronGeometry args={[0.4]} />}
        {config.geometry === 'tetrahedron' && <tetrahedronGeometry args={[0.4]} />}
        {config.geometry === 'dodecahedron' && <dodecahedronGeometry args={[0.4]} />}

        <meshStandardMaterial
          color={nodeColor}
          emissive={config.emissiveColor}
          emissiveIntensity={isHovered ? 0.6 : emissiveIntensity}
          transparent
          opacity={node.active ? 0.9 : 0.6}
          roughness={0.3}
          metalness={0.7}
        />
      </mesh>

      {/* Orbital Ring */}
      <mesh ref={ringRef} rotation={[Math.PI / 2, 0, 0]}>
        <ringGeometry args={[0.6, 0.7, 32]} />
        <meshBasicMaterial
          color={nodeColor}
          transparent
          opacity={isHovered ? 0.8 : 0.4}
          side={THREE.DoubleSide}
        />
      </mesh>

      {/* Activity Particles */}
      {node.active && (
        <points ref={particlesRef}>
          <bufferGeometry>
            <bufferAttribute
              attach="attributes-position"
              count={particleCount}
              array={particles}
              itemSize={3}
              args={[particles, 3]}
            />
          </bufferGeometry>
          <pointsMaterial
            color={config.baseColor}
            size={0.03}
            transparent
            opacity={0.6}
            sizeAttenuation
            blending={THREE.AdditiveBlending}
          />
        </points>
      )}

      {/* Node Label with Emoji */}
      {(isHovered || isSelected) && (
        <Html position={[0, 1, 0]} center>
          <div className="pointer-events-none text-center">
            <div className="text-2xl mb-1">{config.emoji}</div>
            <div className="text-xs text-white bg-black/50 px-2 py-1 rounded backdrop-blur-sm">
              {node.title}
            </div>
            <div className="text-xs text-white/60 mt-1">
              {node.participants} active • {node.energyLevel}% energy
            </div>
          </div>
        </Html>
      )}

      {/* Collaboration Indicator */}
      {node.participants > 1 && (
        <mesh position={[0.5, 0.5, 0]}>
          <sphereGeometry args={[0.08, 8, 8]} />
          <meshBasicMaterial color="#00FF88" />
        </mesh>
      )}
    </group>
  )
}

// Enhanced Player Tracker Component
function PlayerTracker({
  players,
  currentUserId,
  showTrails
}: {
  players: PlayerData[]
  currentUserId: string
  showTrails: boolean
}) {
  return (
    <>
      {players.map((player) => (
        <PlayerAvatar
          key={player.id}
          player={player}
          isCurrentUser={player.id === currentUserId}
          showTrail={showTrails}
        />
      ))}
    </>
  )
}

// Individual Player Avatar Component
function PlayerAvatar({
  player,
  isCurrentUser,
  showTrail
}: {
  player: PlayerData
  isCurrentUser: boolean
  showTrail: boolean
}) {
  const meshRef = useRef<THREE.Mesh>(null)
  const trailRef = useRef<THREE.Mesh>(null)
  const haloRef = useRef<THREE.Mesh>(null)

  useFrame((state) => {
    if (!meshRef.current) return

    const time = state.clock.elapsedTime

    // Floating animation
    meshRef.current.position.y = player.position[1] + Math.sin(time * 3 + player.position[0]) * 0.08

    // Rotation for current user
    if (isCurrentUser) {
      meshRef.current.rotation.y = time * 0.8
    }

    // Halo pulsing
    if (haloRef.current) {
      const scale = 1 + Math.sin(time * 2) * 0.2
      haloRef.current.scale.setScalar(scale)
      haloRef.current.rotation.z = time * 0.5
    }

    // Status-based animations
    if (player.status === 'helping') {
      const helpScale = 1 + Math.sin(time * 6) * 0.3
      meshRef.current.scale.setScalar(helpScale)
    }
  })

  // Player colors and sizes based on status
  const getPlayerConfig = () => {
    if (isCurrentUser) {
      return {
        color: '#22D3EE',
        emissiveColor: '#22D3EE',
        size: 0.2,
        emissiveIntensity: 0.6
      }
    }

    switch (player.status) {
      case 'helping':
        return {
          color: '#10B981',
          emissiveColor: '#10B981',
          size: 0.15,
          emissiveIntensity: 0.5
        }
      case 'active':
        return {
          color: player.color,
          emissiveColor: player.color,
          size: 0.12,
          emissiveIntensity: 0.4
        }
      default:
        return {
          color: '#6B7280',
          emissiveColor: '#6B7280',
          size: 0.1,
          emissiveIntensity: 0.2
        }
    }
  }

  const config = getPlayerConfig()

  return (
    <group position={player.position}>
      {/* Main Player Orb */}
      <mesh ref={meshRef}>
        <sphereGeometry args={[config.size, 12, 12]} />
        <meshStandardMaterial
          color={config.color}
          emissive={config.emissiveColor}
          emissiveIntensity={config.emissiveIntensity}
          transparent
          opacity={0.9}
          roughness={0.2}
          metalness={0.8}
        />
      </mesh>

      {/* Player Halo */}
      <mesh ref={haloRef} rotation={[Math.PI / 2, 0, 0]}>
        <ringGeometry args={[config.size * 1.5, config.size * 2, 16]} />
        <meshBasicMaterial
          color={config.color}
          transparent
          opacity={0.3}
          side={THREE.DoubleSide}
        />
      </mesh>

      {/* Player Trail */}
      {showTrail && (
        <mesh ref={trailRef} position={[0, -0.1, 0]}>
          <cylinderGeometry args={[0.02, 0.02, 0.4, 8]} />
          <meshBasicMaterial
            color={config.color}
            transparent
            opacity={0.4}
          />
        </mesh>
      )}

      {/* Status Indicator */}
      {player.status === 'helping' && (
        <mesh position={[0, config.size + 0.1, 0]}>
          <sphereGeometry args={[0.03, 8, 8]} />
          <meshBasicMaterial color="#10B981" />
        </mesh>
      )}

      {/* Streak Indicator for Current User */}
      {isCurrentUser && player.streak > 0 && (
        <Html position={[0, 0.4, 0]} center>
          <div className="pointer-events-none text-center">
            <div className="text-xs text-neural-cyan bg-black/50 px-2 py-1 rounded backdrop-blur-sm">
              🔥 {player.streak}
            </div>
          </div>
        </Html>
      )}

      {/* Player Name on Hover */}
      <Html position={[0, -0.3, 0]} center>
        <div className="pointer-events-none text-center opacity-0 hover:opacity-100 transition-opacity">
          <div className="text-xs text-white bg-black/50 px-2 py-1 rounded backdrop-blur-sm">
            {player.name}
          </div>
        </div>
      </Html>
    </group>
  )
}

// Collaboration Pings Component
function CollaborationPings({
  pings,
  onPingClick
}: {
  pings: CollaborationPing[]
  onPingClick: (ping: CollaborationPing) => void
}) {
  return (
    <>
      {pings.map((ping) => (
        <CollaborationPing
          key={ping.id}
          ping={ping}
          onClick={() => onPingClick(ping)}
        />
      ))}
    </>
  )
}

// Individual Collaboration Ping Component
function CollaborationPing({
  ping,
  onClick
}: {
  ping: CollaborationPing
  onClick: () => void
}) {
  const meshRef = useRef<THREE.Mesh>(null)
  const ringRef = useRef<THREE.Mesh>(null)

  useFrame((state) => {
    if (!meshRef.current || !ringRef.current) return

    const time = state.clock.elapsedTime
    const age = (Date.now() - ping.timestamp) / 1000 // Age in seconds

    // Fade out over time
    const opacity = Math.max(0, 1 - age / 30) // Fade over 30 seconds

    // Pulsing animation based on urgency
    const pulseSpeed = ping.urgency === 'high' ? 4 : ping.urgency === 'medium' ? 2 : 1
    const scale = 1 + Math.sin(time * pulseSpeed) * 0.3
    meshRef.current.scale.setScalar(scale)

    // Ring expansion
    const ringScale = 1 + (age * 0.5)
    ringRef.current.scale.setScalar(ringScale)

    // Update materials
    const material = meshRef.current.material as THREE.MeshBasicMaterial
    material.opacity = opacity

    const ringMaterial = ringRef.current.material as THREE.MeshBasicMaterial
    ringMaterial.opacity = opacity * 0.3
  })

  const urgencyColors = {
    low: '#10B981',
    medium: '#F59E0B',
    high: '#EF4444'
  }

  const color = urgencyColors[ping.urgency]

  return (
    <group position={ping.position} onClick={onClick}>
      {/* Main Ping Orb */}
      <mesh ref={meshRef}>
        <sphereGeometry args={[0.1, 8, 8]} />
        <meshBasicMaterial
          color={color}
          transparent
          opacity={0.8}
        />
      </mesh>

      {/* Expanding Ring */}
      <mesh ref={ringRef} rotation={[Math.PI / 2, 0, 0]}>
        <ringGeometry args={[0.2, 0.3, 16]} />
        <meshBasicMaterial
          color={color}
          transparent
          opacity={0.3}
          side={THREE.DoubleSide}
        />
      </mesh>

      {/* Help Icon */}
      <Html position={[0, 0.2, 0]} center>
        <div className="pointer-events-none text-center">
          <div className="text-lg">🆘</div>
        </div>
      </Html>
    </group>
  )
}

// Node Legend Component
function NodeLegend({
  nodes,
  onNodeTypeFilter,
  activeFilter
}: NodeLegendProps) {
  const nodeTypes = [
    { type: 'learning', emoji: '🧠', label: 'Learning', color: '#22D3EE' },
    { type: 'echo', emoji: '🌀', label: 'Echo Field', color: '#8B5CF6' },
    { type: 'meditation', emoji: '🧘', label: 'Meditation', color: '#10B981' },
    { type: 'timelinegen', emoji: '🧬', label: 'TimelineGen', color: '#F59E0B' }
  ]

  const getNodeCount = (type: string) => {
    return nodes.filter(node => node.type === type).length
  }

  const getActiveCount = (type: string) => {
    return nodes.filter(node => node.type === type && node.active).length
  }

  return (
    <div className="space-y-3">
      <div className="text-sm font-medium text-white/90 mb-4">Node Types</div>

      {nodeTypes.map((nodeType) => {
        const total = getNodeCount(nodeType.type)
        const active = getActiveCount(nodeType.type)
        const isFiltered = activeFilter === nodeType.type

        return (
          <TooltipProvider key={nodeType.type}>
            <Tooltip>
              <TooltipTrigger asChild>
                <motion.button
                  className={`
                    w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200
                    ${isFiltered
                      ? 'bg-gradient-to-r from-neural-cyan/20 to-quantum-purple/20 border border-neural-cyan/40'
                      : 'bg-space-dark/60 border border-white/10 hover:border-white/20'
                    }
                  `}
                  onClick={() => onNodeTypeFilter(isFiltered ? null : nodeType.type)}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="text-xl">{nodeType.emoji}</div>
                  <div className="flex-1 text-left">
                    <div className="text-sm font-medium text-white/90">{nodeType.label}</div>
                    <div className="text-xs text-white/60">
                      {active}/{total} active
                    </div>
                  </div>
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: nodeType.color }}
                  />
                </motion.button>
              </TooltipTrigger>
              <TooltipContent className="bg-gradient-to-br from-space-dark via-black to-space-blue border border-neural-cyan/30">
                <div className="text-xs">
                  <div className="font-bold">{nodeType.label} Nodes</div>
                  <div className="text-white/80 mt-1">
                    {active} active out of {total} total
                  </div>
                  <div className="text-white/60 mt-1">
                    Click to filter view
                  </div>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
      })}

      {/* Clear Filter Button */}
      {activeFilter && (
        <motion.button
          className="w-full p-2 text-xs text-white/60 hover:text-white/80 transition-colors"
          onClick={() => onNodeTypeFilter(null)}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          Clear Filter
        </motion.button>
      )}
    </div>
  )
}

// Biome Environment Component
function BiomeEnvironment({ viewMode }: { viewMode: string }) {
  const particlesRef = useRef<THREE.Points>(null)

  useEffect(() => {
    if (!particlesRef.current) return
    
    const particleCount = 200
    const positions = new Float32Array(particleCount * 3)
    
    for (let i = 0; i < particleCount; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 20
      positions[i * 3 + 1] = (Math.random() - 0.5) * 20
      positions[i * 3 + 2] = (Math.random() - 0.5) * 20
    }
    
    particlesRef.current.geometry.setAttribute(
      'position',
      new THREE.BufferAttribute(positions, 3)
    )
  }, [])
  
  useFrame((state) => {
    if (!particlesRef.current) return
    
    const time = state.clock.elapsedTime
    particlesRef.current.rotation.y = time * 0.1
    particlesRef.current.rotation.x = time * 0.05
  })
  
  return (
    <>
      {/* Ground Plane */}
      <Plane args={[20, 20]} rotation={[-Math.PI / 2, 0, 0]} position={[0, -2, 0]}>
        <meshBasicMaterial
          color="#0A0F1C"
          transparent
          opacity={0.3}
          side={THREE.DoubleSide}
        />
      </Plane>
      
      {/* Ambient Particles */}
      <points ref={particlesRef}>
        <bufferGeometry />
        <pointsMaterial
          color="#22D3EE"
          size={0.02}
          transparent
          opacity={0.6}
          sizeAttenuation
        />
      </points>
      
      {/* Grid Lines */}
      <gridHelper args={[20, 20, '#1A2332', '#1A2332']} position={[0, -2, 0]} />
    </>
  )
}

// Camera Controller
function CameraController({ viewMode }: { viewMode: string }) {
  const { camera } = useThree()
  
  useEffect(() => {
    switch (viewMode) {
      case 'explore':
        camera.position.set(0, 5, 8)
        break
      case 'focus':
        camera.position.set(0, 2, 5)
        break
      case 'assist':
        camera.position.set(0, 8, 0)
        break
      case 'syntropy':
        camera.position.set(0, 10, 10)
        break
    }
  }, [viewMode, camera])
  
  return null
}

// Main Timeline Map Component
export default function TimelineMap({ className }: TimelineMapProps) {
  const { playerData, timelineData, setSelectedNode, selectedNode } = useDashboardStore()
  const [hoveredNode, setHoveredNode] = useState<string | null>(null)
  const [cameraMode, setCameraMode] = useState<'free' | 'follow' | 'focus'>('free')
  const [showTrails, setShowTrails] = useState(true)
  const [nodeTypeFilter, setNodeTypeFilter] = useState<string | null>(null)
  const [isCollapsed, setIsCollapsed] = useState(false)

  // Enhanced node data with additional properties
  const enhancedNodes: NodeData[] = [
    {
      id: 'learning-node-1',
      type: 'learning',
      position: [-4, 0, -3],
      active: true,
      title: 'Neural Learning Hub',
      description: 'AI-powered adaptive learning experiences',
      color: '#22D3EE',
      emoji: '🧠',
      participants: 12,
      energyLevel: 85,
      lastActivity: '2 min ago'
    },
    {
      id: 'echo-field-1',
      type: 'echo',
      position: [4, 0, -3],
      active: true,
      title: 'Echo Field Alpha',
      description: 'Community wisdom sharing space',
      color: '#8B5CF6',
      emoji: '🌀',
      participants: 8,
      energyLevel: 72,
      lastActivity: '5 min ago'
    },
    {
      id: 'meditation-node-1',
      type: 'meditation',
      position: [-3, 0, 4],
      active: false,
      title: 'Quantum Meditation',
      description: 'Consciousness expansion chamber',
      color: '#10B981',
      emoji: '🧘',
      participants: 3,
      energyLevel: 45,
      lastActivity: '15 min ago'
    },
    {
      id: 'timelinegen-1',
      type: 'timelinegen',
      position: [3, 0, 4],
      active: true,
      title: 'TimelineGen Forge',
      description: 'Reality synthesis workshop',
      color: '#F59E0B',
      emoji: '🧬',
      participants: 15,
      energyLevel: 95,
      lastActivity: '1 min ago'
    },
    {
      id: 'learning-node-2',
      type: 'learning',
      position: [0, 0, -6],
      active: false,
      title: 'Advanced Neural Lab',
      description: 'Deep learning research facility',
      color: '#22D3EE',
      emoji: '🧠',
      participants: 5,
      energyLevel: 60,
      lastActivity: '8 min ago'
    },
    {
      id: 'echo-field-2',
      type: 'echo',
      position: [6, 0, 0],
      active: true,
      title: 'Echo Field Beta',
      description: 'Collaborative knowledge network',
      color: '#8B5CF6',
      emoji: '🌀',
      participants: 20,
      energyLevel: 88,
      lastActivity: '30 sec ago'
    }
  ]

  // Mock player data
  const mockPlayers: PlayerData[] = [
    {
      id: 'current-user',
      name: playerData?.username || 'Player',
      position: [0, 0, 0],
      color: '#22D3EE',
      isCurrentUser: true,
      streak: playerData?.stats?.learningStreak || 0,
      status: 'active'
    },
    {
      id: 'player-2',
      name: 'Luna',
      position: [-2, 0, -1],
      color: '#8B5CF6',
      isCurrentUser: false,
      streak: 12,
      status: 'helping'
    },
    {
      id: 'player-3',
      name: 'Zara',
      position: [1, 0, 2],
      color: '#10B981',
      isCurrentUser: false,
      streak: 8,
      status: 'active'
    }
  ]

  // Mock collaboration pings
  const mockPings: CollaborationPing[] = [
    {
      id: 'ping-1',
      fromPlayer: 'Luna',
      nodeId: 'learning-node-1',
      position: [-3, 0.5, -2],
      message: 'Need help with quantum mechanics!',
      timestamp: Date.now() - 5000,
      urgency: 'medium'
    }
  ]

  // Filter nodes based on type filter
  const filteredNodes = nodeTypeFilter
    ? enhancedNodes.filter(node => node.type === nodeTypeFilter)
    : enhancedNodes

  const handleNodeClick = (nodeId: string) => {
    setSelectedNode(nodeId)
  }

  const handleNodeHover = (nodeId: string | null) => {
    setHoveredNode(nodeId)
  }

  const handlePingClick = (ping: CollaborationPing) => {
    // Focus camera on the ping location
    setCameraMode('focus')
    setSelectedNode(ping.nodeId)
  }
  
  return (
    <TooltipProvider>
      <div className={`h-full flex relative ${className}`}>
        {/* Left Panel - Floating UI */}
        <motion.div
          className={`
            absolute left-4 top-4 bottom-4 z-10
            ${isCollapsed ? 'w-16' : 'w-80'}
            transition-all duration-300
          `}
          initial={{ x: -100, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.6 }}
        >
          <div className="h-full gaming-panel rounded-2xl overflow-hidden" style={{
            background: 'linear-gradient(135deg, rgba(10, 15, 28, 0.95) 0%, rgba(26, 35, 50, 0.9) 50%, rgba(10, 15, 28, 0.95) 100%)',
            backdropFilter: 'blur(16px)',
            border: '1px solid rgba(34, 211, 238, 0.3)',
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 30px rgba(34, 211, 238, 0.1)'
          }}>
            {/* Panel Header */}
            <div className="p-6 border-b" style={{
              borderColor: 'rgba(34, 211, 238, 0.2)',
              background: 'linear-gradient(90deg, rgba(34, 211, 238, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%)'
            }}>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    className="p-2 rounded-lg bg-neural-cyan/10 border border-neural-cyan/30"
                  >
                    <Map className="w-5 h-5 text-neural-cyan" />
                  </motion.div>
                  {!isCollapsed && (
                    <div>
                      <h3 className="text-lg font-orbitron font-bold text-neural-cyan">Timeline Map</h3>
                      <p className="text-xs text-white/60">Nanoverse Explorer</p>
                    </div>
                  )}
                </div>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setIsCollapsed(!isCollapsed)}
                  className="p-2"
                >
                  {isCollapsed ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
                </Button>
              </div>
            </div>

            {!isCollapsed && (
              <div className="flex-1 overflow-y-auto p-4 space-y-6">
                {/* Camera Controls */}
                <div className="space-y-3">
                  <div className="text-sm font-medium text-white/90">Navigation</div>
                  <div className="grid grid-cols-3 gap-2">
                    {(['free', 'follow', 'focus'] as const).map((mode) => (
                      <Button
                        key={mode}
                        size="sm"
                        variant={cameraMode === mode ? "quantum" : "ghost"}
                        onClick={() => setCameraMode(mode)}
                        className="text-xs"
                      >
                        {mode === 'free' && <Navigation className="w-3 h-3" />}
                        {mode === 'follow' && <User className="w-3 h-3" />}
                        {mode === 'focus' && <Focus className="w-3 h-3" />}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Node Legend */}
                <NodeLegend
                  nodes={enhancedNodes}
                  onNodeTypeFilter={setNodeTypeFilter}
                  activeFilter={nodeTypeFilter}
                />

                {/* Map Options */}
                <div className="space-y-3">
                  <div className="text-sm font-medium text-white/90">Options</div>
                  <div className="space-y-2">
                    <label className="flex items-center gap-2 text-sm text-white/80">
                      <input
                        type="checkbox"
                        checked={showTrails}
                        onChange={(e) => setShowTrails(e.target.checked)}
                        className="rounded border-neural-cyan/30"
                      />
                      Show Player Trails
                    </label>
                  </div>
                </div>

                {/* Active Collaborations */}
                {mockPings.length > 0 && (
                  <div className="space-y-3">
                    <div className="text-sm font-medium text-white/90">Help Requests</div>
                    {mockPings.map((ping) => (
                      <motion.div
                        key={ping.id}
                        className="p-3 rounded-lg bg-gradient-to-r from-flame-orange/10 to-flame-red/10 border border-flame-orange/30 cursor-pointer"
                        onClick={() => handlePingClick(ping)}
                        whileHover={{ scale: 1.02 }}
                      >
                        <div className="flex items-center gap-2 mb-2">
                          <div className="text-lg">🆘</div>
                          <div className="text-sm font-medium text-flame-orange">{ping.fromPlayer}</div>
                        </div>
                        <div className="text-xs text-white/80">{ping.message}</div>
                      </motion.div>
                    ))}
                  </div>
                )}

                {/* Map Stats */}
                <div className="space-y-3">
                  <div className="text-sm font-medium text-white/90">Statistics</div>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="p-3 rounded-lg bg-neural-cyan/10 border border-neural-cyan/30">
                      <div className="text-xs text-white/60">Active Nodes</div>
                      <div className="text-lg font-bold text-neural-cyan">
                        {enhancedNodes.filter(n => n.active).length}
                      </div>
                    </div>
                    <div className="p-3 rounded-lg bg-quantum-purple/10 border border-quantum-purple/30">
                      <div className="text-xs text-white/60">Players</div>
                      <div className="text-lg font-bold text-quantum-purple">
                        {mockPlayers.length}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </motion.div>

        {/* Main 3D Canvas */}
        <div className="flex-1 relative">
          <Canvas
            camera={{ position: [0, 8, 12], fov: 60 }}
            className="w-full h-full"
          >
            {/* Enhanced Lighting */}
            <ambientLight intensity={0.3} />
            <pointLight position={[10, 10, 10]} intensity={0.8} color="#22D3EE" />
            <pointLight position={[-10, 10, -10]} intensity={0.6} color="#8B5CF6" />
            <pointLight position={[0, 15, 0]} intensity={0.4} color="#F59E0B" />
            <directionalLight position={[5, 10, 5]} intensity={0.5} />

            {/* Environment */}
            <BiomeEnvironment viewMode="explore" />

            {/* Players */}
            <PlayerTracker
              players={mockPlayers}
              currentUserId="current-user"
              showTrails={showTrails}
            />

            {/* Interactive Nodes */}
            {filteredNodes.map((node) => (
              <InteractiveNode
                key={node.id}
                node={node}
                onClick={() => handleNodeClick(node.id)}
                onHover={(hovered: boolean) => handleNodeHover(hovered ? node.id : null)}
                isSelected={selectedNode === node.id}
                isHovered={hoveredNode === node.id}
                showTrails={showTrails}
              />
            ))}

            {/* Collaboration Pings */}
            <CollaborationPings
              pings={mockPings}
              onPingClick={handlePingClick}
            />

            {/* Camera Controls */}
            <OrbitControls
              enablePan={cameraMode === 'free'}
              enableZoom={true}
              enableRotate={true}
              maxDistance={20}
              minDistance={4}
              maxPolarAngle={Math.PI / 2.2}
              minPolarAngle={Math.PI / 6}
            />
          </Canvas>

          {/* Floating HUD Elements */}
          <div className="absolute top-4 right-4 space-y-2">
            <motion.div
              className="gaming-panel-inner p-3 rounded-lg"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
            >
              <div className="flex items-center gap-2 mb-2">
                <Activity className="w-4 h-4 text-neural-cyan" />
                <span className="text-sm text-white/90">Live Activity</span>
              </div>
              <div className="text-xs text-white/60">
                {enhancedNodes.filter(n => n.active).length} nodes active
              </div>
            </motion.div>

            {selectedNode && (
              <motion.div
                className="gaming-panel-inner p-3 rounded-lg max-w-xs"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
              >
                <div className="text-sm font-medium text-neural-cyan mb-1">
                  {enhancedNodes.find(n => n.id === selectedNode)?.title}
                </div>
                <div className="text-xs text-white/60">
                  {enhancedNodes.find(n => n.id === selectedNode)?.description}
                </div>
              </motion.div>
            )}

            {/* Quick Controls */}
            <motion.div
              className="gaming-panel-inner p-3 rounded-lg"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              <div className="text-xs text-white/60 mb-2">Quick Controls</div>
              <div className="space-y-1 text-xs text-white/50">
                <div>• Click nodes to enter</div>
                <div>• Drag to rotate view</div>
                <div>• Scroll to zoom</div>
                <div>• Use panel for filters</div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}
