"use client"

import React, { useEffect, useRef } from 'react'
import { useFrame, useThree } from '@react-three/fiber'
import * as THREE from 'three'
import { renderingPipeline } from '@/utils/renderingPipeline'

interface RenderOptimizerProps {
  enableOcclusionCulling?: boolean
  enableRenderOrderOptimization?: boolean
  enableMaterialBatching?: boolean
  performanceTarget?: 'low' | 'medium' | 'high'
}

/**
 * RenderOptimizer component that automatically optimizes rendering performance
 * Should be placed inside Canvas component to access Three.js context
 */
export function RenderOptimizer({
  enableOcclusionCulling = true,
  enableRenderOrderOptimization = true,
  enableMaterialBatching = true,
  performanceTarget = 'medium'
}: RenderOptimizerProps) {
  const { gl, scene, camera } = useThree()
  const lastOptimizationTime = useRef(0)
  const optimizationInterval = useRef(1000) // 1 second default
  
  // Initialize rendering pipeline
  useEffect(() => {
    renderingPipeline.initialize(gl, scene, camera)
    
    // Set optimization interval based on performance target
    switch (performanceTarget) {
      case 'low':
        optimizationInterval.current = 2000 // 2 seconds
        break
      case 'medium':
        optimizationInterval.current = 1000 // 1 second
        break
      case 'high':
        optimizationInterval.current = 500 // 0.5 seconds
        break
    }
    
    console.log(`RenderOptimizer initialized with ${performanceTarget} performance target`)
    
    return () => {
      renderingPipeline.dispose()
    }
  }, [gl, scene, camera, performanceTarget])

  // Optimization frame loop
  useFrame((state) => {
    const currentTime = state.clock.elapsedTime * 1000
    
    // Only optimize at specified intervals
    if (currentTime - lastOptimizationTime.current < optimizationInterval.current) {
      return
    }
    
    lastOptimizationTime.current = currentTime
    
    // Reset stats
    renderingPipeline.resetStats()
    
    // Get all renderable objects
    const renderableObjects: THREE.Object3D[] = []
    scene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.visible) {
        renderableObjects.push(object)
      }
    })
    
    if (renderableObjects.length === 0) return
    
    // Apply optimizations
    let optimizedObjects = renderableObjects
    
    // 1. Occlusion culling
    if (enableOcclusionCulling) {
      optimizedObjects = renderingPipeline.performOcclusionCulling(optimizedObjects)
    }
    
    // 2. Render order optimization
    if (enableRenderOrderOptimization) {
      optimizedObjects = renderingPipeline.optimizeRenderOrder(optimizedObjects)
      
      // Apply optimized render order
      optimizedObjects.forEach((obj, index) => {
        obj.renderOrder = index
      })
    }
    
    // 3. Material batching
    if (enableMaterialBatching) {
      renderingPipeline.optimizeMaterials(optimizedObjects)
    }
    
    // Log optimization stats periodically
    if (Math.floor(currentTime / 5000) % 5 === 0) { // Every 25 seconds
      const stats = renderingPipeline.getStats()
      console.log('Render optimization stats:', stats)
    }
  })

  return null // This component doesn't render anything visible
}

/**
 * Advanced LOD (Level of Detail) component
 * Automatically adjusts geometry detail based on distance from camera
 */
interface LODOptimizerProps {
  children: React.ReactNode
  distances?: number[]
  geometryLODs?: THREE.BufferGeometry[]
}

export function LODOptimizer({ 
  children, 
  distances = [10, 25, 50], 
  geometryLODs = [] 
}: LODOptimizerProps) {
  const groupRef = useRef<THREE.Group>(null)
  const { camera } = useThree()
  const currentLOD = useRef(0)

  useFrame(() => {
    if (!groupRef.current) return

    const distance = groupRef.current.position.distanceTo(camera.position)
    let newLOD = 0

    // Determine LOD level based on distance
    for (let i = 0; i < distances.length; i++) {
      if (distance > distances[i]) {
        newLOD = i + 1
      }
    }

    // Update LOD if changed
    if (newLOD !== currentLOD.current) {
      currentLOD.current = newLOD
      
      // Apply LOD to all meshes in group
      groupRef.current.traverse((object) => {
        if (object instanceof THREE.Mesh) {
          // Hide object if beyond maximum LOD
          if (newLOD >= distances.length + 1) {
            object.visible = false
          } else {
            object.visible = true
            
            // Apply geometry LOD if available
            if (geometryLODs[newLOD]) {
              object.geometry = geometryLODs[newLOD]
            }
            
            // Adjust material quality based on LOD
            const material = object.material as THREE.Material
            if (material instanceof THREE.MeshStandardMaterial) {
              // Reduce material quality for distant objects
              material.roughness = Math.min(1, 0.5 + newLOD * 0.2)
              material.metalness = Math.max(0, material.metalness - newLOD * 0.1)
            }
          }
        }
      })
    }
  })

  return <group ref={groupRef}>{children}</group>
}

/**
 * Frustum culling component
 * Automatically hides objects outside camera view
 */
interface FrustumCullerProps {
  children: React.ReactNode
  margin?: number
}

export function FrustumCuller({ children, margin = 0 }: FrustumCullerProps) {
  const groupRef = useRef<THREE.Group>(null)
  const { camera } = useThree()
  const frustum = useRef(new THREE.Frustum())
  const matrix = useRef(new THREE.Matrix4())

  useFrame(() => {
    if (!groupRef.current) return

    // Update frustum from camera
    matrix.current.multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse)
    frustum.current.setFromProjectionMatrix(matrix.current)

    // Test all children against frustum
    groupRef.current.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        // Get bounding sphere
        if (!object.geometry.boundingSphere) {
          object.geometry.computeBoundingSphere()
        }

        if (object.geometry.boundingSphere) {
          const sphere = object.geometry.boundingSphere.clone()
          sphere.applyMatrix4(object.matrixWorld)
          sphere.radius += margin

          // Test against frustum
          object.visible = frustum.current.intersectsSphere(sphere)
        }
      }
    })
  })

  return <group ref={groupRef}>{children}</group>
}

/**
 * Material optimizer component
 * Automatically optimizes materials based on performance requirements
 */
interface MaterialOptimizerProps {
  children: React.ReactNode
  qualityLevel?: 'low' | 'medium' | 'high'
}

export function MaterialOptimizer({ children, qualityLevel = 'medium' }: MaterialOptimizerProps) {
  const groupRef = useRef<THREE.Group>(null)

  useEffect(() => {
    if (!groupRef.current) return

    groupRef.current.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        const material = object.material as THREE.Material

        if (material instanceof THREE.MeshStandardMaterial) {
          switch (qualityLevel) {
            case 'low':
              // Convert to basic material for performance
              const basicMaterial = new THREE.MeshBasicMaterial({
                color: material.color,
                map: material.map,
                transparent: material.transparent,
                opacity: material.opacity
              })
              object.material = basicMaterial
              material.dispose()
              break

            case 'medium':
              // Reduce material complexity
              material.roughness = Math.max(0.5, material.roughness)
              material.metalness = Math.min(0.5, material.metalness)
              break

            case 'high':
              // Keep full material quality
              break
          }
        }
      }
    })
  }, [qualityLevel])

  return <group ref={groupRef}>{children}</group>
}

/**
 * Composite render optimization component
 * Combines all optimization techniques
 */
interface RenderOptimizationSuiteProps {
  children: React.ReactNode
  performanceTarget?: 'low' | 'medium' | 'high'
  enableLOD?: boolean
  enableFrustumCulling?: boolean
  enableMaterialOptimization?: boolean
  lodDistances?: number[]
}

export function RenderOptimizationSuite({
  children,
  performanceTarget = 'medium',
  enableLOD = true,
  enableFrustumCulling = true,
  enableMaterialOptimization = true,
  lodDistances = [15, 30, 60]
}: RenderOptimizationSuiteProps) {
  let optimizedChildren = children

  // Apply optimizations in order
  if (enableMaterialOptimization) {
    optimizedChildren = (
      <MaterialOptimizer qualityLevel={performanceTarget}>
        {optimizedChildren}
      </MaterialOptimizer>
    )
  }

  if (enableLOD) {
    optimizedChildren = (
      <LODOptimizer distances={lodDistances}>
        {optimizedChildren}
      </LODOptimizer>
    )
  }

  if (enableFrustumCulling) {
    optimizedChildren = (
      <FrustumCuller margin={5}>
        {optimizedChildren}
      </FrustumCuller>
    )
  }

  return (
    <>
      <RenderOptimizer
        performanceTarget={performanceTarget}
        enableOcclusionCulling={true}
        enableRenderOrderOptimization={true}
        enableMaterialBatching={true}
      />
      {optimizedChildren}
    </>
  )
}

export default RenderOptimizer
