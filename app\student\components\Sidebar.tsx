'use client'

import { useState, useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { motion } from 'framer-motion'

import { usePlayerData, useDashboardStore } from '../store/dashboardStore'
import { SidebarHeader } from './sidebar/SidebarHeader'
import { SidebarNavigation } from './sidebar/SidebarNavigation'
import { SidebarBottomMenu } from './sidebar/SidebarBottomMenu'
import { SidebarEffects } from './sidebar/SidebarEffects'
import { useEvolutionTheme } from './sidebar/hooks/useEvolutionTheme'
import { useSidebarKeyboardShortcuts } from './sidebar/hooks/useSidebarKeyboardShortcuts'
import { getMenuItems, getBottomMenuItems } from './sidebar/menuConfig'
import { SidebarProps, MenuItem } from './sidebar/types'

export default function Sidebar({ className, onCollapseChange }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [activeItem, setActiveItem] = useState('dashboard')
  const [compactMode, setCompactMode] = useState(false)
  const [showCommandPalette, setShowCommandPalette] = useState(false)

  const router = useRouter()
  const pathname = usePathname()
  const playerData = usePlayerData()
  const { setActivePanel } = useDashboardStore()
  
  const evolutionTheme = useEvolutionTheme()

  // Update active item based on current pathname
  useEffect(() => {
    const currentPath = pathname.split('/').pop() || 'dashboard'
    setActiveItem(currentPath)
  }, [pathname])

  const menuItems = getMenuItems(playerData, null, setActiveItem, setActivePanel)
  const bottomMenuItems = getBottomMenuItems()

  // Keyboard shortcuts
  useSidebarKeyboardShortcuts({
    menuItems,
    setActiveItem,
    setShowCommandPalette,
    setCompactMode
  })

  const handleItemClick = (item: MenuItem) => {
    setActiveItem(item.id)
    if (item.route) {
      router.push(item.route)
    } else if (item.action) {
      item.action()
    }
  }

  const handleToggleCollapse = () => {
    const newCollapsed = !isCollapsed
    setIsCollapsed(newCollapsed)
    onCollapseChange?.(newCollapsed)
  }

  return (
    <motion.div
      className={`h-screen bg-gradient-to-b from-space-dark via-space-blue to-space-dark border-r-2 border-neural-cyan/20 backdrop-blur-xl relative overflow-hidden ${className}`}
      initial={{ width: isCollapsed ? 80 : 280 }}
      animate={{ width: isCollapsed ? 80 : 280 }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      onAnimationComplete={() => onCollapseChange?.(isCollapsed)}
    >
      <SidebarEffects evolutionTheme={evolutionTheme} />
      
      <SidebarHeader
        isCollapsed={isCollapsed}
        evolutionTheme={evolutionTheme}
        onToggleCollapse={handleToggleCollapse}
      />
      
      <SidebarNavigation
        menuItems={menuItems}
        activeItem={activeItem}
        isCollapsed={isCollapsed}
        evolutionTheme={evolutionTheme}
        onItemClick={handleItemClick}
      />
      
      <SidebarBottomMenu
        bottomMenuItems={bottomMenuItems}
        activeItem={activeItem}
        isCollapsed={isCollapsed}
        evolutionTheme={evolutionTheme}
        onItemClick={handleItemClick}
      />
    </motion.div>
  )
}
