import type { Metadata } from "next";
import "@/style/globals.css";

export const metadata: Metadata = {
  title: "NanoHero - Where Young Minds Become Digital Heroes",
  description: "Safe, educational tech experiences for young learners. Learn coding, cybersecurity, and digital citizenship through interactive games and projects.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className="font-inter antialiased"
        suppressHydrationWarning={true}
      >
        {children}
      </body>
    </html>
  );
}
