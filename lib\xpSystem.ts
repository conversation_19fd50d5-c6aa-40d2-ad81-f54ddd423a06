// XP System for Creator Studio Integration

export interface XPAction {
  id: string
  name: string
  description: string
  baseXP: number
  category: 'content' | 'engagement' | 'social' | 'achievement' | 'moderation'
  multipliers?: {
    quality?: number
    engagement?: number
    streak?: number
    firstTime?: number
  }
  cooldown?: number // in hours
  maxDaily?: number
  requirements?: string[]
}

export interface XPTransaction {
  id: string
  userId: string
  action: string
  amount: number
  multiplier: number
  timestamp: string
  metadata?: Record<string, any>
}

export interface UserXPData {
  userId: string
  totalXP: number
  level: number
  currentLevelXP: number
  nextLevelXP: number
  weeklyXP: number
  monthlyXP: number
  streak: number
  lastActivity: string
  achievements: string[]
  badges: string[]
}

// XP Actions for Creator Studio
export const CREATOR_XP_ACTIONS: Record<string, XPAction> = {
  // Content Creation
  CREATE_BLOG_POST: {
    id: 'create_blog_post',
    name: 'Create Blog Post',
    description: 'Publish a new blog post',
    baseXP: 50,
    category: 'content',
    multipliers: {
      quality: 1.5, // Based on content analysis
      firstTime: 2.0
    },
    maxDaily: 5
  },
  CREATE_JOURNAL_ENTRY: {
    id: 'create_journal_entry',
    name: 'Create Journal Entry',
    description: 'Write a personal journal entry',
    baseXP: 25,
    category: 'content',
    maxDaily: 3
  },
  UPLOAD_VIDEO: {
    id: 'upload_video',
    name: 'Upload Video',
    description: 'Upload and publish a video',
    baseXP: 100,
    category: 'content',
    multipliers: {
      quality: 2.0,
      firstTime: 3.0
    },
    maxDaily: 3
  },
  UPDATE_PROFILE: {
    id: 'update_profile',
    name: 'Update Profile',
    description: 'Customize your profile',
    baseXP: 20,
    category: 'social',
    cooldown: 24,
    maxDaily: 1
  },

  // Engagement Actions
  RECEIVE_LIKE: {
    id: 'receive_like',
    name: 'Receive Like',
    description: 'Someone likes your content',
    baseXP: 5,
    category: 'engagement',
    maxDaily: 50
  },
  RECEIVE_COMMENT: {
    id: 'receive_comment',
    name: 'Receive Comment',
    description: 'Someone comments on your content',
    baseXP: 10,
    category: 'engagement',
    maxDaily: 25
  },
  RECEIVE_SHARE: {
    id: 'receive_share',
    name: 'Receive Share',
    description: 'Someone shares your content',
    baseXP: 15,
    category: 'engagement',
    maxDaily: 20
  },
  GIVE_HELPFUL_COMMENT: {
    id: 'give_helpful_comment',
    name: 'Give Helpful Comment',
    description: 'Leave a helpful comment on content',
    baseXP: 8,
    category: 'social',
    maxDaily: 15
  },

  // Social Actions
  FOLLOW_USER: {
    id: 'follow_user',
    name: 'Follow User',
    description: 'Follow another creator',
    baseXP: 3,
    category: 'social',
    maxDaily: 10
  },
  GET_FOLLOWER: {
    id: 'get_follower',
    name: 'Gain Follower',
    description: 'Someone follows you',
    baseXP: 10,
    category: 'social',
    maxDaily: 20
  },

  // Quality & Moderation
  CONTENT_APPROVED: {
    id: 'content_approved',
    name: 'Content Approved',
    description: 'Your content passes moderation',
    baseXP: 15,
    category: 'moderation'
  },
  HELP_MODERATE: {
    id: 'help_moderate',
    name: 'Help Moderate',
    description: 'Report inappropriate content',
    baseXP: 20,
    category: 'moderation',
    maxDaily: 5
  },

  // Achievements
  COMPLETE_PROFILE: {
    id: 'complete_profile',
    name: 'Complete Profile',
    description: 'Fill out all profile sections',
    baseXP: 100,
    category: 'achievement',
    multipliers: {
      firstTime: 1.0
    }
  },
  FIRST_VIRAL_CONTENT: {
    id: 'first_viral_content',
    name: 'First Viral Content',
    description: 'Content reaches 100+ views',
    baseXP: 200,
    category: 'achievement'
  },
  CONSISTENT_CREATOR: {
    id: 'consistent_creator',
    name: 'Consistent Creator',
    description: 'Post content for 7 days straight',
    baseXP: 300,
    category: 'achievement'
  }
}

// Level progression system
export const LEVEL_THRESHOLDS = [
  0,     // Level 1
  100,   // Level 2
  250,   // Level 3
  450,   // Level 4
  700,   // Level 5
  1000,  // Level 6
  1350,  // Level 7
  1750,  // Level 8
  2200,  // Level 9
  2700,  // Level 10
  3250,  // Level 11
  3850,  // Level 12
  4500,  // Level 13
  5200,  // Level 14
  5950,  // Level 15
  6750,  // Level 16
  7600,  // Level 17
  8500,  // Level 18
  9450,  // Level 19
  10450, // Level 20
]

/**
 * Calculate user level from total XP
 */
export function calculateLevel(totalXP: number): number {
  for (let i = LEVEL_THRESHOLDS.length - 1; i >= 0; i--) {
    if (totalXP >= LEVEL_THRESHOLDS[i]) {
      return i + 1
    }
  }
  return 1
}

/**
 * Get XP required for next level
 */
export function getNextLevelXP(currentLevel: number): number {
  if (currentLevel >= LEVEL_THRESHOLDS.length) {
    return LEVEL_THRESHOLDS[LEVEL_THRESHOLDS.length - 1] + (currentLevel - LEVEL_THRESHOLDS.length + 1) * 1000
  }
  return LEVEL_THRESHOLDS[currentLevel] || 0
}

/**
 * Calculate XP for current level
 */
export function getCurrentLevelXP(totalXP: number, level: number): number {
  const levelStartXP = level > 1 ? LEVEL_THRESHOLDS[level - 2] : 0
  return totalXP - levelStartXP
}

/**
 * Award XP for an action
 */
export function awardXP(
  action: XPAction,
  metadata: Record<string, any> = {}
): { amount: number; multiplier: number; breakdown: string[] } {
  let amount = action.baseXP
  let totalMultiplier = 1
  const breakdown: string[] = [`Base: ${action.baseXP} XP`]

  // Apply multipliers
  if (action.multipliers) {
    // Quality multiplier
    if (action.multipliers.quality && metadata.qualityScore) {
      const qualityMultiplier = Math.min(action.multipliers.quality, 1 + (metadata.qualityScore - 0.5))
      totalMultiplier *= qualityMultiplier
      breakdown.push(`Quality bonus: x${qualityMultiplier.toFixed(1)}`)
    }

    // Engagement multiplier
    if (action.multipliers.engagement && metadata.engagementScore) {
      const engagementMultiplier = Math.min(action.multipliers.engagement, 1 + metadata.engagementScore)
      totalMultiplier *= engagementMultiplier
      breakdown.push(`Engagement bonus: x${engagementMultiplier.toFixed(1)}`)
    }

    // Streak multiplier
    if (action.multipliers.streak && metadata.streak) {
      const streakMultiplier = Math.min(2.0, 1 + (metadata.streak * 0.1))
      totalMultiplier *= streakMultiplier
      breakdown.push(`Streak bonus: x${streakMultiplier.toFixed(1)}`)
    }

    // First time multiplier
    if (action.multipliers.firstTime && metadata.isFirstTime) {
      totalMultiplier *= action.multipliers.firstTime
      breakdown.push(`First time bonus: x${action.multipliers.firstTime}`)
    }
  }

  amount = Math.round(amount * totalMultiplier)
  
  return {
    amount,
    multiplier: totalMultiplier,
    breakdown
  }
}

/**
 * Check if user can perform action (cooldown, daily limits)
 */
export function canPerformAction(
  action: XPAction,
  userHistory: XPTransaction[],
  currentTime: Date = new Date()
): { canPerform: boolean; reason?: string; nextAvailable?: Date } {
  const actionHistory = userHistory.filter(t => t.action === action.id)

  // Check cooldown
  if (action.cooldown) {
    const lastAction = actionHistory
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0]
    
    if (lastAction) {
      const lastActionTime = new Date(lastAction.timestamp)
      const cooldownEnd = new Date(lastActionTime.getTime() + action.cooldown * 60 * 60 * 1000)
      
      if (currentTime < cooldownEnd) {
        return {
          canPerform: false,
          reason: `Action on cooldown`,
          nextAvailable: cooldownEnd
        }
      }
    }
  }

  // Check daily limit
  if (action.maxDaily) {
    const today = new Date(currentTime)
    today.setHours(0, 0, 0, 0)
    
    const todayActions = actionHistory.filter(t => {
      const actionDate = new Date(t.timestamp)
      actionDate.setHours(0, 0, 0, 0)
      return actionDate.getTime() === today.getTime()
    })

    if (todayActions.length >= action.maxDaily) {
      const tomorrow = new Date(today)
      tomorrow.setDate(tomorrow.getDate() + 1)
      
      return {
        canPerform: false,
        reason: `Daily limit reached (${action.maxDaily})`,
        nextAvailable: tomorrow
      }
    }
  }

  return { canPerform: true }
}

/**
 * Get user's XP statistics
 */
export function getUserXPStats(
  totalXP: number,
  transactions: XPTransaction[]
): UserXPData {
  const level = calculateLevel(totalXP)
  const nextLevelXP = getNextLevelXP(level)
  const currentLevelXP = getCurrentLevelXP(totalXP, level)

  // Calculate weekly and monthly XP
  const now = new Date()
  const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
  const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

  const weeklyXP = transactions
    .filter(t => new Date(t.timestamp) >= weekAgo)
    .reduce((sum, t) => sum + t.amount, 0)

  const monthlyXP = transactions
    .filter(t => new Date(t.timestamp) >= monthAgo)
    .reduce((sum, t) => sum + t.amount, 0)

  // Calculate streak (consecutive days with activity)
  let streak = 0
  const sortedDates = [...new Set(
    transactions
      .map(t => new Date(t.timestamp).toDateString())
      .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
  )]

  for (let i = 0; i < sortedDates.length; i++) {
    const date = new Date(sortedDates[i])
    const expectedDate = new Date(now)
    expectedDate.setDate(expectedDate.getDate() - i)
    
    if (date.toDateString() === expectedDate.toDateString()) {
      streak++
    } else {
      break
    }
  }

  return {
    userId: transactions[0]?.userId || '',
    totalXP,
    level,
    currentLevelXP,
    nextLevelXP,
    weeklyXP,
    monthlyXP,
    streak,
    lastActivity: transactions[0]?.timestamp || '',
    achievements: [], // Would be populated from achievement system
    badges: [] // Would be populated from badge system
  }
}

/**
 * Generate XP summary for display
 */
export function generateXPSummary(stats: UserXPData): {
  levelProgress: number
  nextLevelIn: number
  weeklyGrowth: number
  achievements: string[]
} {
  const levelProgress = (stats.currentLevelXP / (stats.nextLevelXP - (stats.level > 1 ? LEVEL_THRESHOLDS[stats.level - 2] : 0))) * 100
  const nextLevelIn = stats.nextLevelXP - stats.totalXP

  return {
    levelProgress,
    nextLevelIn,
    weeklyGrowth: stats.weeklyXP,
    achievements: stats.achievements
  }
}
