"use client"

import React, { createContext, useContext, useState, ReactNode } from 'react'

interface LandingLoadingState {
  isLoading: boolean
  currentSection: string
  progress?: number
  variant: 'section' | 'full-page' | 'minimal'
}

interface LandingLoadingContextType {
  // State
  isLoading: boolean
  currentSection: string
  progress?: number
  variant: 'section' | 'full-page' | 'minimal'
  
  // Actions
  showSectionLoading: (section: string, progress?: number, variant?: 'section' | 'full-page' | 'minimal') => void
  hideSectionLoading: () => void
  updateProgress: (progress: number) => void
  switchSection: (section: string) => void
  setVariant: (variant: 'section' | 'full-page' | 'minimal') => void
}

const LandingLoadingContext = createContext<LandingLoadingContextType | undefined>(undefined)

interface LandingLoadingProviderProps {
  children: ReactNode
}

export function LandingLoadingProvider({ children }: LandingLoadingProviderProps) {
  const [loadingState, setLoadingState] = useState<LandingLoadingState>({
    isLoading: false,
    currentSection: 'default',
    variant: 'section'
  })

  const showSectionLoading = (
    section: string, 
    progress?: number, 
    variant: 'section' | 'full-page' | 'minimal' = 'section'
  ) => {
    setLoadingState({
      isLoading: true,
      currentSection: section,
      progress,
      variant
    })
  }

  const hideSectionLoading = () => {
    setLoadingState(prev => ({
      ...prev,
      isLoading: false,
      progress: undefined
    }))
  }

  const updateProgress = (progress: number) => {
    setLoadingState(prev => ({
      ...prev,
      progress
    }))
  }

  const switchSection = (section: string) => {
    setLoadingState(prev => ({
      ...prev,
      currentSection: section
    }))
  }

  const setVariant = (variant: 'section' | 'full-page' | 'minimal') => {
    setLoadingState(prev => ({
      ...prev,
      variant
    }))
  }

  const contextValue: LandingLoadingContextType = {
    isLoading: loadingState.isLoading,
    currentSection: loadingState.currentSection,
    progress: loadingState.progress,
    variant: loadingState.variant,
    showSectionLoading,
    hideSectionLoading,
    updateProgress,
    switchSection,
    setVariant
  }

  return (
    <LandingLoadingContext.Provider value={contextValue}>
      {children}
    </LandingLoadingContext.Provider>
  )
}

// Hook to use the landing loading context
export function useLandingLoading() {
  const context = useContext(LandingLoadingContext)
  
  if (context === undefined) {
    throw new Error('useLandingLoading must be used within a LandingLoadingProvider')
  }
  
  return context
}

// Custom hook for common landing page loading scenarios
export function useLandingLoadingUtils() {
  const { showSectionLoading, switchSection, hideSectionLoading } = useLandingLoading()

  return {
    // Show loading for section transitions
    showSectionTransition: (fromSection: string, toSection: string) => {
      showSectionLoading(fromSection, undefined, 'minimal')

      setTimeout(() => {
        switchSection(toSection)
        setTimeout(() => {
          hideSectionLoading()
        }, 800)
      }, 300)
    },

    // Show loading for 3D content
    show3DContentLoading: (sectionName: string) => {
      showSectionLoading(sectionName, undefined, 'section')
    },

    // Show loading for data fetching in sections
    showDataLoading: (sectionName: string) => {
      showSectionLoading(sectionName, undefined, 'minimal')
    },

    // Show full page loading for initial load
    showInitialLoading: () => {
      showSectionLoading('hero', undefined, 'full-page')
    }
  }
}

// Higher-order component for landing page sections with loading
export function withLandingLoading<P extends object>(
  Component: React.ComponentType<P>,
  sectionName: string,
  loadingDuration: number = 1000
) {
  return function WrappedLandingSection(props: P) {
    const [isComponentLoading, setIsComponentLoading] = useState(true)
    const { showSectionLoading, hideSectionLoading } = useLandingLoading()

    React.useEffect(() => {
      showSectionLoading(sectionName, undefined, 'section')
      
      const timer = setTimeout(() => {
        setIsComponentLoading(false)
        hideSectionLoading()
      }, loadingDuration)

      return () => {
        clearTimeout(timer)
        hideSectionLoading()
      }
    }, [showSectionLoading, hideSectionLoading])

    if (isComponentLoading) {
      return null
    }

    return <Component {...props} />
  }
}

// Hook for section-specific loading states
export function useSectionLoading(sectionName: string) {
  const { showSectionLoading, hideSectionLoading, updateProgress, currentSection, isLoading } = useLandingLoading()
  const [isSectionLoading, setIsSectionLoading] = useState(false)

  const startSectionLoading = (progress?: number, variant: 'section' | 'full-page' | 'minimal' = 'section') => {
    setIsSectionLoading(true)
    showSectionLoading(sectionName, progress, variant)
  }

  const stopSectionLoading = () => {
    setIsSectionLoading(false)
    hideSectionLoading()
  }

  const updateSectionProgress = (progress: number) => {
    updateProgress(progress)
  }

  const isCurrentSection = currentSection === sectionName
  const isThisSectionLoading = isLoading && isCurrentSection

  return {
    isLoading: isSectionLoading || isThisSectionLoading,
    isCurrentSection,
    startLoading: startSectionLoading,
    stopLoading: stopSectionLoading,
    updateProgress: updateSectionProgress
  }
}
