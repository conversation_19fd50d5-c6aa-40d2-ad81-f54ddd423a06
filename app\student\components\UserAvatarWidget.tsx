'use client'

import { motion } from 'framer-motion'
import { User, Crown } from 'lucide-react'
import { usePlayerData } from '../store/dashboardStore'
import { cn } from '@/lib/utils'
import { Avatar } from '@/components/ui/avatar'

interface UserAvatarWidgetProps {
  className?: string
  onClick?: () => void
  showName?: boolean
  size?: 'sm' | 'md' | 'lg'
}

export default function UserAvatarWidget({ 
  className, 
  onClick,
  showName = true,
  size = 'md'
}: UserAvatarWidgetProps) {
  const playerData = usePlayerData()

  const sizes = {
    sm: {
      container: 'gap-2 px-3 py-1.5',
      avatar: 'w-8 h-8',
      crown: 'w-3 h-3 -bottom-0.5 -right-0.5',
      crownIcon: 'w-1.5 h-1.5',
      status: 'w-2 h-2 -top-0.5 -left-0.5',
      userIcon: 'w-4 h-4',
      nameText: 'text-xs',
      stageText: 'text-xs'
    },
    md: {
      container: 'gap-3 px-4 py-2',
      avatar: 'w-10 h-10',
      crown: 'w-4 h-4 -bottom-1 -right-1',
      crownIcon: 'w-2 h-2',
      status: 'w-3 h-3 -top-1 -left-1',
      userIcon: 'w-5 h-5',
      nameText: 'text-sm',
      stageText: 'text-xs'
    },
    lg: {
      container: 'gap-4 px-5 py-3',
      avatar: 'w-12 h-12',
      crown: 'w-5 h-5 -bottom-1 -right-1',
      crownIcon: 'w-2.5 h-2.5',
      status: 'w-3.5 h-3.5 -top-1 -left-1',
      userIcon: 'w-6 h-6',
      nameText: 'text-base',
      stageText: 'text-sm'
    }
  }

  const sizeConfig = sizes[size]

  return (
    <motion.div
      className={cn(
        'flex items-center rounded-xl bg-gradient-to-r from-space-blue/30 to-quantum-purple/20 hover:transition-all duration-300',
        sizeConfig.container,
        className
      )}
      style={{
        background: 'linear-gradient(135deg, rgba(34, 211, 238, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%)',

      }}
      whileHover={{ scale: 1.00 }}
      whileTap={{ scale: 0.98 }}
      onClick={onClick}
    >
      {/* User Widget Avatar */}
      <div className="relative flex flex-row">
        <div>
          <Avatar size={size} variant="quantum" glow>
            <User className={cn('text-white relative z-10', sizeConfig.userIcon)} />
          </Avatar>
        </div>
      </div>
      
      {/* User Info */}
      {showName && (
        <motion.div 
          className="text-left"
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className={cn('font-bold text-white', sizeConfig.nameText)}>
            {playerData?.username || 'NanoArchitect'}
          </div>
          <div className={cn('text-neural-cyan', sizeConfig.stageText)}>
            Level {playerData?.level || 1}
          </div>
        </motion.div>
      )}
    </motion.div>
  )
}
