'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import {
  Save,
  Copy,
  Eye,
  Settings,
  Zap,
  RotateCcw,
  Download,
  Upload,
  Share,
  PanelLeftClose,
  PanelLeftOpen,
  Loader2,
  CheckCircle
} from 'lucide-react'

interface KanbanPlannerControlsProps {
  hasUnsavedChanges: boolean
  isLoading: boolean
  onSave: () => void
  onToggleSidebar: () => void
  showSidebar: boolean
}

export function KanbanPlannerControls({
  hasUnsavedChanges,
  isLoading,
  onSave,
  onToggleSidebar,
  showSidebar
}: KanbanPlannerControlsProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)

  return (
    <div className="flex items-center gap-3">
      {/* Save Status Indicator */}
      <AnimatePresence>
        {hasUnsavedChanges && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="flex items-center gap-2"
          >
            <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse" />
            <span className="text-sm text-yellow-400">Unsaved changes</span>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Primary Actions */}
      <div className="flex items-center gap-2">
        {/* Save Plan */}
        <Button
          onClick={onSave}
          disabled={isLoading || !hasUnsavedChanges}
          className={`${
            hasUnsavedChanges
              ? 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600'
              : 'bg-gray-600 hover:bg-gray-700'
          } transition-all duration-200`}
        >
          {isLoading ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : hasUnsavedChanges ? (
            <Save className="w-4 h-4 mr-2" />
          ) : (
            <CheckCircle className="w-4 h-4 mr-2" />
          )}
          {isLoading ? 'Saving...' : hasUnsavedChanges ? 'Save Plan' : 'Saved'}
        </Button>

        {/* Preview as Child */}
        <Button
          variant="outline"
          className="border-cyan-500/30 text-cyan-400 hover:bg-cyan-500/10"
        >
          <Eye className="w-4 h-4 mr-2" />
          Preview
        </Button>

        {/* Toggle Sidebar */}
        <Button
          variant="outline"
          onClick={onToggleSidebar}
          className="border-gray-700/50 text-gray-300 hover:bg-gray-800/50"
        >
          {showSidebar ? (
            <PanelLeftClose className="w-4 h-4" />
          ) : (
            <PanelLeftOpen className="w-4 h-4" />
          )}
        </Button>
      </div>

      {/* Secondary Actions */}
      <div className="flex items-center gap-2">
        {/* Copy Plan */}
        <Button
          variant="outline"
          size="sm"
          className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
        >
          <Copy className="w-4 h-4 mr-2" />
          Copy Plan
        </Button>

        {/* AI Suggestions */}
        <Button
          variant="outline"
          size="sm"
          className="border-orange-500/30 text-orange-400 hover:bg-orange-500/10"
        >
          <Zap className="w-4 h-4 mr-2" />
          AI Suggest
        </Button>

        {/* Advanced Actions Toggle */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="border-gray-700/50 text-gray-300 hover:bg-gray-800/50"
        >
          <Settings className="w-4 h-4" />
        </Button>
      </div>

      {/* Advanced Actions Dropdown */}
      <AnimatePresence>
        {showAdvanced && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: -10 }}
            className="absolute top-full right-0 mt-2 bg-black/90 border border-gray-800/50 rounded-lg p-2 space-y-1 backdrop-blur-xl z-50 min-w-[200px]"
          >
            {/* Template Actions */}
            <div className="space-y-1">
              <div className="text-xs text-gray-400 px-2 py-1 font-medium">Templates</div>
              
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-gray-300 hover:bg-gray-800/50"
              >
                <Download className="w-4 h-4 mr-2" />
                Save as Template
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-gray-300 hover:bg-gray-800/50"
              >
                <Upload className="w-4 h-4 mr-2" />
                Load Template
              </Button>
            </div>

            {/* Sharing Actions */}
            <div className="border-t border-gray-700/50 pt-1 space-y-1">
              <div className="text-xs text-gray-400 px-2 py-1 font-medium">Sharing</div>
              
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-gray-300 hover:bg-gray-800/50"
              >
                <Share className="w-4 h-4 mr-2" />
                Share with Educator
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-gray-300 hover:bg-gray-800/50"
              >
                <Copy className="w-4 h-4 mr-2" />
                Copy to Another Child
              </Button>
            </div>

            {/* Reset Actions */}
            <div className="border-t border-gray-700/50 pt-1 space-y-1">
              <div className="text-xs text-gray-400 px-2 py-1 font-medium">Reset</div>
              
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-red-400 hover:bg-red-500/10"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Clear All Lessons
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
