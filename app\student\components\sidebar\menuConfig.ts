import {
  Users,
  Settings,
  Heart,
  LogOut,
  Dna,
  GraduationCap,
  FlaskConical,
  Award,
  Gamepad2,
  Bot,
  MessageCircle
} from 'lucide-react'
import { MenuItem } from './types'

export function getMenuItems(
  playerData: any,
  timelineData: any,
  setActiveItem: (item: string) => void,
  setActivePanel: (panel: string) => void
): MenuItem[] {
  return [
    {
      id: 'dashboard',
      label: 'NanoCore',
      icon: Dna,
      badge: `${playerData?.level || 0}`,
      route: '/student',
      action: () => {
        setActiveItem('dashboard')
        setActivePanel('dashboard-overview')
      }
    },
    // Commented out menu items can be uncommented as features are implemented
    // {
    //   id: 'timeline-map',
    //   label: 'Timeline Map',
    //   icon: Compass,
    //   badge: `${timelineData?.availableNodes.length || 0}`,
    //   action: () => setViewMode('explore')
    // },
    // {
    //   id: 'evolution',
    //   label: 'NanoEvolution',
    //   icon: GraduationCap,
    //   badge: 'AI',
    //   route: '/dashboard/evolution'
    // },
    // {
    //   id: 'mind-balance',
    //   label: 'Mind & Balance',
    //   icon: Heart,
    //   badge: `${Math.round((playerData?.xp || 0) / 100)}%`,
    //   route: '/dashboard/mind-balance'
    // },
    // {
    //   id: 'nanoverse',
    //   label: 'NanoVerse',
    //   icon: Hexagon,
    //   badge: '3D',
    //   route: '/dashboard/nanoverse'
    // },
    // {
    //   id: 'nanolab',
    //   label: 'NanoLab',
    //   icon: FlaskConical,
    //   badge: '🧪',
    //   route: '/dashboard/nanolab'
    // },
    // {
    //   id: 'quantumlink',
    //   label: 'QuantumLink',
    //   icon: MessageCircle,
    //   badge: 'Chat',
    //   route: '/dashboard/quantumlink'
    // },
    // {
    //   id: 'achievements',
    //   label: 'Achievements',
    //   icon: Award,
    //   badge: `${playerData?.stats.learningStreak || 0}d`,
    //   route: '/dashboard/achievements'
    // },
    // {
    //   id: 'playgrounds',
    //   label: 'Playgrounds',
    //   icon: Gamepad2,
    //   badge: 'Sim',
    //   route: '/dashboard/playgrounds'
    // },
    // {
    //   id: 'timeline-cohort',
    //   label: 'Timeline Cohort',
    //   icon: Users,
    //   badge: `${timelineData?.nodes.length || 0}`,
    //   route: '/dashboard/timeline-cohort'
    // },
    // {
    //   id: 'ai-mentor',
    //   label: 'AI Mentor',
    //   icon: Bot,
    //   badge: 'Active',
    //   route: '/dashboard/ai-mentor'
    // }
  ]
}

export function getBottomMenuItems(): MenuItem[] {
  return [
    // Commented out menu items can be uncommented as features are implemented
    // {
    //   id: 'settings',
    //   label: 'Settings & Account',
    //   icon: Settings,
    //   route: '/dashboard/settings'
    // },
    // {
    //   id: 'logout',
    //   label: 'Disconnect',
    //   icon: LogOut,
    //   action: () => console.log('Logout')
    // }
  ]
}
