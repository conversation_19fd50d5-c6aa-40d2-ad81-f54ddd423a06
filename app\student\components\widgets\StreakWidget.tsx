'use client'

import { motion } from 'framer-motion'
import { Flame, Target, Calendar } from 'lucide-react'
import { Progress } from '@/components/ui/progress'
import { StreakWidgetProps } from './types'

export default function StreakWidget({
  current,
  best,
  target = 30,
  className = '',
  size = 'md',
  variant = 'detailed'
}: StreakWidgetProps) {
  const progressToTarget = target ? (current / target) * 100 : 0
  const isOnFire = current >= 7
  const isRecord = current === best

  const sizeClasses = {
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6'
  }

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }

  const textSizes = {
    sm: { title: 'text-sm', value: 'text-lg', label: 'text-xs' },
    md: { title: 'text-base', value: 'text-xl', label: 'text-sm' },
    lg: { title: 'text-lg', value: 'text-2xl', label: 'text-base' }
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`bg-black/40 border border-orange-500/30 backdrop-blur-xl rounded-lg ${sizeClasses[size]} ${className}`}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className="relative">
            <Flame className={`${iconSizes[size]} text-orange-400`} />
            {isOnFire && (
              <motion.div
                className="absolute inset-0"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <Flame className={`${iconSizes[size]} text-orange-400/50`} />
              </motion.div>
            )}
          </div>
          <span className={`font-medium text-white ${textSizes[size].title}`}>
            Daily Streak
          </span>
        </div>
        {isRecord && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="text-yellow-400 text-xs font-bold"
          >
            🏆 RECORD!
          </motion.div>
        )}
      </div>

      <div className="text-center mb-3">
        <motion.div
          className={`font-bold text-orange-400 ${textSizes[size].value}`}
          animate={{ scale: isOnFire ? [1, 1.05, 1] : 1 }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          {current} {current === 1 ? 'Day' : 'Days'}
        </motion.div>
        <div className={`text-white/60 ${textSizes[size].label}`}>
          Best: {best} days
        </div>
      </div>

      {variant === 'detailed' && target && (
        <div className="space-y-2">
          <div className="flex items-center justify-between text-xs">
            <span className="text-white/60 flex items-center gap-1">
              <Target className="w-3 h-3" />
              Goal: {target} days
            </span>
            <span className="text-cyan-400">
              {Math.round(progressToTarget)}%
            </span>
          </div>
          <Progress value={progressToTarget} className="h-1" />
        </div>
      )}

      {variant === 'compact' && (
        <div className="flex items-center justify-center gap-2 text-xs text-white/60">
          <Calendar className="w-3 h-3" />
          <span>Best: {best}</span>
        </div>
      )}
    </motion.div>
  )
}
