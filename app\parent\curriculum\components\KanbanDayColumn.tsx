'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { useDroppable } from '@dnd-kit/core'
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable'
import {
  AlertCircle,
  Target
} from 'lucide-react'

import {
  KanbanDayColumn as DayColumnType,
  KanbanLessonCard,
  WeekDay
} from '../types/curriculum'
import { SortableLessonCard } from './SortableLessonCard'

interface KanbanDayColumnProps {
  column: DayColumnType
  onLessonDrop: (lesson: KanbanLessonCard, targetDay: WeekDay) => void
  onLessonRemove: (lessonId: string, fromDay: WeekDay) => void
  onLessonReorder: (lessonId: string, fromDay: WeekDay, toDay: WeekDay, newIndex: number) => void
  draggedLesson: KanbanLessonCard | null
  index: number
}

export function KanbanDayColumn({
  column,
  onLessonDrop: _onLessonDrop,
  onLessonRemove,
  onLessonReorder: _onLessonReorder,
  draggedLesson: _draggedLesson,
  index
}: KanbanDayColumnProps) {
  const { isOver, setNodeRef } = useDroppable({
    id: `day-${column.day}`,
    data: {
      type: 'day-column',
      day: column.day
    }
  })

  // Calculate time utilization
  const timeUtilization = column.maxTime > 0 ? (column.totalTime / column.maxTime) * 100 : 0
  const isOverTime = timeUtilization > 100

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="h-full"
    >
      <Card className={`h-full bg-black/40 border-gray-800/50 backdrop-blur-xl transition-all duration-200 ${
        isOver ? 'border-cyan-500/50 bg-cyan-500/5' : ''
      } ${column.isWeekend ? 'opacity-75' : ''}`}>
        
        {/* Column Header */}
        <CardHeader className="pb-3">
          <CardTitle className="text-white font-space-grotesk text-lg flex items-center justify-between">
            <span>{column.displayName}</span>
            {column.isWeekend && (
              <Badge className="bg-purple-500/20 text-purple-400 text-xs">
                Weekend
              </Badge>
            )}
          </CardTitle>
          
          {/* Time Summary */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Time Planned</span>
              <span className={`font-medium ${isOverTime ? 'text-red-400' : 'text-gray-300'}`}>
                {column.totalTime} / {column.maxTime} min
              </span>
            </div>
            
            <Progress 
              value={Math.min(timeUtilization, 100)} 
              className={`h-2 ${isOverTime ? 'bg-red-900/30' : 'bg-gray-800'}`}
            />
            
            {isOverTime && (
              <div className="flex items-center gap-1 text-xs text-red-400">
                <AlertCircle className="w-3 h-3" />
                Over daily limit by {column.totalTime - column.maxTime} min
              </div>
            )}
          </div>

          {/* Lesson Count */}
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-400">Lessons</span>
            <Badge className="bg-gray-700/50 text-gray-300">
              {column.lessons.length}
            </Badge>
          </div>
        </CardHeader>

        {/* Drop Zone */}
        <CardContent
          ref={setNodeRef}
          className="flex-1 space-y-3 min-h-[400px]"
        >
          <SortableContext
            items={column.lessons.map(lesson => lesson.id)}
            strategy={verticalListSortingStrategy}
          >
            <AnimatePresence>
              {column.lessons.map((lesson) => (
                <SortableLessonCard
                  key={lesson.id}
                  lesson={lesson}
                  onRemove={onLessonRemove}
                  day={column.day}
                />
              ))}
            </AnimatePresence>
          </SortableContext>

          {/* Empty State */}
          {column.lessons.length === 0 && (
            <div className={`flex-1 flex items-center justify-center border-2 border-dashed rounded-lg transition-all duration-200 min-h-[200px] ${
              isOver
                ? 'border-cyan-500/50 bg-cyan-500/5'
                : 'border-gray-700/50'
            }`}>
              <div className="text-center">
                <Target className={`w-8 h-8 mx-auto mb-2 transition-colors ${
                  isOver ? 'text-cyan-400' : 'text-gray-600'
                }`} />
                <p className={`text-sm transition-colors ${
                  isOver ? 'text-cyan-400' : 'text-gray-500'
                }`}>
                  {isOver ? 'Drop lesson here' : 'Drag lessons here'}
                </p>
              </div>
            </div>
          )}

          {/* Drop Indicator */}
          {isOver && column.lessons.length > 0 && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="h-2 bg-cyan-500/30 rounded-full border border-cyan-500/50"
            />
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}
