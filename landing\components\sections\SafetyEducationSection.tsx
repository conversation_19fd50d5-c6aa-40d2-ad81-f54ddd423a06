"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle } from 'lucide-react'
import { safetyFeatures } from '../../data/constants'

export function SafetyEducationSection() {
  return (
    <section className="px-6 py-20 bg-black/20">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <motion.div initial={{ opacity: 0, x: -30 }} whileInView={{ opacity: 1, x: 0 }} viewport={{ once: true }}>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              Safe Learning, Real Skills
            </h2>
            <p className="text-xl text-gray-400 mb-8 leading-relaxed">
              ByteHero provides a secure, moderated environment where young learners can explore technology, develop
              critical thinking, and build confidence through hands-on experience.
            </p>

            <div className="space-y-6">
              {safetyFeatures.map((safety, index) => (
                <motion.div
                  key={safety.title}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-start gap-4"
                >
                  <div className="p-2 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-lg border border-green-500/30">
                    <safety.icon className="w-5 h-5 text-green-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-white mb-1">{safety.title}</h4>
                    <p className="text-gray-400 text-sm">{safety.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="relative"
          >
            <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl p-8">
              <CardHeader className="text-center pb-6">
                <CardTitle className="text-2xl font-bold text-white mb-2">Parent Dashboard Preview</CardTitle>
                <p className="text-gray-400">Track your child&apos;s learning journey</p>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Weekly Screen Time</span>
                  <span className="text-cyan-400 font-semibold">4.5 hours</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Skills Developed</span>
                  <div className="flex gap-2">
                    <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">Python</Badge>
                    <Badge className="bg-green-500/20 text-green-400 border-green-500/30">Logic</Badge>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Safety Score</span>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-green-400 font-semibold">Excellent</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Community Interactions</span>
                  <span className="text-purple-400 font-semibold">23 helpful posts</span>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
