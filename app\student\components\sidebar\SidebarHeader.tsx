'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { ChevronLeft, ChevronRight, Hexagon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { EvolutionTheme } from './types'

interface SidebarHeaderProps {
  isCollapsed: boolean
  evolutionTheme: EvolutionTheme
  onToggleCollapse: () => void
}

export function SidebarHeader({ 
  isCollapsed, 
  evolutionTheme, 
  onToggleCollapse 
}: SidebarHeaderProps) {
  return (
    <div
      className="p-4 border-b-2 relative overflow-hidden"
      style={{
        borderBottomColor: `${evolutionTheme.primary}30`,
        background: 'linear-gradient(135deg, rgba(34, 211, 238, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%)'
      }}
    >
      <div className="flex items-center justify-between relative z-10">
        <AnimatePresence>
          {!isCollapsed && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="flex items-center gap-3"
            >
              <Hexagon
                className="w-8 h-8 text-neural-cyan"
                style={{
                  filter: `drop-shadow(0 0 10px ${evolutionTheme.primary})`
                }}
              />

              <div className='flex flex-col space-y-1 p-2'>
                <motion.h3
                  className="text-lg font-orbitron font-bold"
                  style={{
                    background: `linear-gradient(45deg, ${evolutionTheme.primary}, ${evolutionTheme.secondary})`,
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                    textShadow: `0 0 20px ${evolutionTheme.glow}`
                  }}
                  animate={{
                    textShadow: [
                      `0 0 20px ${evolutionTheme.glow}`,
                      `0 0 30px ${evolutionTheme.glow}`,
                      `0 0 20px ${evolutionTheme.glow}`
                    ]
                  }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                >
                  NanoHero
                </motion.h3>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <div className="flex gap-2">
          <Button
            size="sm"
            variant="quantum"
            onClick={onToggleCollapse}
            className="p-2 relative overflow-hidden group"
            style={{
              background: `linear-gradient(45deg, ${evolutionTheme.secondary}80, ${evolutionTheme.primary}80)`,
              border: `2px solid ${evolutionTheme.secondary}60`,
              boxShadow: `0 0 15px ${evolutionTheme.glow}`
            }}
          >
            <motion.div
              animate={{ rotate: isCollapsed ? 180 : 0 }}
              transition={{ duration: 0.3 }}
            >
              {isCollapsed ? (
                <ChevronRight className="w-4 h-4 relative z-10" />
              ) : (
                <ChevronLeft className="w-4 h-4 relative z-10" />
              )}
            </motion.div>
          </Button>
        </div>
      </div>
    </div>
  )
}
