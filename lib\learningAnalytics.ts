// Advanced Learning Analytics for QuantumLink Educational Features
import { ChatMessage } from '@/types/chat'

export interface LearningMetrics {
  communicationSkillLevel: number
  empathyScore: number
  collaborationRating: number
  leadershipPotential: number
  conflictResolutionSkill: number
  creativityIndex: number
  criticalThinkingLevel: number
  digitalCitizenshipScore: number
}

export interface LearningInsight {
  id: string
  type: 'strength' | 'improvement' | 'achievement' | 'concern'
  category: 'communication' | 'empathy' | 'collaboration' | 'leadership' | 'creativity'
  title: string
  description: string
  actionItems: string[]
  confidence: number
  timestamp: Date
}

export interface PersonalizedRecommendation {
  id: string
  type: 'quest' | 'reflection' | 'skill_practice' | 'peer_interaction'
  title: string
  description: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedTime: number // minutes
  rewards: {
    ce: number
    cubits: number
    skillPoints: Record<string, number>
  }
  prerequisites?: string[]
}

export interface SkillProgressTracker {
  skillName: string
  currentLevel: number
  experience: number
  nextLevelThreshold: number
  recentActivities: Array<{
    activity: string
    impact: number
    timestamp: Date
  }>
  milestones: Array<{
    level: number
    title: string
    description: string
    unlocked: boolean
    unlockedAt?: Date
  }>
}

// Analyze communication patterns to extract learning insights
export function analyzeCommunicationPatterns(
  messages: ChatMessage[],
  timeframe: 'day' | 'week' | 'month' = 'week'
): LearningMetrics {
  const cutoffDate = new Date()
  cutoffDate.setDate(cutoffDate.getDate() - (timeframe === 'day' ? 1 : timeframe === 'week' ? 7 : 30))
  
  const recentMessages = messages.filter(m => new Date(m.timestamp) >= cutoffDate)
  
  if (recentMessages.length === 0) {
    return {
      communicationSkillLevel: 1,
      empathyScore: 0.5,
      collaborationRating: 0.5,
      leadershipPotential: 0.3,
      conflictResolutionSkill: 0.4,
      creativityIndex: 0.5,
      criticalThinkingLevel: 0.4,
      digitalCitizenshipScore: 0.8
    }
  }

  // Calculate communication skill level
  const avgKindnessScore = recentMessages.reduce((sum, m) => sum + (m.kindnessScore || 0.5), 0) / recentMessages.length
  const avgHelpfulnessScore = recentMessages.reduce((sum, m) => sum + (m.helpfulnessScore || 0.5), 0) / recentMessages.length
  const communicationSkillLevel = Math.min(10, Math.floor((avgKindnessScore + avgHelpfulnessScore) * 5))

  // Analyze empathy through emotion tags and responses
  const empathyIndicators = recentMessages.filter(m => 
    m.emotionTags?.includes('supportive') || 
    m.emotionTags?.includes('encouraging') ||
    m.content.toLowerCase().includes('understand') ||
    m.content.toLowerCase().includes('feel')
  ).length
  const empathyScore = Math.min(1, empathyIndicators / Math.max(1, recentMessages.length * 0.3))

  // Collaboration rating based on team interactions and helping behavior
  const collaborationMessages = recentMessages.filter(m =>
    m.content.toLowerCase().includes('together') ||
    m.content.toLowerCase().includes('team') ||
    m.content.toLowerCase().includes('help') ||
    m.type === 'quest_completion'
  ).length
  const collaborationRating = Math.min(1, collaborationMessages / Math.max(1, recentMessages.length * 0.2))

  // Leadership potential through initiative and guidance
  const leadershipMessages = recentMessages.filter(m =>
    m.content.toLowerCase().includes('let\'s') ||
    m.content.toLowerCase().includes('we should') ||
    m.content.toLowerCase().includes('i suggest') ||
    (m.helpfulnessScore || 0) > 0.8
  ).length
  const leadershipPotential = Math.min(1, leadershipMessages / Math.max(1, recentMessages.length * 0.15))

  // Conflict resolution through peaceful responses
  const conflictResolutionMessages = recentMessages.filter(m =>
    m.emotionTags?.includes('peaceful') ||
    m.content.toLowerCase().includes('understand both') ||
    m.content.toLowerCase().includes('compromise') ||
    m.content.toLowerCase().includes('work together')
  ).length
  const conflictResolutionSkill = Math.min(1, conflictResolutionMessages / Math.max(1, recentMessages.length * 0.1))

  // Creativity through unique expressions and ideas
  const creativeMessages = recentMessages.filter(m =>
    m.emotionTags?.includes('creative') ||
    m.content.toLowerCase().includes('idea') ||
    m.content.toLowerCase().includes('imagine') ||
    m.content.toLowerCase().includes('what if')
  ).length
  const creativityIndex = Math.min(1, creativeMessages / Math.max(1, recentMessages.length * 0.2))

  // Critical thinking through questions and analysis
  const criticalThinkingMessages = recentMessages.filter(m =>
    m.content.includes('?') ||
    m.content.toLowerCase().includes('because') ||
    m.content.toLowerCase().includes('analyze') ||
    m.content.toLowerCase().includes('think about')
  ).length
  const criticalThinkingLevel = Math.min(1, criticalThinkingMessages / Math.max(1, recentMessages.length * 0.25))

  // Digital citizenship through respectful and safe behavior
  const flaggedMessages = recentMessages.filter(m => m.isFlagged).length
  const digitalCitizenshipScore = Math.max(0.3, 1 - (flaggedMessages / Math.max(1, recentMessages.length)))

  return {
    communicationSkillLevel,
    empathyScore,
    collaborationRating,
    leadershipPotential,
    conflictResolutionSkill,
    creativityIndex,
    criticalThinkingLevel,
    digitalCitizenshipScore
  }
}

// Generate personalized learning insights
export function generateLearningInsights(
  metrics: LearningMetrics,
  previousMetrics?: LearningMetrics
): LearningInsight[] {
  const insights: LearningInsight[] = []

  // Identify strengths
  Object.entries(metrics).forEach(([skill, score]) => {
    if (typeof score === 'number' && score > 0.8) {
      insights.push({
        id: `strength_${skill}_${Date.now()}`,
        type: 'strength',
        category: mapSkillToCategory(skill),
        title: `Strong ${formatSkillName(skill)}`,
        description: `You're excelling in ${formatSkillName(skill)} with a score of ${(score * 100).toFixed(0)}%`,
        actionItems: [
          `Continue practicing ${formatSkillName(skill)}`,
          'Help others develop this skill',
          'Take on leadership roles in this area'
        ],
        confidence: 0.9,
        timestamp: new Date()
      })
    }
  })

  // Identify improvement areas
  Object.entries(metrics).forEach(([skill, score]) => {
    if (typeof score === 'number' && score < 0.4) {
      insights.push({
        id: `improvement_${skill}_${Date.now()}`,
        type: 'improvement',
        category: mapSkillToCategory(skill),
        title: `Opportunity in ${formatSkillName(skill)}`,
        description: `There's room to grow in ${formatSkillName(skill)}. Let's work on this together!`,
        actionItems: getImprovementActions(skill),
        confidence: 0.8,
        timestamp: new Date()
      })
    }
  })

  // Detect achievements through comparison with previous metrics
  if (previousMetrics) {
    Object.entries(metrics).forEach(([skill, score]) => {
      const previousScore = previousMetrics[skill as keyof LearningMetrics]
      if (typeof score === 'number' && typeof previousScore === 'number' && score > previousScore + 0.1) {
        insights.push({
          id: `achievement_${skill}_${Date.now()}`,
          type: 'achievement',
          category: mapSkillToCategory(skill),
          title: `${formatSkillName(skill)} Improvement!`,
          description: `You've improved your ${formatSkillName(skill)} by ${((score - previousScore) * 100).toFixed(0)}%!`,
          actionItems: [
            'Celebrate your progress!',
            'Keep up the great work',
            'Share your success with others'
          ],
          confidence: 0.95,
          timestamp: new Date()
        })
      }
    })
  }

  return insights.slice(0, 5) // Return top 5 insights
}

// Generate personalized quest recommendations
export function generatePersonalizedQuests(
  metrics: LearningMetrics,
  userLevel: number,
  _completedQuests: string[]
): PersonalizedRecommendation[] {
  const recommendations: PersonalizedRecommendation[] = []

  // Empathy building quests
  if (metrics.empathyScore < 0.6) {
    recommendations.push({
      id: 'empathy_builder_1',
      type: 'quest',
      title: 'Emotion Detective',
      description: 'Help 3 people by recognizing and responding to their emotions',
      difficulty: userLevel < 3 ? 'beginner' : 'intermediate',
      estimatedTime: 30,
      rewards: {
        ce: 40,
        cubits: 8,
        skillPoints: { empathy: 15, communication: 5 }
      }
    })
  }

  // Collaboration quests
  if (metrics.collaborationRating < 0.5) {
    recommendations.push({
      id: 'collaboration_master_1',
      type: 'quest',
      title: 'Team Builder',
      description: 'Successfully complete 2 group challenges with other users',
      difficulty: 'intermediate',
      estimatedTime: 45,
      rewards: {
        ce: 60,
        cubits: 12,
        skillPoints: { collaboration: 20, leadership: 5 }
      }
    })
  }

  // Critical thinking development
  if (metrics.criticalThinkingLevel < 0.6) {
    recommendations.push({
      id: 'critical_thinker_1',
      type: 'quest',
      title: 'Question Master',
      description: 'Ask 5 thoughtful questions that help others learn',
      difficulty: userLevel < 5 ? 'beginner' : 'intermediate',
      estimatedTime: 25,
      rewards: {
        ce: 35,
        cubits: 7,
        skillPoints: { criticalThinking: 18, communication: 8 }
      }
    })
  }

  // Leadership development
  if (metrics.leadershipPotential > 0.7 && userLevel >= 4) {
    recommendations.push({
      id: 'leadership_path_1',
      type: 'quest',
      title: 'Mentor in Training',
      description: 'Guide 2 newer users through their first week',
      difficulty: 'advanced',
      estimatedTime: 60,
      rewards: {
        ce: 100,
        cubits: 20,
        skillPoints: { leadership: 25, empathy: 10, communication: 10 }
      },
      prerequisites: ['complete_5_help_quests']
    })
  }

  return recommendations.slice(0, 3)
}

// Helper functions
function mapSkillToCategory(skill: string): LearningInsight['category'] {
  const mapping: Record<string, LearningInsight['category']> = {
    communicationSkillLevel: 'communication',
    empathyScore: 'empathy',
    collaborationRating: 'collaboration',
    leadershipPotential: 'leadership',
    conflictResolutionSkill: 'communication',
    creativityIndex: 'creativity',
    criticalThinkingLevel: 'communication',
    digitalCitizenshipScore: 'communication'
  }
  return mapping[skill] || 'communication'
}

function formatSkillName(skill: string): string {
  const mapping: Record<string, string> = {
    communicationSkillLevel: 'Communication Skills',
    empathyScore: 'Empathy',
    collaborationRating: 'Collaboration',
    leadershipPotential: 'Leadership',
    conflictResolutionSkill: 'Conflict Resolution',
    creativityIndex: 'Creativity',
    criticalThinkingLevel: 'Critical Thinking',
    digitalCitizenshipScore: 'Digital Citizenship'
  }
  return mapping[skill] || skill
}

function getImprovementActions(skill: string): string[] {
  const actions: Record<string, string[]> = {
    empathyScore: [
      'Practice active listening in conversations',
      'Ask others how they\'re feeling',
      'Share encouraging words when someone is struggling'
    ],
    collaborationRating: [
      'Join team challenges and group projects',
      'Offer to help others with their goals',
      'Practice compromising and finding win-win solutions'
    ],
    leadershipPotential: [
      'Take initiative in group discussions',
      'Help organize team activities',
      'Practice giving constructive feedback'
    ],
    criticalThinkingLevel: [
      'Ask more "why" and "how" questions',
      'Practice analyzing different perspectives',
      'Share your reasoning when making suggestions'
    ]
  }
  return actions[skill] || ['Practice this skill in daily conversations', 'Ask for feedback from others', 'Set small daily goals']
}
