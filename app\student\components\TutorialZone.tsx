"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Plus, Search, Eye, Heart, MessageCircle, Clock, Play, TrendingUp } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tutorial, Category } from "../types/dashboard"

interface TutorialZoneProps {
  tutorials: Tutorial[]
  categories: Category[]
  onTutorialSelect: (tutorial: Tutorial) => void
  onUploadClick: () => void
}

export function TutorialZone({ tutorials, categories, onTutorialSelect, onUploadClick }: TutorialZoneProps) {
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [sortBy, setSortBy] = useState("trending")

  const filteredTutorials = tutorials.filter((tutorial) => {
    const matchesCategory = selectedCategory === "all" || tutorial.category === selectedCategory
    const matchesSearch =
      tutorial.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tutorial.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    return matchesCategory && matchesSearch
  })

  const sortedTutorials = [...filteredTutorials].sort((a, b) => {
    switch (sortBy) {
      case "trending":
        return b.views - a.views
      case "newest":
        return new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime()
      case "popular":
        return b.likes - a.likes
      default:
        return 0
    }
  })

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "beginner":
        return "from-green-500 to-emerald-500"
      case "intermediate":
        return "from-yellow-500 to-orange-500"
      case "advanced":
        return "from-red-500 to-pink-500"
      default:
        return "from-gray-500 to-gray-600"
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "cybersecurity":
        return "from-red-500 to-orange-500"
      case "gaming":
        return "from-purple-500 to-pink-500"
      case "coding":
        return "from-blue-500 to-cyan-500"
      default:
        return "from-gray-500 to-gray-600"
    }
  }

  return (
    <motion.div
      key="learn"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-6"
    >
      {/* Tutorial Zone Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            onClick={onUploadClick}
            className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600"
          >
            <Plus className="w-4 h-4 mr-2" />
            Create Tutorial
          </Button>
        </div>
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Search tutorials..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-black/50 border-gray-700 text-white placeholder:text-gray-500 w-64"
            />
          </div>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-40 bg-black/50 border-gray-700 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-black/90 border-gray-700">
              <SelectItem value="trending">Trending</SelectItem>
              <SelectItem value="newest">Newest</SelectItem>
              <SelectItem value="popular">Most Popular</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Categories */}
      <div className="flex gap-4 overflow-x-auto pb-2">
        {categories.map((category) => (
          <motion.button
            key={category.id}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setSelectedCategory(category.id)}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg whitespace-nowrap transition-all duration-300 ${
              selectedCategory === category.id
                ? "bg-gradient-to-r from-cyan-500/20 to-blue-500/20 border border-cyan-500/50 text-cyan-400"
                : "bg-black/40 border border-gray-700/50 text-gray-300 hover:bg-gray-800/50"
            }`}
          >
            <category.icon className="w-4 h-4" />
            <span className="font-medium">{category.name}</span>
            <Badge className="bg-gray-700 text-gray-300 text-xs">{category.count}</Badge>
          </motion.button>
        ))}
      </div>

      {/* Tutorials Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sortedTutorials.map((tutorial, index) => (
          <motion.div
            key={tutorial.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.02 }}
            className="cursor-pointer"
            onClick={() => onTutorialSelect(tutorial)}
          >
            <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl hover:bg-black/60 transition-all duration-300 overflow-hidden group">
              <div className="relative">
                <img
                  src={tutorial.thumbnail || "/placeholder.svg"}
                  alt={tutorial.title}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                    <Play className="w-6 h-6 text-white ml-1" />
                  </div>
                </div>
                <div className="absolute top-2 right-2">
                  <Badge className="bg-black/70 text-white text-xs">{tutorial.duration}</Badge>
                </div>
                <div className="absolute bottom-2 left-2">
                  <Badge
                    className={`bg-gradient-to-r ${getDifficultyColor(tutorial.difficulty)} text-white text-xs`}
                  >
                    {tutorial.difficulty}
                  </Badge>
                </div>
              </div>
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div>
                    <h3 className="font-semibold text-white line-clamp-2 group-hover:text-cyan-400 transition-colors duration-300">
                      {tutorial.title}
                    </h3>
                    <p className="text-sm text-gray-400 mt-1">by {tutorial.creator}</p>
                  </div>

                  <div className="flex items-center gap-4 text-sm text-gray-400">
                    <div className="flex items-center gap-1">
                      <Eye className="w-4 h-4" />
                      {tutorial.views || 0}
                    </div>
                    <div className="flex items-center gap-1">
                      <Heart className="w-4 h-4" />
                      {tutorial.likes || 0}
                    </div>
                    <div className="flex items-center gap-1">
                      <MessageCircle className="w-4 h-4" />
                      {tutorial.comments || 0}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <Badge
                      className={`bg-gradient-to-r ${getCategoryColor(tutorial.category)} text-white text-xs`}
                    >
                      {tutorial.category}
                    </Badge>
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <Clock className="w-3 h-3" />
                      {tutorial.uploadDate}
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-1">
                    {tutorial.tags.slice(0, 3).map((tag) => (
                      <Badge
                        key={tag}
                        variant="outline"
                        className="bg-black/50 border-gray-700 text-gray-400 text-xs"
                      >
                        #{tag}
                      </Badge>
                    ))}
                    {tutorial.tags.length > 3 && (
                      <Badge variant="outline" className="bg-black/50 border-gray-700 text-gray-400 text-xs">
                        +{tutorial.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Trending Section */}
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3 }}>
        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-400">
              <TrendingUp className="w-5 h-5" />
              Trending This Week
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {tutorials.slice(0, 3).map((tutorial, index) => (
                <motion.div
                  key={tutorial.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 + index * 0.1 }}
                  className="flex items-center gap-3 p-3 bg-gray-800/30 rounded-lg border border-gray-700/50 hover:bg-gray-800/50 transition-all duration-300 cursor-pointer"
                  onClick={() => onTutorialSelect(tutorial)}
                >
                  <div className="relative">
                    <img
                      src={tutorial.thumbnail || "/placeholder.svg"}
                      alt={tutorial.title}
                      className="w-16 h-12 object-cover rounded"
                    />
                    <div className="absolute inset-0 bg-black/40 rounded flex items-center justify-center">
                      <Play className="w-4 h-4 text-white" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-white text-sm line-clamp-1">{tutorial.title}</h4>
                    <p className="text-xs text-gray-400">{tutorial.creator}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-xs text-gray-500">{tutorial.views || 0} views</span>
                      <span className="text-xs text-gray-500">•</span>
                      <span className="text-xs text-gray-500">{tutorial.uploadDate}</span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  )
}
