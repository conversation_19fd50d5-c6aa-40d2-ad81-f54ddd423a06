"use client"

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import {
  Rocket,
  Sparkles,
  Target,
  Zap,
  Brain,
  Globe,
  Star,
  Heart,
  Shield
} from 'lucide-react'

interface CTASectionProps {
  onSignupClick: () => void
}

export function CTASection({ onSignupClick }: CTASectionProps) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Quantum CTA particles
  const ctaParticles = Array.from({ length: 20 }, (_, i) => ({
    left: (i * 17 + 3) % 100,
    top: (i * 23 + 7) % 100,
    delay: i * 0.2,
    color: i % 4 === 0 ? '#22d3ee' : i % 4 === 1 ? '#8b5cf6' : i % 4 === 2 ? '#fbbf24' : '#10b981'
  }))
  return (
    <section className="relative px-6 py-20 overflow-hidden">
      {/* Quantum CTA background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-space-dark via-space-blue to-space-dark" />
        <div className="absolute inset-0 consciousness-wave opacity-20" />

        {/* CTA quantum field particles */}
        {isClient && ctaParticles.map((particle, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 rounded-full"
            style={{
              left: `${particle.left}%`,
              top: `${particle.top}%`,
              backgroundColor: particle.color
            }}
            animate={{
              opacity: [0, 0.8, 0],
              scale: [0.5, 1.2, 0.5],
              rotate: 360
            }}
            transition={{
              duration: 4,
              repeat: Number.POSITIVE_INFINITY,
              delay: particle.delay,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Quantum CTA glow orbs */}
        <div className="absolute top-1/4 left-1/4 w-40 h-40 bg-neural-cyan/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/3 right-1/4 w-48 h-48 bg-quantum-purple/10 rounded-full blur-3xl" />
        <div className="absolute top-1/2 right-1/3 w-32 h-32 bg-quantum-gold/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 left-1/3 w-36 h-36 bg-emerald-500/10 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-4xl mx-auto text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
        >
          {/* Quantum CTA Header */}
          <motion.h2
            className="text-4xl md:text-5xl lg:text-7xl font-bold mb-6 font-orbitron"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <span className="bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold bg-clip-text text-transparent">
              Ready to Begin Your
            </span>
            <br />
            <span className="bg-gradient-to-r from-white via-gray-200 to-white bg-clip-text text-transparent text-3xl md:text-4xl lg:text-6xl">
              Quantum Journey?
            </span>
          </motion.h2>

          <motion.p
            className="text-lg md:text-xl lg:text-2xl text-white/80 mb-8 max-w-4xl mx-auto font-space-grotesk leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3, duration: 0.8 }}
          >
            Join thousands of young <span className="text-neural-cyan font-semibold">neural learners</span> who are already building their
            quantum future in consciousness technology. Your <span className="text-quantum-purple font-semibold">NanoHero adventure</span> starts
            with a single quantum click.
          </motion.p>

          {/* Quantum Feature Highlights */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-6 lg:gap-8 mb-12"
          >
            {[
              {
                icon: Brain,
                text: "Launch Neural Journey",
                quantumColor: "#22d3ee",
                description: "Begin consciousness exploration"
              },
              {
                icon: Sparkles,
                text: "Create Quantum Projects",
                quantumColor: "#8b5cf6",
                description: "Build consciousness applications"
              },
              {
                icon: Target,
                text: "Master Neural Skills",
                quantumColor: "#10b981",
                description: "Develop quantum abilities"
              },
              {
                icon: Zap,
                text: "Quantum Level Up",
                quantumColor: "#fbbf24",
                description: "Accelerate consciousness growth"
              },
            ].map((item, index) => (
              <motion.div
                key={item.text}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.15 }}
                whileHover={{ scale: 1.08, y: -5 }}
                className="flex flex-col items-center gap-4 group"
              >
                <motion.div
                  className="relative w-16 h-16 lg:w-20 lg:h-20 rounded-full flex items-center justify-center border-4"
                  style={{
                    background: `linear-gradient(135deg, ${item.quantumColor}, ${item.quantumColor}80)`,
                    borderColor: item.quantumColor,
                    boxShadow: `0 0 30px ${item.quantumColor}40`
                  }}
                  whileHover={{ rotate: 360, scale: 1.1 }}
                  transition={{ duration: 0.8 }}
                >
                  <item.icon className="w-8 h-8 lg:w-10 lg:h-10 text-white relative z-10" />

                  {/* Quantum icon glow */}
                  <div
                    className="absolute inset-0 rounded-full blur-xl opacity-60"
                    style={{ backgroundColor: item.quantumColor }}
                  />

                  {/* Quantum particles around icons */}
                  {isClient && (
                    <div className="absolute inset-0">
                      {[...Array(4)].map((_, i) => (
                        <motion.div
                          key={i}
                          className="absolute w-1 h-1 rounded-full"
                          style={{
                            backgroundColor: item.quantumColor,
                            left: `${25 + Math.cos(i * Math.PI / 2) * 120}%`,
                            top: `${25 + Math.sin(i * Math.PI / 2) * 120}%`,
                          }}
                          animate={{
                            scale: [0, 1, 0],
                            opacity: [0, 0.8, 0],
                            rotate: 360
                          }}
                          transition={{
                            duration: 2,
                            repeat: Number.POSITIVE_INFINITY,
                            delay: i * 0.2
                          }}
                        />
                      ))}
                    </div>
                  )}
                </motion.div>

                <div className="text-center">
                  <p
                    className="text-sm lg:text-base font-medium font-orbitron group-hover:text-opacity-90 transition-all duration-300"
                    style={{
                      color: item.quantumColor,
                      textShadow: `0 0 15px ${item.quantumColor}60`
                    }}
                  >
                    {item.text}
                  </p>
                  <p className="text-xs lg:text-sm text-white/60 mt-1 font-space-grotesk">
                    {item.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Quantum CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4 }}
            className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12"
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="relative group"
            >
              {/* Enhanced quantum glow effect */}
              <div className="absolute -inset-2 bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold rounded-2xl blur-xl opacity-50 group-hover:opacity-80 transition-opacity duration-500 quantum-pulse" />

              <Button
                onClick={() => window.location.href = '/signup'}
                variant="quantum"
                size="xl"
                className="relative bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold text-white font-bold shadow-2xl hover:shadow-neural-cyan/50 transition-all duration-500 border-2 border-neural-cyan/40 font-orbitron px-10 py-5 text-lg lg:text-xl"
              >
                <Rocket className="w-6 h-6 mr-3" />
                Start Your Quantum Adventure
                <Sparkles className="w-6 h-6 ml-3" />
              </Button>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="outline"
                size="xl"
                className="border-neural-cyan/50 text-neural-cyan hover:bg-neural-cyan/10 px-10 py-5 text-lg lg:text-xl font-semibold rounded-xl font-space-grotesk"
              >
                <Globe className="w-6 h-6 mr-3" />
                Explore Quantum Features
              </Button>
            </motion.div>
          </motion.div>

          {/* Quantum Trust Indicators */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.6 }}
            className="flex flex-wrap justify-center gap-6 lg:gap-8"
          >
            {[
              {
                icon: Shield,
                text: "Quantum Free to Start",
                quantumColor: "#10b981",
                description: "Neural access included"
              },
              {
                icon: Star,
                text: "Consciousness Safe & Secure",
                quantumColor: "#22d3ee",
                description: "Quantum protection protocols"
              },
              {
                icon: Heart,
                text: "Neural Parent Approved",
                quantumColor: "#8b5cf6",
                description: "Consciousness guardian verified"
              },
              {
                icon: Brain,
                text: "No Quantum Credit Card Required",
                quantumColor: "#fbbf24",
                description: "Neural learning accessible"
              }
            ].map((indicator, index) => (
              <motion.div
                key={indicator.text}
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.6 + index * 0.1 }}
                whileHover={{ scale: 1.05, y: -2 }}
                className="flex items-center gap-3 group"
              >
                <motion.div
                  className="relative"
                  animate={{
                    scale: [1, 1.1, 1],
                    opacity: [0.8, 1, 0.8]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Number.POSITIVE_INFINITY,
                    delay: index * 0.3
                  }}
                >
                  <div
                    className="w-3 h-3 lg:w-4 lg:h-4 rounded-full border-2"
                    style={{
                      backgroundColor: indicator.quantumColor,
                      borderColor: indicator.quantumColor,
                      boxShadow: `0 0 10px ${indicator.quantumColor}60`
                    }}
                  />

                  {/* Quantum pulse ring */}
                  {isClient && (
                    <motion.div
                      className="absolute inset-0 rounded-full border-2"
                      style={{ borderColor: indicator.quantumColor }}
                      animate={{
                        scale: [1, 2, 1],
                        opacity: [0.6, 0, 0.6]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Number.POSITIVE_INFINITY,
                        delay: index * 0.3
                      }}
                    />
                  )}
                </motion.div>

                <div className="flex items-center gap-2">
                  <indicator.icon
                    className="w-4 h-4 lg:w-5 lg:h-5"
                    style={{ color: indicator.quantumColor }}
                  />
                  <div>
                    <span
                      className="text-sm lg:text-base font-medium font-space-grotesk group-hover:text-opacity-90 transition-all duration-300"
                      style={{
                        color: indicator.quantumColor,
                        textShadow: `0 0 10px ${indicator.quantumColor}40`
                      }}
                    >
                      {indicator.text}
                    </span>
                    <p className="text-xs text-white/60 font-space-grotesk">
                      {indicator.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Quantum Final Encouragement */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.8 }}
            className="mt-12 p-6 lg:p-8 quantum-glass rounded-2xl border-2 relative overflow-hidden"
            style={{
              borderColor: '#22d3ee40',
              background: `linear-gradient(135deg, rgba(34, 211, 238, 0.1), rgba(139, 92, 246, 0.1))`,
              boxShadow: `0 0 30px rgba(34, 211, 238, 0.2)`
            }}
          >
            {/* Quantum encouragement background effects */}
            <div className="absolute inset-0 consciousness-wave opacity-30" />

            {/* Quantum launch particles */}
            {isClient && (
              <div className="absolute inset-0 overflow-hidden rounded-2xl">
                {ctaParticles.slice(0, 8).map((particle, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-1 h-1 rounded-full"
                    style={{
                      left: `${particle.left}%`,
                      top: `${particle.top}%`,
                      backgroundColor: particle.color
                    }}
                    animate={{
                      opacity: [0, 0.8, 0],
                      scale: [0.5, 1.2, 0.5],
                      rotate: 360
                    }}
                    transition={{
                      duration: 3,
                      repeat: Number.POSITIVE_INFINITY,
                      delay: particle.delay
                    }}
                  />
                ))}
              </div>
            )}

            <div className="relative z-10 text-center">
              <p className="text-white/90 text-lg lg:text-xl font-space-grotesk leading-relaxed">
                <span className="text-neural-cyan">🚀</span>{' '}
                <strong
                  className="text-white font-orbitron"
                  style={{ textShadow: '0 0 15px #22d3ee60' }}
                >
                  Special Quantum Launch Offer:
                </strong>{' '}
                Join now and get access to exclusive{' '}
                <span className="text-quantum-purple font-semibold">neural beta features</span> and direct{' '}
                <span className="text-quantum-gold font-semibold">consciousness feedback sessions</span> with our quantum development team!
              </p>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
