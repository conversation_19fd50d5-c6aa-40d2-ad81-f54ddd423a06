'use client'

import { useRef, useMemo, useEffect, useState } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import * as THREE from 'three'
import { useThreeCleanup } from '@/utils/threeCleanup'

// Create shared circular texture for spherical particles
const createParticleTexture = () => {
  const canvas = document.createElement('canvas')
  canvas.width = 64
  canvas.height = 64
  const context = canvas.getContext('2d')!

  // Clear canvas to transparent
  context.clearRect(0, 0, 64, 64)

  // Create radial gradient for spherical appearance with transparent fade
  const gradient = context.createRadialGradient(32, 32, 0, 32, 32, 32)
  gradient.addColorStop(0, 'rgba(255, 255, 255, 1.0)')    // Solid white center
  gradient.addColorStop(0.2, 'rgba(255, 255, 255, 0.9)')  // Bright core
  gradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.6)')  // Medium fade
  gradient.addColorStop(0.8, 'rgba(255, 255, 255, 0.2)')  // Soft edge
  gradient.addColorStop(1.0, 'rgba(255, 255, 255, 0)')  // Completely transparent

  context.fillStyle = gradient
  context.fillRect(0, 0, 64, 64)

  const texture = new THREE.CanvasTexture(canvas)
  texture.needsUpdate = true
  return texture
}

// Optimized quantum wavefield particles with reduced complexity
function Particles({ count }: { count: number }) {
  const meshRef = useRef<THREE.Points>(null)
  const positionAttributeRef = useRef<THREE.BufferAttribute>(null)
  const positionsRef = useRef<Float32Array | null>(null)
  const phasesRef = useRef<Float32Array | null>(null)
  const { addCleanupRef, cleanup } = useThreeCleanup()

  // Cleanup on unmount
  useEffect(() => {
    if (meshRef.current) {
      addCleanupRef(meshRef)
    }
    return () => {
      cleanup()
      // Clean up arrays
      if (positionsRef.current) {
        positionsRef.current = null
      }
      if (phasesRef.current) {
        phasesRef.current = null
      }
    }
  }, [addCleanupRef, cleanup])

  // Create circular texture for glowing particles (cached)
  const particleTexture = useMemo(() => createParticleTexture(), [])

  // Calculate grid resolution based on reduced count
  const resolution = Math.ceil(Math.sqrt(Math.min(count, 1500))) // Reduced max particles

  // Initialize grid positions and phases
  useEffect(() => {
    const totalParticles = resolution * resolution
    const posArray = new Float32Array(totalParticles * 3)
    const phaseArray = new Float32Array(totalParticles)

    for (let x = 0; x < resolution; x++) {
      for (let z = 0; z < resolution; z++) {
        const i = x * resolution + z
        const xPos = (x - resolution / 2) * 0.8 // Spacing between particles
        const zPos = (z - resolution / 2) * 0.8

        // Validate positions before setting
        const safeXPos = isFinite(xPos) ? xPos : 0
        const safeZPos = isFinite(zPos) ? zPos : 0

        // Set initial positions
        posArray.set([safeXPos, 0, safeZPos], i * 3)

        // Random phase for each particle
        phaseArray[i] = Math.random() * Math.PI * 2
      }
    }

    positionsRef.current = posArray
    phasesRef.current = phaseArray
  }, [resolution])

  // Optimized wave motion with reduced calculations
  useFrame(({ clock }) => {
    if (!positionAttributeRef.current || !positionsRef.current || !phasesRef.current) return

    const time = clock.getElapsedTime()
    const pos = positionAttributeRef.current.array as Float32Array
    const totalParticles = resolution * resolution

    // Reduce update frequency for better performance
    const updateStep = Math.max(1, Math.floor(totalParticles / 800)) // Update fewer particles per frame
    const startIndex = Math.floor(time * 100) % updateStep

    for (let i = startIndex; i < totalParticles; i += updateStep) {
      const x = i % resolution
      const z = Math.floor(i / resolution)

      // Get world position with reduced precision
      const worldX = (x - resolution / 2) * 0.6 // Reduced from 0.8
      const worldZ = (z - resolution / 2) * 0.6

      // Simplified wave calculations - only 2 waves instead of 4
      const t = time * 0.8 // Slower animation
      const px = phasesRef.current[i] || 0

      // Reduced wave complexity
      const wave1 = Math.sin(worldX * 0.04 + t + px) * 3 // Reduced amplitude
      const wave2 = Math.sin(worldZ * 0.03 + t * 0.7) * 2

      // Simple final calculation
      const finalY = wave1 + wave2
      const safeY = Math.max(-8, Math.min(8, finalY)) // Reduced range

      // Update Y position
      pos[i * 3 + 1] = safeY
    }

    positionAttributeRef.current.needsUpdate = true

    // Simplified rotation and culling
    if (meshRef.current) {
      meshRef.current.frustumCulled = false
      meshRef.current.rotation.y = time * 0.005 // Slower rotation
    }
  })

  if (!positionsRef.current || !phasesRef.current) return null

  return (
    <points ref={meshRef} frustumCulled={false}>
      <bufferGeometry>
        <bufferAttribute
          ref={positionAttributeRef}
          attach="attributes-position"
          array={positionsRef.current}
          count={positionsRef.current.length / 3}
          itemSize={3}
          usage={THREE.DynamicDrawUsage}
          args={[positionsRef.current, 3]}
        />
      </bufferGeometry>
      <pointsMaterial
        color="#22D3EE"
        size={0.3} // Reduced size
        transparent={true}
        opacity={0.2} // Reduced opacity
        sizeAttenuation={true}
        map={particleTexture}
        alphaTest={0.1} // Increased alpha test
        blending={THREE.AdditiveBlending}
      />
    </points>
  )
}

// Floating energy orbs that flow with the wavefield
function FloatingOrbs() {
  const orbsRef = useRef<THREE.Group>(null)

  const orbs = useMemo(() => {
    return Array.from({ length: 6 }, (_, i) => ({
      baseRadius: 25 + i * 8, // Different flow distances
      speed: 0.3 + Math.random() * 0.4, // Slower, more graceful movement
      phase: (i / 6) * Math.PI * 2, // Evenly distributed starting positions
      flowAmplitude: 3 + Math.random() * 4, // Vertical flow amplitude
      scale: 0.4 + Math.random() * 0.3,
      color: i % 3 === 0 ? 0x8B5CF6 : i % 3 === 1 ? 0x22D3EE : 0xF59E0B, // Three colors
      glowPhase: Math.random() * Math.PI * 2
    }))
  }, [])

  useFrame((state) => {
    if (orbsRef.current) {
      orbsRef.current.children.forEach((orb, i) => {
        const time = state.clock.elapsedTime
        const orbData = orbs[i]

        // Create flowing motion that follows the wavefield
        const angle = time * orbData.speed + orbData.phase
        const x = Math.cos(angle) * orbData.baseRadius
        const z = Math.sin(angle) * orbData.baseRadius

        // Follow the water-like wave motion
        const distanceFromCenter = Math.sqrt(x * x + z * z)

        // Match the main wavefield motion
        const waveY1 = Math.sin(x * 0.05 + z * 0.03 + time * 1.0) * 2
        const waveY2 = Math.sin((x * 0.7 + z * 0.3) * 0.08 + time * 0.7) * 1.5
        const radialWave = Math.sin(distanceFromCenter * 0.08 - time * 2.0) * 1.0 * Math.exp(-distanceFromCenter * 0.02)

        const totalWaveY = waveY1 + waveY2 + radialWave
        const floatY = Math.sin(time * 0.4 + orbData.glowPhase) * 1.5

        orb.position.set(x, totalWaveY + floatY + 3, z) // Offset by +3 to float above surface

        // Gentle rotation
        orb.rotation.x = time * 0.3
        orb.rotation.y = time * 0.2
        orb.rotation.z = Math.sin(time * 0.4 + orbData.glowPhase) * 0.2

        // Pulsing glow effect
        const material = (orb as THREE.Mesh).material as THREE.MeshBasicMaterial
        const glowIntensity = 0.4 + 0.3 * Math.sin(time * 1.5 + orbData.glowPhase)
        material.opacity = glowIntensity
      })
    }
  })

  return (
    <group ref={orbsRef}>
      {orbs.map((orb, i) => (
        <mesh key={i} scale={orb.scale}>
          <sphereGeometry args={[1.5, 16, 16]} />
          <meshBasicMaterial
            color={orb.color}
            transparent={true}
            opacity={0.5}
          />
        </mesh>
      ))}
    </group>
  )
}

// Central energy source that drives the wavefield
function EnergyCore() {
  const coreRef = useRef<THREE.Group>(null)
  const energyRingsRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (!coreRef.current) return

    const time = state.clock.elapsedTime

    // Gentle pulsing of the core
    const scale = 1 + Math.sin(time * 1.5) * 0.2
    coreRef.current.scale.setScalar(scale)

    // Rotation
    coreRef.current.rotation.y = time * 0.2
    coreRef.current.rotation.x = Math.sin(time * 0.3) * 0.1

    // Animate energy rings
    if (energyRingsRef.current) {
      energyRingsRef.current.children.forEach((ring, index) => {
        ring.rotation.x = time * (0.3 + index * 0.1)
        ring.rotation.z = time * (0.2 + index * 0.05)
      })
    }
  })

  return (
    <group ref={coreRef} position={[0, 0, 0]}>
      {/* Central energy core */}
      <mesh>
        <sphereGeometry args={[2, 16, 16]} />
        <meshBasicMaterial
          color={0xF59E0B}
          transparent={true}
          opacity={0.8}
        />
      </mesh>

      {/* Energy rings */}
      <group ref={energyRingsRef}>
        {Array.from({ length: 3 }, (_, i) => (
          <mesh key={i} rotation={[0, 0, i * Math.PI / 3]}>
            <torusGeometry args={[4 + i * 1.5, 0.1, 8, 32]} />
            <meshBasicMaterial
              color={i === 0 ? 0x22D3EE : i === 1 ? 0x8B5CF6 : 0xF59E0B}
              transparent={true}
              opacity={0.6 - i * 0.1}
            />
          </mesh>
        ))}
      </group>
    </group>
  )
}

// Optimized ParticleBackground component
const ParticleBackground = () => {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  if (!isClient) {
    return <div className="fixed inset-0 -z-10 overflow-hidden w-full h-full" />
  }

  return (
    <div className="fixed inset-0 -z-10 overflow-hidden w-full h-full">
      <Canvas
        camera={{ position: [-10, 10, 20], fov: 55 }}
        gl={{
          antialias: false,
          alpha: true,
          powerPreference: 'low-power',
          stencil: false,
          depth: true
        }}
        style={{ background: 'transparent' }}
        dpr={[1, 1.2]} // Reduced from 1.5
        performance={{ min: 0.5 }}
        frameloop="demand"
      >
        {/* Reduced lighting */}
        <ambientLight intensity={0.3} />
        <pointLight position={[10, 10, 10]} intensity={0.2} color={0x22D3EE} />

        {/* Reduced particle count */}
        <Particles count={3000} /> {/* Reduced from 8000 */}

        {/* Energy effects */}
        <FloatingOrbs />
        <EnergyCore />

        {/* Optimized fog */}
        <fog attach="fog" args={[0x0A0F1C, 12, 60]} />
      </Canvas>
    </div>
  )
}

export default ParticleBackground
