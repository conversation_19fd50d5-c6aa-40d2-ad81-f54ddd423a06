'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown, ChevronUp, BarChart3 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

// Import all widgets
import LevelProgressWidget from './LevelProgressWidget'
import StreakWidget from './StreakWidget'
import PerformanceIndicator from './PerformanceIndicator'
import ActivityWidget from './ActivityWidget'
import QuickStatsWidget from './QuickStatsWidget'
import ProgressIndicator from './ProgressIndicator'

import { StudentStats, PerformanceMetrics, ActivityData } from './types'

interface StudentStatsPanelProps {
  className?: string
  variant?: 'topnav' | 'dashboard' | 'sidebar'
  isCollapsed?: boolean
}

export default function StudentStatsPanel({ 
  className = '',
  variant = 'dashboard',
  isCollapsed = false
}: StudentStatsPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  // Mock student data - in real app this would come from props/store
  const studentStats: StudentStats = {
    level: 7,
    xp: 2850,
    xpToNextLevel: 3200,
    totalXP: 15420,
    streak: 12,
    lessonsCompleted: 34,
    averageScore: 87,
    timeSpent: 1240, // minutes
    rank: 'Quantum Explorer',
    badges: 8
  }

  const performanceMetrics: PerformanceMetrics = {
    weeklyProgress: 85,
    monthlyProgress: 92,
    accuracyRate: 89,
    completionRate: 94,
    engagementScore: 88,
    learningVelocity: 1.3
  }

  // Mock activity data for the last 7 days
  const activityData: ActivityData[] = [
    { date: '2024-01-08', xpEarned: 150, lessonsCompleted: 2, timeSpent: 45, score: 85 },
    { date: '2024-01-09', xpEarned: 200, lessonsCompleted: 3, timeSpent: 60, score: 92 },
    { date: '2024-01-10', xpEarned: 180, lessonsCompleted: 2, timeSpent: 50, score: 88 },
    { date: '2024-01-11', xpEarned: 220, lessonsCompleted: 4, timeSpent: 75, score: 94 },
    { date: '2024-01-12', xpEarned: 160, lessonsCompleted: 2, timeSpent: 40, score: 86 },
    { date: '2024-01-13', xpEarned: 190, lessonsCompleted: 3, timeSpent: 55, score: 90 },
    { date: '2024-01-14', xpEarned: 210, lessonsCompleted: 3, timeSpent: 65, score: 91 }
  ]

  // TopNav variant - compact widgets for header
  if (variant === 'topnav') {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <LevelProgressWidget
          currentLevel={studentStats.level}
          currentXP={studentStats.xp}
          xpToNext={studentStats.xpToNextLevel}
          totalXP={studentStats.totalXP}
          size="sm"
          variant="compact"
          className="min-w-[120px]"
        />
        <StreakWidget
          current={studentStats.streak}
          best={15}
          size="sm"
          variant="compact"
          className="min-w-[80px]"
        />
        <QuickStatsWidget
          stats={studentStats}
          size="sm"
          variant="compact"
          className="min-w-[200px]"
        />
      </div>
    )
  }

  // Sidebar variant - collapsible panel
  if (variant === 'sidebar') {
    return (
      <Card className={`bg-black/40 border-gray-500/30 backdrop-blur-xl ${className}`}>
        <CardContent className="p-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="w-full flex items-center justify-between text-white/80 hover:text-white"
          >
            <div className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              {!isCollapsed && <span>Stats</span>}
            </div>
            {!isCollapsed && (
              isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
            )}
          </Button>

          <AnimatePresence>
            {isExpanded && !isCollapsed && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="space-y-3 mt-3"
              >
                <LevelProgressWidget
                  currentLevel={studentStats.level}
                  currentXP={studentStats.xp}
                  xpToNext={studentStats.xpToNextLevel}
                  totalXP={studentStats.totalXP}
                  size="sm"
                  variant="compact"
                />
                <StreakWidget
                  current={studentStats.streak}
                  best={15}
                  size="sm"
                  variant="compact"
                />
                <PerformanceIndicator
                  metrics={performanceMetrics}
                  size="sm"
                  variant="compact"
                />
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>
    )
  }

  // Dashboard variant - full widget showcase
  return (
    <div className={`space-y-6 ${className}`}>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Level Progress */}
        <LevelProgressWidget
          currentLevel={studentStats.level}
          currentXP={studentStats.xp}
          xpToNext={studentStats.xpToNextLevel}
          totalXP={studentStats.totalXP}
        />

        {/* Streak Widget */}
        <StreakWidget
          current={studentStats.streak}
          best={15}
          target={30}
        />

        {/* Quick Stats */}
        <QuickStatsWidget
          stats={studentStats}
          showAnimation={true}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Performance Indicator */}
        <PerformanceIndicator
          metrics={performanceMetrics}
        />

        {/* Activity Widget */}
        <ActivityWidget
          data={activityData}
          timeframe="week"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Progress Indicators */}
        <ProgressIndicator
          current={studentStats.lessonsCompleted}
          target={50}
          label="Lessons Goal"
          color="green"
        />
        <ProgressIndicator
          current={studentStats.timeSpent}
          target={1500}
          label="Study Time (min)"
          color="yellow"
        />
        <ProgressIndicator
          current={studentStats.badges}
          target={15}
          label="Badge Collection"
          color="purple"
        />
        <ProgressIndicator
          current={studentStats.averageScore}
          target={90}
          label="Score Target"
          color="cyan"
        />
      </div>
    </div>
  )
}
