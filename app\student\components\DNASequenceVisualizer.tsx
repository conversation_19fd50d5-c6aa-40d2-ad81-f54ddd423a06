"use client"

import { motion } from 'framer-motion'
import { useMemo } from 'react'

interface DNASequenceVisualizerProps {
  sequence: string
  evolutionStage?: number
  ceLevel?: number
  qsLevel?: number
  className?: string
}

export default function DNASequenceVisualizer({
  sequence,
  evolutionStage = 1,
  ceLevel = 0.5,
  qsLevel = 0.5,
  className = ""
}: DNASequenceVisualizerProps) {
  
  // Convert sequence to base pairs
  const basePairs = useMemo(() => {
    const segments = sequence.split('-')
    const pairs: Array<{
      segment: string
      bases: Array<{
        base: string
        color: string
        complement: string
        type: 'standard' | 'nano' | 'quantum'
      }>
    }> = []

    segments.forEach((segment, segmentIndex) => {
      const bases = segment.split('').map((char, charIndex) => {
        let baseType: 'standard' | 'nano' | 'quantum' = 'standard'
        let complement = ''
        let color = ''

        // Determine base type and properties based on evolution stage
        if (evolutionStage >= 8 && (segment.includes('QUANTUM') || segment.includes('SYNC'))) {
          baseType = 'quantum'
          color = '#FFD700' // Gold for quantum bases
        } else if (evolutionStage >= 5 && (segment.includes('NANO') || segment.includes('EVOL'))) {
          baseType = 'nano'
          color = '#8B5CF6' // Purple for nano bases
        } else {
          baseType = 'standard'
          color = segmentIndex % 2 === 0 ? '#22D3EE' : '#F59E0B' // Cyan/Orange for standard
        }

        // Generate complement based on character
        switch (char) {
          case 'A': complement = 'T'; break
          case 'T': complement = 'A'; break
          case 'G': complement = 'C'; break
          case 'C': complement = 'G'; break
          case 'N': complement = 'O'; break // NANO specific
          case 'O': complement = 'N'; break
          case 'Q': complement = 'X'; break // QUANTUM specific
          case 'X': complement = 'Q'; break
          default: complement = char; break
        }

        return {
          base: char,
          color,
          complement,
          type: baseType
        }
      })

      pairs.push({ segment, bases })
    })

    return pairs
  }, [sequence, evolutionStage])

  const getIntensity = (baseType: string) => {
    switch (baseType) {
      case 'quantum': return 0.4 + qsLevel * 0.4
      case 'nano': return 0.3 + ceLevel * 0.3
      default: return 0.2 + (ceLevel + qsLevel) * 0.2
    }
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Sequence Header */}
      <div className="text-center">
        <div className="text-sm text-white/60 mb-2">DNA Sequence Visualization</div>
        <div className="font-mono text-lg font-bold text-cyan-400">{sequence}</div>
      </div>

      {/* Base Pair Visualization */}
      <div className="space-y-6">
        {basePairs.map((segment, segmentIndex) => (
          <motion.div
            key={segmentIndex}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: segmentIndex * 0.2 }}
            className="space-y-3"
          >
            {/* Segment Label */}
            <div className="text-center">
              <div className="text-xs text-white/40 uppercase tracking-wider">
                {segment.segment}
              </div>
            </div>

            {/* Base Pairs */}
            <div className="flex justify-center">
              <div className="space-y-2">
                {/* Top strand */}
                <div className="flex gap-1">
                  {segment.bases.map((base, baseIndex) => (
                    <motion.div
                      key={`top-${baseIndex}`}
                      className="relative"
                      animate={{
                        scale: [1, 1.1, 1],
                        opacity: [0.8, 1, 0.8]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        delay: baseIndex * 0.1
                      }}
                    >
                      <div
                        className="w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold border-2"
                        style={{
                          backgroundColor: base.color + '20',
                          borderColor: base.color,
                          color: base.color,
                          boxShadow: `0 0 ${getIntensity(base.type) * 20}px ${base.color}40`
                        }}
                      >
                        {base.base}
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Connection lines */}
                <div className="flex gap-1">
                  {segment.bases.map((base, baseIndex) => (
                    <div key={`line-${baseIndex}`} className="w-6 flex justify-center">
                      <motion.div
                        className="w-0.5 h-4 rounded-full"
                        style={{
                          backgroundColor: base.color,
                          opacity: 0.6
                        }}
                        animate={{
                          opacity: [0.4, 0.8, 0.4],
                          scaleY: [1, 1.2, 1]
                        }}
                        transition={{
                          duration: 1.5,
                          repeat: Infinity,
                          delay: baseIndex * 0.1
                        }}
                      />
                    </div>
                  ))}
                </div>

                {/* Bottom strand (complement) */}
                <div className="flex gap-1">
                  {segment.bases.map((base, baseIndex) => (
                    <motion.div
                      key={`bottom-${baseIndex}`}
                      className="relative"
                      animate={{
                        scale: [1, 1.1, 1],
                        opacity: [0.8, 1, 0.8]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        delay: baseIndex * 0.1 + 0.5
                      }}
                    >
                      <div
                        className="w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold border-2"
                        style={{
                          backgroundColor: base.color + '15',
                          borderColor: base.color + '80',
                          color: base.color,
                          boxShadow: `0 0 ${getIntensity(base.type) * 15}px ${base.color}30`
                        }}
                      >
                        {base.complement}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>

            {/* Segment Stats */}
            <div className="flex justify-center gap-4 text-xs">
              <div className="text-cyan-400">
                Bases: {segment.bases.length}
              </div>
              <div className="text-purple-400">
                Type: {segment.bases[0]?.type || 'standard'}
              </div>
              <div className="text-yellow-400">
                Stability: {Math.round(qsLevel * 100)}%
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Legend */}
      <div className="mt-6 p-3 bg-gray-800/30 rounded-lg border border-gray-700">
        <div className="text-xs text-white/60 mb-2 text-center font-bold">Base Pair Legend</div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-cyan-400/20 border border-cyan-400" />
            <span className="text-cyan-400">Standard (A-T, G-C)</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-purple-400/20 border border-purple-400" />
            <span className="text-purple-400">Nano (N-O)</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-yellow-400/20 border border-yellow-400" />
            <span className="text-yellow-400">Quantum (Q-X)</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-gradient-to-r from-cyan-400 to-purple-400" />
            <span className="text-white/60">Evolved</span>
          </div>
        </div>
      </div>
    </div>
  )
}
