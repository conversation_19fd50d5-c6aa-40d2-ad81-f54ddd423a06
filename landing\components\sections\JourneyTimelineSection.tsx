"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { Trophy } from 'lucide-react'
import { journeyStages } from '../../data/constants'

interface JourneyTimelineSectionProps {
  onSignupClick: () => void
}

export function JourneyTimelineSection({ onSignupClick }: JourneyTimelineSectionProps) {
  return (
    <section id="features" className="px-6 py-20 bg-gradient-to-r from-purple-900/20 via-blue-900/20 to-cyan-900/20">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-purple-400 via-blue-400 to-cyan-400 bg-clip-text text-transparent">
            Your Epic Journey Awaits
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            Level up from curious learner to tech mentor through our progressive pathway
          </p>
        </motion.div>

        {/* Journey Timeline */}
        <div className="relative">
          {/* Progress Line */}
          <div className="absolute top-1/2 left-0 right-0 h-1 bg-gradient-to-r from-purple-500 via-blue-500 to-cyan-500 transform -translate-y-1/2 rounded-full"></div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 relative">
            {journeyStages.map((stage, index) => (
              <motion.div
                key={stage.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.2 }}
                whileHover={{ scale: 1.05, y: -10 }}
                className="relative"
              >
                {/* Timeline Node */}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
                  <motion.div
                    className={`w-16 h-16 bg-gradient-to-r ${stage.color} rounded-full flex items-center justify-center border-4 border-white/20 shadow-lg`}
                    whileHover={{ scale: 1.2, rotate: 360 }}
                    transition={{ duration: 0.5 }}
                  >
                    <stage.icon className="w-8 h-8 text-white" />
                  </motion.div>
                </div>

                {/* Stage Card */}
                <motion.div
                  className="bg-black/40 border border-gray-800/50 backdrop-blur-xl rounded-xl p-6 mt-8 hover:bg-black/60 transition-all duration-300"
                  whileHover={{ borderColor: "rgba(34, 211, 238, 0.5)" }}
                >
                  <div className="text-center">
                    <div className="text-sm text-gray-400 mb-1">{stage.level}</div>
                    <h3 className="text-2xl font-bold text-white mb-3">{stage.title}</h3>
                    <p className="text-gray-300 mb-4 leading-relaxed">{stage.description}</p>

                    <div className="space-y-2">
                      <div className="text-sm font-semibold text-cyan-400 mb-2">Unlock:</div>
                      {stage.achievements.map((achievement, i) => (
                        <motion.div
                          key={achievement}
                          initial={{ opacity: 0, x: -20 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          viewport={{ once: true }}
                          transition={{ delay: index * 0.2 + i * 0.1 }}
                          className="flex items-center justify-center gap-2 text-sm text-gray-400"
                        >
                          <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>
                          {achievement}
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Floating CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.8 }}
          className="text-center mt-16"
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-purple-500 via-blue-500 to-cyan-500 hover:from-purple-600 hover:via-blue-600 hover:to-cyan-600 text-white px-12 py-4 text-xl font-semibold rounded-xl shadow-lg hover:shadow-purple-500/25 transition-all duration-300"
              onClick={onSignupClick}
            >
              <Trophy className="w-6 h-6 mr-2" />
              Begin Your Quest
            </Button>
          </motion.div>
          <p className="text-gray-400 mt-4">Start at Level 1 and unlock your potential!</p>
        </motion.div>
      </div>
    </section>
  )
}
