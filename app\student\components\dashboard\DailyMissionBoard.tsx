'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Target, 
  Clock, 
  Play, 
  CheckCircle, 
  Flame, 
  Star,
  BookOpen,
  Puzzle,
  Users,
  Zap,
  Trophy,
  Calendar,
  ChevronRight,
  Sparkles
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { MetricsComponentProps, EvolutionTheme } from './types'

interface DailyMissionBoardProps extends MetricsComponentProps {
  evolutionTheme: EvolutionTheme
}

interface DailyMission {
  id: string
  title: string
  description: string
  type: 'lesson' | 'puzzle' | 'lab' | 'collaboration' | 'meditation'
  difficulty: 'easy' | 'medium' | 'hard'
  timeEstimate: string
  xpReward: number
  status: 'available' | 'in-progress' | 'completed'
  progress?: number
  icon: string
  color: string
}

interface StreakData {
  current: number
  best: number
  lastActive: string
  todayCompleted: boolean
}

export default function DailyMissionBoard({ 
  systemStatus, 
  playerData,
  evolutionTheme 
}: DailyMissionBoardProps) {
  const [selectedMission, setSelectedMission] = useState<DailyMission | null>(null)
  const [showMotivation, setShowMotivation] = useState(true)

  // Mock streak data - in real app this would come from props/store
  const streakData: StreakData = {
    current: 7,
    best: 12,
    lastActive: new Date().toISOString().split('T')[0],
    todayCompleted: false
  }

  // Motivational quotes that rotate
  const motivationalQuotes = [
    "Every quantum leap begins with a single step! 🚀",
    "Your consciousness is expanding with each lesson! 🧠✨",
    "Today's learning shapes tomorrow's breakthroughs! 💡",
    "You're building neural pathways to greatness! ⚡",
    "Knowledge is the ultimate superpower! 🦸‍♂️",
    "Your curiosity is your greatest asset! 🔍",
    "Small daily improvements lead to stunning results! 📈"
  ]

  const [currentQuote, setCurrentQuote] = useState(0)

  // Rotate quotes every 10 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentQuote(prev => (prev + 1) % motivationalQuotes.length)
    }, 10000)
    return () => clearInterval(interval)
  }, [motivationalQuotes.length])

  // Mock daily missions - in real app this would be personalized based on student progress
  const dailyMissions: DailyMission[] = [
    {
      id: 'lesson-quantum-basics',
      title: 'Quantum Fundamentals',
      description: 'Complete the next chapter in Quantum Physics basics',
      type: 'lesson',
      difficulty: 'medium',
      timeEstimate: '15 min',
      xpReward: 150,
      status: 'available',
      icon: '📚',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      id: 'puzzle-wave-function',
      title: 'Wave Function Puzzle',
      description: 'Solve the quantum wave interference challenge',
      type: 'puzzle',
      difficulty: 'hard',
      timeEstimate: '10 min',
      xpReward: 200,
      status: 'available',
      icon: '🧩',
      color: 'from-purple-500 to-pink-500'
    },
    {
      id: 'lab-experiment',
      title: 'Virtual Lab Session',
      description: 'Join the quantum entanglement experiment',
      type: 'lab',
      difficulty: 'easy',
      timeEstimate: '20 min',
      xpReward: 180,
      status: 'in-progress',
      progress: 65,
      icon: '🔬',
      color: 'from-green-500 to-emerald-500'
    }
  ]

  const completedMissions = dailyMissions.filter(m => m.status === 'completed').length
  const totalMissions = dailyMissions.length
  const dailyProgress = (completedMissions / totalMissions) * 100

  const getMissionIcon = (type: string) => {
    switch (type) {
      case 'lesson': return <BookOpen className="w-4 h-4" />
      case 'puzzle': return <Puzzle className="w-4 h-4" />
      case 'lab': return <Users className="w-4 h-4" />
      case 'collaboration': return <Users className="w-4 h-4" />
      case 'meditation': return <Sparkles className="w-4 h-4" />
      default: return <Target className="w-4 h-4" />
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-400 border-green-400/30'
      case 'medium': return 'text-yellow-400 border-yellow-400/30'
      case 'hard': return 'text-red-400 border-red-400/30'
      default: return 'text-gray-400 border-gray-400/30'
    }
  }

  const handleMissionAction = (mission: DailyMission) => {
    if (mission.status === 'available') {
      // Start mission logic
      console.log(`Starting mission: ${mission.title}`)
    } else if (mission.status === 'in-progress') {
      // Continue mission logic
      console.log(`Continuing mission: ${mission.title}`)
    }
  }

  return (
    <Card className="bg-black/40 border-orange-500/30 backdrop-blur-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-orange-400">
          <Target className="w-5 h-5" />
          Daily Mission Board
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Streak Tracker */}
        <div className="flex items-center justify-between p-4 bg-gradient-to-r from-orange-500/10 to-red-500/10 rounded-lg border border-orange-500/30">
          <div className="flex items-center gap-3">
            <div className="relative">
              <Flame className="w-8 h-8 text-orange-400" />
              <motion.div
                className="absolute inset-0"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <Flame className="w-8 h-8 text-orange-400/50" />
              </motion.div>
            </div>
            <div>
              <div className="text-lg font-bold text-orange-400">
                {streakData.current} Day Streak!
              </div>
              <div className="text-sm text-white/60">
                Best: {streakData.best} days
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-white/60">Today's Progress</div>
            <div className="text-xl font-bold text-orange-400">
              {completedMissions}/{totalMissions}
            </div>
          </div>
        </div>

        {/* Motivational Quote */}
        <AnimatePresence mode="wait">
          {showMotivation && (
            <motion.div
              key={currentQuote}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
              className="p-4 bg-gradient-to-r from-cyan-500/10 to-purple-500/10 rounded-lg border border-cyan-500/30 text-center"
            >
              <div className="text-cyan-400 font-medium italic">
                "{motivationalQuotes[currentQuote]}"
              </div>
              <Button
                size="sm"
                variant="ghost"
                className="mt-2 text-xs text-white/40 hover:text-white/60"
                onClick={() => setShowMotivation(false)}
              >
                Hide
              </Button>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Daily Progress */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-white/80 font-medium">Daily Progress</span>
            <span className="text-orange-400 font-bold">
              {Math.round(dailyProgress)}%
            </span>
          </div>
          <div className="relative">
            <Progress 
              value={dailyProgress} 
              className="h-3 bg-gray-700"
            />
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-full"
              animate={{ x: [-100, 200] }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            />
          </div>
        </div>

        {/* Today's Missions */}
        <div className="space-y-3">
          <h4 className="text-white font-medium flex items-center gap-2">
            <Calendar className="w-4 h-4 text-orange-400" />
            Today's Missions
          </h4>
          
          <div className="space-y-3">
            {dailyMissions.map((mission, index) => (
              <motion.div
                key={mission.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`p-4 rounded-lg border transition-all duration-300 cursor-pointer ${
                  mission.status === 'completed' 
                    ? 'bg-green-500/10 border-green-500/30 opacity-75' 
                    : 'bg-gray-800/30 border-gray-700/50 hover:border-orange-500/30'
                }`}
                onClick={() => setSelectedMission(mission)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="text-2xl">{mission.icon}</div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h5 className="text-white font-medium">{mission.title}</h5>
                        <Badge className={`text-xs ${getDifficultyColor(mission.difficulty)}`}>
                          {mission.difficulty}
                        </Badge>
                      </div>
                      <p className="text-sm text-white/60 mt-1">{mission.description}</p>
                      
                      {/* Mission Progress for in-progress missions */}
                      {mission.status === 'in-progress' && mission.progress && (
                        <div className="mt-2">
                          <Progress value={mission.progress} className="h-2" />
                          <div className="text-xs text-cyan-400 mt-1">
                            {mission.progress}% complete
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="text-right space-y-2">
                    <div className="flex items-center gap-2 text-sm text-white/60">
                      <Clock className="w-3 h-3" />
                      {mission.timeEstimate}
                    </div>
                    <div className="flex items-center gap-2 text-sm text-yellow-400">
                      <Star className="w-3 h-3" />
                      +{mission.xpReward} XP
                    </div>
                    
                    {/* Action Button */}
                    <Button
                      size="sm"
                      className={`${
                        mission.status === 'completed'
                          ? 'bg-green-500/20 text-green-400 border-green-500/30'
                          : mission.status === 'in-progress'
                          ? 'bg-cyan-500/20 text-cyan-400 border-cyan-500/30'
                          : 'bg-orange-500/20 text-orange-400 border-orange-500/30'
                      } hover:opacity-80`}
                      onClick={(e) => {
                        e.stopPropagation()
                        handleMissionAction(mission)
                      }}
                    >
                      {mission.status === 'completed' ? (
                        <CheckCircle className="w-4 h-4" />
                      ) : mission.status === 'in-progress' ? (
                        <>
                          <Play className="w-3 h-3 mr-1" />
                          Continue
                        </>
                      ) : (
                        <>
                          <Play className="w-3 h-3 mr-1" />
                          Start
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex gap-2 pt-4 border-t border-white/10">
          <Button 
            size="sm" 
            className="flex-1 bg-blue-500/20 text-blue-400 border border-blue-500/30 hover:bg-blue-500/30"
          >
            <BookOpen className="w-4 h-4 mr-2" />
            Continue Lesson
          </Button>
          <Button 
            size="sm" 
            className="flex-1 bg-purple-500/20 text-purple-400 border border-purple-500/30 hover:bg-purple-500/30"
          >
            <Puzzle className="w-4 h-4 mr-2" />
            Solve Puzzle
          </Button>
          <Button 
            size="sm" 
            className="flex-1 bg-green-500/20 text-green-400 border border-green-500/30 hover:bg-green-500/30"
          >
            <Users className="w-4 h-4 mr-2" />
            Join Lab
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
