/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Syne:wght@400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Exo+2:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');



@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-gray-200;
  }
  body {
    @apply bg-background text-foreground font-inter;
  }
}

/* Custom font utility classes */
@layer utilities {
  /* Primary fonts for different use cases */
  .font-primary {
    @apply font-inter;
  }

  .font-heading {
    @apply font-syne;
  }

  .font-display {
    @apply font-orbitron;
  }

  .font-tech {
    @apply font-exo;
  }

  .font-modern {
    @apply font-space-grotesk;
  }

  /* Semantic font classes */
  .font-hero {
    @apply font-orbitron font-black tracking-wide;
  }

  .font-title {
    @apply font-syne font-bold;
  }

  .font-subtitle {
    @apply font-space-grotesk font-medium;
  }

  .font-body {
    @apply font-inter font-normal;
  }

  .font-caption {
    @apply font-inter font-light text-sm;
  }

  .font-button {
    @apply font-space-grotesk font-semibold tracking-wide;
  }

  .font-code {
    @apply font-orbitron font-mono;
  }

  /* Quantum-themed CSS animations and effects */
  .bg-space-gradient {
    background: linear-gradient(
      135deg,
      #0f0f23 0%,
      #1e1b4b 25%,
      #134e4a 50%,
      #1e1b4b 75%,
      #0f0f23 100%
    );
  }

  .consciousness-wave {
    background: linear-gradient(
      45deg,
      rgba(34, 211, 238, 0.1) 0%,
      rgba(139, 92, 246, 0.1) 25%,
      rgba(251, 191, 36, 0.1) 50%,
      rgba(139, 92, 246, 0.1) 75%,
      rgba(34, 211, 238, 0.1) 100%
    );
    background-size: 400% 400%;
    animation: consciousness-flow 8s ease-in-out infinite;
  }

  .quantum-glass {
    backdrop-filter: blur(16px);
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .quantum-pulse {
    animation: quantum-pulse 2s ease-in-out infinite;
  }

  .quantum-loader-ring {
    animation: quantum-rotate 2s linear infinite;
  }

  .quantum-particle {
    animation: quantum-float 3s ease-in-out infinite;
  }

  .quantum-glow {
    animation: quantum-glow 2s ease-in-out infinite alternate;
  }

  /* Enhanced student dashboard gradients */
  .student-quantum-bg {
    background: radial-gradient(
      ellipse at center,
      rgba(34, 211, 238, 0.1) 0%,
      rgba(139, 92, 246, 0.08) 35%,
      rgba(15, 15, 35, 0.95) 70%,
      rgba(15, 15, 35, 1) 100%
    );
  }

  .neural-pulse {
    animation: neural-pulse 3s ease-in-out infinite;
  }

  .quantum-shimmer {
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(34, 211, 238, 0.1) 50%,
      transparent 100%
    );
    background-size: 200% 100%;
    animation: shimmer 2s ease-in-out infinite;
  }
}

@layer utilities {
  /* Quantum loading animation keyframes */
  @keyframes consciousness-flow {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  @keyframes quantum-pulse {
    0%, 100% {
      opacity: 0.6;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.05);
    }
  }

  @keyframes quantum-rotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes quantum-float {
    0%, 100% {
      transform: translateY(0px) scale(1);
      opacity: 0.7;
    }
    50% {
      transform: translateY(-10px) scale(1.1);
      opacity: 1;
    }
  }

  @keyframes quantum-glow {
    0% {
      box-shadow: 0 0 20px rgba(34, 211, 238, 0.3);
    }
    100% {
      box-shadow: 0 0 40px rgba(34, 211, 238, 0.6), 0 0 60px rgba(139, 92, 246, 0.3);
    }
  }

  @keyframes neural-pulse {
    0%, 100% {
      opacity: 0.4;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.1);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  @keyframes quantum-energy-flow {
    0% {
      transform: rotate(0deg) scale(1);
      opacity: 0.8;
    }
    50% {
      transform: rotate(180deg) scale(1.1);
      opacity: 1;
    }
    100% {
      transform: rotate(360deg) scale(1);
      opacity: 0.8;
    }
  }

  @keyframes quantum-particle-drift {
    0%, 100% {
      transform: translate(0, 0) scale(0.8);
      opacity: 0.4;
    }
    25% {
      transform: translate(10px, -5px) scale(1);
      opacity: 0.8;
    }
    50% {
      transform: translate(-5px, -10px) scale(1.2);
      opacity: 1;
    }
    75% {
      transform: translate(-10px, 5px) scale(1);
      opacity: 0.8;
    }
  }
}
