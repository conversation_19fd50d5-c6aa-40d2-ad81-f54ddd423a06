'use client'

import React, { useRef, useEffect, useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Eye,
  Link,
  Trash2
} from 'lucide-react'
import { Lesson, LessonAssignment } from '../types/curriculum'

interface FlowchartNode {
  id: string
  x: number
  y: number
  width: number
  height: number
  lesson: Lesson & { assignment: LessonAssignment }
  subject: string
  status: string
  connections: string[]
}

interface FlowchartCanvasProps {
  scheduledLessons: (Lesson & { assignment: LessonAssignment })[]
}

export function FlowchartCanvas({ scheduledLessons }: FlowchartCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [scale, setScale] = useState(1)
  const [offset, setOffset] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [selectedNode, setSelectedNode] = useState<FlowchartNode | null>(null)
  const [hoveredNode, setHoveredNode] = useState<FlowchartNode | null>(null)
  const [draggedNode, setDraggedNode] = useState<FlowchartNode | null>(null)
  const [isNodeDragging, setIsNodeDragging] = useState(false)
  const [connectionMode, setConnectionMode] = useState(false)
  const [connectionStart, setConnectionStart] = useState<FlowchartNode | null>(null)
  const [tempConnection, setTempConnection] = useState<{ x: number, y: number } | null>(null)
  const [nodes, setNodes] = useState<FlowchartNode[]>([])
  const [connections, setConnections] = useState<{ from: string, to: string }[]>([])

  // Initialize nodes from scheduled lessons
  const initializeNodes = useCallback((): FlowchartNode[] => {
    const initialNodes: FlowchartNode[] = []
    const subjectColumns: Record<string, number> = {}

    // Group lessons by subject and sort by date
    const lessonsBySubject = scheduledLessons.reduce((acc, lesson) => {
      if (!acc[lesson.subject]) {
        acc[lesson.subject] = []
      }
      acc[lesson.subject].push(lesson)
      return acc
    }, {} as Record<string, (Lesson & { assignment: LessonAssignment })[]>)

    // Sort lessons within each subject by scheduled date
    Object.keys(lessonsBySubject).forEach(subject => {
      lessonsBySubject[subject].sort((a, b) =>
        new Date(a.assignment.scheduledDate).getTime() - new Date(b.assignment.scheduledDate).getTime()
      )
    })

    const subjects = Object.keys(lessonsBySubject)
    const nodeWidth = 200
    const nodeHeight = 120
    const horizontalSpacing = 280
    const verticalSpacing = 150

    subjects.forEach((subject, subjectIndex) => {
      subjectColumns[subject] = subjectIndex

      lessonsBySubject[subject].forEach((lesson, lessonIndex) => {
        const x = subjectIndex * horizontalSpacing + 50
        const y = lessonIndex * verticalSpacing + 100

        // Find connections (prerequisites)
        const nodeConnections = lesson.prerequisites.filter(prereqId =>
          scheduledLessons.some(sl => sl.id === prereqId)
        )

        initialNodes.push({
          id: lesson.id,
          x,
          y,
          width: nodeWidth,
          height: nodeHeight,
          lesson,
          subject,
          status: lesson.assignment.status,
          connections: nodeConnections
        })
      })
    })

    return initialNodes
  }, [scheduledLessons])

  // Initialize nodes and connections on component mount
  React.useEffect(() => {
    if (scheduledLessons.length > 0 && nodes.length === 0) {
      const initialNodes = initializeNodes()
      setNodes(initialNodes)

      // Initialize connections from prerequisites
      const initialConnections: { from: string, to: string }[] = []
      initialNodes.forEach(node => {
        node.connections.forEach(prereqId => {
          initialConnections.push({ from: prereqId, to: node.id })
        })
      })
      setConnections(initialConnections)
    }
  }, [scheduledLessons, initializeNodes, nodes.length])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#22c55e'
      case 'in-progress': return '#3b82f6'
      case 'pending': return '#eab308'
      case 'approved': return '#06b6d4'
      case 'rejected': return '#ef4444'
      default: return '#6b7280'
    }
  }

  const _getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return '✅'
      case 'in-progress': return '🔄'
      case 'pending': return '⏳'
      case 'approved': return '✅'
      case 'rejected': return '❌'
      default: return '🔒'
    }
  }

  const drawFlowchart = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Save context for transformations
    ctx.save()

    // Apply zoom and pan
    ctx.translate(offset.x, offset.y)
    ctx.scale(scale, scale)

    // Draw connections first (behind nodes)
    connections.forEach(connection => {
      const fromNode = nodes.find(n => n.id === connection.from)
      const toNode = nodes.find(n => n.id === connection.to)
      if (fromNode && toNode) {
        drawConnection(ctx, fromNode, toNode)
      }
    })

    // Draw temporary connection line if in connection mode
    if (connectionMode && connectionStart && tempConnection) {
      drawTempConnection(ctx, connectionStart, tempConnection)
    }

    // Draw nodes
    nodes.forEach(node => {
      drawNode(ctx, node)
    })

    // Draw connection points if in connection mode
    if (connectionMode) {
      nodes.forEach(node => {
        drawConnectionPoints(ctx, node)
      })
    }

    // Restore context
    ctx.restore()
  }, [nodes, connections, scale, offset, hoveredNode, selectedNode, connectionMode, tempConnection])

  const drawConnection = (ctx: CanvasRenderingContext2D, fromNode: FlowchartNode, toNode: FlowchartNode) => {
    const fromX = fromNode.x + fromNode.width / 2
    const fromY = fromNode.y + fromNode.height
    const toX = toNode.x + toNode.width / 2
    const toY = toNode.y

    // Draw curved connection
    ctx.beginPath()
    ctx.moveTo(fromX, fromY)

    const controlY = fromY + (toY - fromY) / 2
    ctx.bezierCurveTo(fromX, controlY, toX, controlY, toX, toY)

    ctx.strokeStyle = '#06b6d4'
    ctx.lineWidth = 2
    ctx.stroke()

    // Draw arrow head
    const angle = Math.atan2(toY - controlY, toX - toX)
    const arrowLength = 10
    ctx.beginPath()
    ctx.moveTo(toX, toY)
    ctx.lineTo(
      toX - arrowLength * Math.cos(angle - Math.PI / 6),
      toY - arrowLength * Math.sin(angle - Math.PI / 6)
    )
    ctx.moveTo(toX, toY)
    ctx.lineTo(
      toX - arrowLength * Math.cos(angle + Math.PI / 6),
      toY - arrowLength * Math.sin(angle + Math.PI / 6)
    )
    ctx.stroke()
  }

  const drawTempConnection = (ctx: CanvasRenderingContext2D, fromNode: FlowchartNode, toPoint: { x: number, y: number }) => {
    const fromX = fromNode.x + fromNode.width / 2
    const fromY = fromNode.y + fromNode.height

    ctx.beginPath()
    ctx.moveTo(fromX, fromY)
    ctx.lineTo(toPoint.x, toPoint.y)
    ctx.strokeStyle = '#fbbf24'
    ctx.lineWidth = 2
    ctx.setLineDash([5, 5])
    ctx.stroke()
    ctx.setLineDash([])
  }

  const drawConnectionPoints = (ctx: CanvasRenderingContext2D, node: FlowchartNode) => {
    const points = [
      { x: node.x + node.width / 2, y: node.y }, // top
      { x: node.x + node.width, y: node.y + node.height / 2 }, // right
      { x: node.x + node.width / 2, y: node.y + node.height }, // bottom
      { x: node.x, y: node.y + node.height / 2 }, // left
    ]

    points.forEach(point => {
      ctx.beginPath()
      ctx.arc(point.x, point.y, 6, 0, 2 * Math.PI)
      ctx.fillStyle = connectionStart?.id === node.id ? '#fbbf24' : '#06b6d4'
      ctx.fill()
      ctx.strokeStyle = '#ffffff'
      ctx.lineWidth = 2
      ctx.stroke()
    })
  }

  const drawNode = (ctx: CanvasRenderingContext2D, node: FlowchartNode) => {
    const { x, y, width, height, lesson, status } = node
    const isHovered = hoveredNode?.id === node.id
    const isSelected = selectedNode?.id === node.id

    // Node background
    ctx.fillStyle = isSelected ? '#1f2937' : isHovered ? '#374151' : '#111827'
    ctx.strokeStyle = getStatusColor(status)
    ctx.lineWidth = isSelected ? 3 : 2
    
    // Draw rounded rectangle
    const radius = 8
    ctx.beginPath()
    ctx.roundRect(x, y, width, height, radius)
    ctx.fill()
    ctx.stroke()

    // Status indicator circle
    const statusRadius = 12
    ctx.beginPath()
    ctx.arc(x + width - 20, y + 20, statusRadius, 0, 2 * Math.PI)
    ctx.fillStyle = getStatusColor(status)
    ctx.fill()

    // Text content
    ctx.fillStyle = '#ffffff'
    ctx.font = 'bold 14px Inter'
    ctx.textAlign = 'left'
    
    // Title (truncated if too long)
    const title = lesson.title.length > 25 ? lesson.title.substring(0, 22) + '...' : lesson.title
    ctx.fillText(title, x + 10, y + 25)

    // Subject
    ctx.font = '12px Inter'
    ctx.fillStyle = '#9ca3af'
    ctx.fillText(lesson.subject.replace('-', ' '), x + 10, y + 45)

    // Duration and difficulty
    ctx.fillText(`${lesson.duration} min • ${lesson.difficulty}`, x + 10, y + 65)

    // Scheduled date
    const date = new Date(lesson.assignment.scheduledDate).toLocaleDateString()
    ctx.fillText(`Scheduled: ${date}`, x + 10, y + 85)

    // Status text
    ctx.fillStyle = getStatusColor(status)
    ctx.font = 'bold 12px Inter'
    ctx.fillText(status.toUpperCase(), x + 10, y + 105)
  }

  const getCanvasCoordinates = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return { x: 0, y: 0 }

    const rect = canvas.getBoundingClientRect()
    return {
      x: (event.clientX - rect.left - offset.x) / scale,
      y: (event.clientY - rect.top - offset.y) / scale
    }
  }

  const getNodeAt = (x: number, y: number): FlowchartNode | null => {
    return nodes.find(node =>
      x >= node.x && x <= node.x + node.width &&
      y >= node.y && y <= node.y + node.height
    ) || null
  }

  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const { x, y } = getCanvasCoordinates(event)
    const clickedNode = getNodeAt(x, y)

    if (connectionMode) {
      if (clickedNode) {
        if (!connectionStart) {
          // Start connection
          setConnectionStart(clickedNode)
        } else if (connectionStart.id !== clickedNode.id) {
          // Complete connection
          const newConnection = { from: connectionStart.id, to: clickedNode.id }

          // Check if connection already exists
          const exists = connections.some(conn =>
            conn.from === newConnection.from && conn.to === newConnection.to
          )

          if (!exists) {
            setConnections(prev => [...prev, newConnection])
          }

          setConnectionStart(null)
          setConnectionMode(false)
          setTempConnection(null)
        }
      } else {
        // Cancel connection
        setConnectionStart(null)
        setConnectionMode(false)
        setTempConnection(null)
      }
    } else {
      setSelectedNode(clickedNode)
    }
  }

  const handleCanvasMouseMove = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const { x, y } = getCanvasCoordinates(event)

    if (isNodeDragging && draggedNode) {
      // Drag node
      const deltaX = x - dragStart.x
      const deltaY = y - dragStart.y

      setNodes(prev => prev.map(node =>
        node.id === draggedNode.id
          ? { ...node, x: node.x + deltaX, y: node.y + deltaY }
          : node
      ))

      setDragStart({ x, y })
    } else if (isDragging && !isNodeDragging) {
      // Pan canvas
      const deltaX = event.clientX - dragStart.x
      const deltaY = event.clientY - dragStart.y
      setOffset(prev => ({
        x: prev.x + deltaX,
        y: prev.y + deltaY
      }))
      setDragStart({ x: event.clientX, y: event.clientY })
    } else if (connectionMode && connectionStart) {
      // Update temp connection line
      setTempConnection({ x, y })
    } else {
      // Check for hover
      const hoveredNode = getNodeAt(x, y)
      setHoveredNode(hoveredNode)
    }
  }

  const handleMouseDown = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const { x, y } = getCanvasCoordinates(event)
    const clickedNode = getNodeAt(x, y)

    if (clickedNode && !connectionMode) {
      // Start dragging node
      setIsNodeDragging(true)
      setDraggedNode(clickedNode)
      setDragStart({ x, y })
    } else if (!connectionMode) {
      // Start dragging canvas
      setIsDragging(true)
      setDragStart({ x: event.clientX, y: event.clientY })
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
    setIsNodeDragging(false)
    setDraggedNode(null)
  }

  const handleZoom = (direction: 'in' | 'out') => {
    const factor = direction === 'in' ? 1.2 : 0.8
    setScale(prev => Math.max(0.1, Math.min(3, prev * factor)))
  }

  const handleReset = () => {
    setScale(1)
    setOffset({ x: 0, y: 0 })
    setSelectedNode(null)
    setConnectionMode(false)
    setConnectionStart(null)
    setTempConnection(null)
  }

  const toggleConnectionMode = () => {
    setConnectionMode(!connectionMode)
    setConnectionStart(null)
    setTempConnection(null)
    setSelectedNode(null)
  }

  const deleteConnection = (from: string, to: string) => {
    setConnections(prev => prev.filter(conn =>
      !(conn.from === from && conn.to === to)
    ))
  }

  const deleteSelectedNode = () => {
    if (selectedNode) {
      // Remove node
      setNodes(prev => prev.filter(node => node.id !== selectedNode.id))

      // Remove connections involving this node
      setConnections(prev => prev.filter(conn =>
        conn.from !== selectedNode.id && conn.to !== selectedNode.id
      ))

      setSelectedNode(null)
    }
  }

  // Redraw canvas when data changes
  useEffect(() => {
    drawFlowchart()
  }, [nodes, connections, scale, offset, hoveredNode, selectedNode, connectionMode, connectionStart, tempConnection, drawFlowchart])

  // Handle canvas resize
  useEffect(() => {
    const canvas = canvasRef.current
    const container = containerRef.current
    if (!canvas || !container) return

    const resizeCanvas = () => {
      canvas.width = container.clientWidth
      canvas.height = container.clientHeight
      drawFlowchart()
    }

    resizeCanvas()
    window.addEventListener('resize', resizeCanvas)
    return () => window.removeEventListener('resize', resizeCanvas)
  }, [drawFlowchart])

  return (
    <div className="space-y-4">
      {/* Flowchart Controls */}
      <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="border-cyan-500/30 text-cyan-400">
                Interactive Flowchart
              </Badge>
              <span className="text-sm text-gray-400">
                {nodes.length} lessons • {connections.length} connections
              </span>
              {connectionMode && (
                <Badge className="bg-yellow-500 text-black">
                  Connection Mode: Click nodes to connect
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={toggleConnectionMode}
                className={`${
                  connectionMode
                    ? 'border-yellow-500/50 text-yellow-400 bg-yellow-500/10'
                    : 'border-gray-600 text-gray-300 hover:bg-gray-700/50'
                }`}
              >
                <Link className="w-4 h-4" />
              </Button>

              {selectedNode && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={deleteSelectedNode}
                  className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              )}

              <div className="w-px h-6 bg-gray-600" />

              <Button
                size="sm"
                variant="outline"
                onClick={() => handleZoom('out')}
                className="border-gray-600 text-gray-300 hover:bg-gray-700/50"
              >
                <ZoomOut className="w-4 h-4" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleZoom('in')}
                className="border-gray-600 text-gray-300 hover:bg-gray-700/50"
              >
                <ZoomIn className="w-4 h-4" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={handleReset}
                className="border-gray-600 text-gray-300 hover:bg-gray-700/50"
              >
                <RotateCcw className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Flowchart Canvas */}
      <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
        <CardContent className="p-0">
          <div 
            ref={containerRef}
            className="relative w-full h-[600px] overflow-hidden rounded-lg"
          >
            <canvas
              ref={canvasRef}
              className={`absolute inset-0 ${
                connectionMode
                  ? 'cursor-crosshair'
                  : isNodeDragging
                    ? 'cursor-grabbing'
                    : hoveredNode
                      ? 'cursor-grab'
                      : 'cursor-move'
              }`}
              onClick={handleCanvasClick}
              onMouseMove={handleCanvasMouseMove}
              onMouseDown={handleMouseDown}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
            />
          </div>
        </CardContent>
      </Card>

      {/* Selected Node Details */}
      {selectedNode && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="text-white font-space-grotesk flex items-center gap-2">
                <Eye className="w-5 h-5 text-cyan-400" />
                Lesson Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold text-white mb-2">{selectedNode.lesson.title}</h3>
                  <p className="text-gray-400 text-sm mb-3">{selectedNode.lesson.description}</p>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Duration:</span>
                      <span className="text-white">{selectedNode.lesson.duration} minutes</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Difficulty:</span>
                      <span className="text-white capitalize">{selectedNode.lesson.difficulty}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Scheduled:</span>
                      <span className="text-white">
                        {new Date(selectedNode.lesson.assignment.scheduledDate).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Status:</span>
                      <Badge className={`${
                        selectedNode.status === 'completed' ? 'bg-green-500' :
                        selectedNode.status === 'in-progress' ? 'bg-blue-500' :
                        selectedNode.status === 'pending' ? 'bg-yellow-500' :
                        selectedNode.status === 'approved' ? 'bg-cyan-500' :
                        'bg-gray-500'
                      } text-white`}>
                        {selectedNode.status}
                      </Badge>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-white mb-2">Skills Developed</h4>
                  <div className="flex flex-wrap gap-1 mb-4">
                    {selectedNode.lesson.skills.map(skill => (
                      <Badge key={skill} variant="outline" className="text-xs border-gray-600 text-gray-400">
                        {skill.replace('-', ' ')}
                      </Badge>
                    ))}
                  </div>
                  {selectedNode.lesson.assignment.parentNotes && (
                    <div className="mb-4">
                      <h4 className="font-medium text-white mb-2">Parent Notes</h4>
                      <p className="text-gray-400 text-sm">{selectedNode.lesson.assignment.parentNotes}</p>
                    </div>
                  )}

                  {/* Node Connections */}
                  <div>
                    <h4 className="font-medium text-white mb-2">Connections</h4>
                    <div className="space-y-2">
                      {/* Incoming connections */}
                      {connections.filter(conn => conn.to === selectedNode.id).map((conn, index) => {
                        const fromNode = nodes.find(n => n.id === conn.from)
                        return fromNode ? (
                          <div key={index} className="flex items-center justify-between text-xs bg-gray-800/30 rounded p-2">
                            <span className="text-gray-300">← {fromNode.lesson.title}</span>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => deleteConnection(conn.from, conn.to)}
                              className="text-red-400 hover:text-red-300 p-1 h-auto"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        ) : null
                      })}

                      {/* Outgoing connections */}
                      {connections.filter(conn => conn.from === selectedNode.id).map((conn, index) => {
                        const toNode = nodes.find(n => n.id === conn.to)
                        return toNode ? (
                          <div key={index} className="flex items-center justify-between text-xs bg-gray-800/30 rounded p-2">
                            <span className="text-gray-300">→ {toNode.lesson.title}</span>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => deleteConnection(conn.from, conn.to)}
                              className="text-red-400 hover:text-red-300 p-1 h-auto"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        ) : null
                      })}

                      {connections.filter(conn => conn.from === selectedNode.id || conn.to === selectedNode.id).length === 0 && (
                        <p className="text-gray-500 text-xs">No connections. Use connection mode to link lessons.</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  )
}
