'use client'

import { motion } from 'framer-motion'
import { Dna } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import DNA3DHelix from '../DNA3DHelix'
import { DNAAnalysisProps } from './types'

export default function DNAStructureAnalysis({ 
  playerData, 
  systemStatus 
}: DNAAnalysisProps) {
  return (
    <Card className="bg-black/40 border-cyan-500/30 backdrop-blur-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-cyan-400">
          <Dna className="w-5 h-5" />
          3D DNA Structure Analysis
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="h-80 rounded-lg border border-cyan-500/30 overflow-hidden bg-gradient-to-br from-cyan-900/10 to-purple-900/10">
            <DNA3DHelix
              height={4}
              radius={0.6}
              turns={2}
              baseCount={24}
              evolutionStage={playerData?.level || 1}
              ceLevel={(systemStatus?.consciousnessLevel || 0)}
              qsLevel={(systemStatus?.systemHealth || 0)}
              isAnimated={true}
            />
          </div>

          {/* DNA Structure Stats */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="p-3 bg-cyan-500/10 border border-cyan-500/30 rounded-lg">
              <div className="text-cyan-400 font-bold">Base Pairs</div>
              <div className="text-white/80">24 active</div>
              <div className="text-xs text-cyan-400/60">2 helical turns</div>
            </div>
            <div className="p-3 bg-purple-500/10 border border-purple-500/30 rounded-lg">
              <div className="text-purple-400 font-bold">Complexity</div>
              <div className="text-white/80">Level {playerData?.level || 1}</div>
              <div className="text-xs text-purple-400/60">Quantum enhanced</div>
            </div>
          </div>

          {/* Real-time Flux Indicators */}
          <div className="space-y-2">
            <div className="flex justify-between items-center text-sm">
              <span className="text-white/60">CE Flux Intensity</span>
              <span className="text-cyan-400">{Math.round((systemStatus?.consciousnessLevel || 0) * 100)}%</span>
            </div>
            <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
              <motion.div
                className="h-full bg-gradient-to-r from-cyan-500 to-cyan-400 rounded-full"
                animate={{ width: `${Math.round((systemStatus?.consciousnessLevel || 0) * 100)}%` }}
                transition={{ duration: 1 }}
              />
            </div>

            <div className="flex justify-between items-center text-sm">
              <span className="text-white/60">QS Stability</span>
              <span className="text-purple-400">{Math.round((systemStatus?.systemHealth || 0) * 100)}%</span>
            </div>
            <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
              <motion.div
                className="h-full bg-gradient-to-r from-purple-500 to-purple-400 rounded-full"
                animate={{ width: `${Math.round((systemStatus?.systemHealth || 0) * 100)}%` }}
                transition={{ duration: 1, delay: 0.2 }}
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
