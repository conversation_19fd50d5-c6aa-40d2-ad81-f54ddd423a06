'use client'

import React, { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Calendar,
  Clock,
  Users,
  CheckCircle,
  XCircle,
  AlertCircle,
  Play,
  Eye,
  Plus,
  BookOpen,
  Zap,
  Award,
  GripVertical,
  MoreVertical
} from 'lucide-react'
import { useCurriculumPlanner } from './CurriculumPlannerProvider'
import { Lesson, LessonAssignment } from '../types/curriculum'

// Drag and Drop Types
interface DragItem {
  type: 'lesson'
  lesson: Lesson
  assignment?: LessonAssignment
  sourceColumn: string
}

// Kanban Column Configuration
const KANBAN_COLUMNS = [
  {
    id: 'available',
    title: 'Available Lessons',
    description: 'Lessons ready to be scheduled',
    color: 'from-gray-500 to-gray-600',
    textColor: 'text-gray-400',
    bgColor: 'bg-gray-500/10',
    borderColor: 'border-gray-500/20'
  },
  {
    id: 'pending',
    title: 'Pending Approval',
    description: 'Lessons waiting for parent approval',
    color: 'from-yellow-500 to-orange-500',
    textColor: 'text-yellow-400',
    bgColor: 'bg-yellow-500/10',
    borderColor: 'border-yellow-500/20'
  },
  {
    id: 'approved',
    title: 'Approved & Scheduled',
    description: 'Lessons approved and ready to start',
    color: 'from-cyan-500 to-blue-500',
    textColor: 'text-cyan-400',
    bgColor: 'bg-cyan-500/10',
    borderColor: 'border-cyan-500/20'
  },
  {
    id: 'in-progress',
    title: 'In Progress',
    description: 'Lessons currently being taken',
    color: 'from-blue-500 to-purple-500',
    textColor: 'text-blue-400',
    bgColor: 'bg-blue-500/10',
    borderColor: 'border-blue-500/20'
  },
  {
    id: 'completed',
    title: 'Completed',
    description: 'Successfully finished lessons',
    color: 'from-green-500 to-emerald-500',
    textColor: 'text-green-400',
    bgColor: 'bg-green-500/10',
    borderColor: 'border-green-500/20'
  }
]

// Kanban Lesson Card Component
interface KanbanLessonCardProps {
  lesson: Lesson
  assignment?: LessonAssignment
  columnId: string
  onDragStart: (item: DragItem) => void
  onLessonAction: (lesson: Lesson, action: 'preview' | 'schedule' | 'approve' | 'reject') => void
  isDragging?: boolean
}

function KanbanLessonCard({
  lesson,
  assignment,
  columnId,
  onDragStart,
  onLessonAction,
  isDragging = false
}: KanbanLessonCardProps) {
  const handleDragStart = (e: React.DragEvent) => {
    const dragItem: DragItem = {
      type: 'lesson',
      lesson,
      assignment,
      sourceColumn: columnId
    }
    onDragStart(dragItem)
    e.dataTransfer.setData('application/json', JSON.stringify(dragItem))
    e.dataTransfer.effectAllowed = 'move'
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'border-green-500/30 text-green-400'
      case 'intermediate': return 'border-yellow-500/30 text-yellow-400'
      case 'advanced': return 'border-red-500/30 text-red-400'
      default: return 'border-gray-500/30 text-gray-400'
    }
  }

  const getStatusIcon = () => {
    if (!assignment) return Plus
    switch (assignment.status) {
      case 'completed': return CheckCircle
      case 'approved': return CheckCircle
      case 'pending': return AlertCircle
      case 'rejected': return XCircle
      case 'in-progress': return Play
      default: return AlertCircle
    }
  }

  const StatusIcon = getStatusIcon()

  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      whileHover={{ scale: 1.02 }}
      className={`${isDragging ? 'opacity-50' : ''}`}
    >
      <Card
        className="bg-gray-800/40 border-gray-700/50 hover:border-gray-600/70 transition-all cursor-move group"
        draggable
        onDragStart={handleDragStart}
      >
        <CardContent className="p-4">
          {/* Card Header */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <GripVertical className="w-4 h-4 text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity" />
                <h4 className="font-semibold text-white text-sm truncate">
                  {lesson.title}
                </h4>
              </div>
              <p className="text-xs text-gray-400 line-clamp-2 mb-2">
                {lesson.description}
              </p>
            </div>
            <div className="flex items-center gap-1 ml-2">
              <StatusIcon className="w-4 h-4 text-gray-400" />
              <Button
                size="sm"
                variant="ghost"
                className="w-6 h-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => onLessonAction(lesson, 'preview')}
              >
                <MoreVertical className="w-3 h-3" />
              </Button>
            </div>
          </div>

          {/* Lesson Meta */}
          <div className="flex items-center gap-3 text-xs text-gray-500 mb-3">
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              {lesson.duration}m
            </div>
            <div className="flex items-center gap-1">
              <Users className="w-3 h-3" />
              {lesson.ageRange[0]}-{lesson.ageRange[1]}
            </div>
            <Badge
              variant="outline"
              className={`text-xs ${getDifficultyColor(lesson.difficulty)}`}
            >
              {lesson.difficulty}
            </Badge>
          </div>

          {/* Skills Tags */}
          <div className="flex flex-wrap gap-1 mb-3">
            {lesson.skills.slice(0, 2).map((skill) => (
              <Badge
                key={skill}
                variant="outline"
                className="text-xs border-gray-600 text-gray-400"
              >
                {skill.replace('-', ' ')}
              </Badge>
            ))}
            {lesson.skills.length > 2 && (
              <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
                +{lesson.skills.length - 2}
              </Badge>
            )}
          </div>

          {/* Assignment Info */}
          {assignment && (
            <div className="text-xs text-gray-400 border-t border-gray-700/50 pt-2">
              <div className="flex justify-between items-center">
                <span>Scheduled:</span>
                <span>{new Date(assignment.scheduledDate).toLocaleDateString()}</span>
              </div>
              {assignment.parentNotes && (
                <div className="mt-1 text-blue-400">
                  Note: {assignment.parentNotes}
                </div>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 mt-3 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              size="sm"
              variant="outline"
              className="flex-1 text-xs border-gray-600 text-gray-300 hover:bg-gray-700/50"
              onClick={() => onLessonAction(lesson, 'preview')}
            >
              <Eye className="w-3 h-3 mr-1" />
              Preview
            </Button>

            {columnId === 'available' && (
              <Button
                size="sm"
                className="flex-1 text-xs bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600"
                onClick={() => onLessonAction(lesson, 'schedule')}
              >
                <Plus className="w-3 h-3 mr-1" />
                Schedule
              </Button>
            )}

            {columnId === 'pending' && (
              <div className="flex gap-1 flex-1">
                <Button
                  size="sm"
                  className="flex-1 text-xs bg-green-500 hover:bg-green-600"
                  onClick={() => onLessonAction(lesson, 'approve')}
                >
                  <CheckCircle className="w-3 h-3" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-1 text-xs border-red-500/30 text-red-400 hover:bg-red-500/10"
                  onClick={() => onLessonAction(lesson, 'reject')}
                >
                  <XCircle className="w-3 h-3" />
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

// Kanban Column Component
interface KanbanColumnProps {
  column: typeof KANBAN_COLUMNS[0]
  lessons: (Lesson & { assignment?: LessonAssignment })[]
  onDrop: (dragItem: DragItem, targetColumn: string) => void
  onLessonAction: (lesson: Lesson, action: 'preview' | 'schedule' | 'approve' | 'reject') => void
  draggedItem: DragItem | null
}

function KanbanColumn({ column, lessons, onDrop, onLessonAction, draggedItem }: KanbanColumnProps) {
  const [isDragOver, setIsDragOver] = useState(false)

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)

    try {
      const dragData = e.dataTransfer.getData('application/json')
      const dragItem: DragItem = JSON.parse(dragData)

      if (dragItem.sourceColumn !== column.id) {
        onDrop(dragItem, column.id)
      }
    } catch (error) {
      console.error('Error handling drop:', error)
    }
  }

  const canAcceptDrop = (dragItem: DragItem | null, targetColumn: string) => {
    if (!dragItem) return true

    // Define valid transitions
    const validTransitions: Record<string, string[]> = {
      'available': ['pending'],
      'pending': ['approved', 'available'], // Can reject back to available
      'approved': ['in-progress', 'pending'], // Can modify or start
      'in-progress': ['completed', 'approved'], // Can complete or pause
      'completed': [] // Final state
    }

    return validTransitions[dragItem.sourceColumn]?.includes(targetColumn) || false
  }

  const isValidDropTarget = canAcceptDrop(draggedItem, column.id)

  return (
    <div className="flex-1 min-w-80">
      <Card className={`h-full ${column.bgColor} ${column.borderColor} backdrop-blur-xl`}>
        {/* Column Header */}
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className={`text-lg font-space-grotesk ${column.textColor} flex items-center gap-2`}>
                {column.title}
                <Badge variant="outline" className={`${column.borderColor} ${column.textColor}`}>
                  {lessons.length}
                </Badge>
              </CardTitle>
              <p className="text-xs text-gray-400 mt-1">{column.description}</p>
            </div>

            {column.id === 'available' && (
              <Button
                size="sm"
                variant="outline"
                className="border-gray-600 text-gray-300 hover:bg-gray-700/50"
                onClick={() => {
                  // Open lesson library or add lesson modal
                  console.log('Add lesson to available')
                }}
              >
                <Plus className="w-4 h-4" />
              </Button>
            )}
          </div>
        </CardHeader>

        {/* Drop Zone */}
        <CardContent
          className={`flex-1 min-h-96 transition-all ${
            isDragOver && isValidDropTarget
              ? `${column.bgColor} border-2 border-dashed ${column.borderColor.replace('border-', 'border-')}`
              : isDragOver && !isValidDropTarget
              ? 'bg-red-500/10 border-2 border-dashed border-red-500/30'
              : ''
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {/* Lessons */}
          <div className="space-y-3">
            <AnimatePresence>
              {lessons.map((lesson) => (
                <KanbanLessonCard
                  key={lesson.id}
                  lesson={lesson}
                  assignment={lesson.assignment}
                  columnId={column.id}
                  onDragStart={() => {}}
                  onLessonAction={onLessonAction}
                  isDragging={draggedItem?.lesson.id === lesson.id}
                />
              ))}
            </AnimatePresence>
          </div>

          {/* Empty State */}
          {lessons.length === 0 && (
            <div className="flex flex-col items-center justify-center h-32 text-gray-500">
              <div className={`w-12 h-12 rounded-full ${column.bgColor} flex items-center justify-center mb-2`}>
                {column.id === 'available' && <BookOpen className="w-6 h-6" />}
                {column.id === 'pending' && <AlertCircle className="w-6 h-6" />}
                {column.id === 'approved' && <CheckCircle className="w-6 h-6" />}
                {column.id === 'in-progress' && <Play className="w-6 h-6" />}
                {column.id === 'completed' && <Award className="w-6 h-6" />}
              </div>
              <p className="text-sm text-center">
                {column.id === 'available' && 'No lessons available'}
                {column.id === 'pending' && 'No lessons pending approval'}
                {column.id === 'approved' && 'No lessons approved'}
                {column.id === 'in-progress' && 'No lessons in progress'}
                {column.id === 'completed' && 'No lessons completed'}
              </p>
              {isDragOver && isValidDropTarget && (
                <p className="text-xs text-cyan-400 mt-1">Drop lesson here</p>
              )}
              {isDragOver && !isValidDropTarget && (
                <p className="text-xs text-red-400 mt-1">Invalid drop target</p>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

// Legacy SubjectFlow component - removed in favor of Kanban board

export function CurriculumFlowChart() {
  const { state, getSelectedChild, scheduleLesson, approveLesson, rejectLesson, updateAssignment } = useCurriculumPlanner()
  const [_selectedLesson, _setSelectedLesson] = useState<Lesson | null>(null)
  const [_showPreview, _setShowPreview] = useState(false)
  const [draggedItem, setDraggedItem] = useState<DragItem | null>(null)

  const selectedChild = getSelectedChild()
  const childAssignments = selectedChild ? state.assignments.filter(a => a.childId === selectedChild.id) : []

  // Get lessons that are actually scheduled/assigned for this child
  const scheduledLessons = childAssignments.map(assignment => {
    const lesson = state.lessons.find(l => l.id === assignment.lessonId)
    return lesson ? { ...lesson, assignment } : null
  }).filter(Boolean) as (Lesson & { assignment: LessonAssignment })[]

  // Get available lessons (not yet scheduled for this child)
  const availableLessons = state.lessons.filter(lesson =>
    !childAssignments.some(assignment => assignment.lessonId === lesson.id)
  ).map(lesson => ({ ...lesson, assignment: undefined }))

  // Organize lessons by status for Kanban columns
  const lessonsByStatus = {
    available: availableLessons,
    pending: scheduledLessons.filter(l => l.assignment.status === 'pending'),
    approved: scheduledLessons.filter(l => l.assignment.status === 'approved'),
    'in-progress': scheduledLessons.filter(l => l.assignment.status === 'in-progress'),
    completed: scheduledLessons.filter(l => l.assignment.status === 'completed')
  }

  const _handleDragStart = useCallback((item: DragItem) => {
    setDraggedItem(item)
  }, [])

  const handleDrop = useCallback((dragItem: DragItem, targetColumn: string) => {
    if (!selectedChild || dragItem.sourceColumn === targetColumn) return

    const { lesson, assignment } = dragItem

    // Handle transitions between columns
    switch (targetColumn) {
      case 'pending':
        if (dragItem.sourceColumn === 'available') {
          // Schedule lesson for approval
          const tomorrow = new Date()
          tomorrow.setDate(tomorrow.getDate() + 1)
          scheduleLesson(lesson.id, tomorrow, selectedChild.id, 'pending')
        }
        break

      case 'approved':
        if (assignment && dragItem.sourceColumn === 'pending') {
          // Approve pending lesson
          approveLesson(assignment.id)
        }
        break

      case 'in-progress':
        if (assignment && dragItem.sourceColumn === 'approved') {
          // Start approved lesson
          updateAssignment(assignment.id, { status: 'in-progress' })
        }
        break

      case 'completed':
        if (assignment && dragItem.sourceColumn === 'in-progress') {
          // Complete in-progress lesson
          updateAssignment(assignment.id, {
            status: 'completed',
            completedAt: new Date()
          })
        }
        break

      case 'available':
        if (assignment && dragItem.sourceColumn === 'pending') {
          // Reject pending lesson (move back to available)
          rejectLesson(assignment.id, 'Parent moved back to available')
        }
        break
    }

    setDraggedItem(null)
  }, [selectedChild, scheduleLesson, approveLesson, rejectLesson, updateAssignment])

  const handleLessonAction = (lesson: Lesson, action: 'preview' | 'schedule' | 'approve' | 'reject') => {
    _setSelectedLesson(lesson)

    switch (action) {
      case 'preview':
        _setShowPreview(true)
        break
      case 'schedule':
        if (selectedChild) {
          const tomorrow = new Date()
          tomorrow.setDate(tomorrow.getDate() + 1)
          scheduleLesson(lesson.id, tomorrow, selectedChild.id, 'pending')
        }
        break
      case 'approve':
        const assignment = childAssignments.find(a => a.lessonId === lesson.id)
        if (assignment) {
          approveLesson(assignment.id)
        }
        break
      case 'reject':
        const rejectAssignment = childAssignments.find(a => a.lessonId === lesson.id)
        if (rejectAssignment) {
          rejectLesson(rejectAssignment.id, 'Parent rejected')
        }
        break
    }
  }

  if (!selectedChild) {
    return (
      <div className="text-center py-12">
        <Users className="w-16 h-16 text-gray-600 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-400 mb-2">Select a Child</h3>
        <p className="text-gray-500">Choose a child to view their curriculum kanban board</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white font-space-grotesk">
            {selectedChild.name}&apos;s Curriculum Kanban
          </h2>
          <p className="text-gray-400 mt-1">
            Drag and drop lessons between columns to manage their status
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
            onClick={() => window.location.href = '/parent-dashboard/curriculum?tab=library'}
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Lessons
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="border-cyan-500/30 text-cyan-400 hover:bg-cyan-500/10"
            onClick={() => {
              // Auto-generate a basic curriculum
              console.log('Auto-generate curriculum')
            }}
          >
            <Zap className="w-4 h-4 mr-2" />
            Auto-Generate
          </Button>
        </div>
      </div>

      {/* Curriculum Stats */}
      <Card className="bg-gradient-to-r from-cyan-500/10 to-blue-500/10 border-cyan-500/20 backdrop-blur-xl">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {KANBAN_COLUMNS.map((column) => {
              const count = lessonsByStatus[column.id as keyof typeof lessonsByStatus]?.length || 0
              return (
                <div key={column.id} className="text-center">
                  <div className={`text-2xl font-bold ${column.textColor}`}>
                    {count}
                  </div>
                  <div className="text-sm text-gray-400">{column.title}</div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Kanban Board */}
      <div className="flex gap-4 overflow-x-auto pb-4">
        {KANBAN_COLUMNS.map((column) => (
          <KanbanColumn
            key={column.id}
            column={column}
            lessons={lessonsByStatus[column.id as keyof typeof lessonsByStatus] || []}
            onDrop={handleDrop}
            onLessonAction={handleLessonAction}
            draggedItem={draggedItem}
          />
        ))}
      </div>

      {/* Instructions */}
      <Card className="bg-gray-800/30 border-gray-700/50 backdrop-blur-xl">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="p-2 bg-cyan-500/20 rounded-lg">
              <GripVertical className="w-5 h-5 text-cyan-400" />
            </div>
            <div>
              <h3 className="text-white font-medium mb-1">How to use the Kanban Board</h3>
              <div className="text-sm text-gray-400 space-y-1">
                <p>• <strong>Available Lessons:</strong> Drag lessons here to schedule them for approval</p>
                <p>• <strong>Pending Approval:</strong> Review and approve/reject lessons your child wants to take</p>
                <p>• <strong>Approved & Scheduled:</strong> Lessons ready for your child to start</p>
                <p>• <strong>In Progress:</strong> Lessons your child is currently taking</p>
                <p>• <strong>Completed:</strong> Successfully finished lessons</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Empty State */}
      {Object.values(lessonsByStatus).every(lessons => lessons.length === 0) && (
        <div className="text-center py-12">
          <Calendar className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-400 mb-2">No Lessons Available</h3>
          <p className="text-gray-500 mb-6">
            Start by adding lessons to {selectedChild.name}&apos;s curriculum.
          </p>
          <Button
            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
            onClick={() => window.location.href = '/parent-dashboard/curriculum?tab=library'}
          >
            <BookOpen className="w-4 h-4 mr-2" />
            Browse Lesson Library
          </Button>
        </div>
      )}
    </div>
  )
}
