"use client"

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Globe, 
  Users, 
  Calendar, 
  Sparkles, 
  ArrowRight,
  X,
  Building,
  Heart,
  Brain,
  Hammer,
  Search,
  Trophy,
  Map
} from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'

interface EpochTownsNavigationProps {
  isOpen: boolean
  onClose: () => void
  onNavigate: (path: string) => void
  className?: string
}

export function EpochTownsNavigation({
  isOpen,
  onClose,
  onNavigate,
  className = ""
}: EpochTownsNavigationProps) {
  const currentGeneration = new Date().toLocaleDateString('en-US', { 
    month: 'short', 
    year: 'numeric' 
  })

  const quickActions = [
    {
      id: 'dashboard',
      title: 'Dashboard Overview',
      description: 'Quick access to your town stats and DNA',
      icon: Globe,
      color: 'from-cyan-500 to-blue-500',
      path: '/dashboard?section=epochtowns&view=overview'
    },
    {
      id: 'town',
      title: 'Enter Your Town',
      description: 'Explore the 3D world and contribute',
      icon: Building,
      color: 'from-green-500 to-emerald-500',
      path: '/dashboard?section=epochtowns&view=town'
    },
    {
      id: 'explore',
      title: 'Explore All Towns',
      description: 'Visit other generations and see their evolution',
      icon: Map,
      color: 'from-purple-500 to-pink-500',
      path: '/epoch-towns'
    },
    {
      id: 'social',
      title: 'Social Hub',
      description: 'Connect with your cohort and mentors',
      icon: Users,
      color: 'from-yellow-500 to-orange-500',
      path: '/social-demo'
    }
  ]

  const townStats = {
    empathy: 78,
    wisdom: 85,
    builder: 72,
    curiosity: 91
  }

  const handleActionClick = (action: typeof quickActions[0]) => {
    onNavigate(action.path)
    onClose()
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className={`bg-gray-900 rounded-xl border border-gray-700 max-w-4xl w-full max-h-[90vh] overflow-y-auto ${className}`}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="p-6 border-b border-gray-700">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-lg">
                    <Globe className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">Epoch Towns</h2>
                    <p className="text-gray-400">Collaborative Evolution Platform</p>
                  </div>
                </div>
                <Button variant="ghost" size="sm" onClick={onClose}>
                  <X className="w-5 h-5" />
                </Button>
              </div>
            </div>

            {/* Current Town Info */}
            <div className="p-6 border-b border-gray-700">
              <Card className="bg-gradient-to-r from-cyan-900/50 to-purple-900/50 border-cyan-600">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-2">
                      <h3 className="text-lg font-semibold text-white">Your Current Town</h3>
                      <div className="flex items-center gap-4 text-sm text-cyan-300">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          <span>Generation: {currentGeneration}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Users className="w-4 h-4" />
                          <span>42 Citizens</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Trophy className="w-4 h-4" />
                          <span>Level 8</span>
                        </div>
                      </div>
                    </div>
                    <Badge className="bg-green-600 text-white">Active</Badge>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Town Stats */}
            <div className="p-6 border-b border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-4">Town DNA Traits</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="flex items-center gap-3 p-3 bg-gray-800 rounded-lg">
                  <Heart className="w-6 h-6 text-red-400" />
                  <div>
                    <p className="text-sm text-gray-400">Empathy</p>
                    <p className="text-lg font-bold text-white">{townStats.empathy}%</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-gray-800 rounded-lg">
                  <Brain className="w-6 h-6 text-blue-400" />
                  <div>
                    <p className="text-sm text-gray-400">Wisdom</p>
                    <p className="text-lg font-bold text-white">{townStats.wisdom}%</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-gray-800 rounded-lg">
                  <Hammer className="w-6 h-6 text-yellow-400" />
                  <div>
                    <p className="text-sm text-gray-400">Builder</p>
                    <p className="text-lg font-bold text-white">{townStats.builder}%</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-gray-800 rounded-lg">
                  <Search className="w-6 h-6 text-green-400" />
                  <div>
                    <p className="text-sm text-gray-400">Curiosity</p>
                    <p className="text-lg font-bold text-white">{townStats.curiosity}%</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {quickActions.map((action) => (
                  <motion.div
                    key={action.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Card 
                      className="bg-gray-800 border-gray-700 hover:border-cyan-400 transition-all cursor-pointer"
                      onClick={() => handleActionClick(action)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start gap-4">
                          <div className={`p-3 bg-gradient-to-r ${action.color} rounded-lg`}>
                            <action.icon className="w-6 h-6 text-white" />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold text-white mb-1">{action.title}</h4>
                            <p className="text-sm text-gray-400 mb-3">{action.description}</p>
                            <div className="flex items-center text-cyan-400 text-sm">
                              <span>Go to {action.title}</span>
                              <ArrowRight className="w-4 h-4 ml-1" />
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Recent Activity */}
            <div className="p-6 border-t border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-4">Recent Town Activity</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-3 bg-gray-800 rounded-lg">
                  <Avatar className="w-8 h-8">
                    <AvatarFallback>CE</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="text-sm text-white">CyberExplorer shared wisdom</p>
                    <p className="text-xs text-gray-400">Contributed to town knowledge base • 2 min ago</p>
                  </div>
                  <Badge variant="outline" className="text-xs">+15 XP</Badge>
                </div>
                <div className="flex items-center gap-3 p-3 bg-gray-800 rounded-lg">
                  <Avatar className="w-8 h-8">
                    <AvatarFallback>TN</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="text-sm text-white">TechNinja built new structure</p>
                    <p className="text-xs text-gray-400">Added learning center to town • 5 min ago</p>
                  </div>
                  <Badge variant="outline" className="text-xs">+25 XP</Badge>
                </div>
                <div className="flex items-center gap-3 p-3 bg-gray-800 rounded-lg">
                  <Avatar className="w-8 h-8">
                    <AvatarFallback>ML</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="text-sm text-white">MindLearner sparked curiosity</p>
                    <p className="text-xs text-gray-400">Asked thought-provoking question • 8 min ago</p>
                  </div>
                  <Badge variant="outline" className="text-xs">+10 XP</Badge>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="p-6 border-t border-gray-700 bg-gray-800/50">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-sm text-gray-400">
                  <Sparkles className="w-4 h-4" />
                  <span>Your town DNA: EWBCSH</span>
                </div>
                <Button
                  onClick={() => handleActionClick(quickActions[2])}
                  className="bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600"
                >
                  <Globe className="w-4 h-4 mr-2" />
                  Explore Full Experience
                </Button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default EpochTownsNavigation
