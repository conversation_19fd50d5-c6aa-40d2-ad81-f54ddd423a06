'use client'

import { TrendingUp } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { DNAAnalysisProps } from './types'

export default function EvolutionPrediction({ 
  playerData, 
  systemStatus 
}: DNAAnalysisProps) {
  return (
    <Card className="bg-black/40 border-yellow-500/30 backdrop-blur-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-yellow-400">
          <TrendingUp className="w-5 h-5" />
          Evolution Prediction Model
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Current State */}
          <div className="space-y-3">
            <h4 className="text-white font-bold">Current State</h4>
            <div className="space-y-2">
              <div className="p-3 bg-cyan-500/10 border border-cyan-500/30 rounded-lg">
                <div className="text-cyan-400 text-sm">DNA Complexity</div>
                <div className="text-white font-bold">Level {playerData?.level || 1}</div>
              </div>
              <div className="p-3 bg-purple-500/10 border border-purple-500/30 rounded-lg">
                <div className="text-purple-400 text-sm">Quantum Coherence</div>
                <div className="text-white font-bold">{Math.round((systemStatus?.systemHealth || 0) * 100)}%</div>
              </div>
            </div>
          </div>

          {/* Predicted Evolution */}
          <div className="space-y-3">
            <h4 className="text-white font-bold">Next Evolution</h4>
            <div className="space-y-2">
              <div className="p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                <div className="text-yellow-400 text-sm">Estimated Time</div>
                <div className="text-white font-bold">2.3 days</div>
              </div>
              <div className="p-3 bg-orange-500/10 border border-orange-500/30 rounded-lg">
                <div className="text-orange-400 text-sm">New Capabilities</div>
                <div className="text-white font-bold">Neural++</div>
              </div>
            </div>
          </div>

          {/* Optimization Suggestions */}
          <div className="space-y-3">
            <h4 className="text-white font-bold">Optimization</h4>
            <div className="space-y-2 text-sm">
              <div className="p-2 bg-green-500/10 border border-green-500/30 rounded">
                <div className="text-green-400">✓ Complete 3 Learning Nodes</div>
              </div>
              <div className="p-2 bg-blue-500/10 border border-blue-500/30 rounded">
                <div className="text-blue-400">→ Meditate for QS boost</div>
              </div>
              <div className="p-2 bg-purple-500/10 border border-purple-500/30 rounded">
                <div className="text-purple-400">→ Assist 2 Timeline members</div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
