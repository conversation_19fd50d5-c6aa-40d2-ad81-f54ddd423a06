/**
 * Texture Atlas System for NanoHero Platform
 * Combines multiple textures into single atlas to reduce draw calls
 */

import * as THREE from 'three'

export interface TextureAtlasEntry {
  name: string
  texture: THREE.Texture
  uvOffset: THREE.Vector2
  uvScale: THREE.Vector2
  originalSize: THREE.Vector2
}

export interface AtlasConfiguration {
  size: number
  padding: number
  format: THREE.PixelFormat
  type: THREE.TextureDataType
  generateMipmaps: boolean
}

export class TextureAtlasManager {
  private atlases: Map<string, THREE.Texture> = new Map()
  private atlasEntries: Map<string, Map<string, TextureAtlasEntry>> = new Map()
  private canvas: HTMLCanvasElement
  private context: CanvasRenderingContext2D

  constructor() {
    this.canvas = document.createElement('canvas')
    this.context = this.canvas.getContext('2d')!
  }

  /**
   * Create a texture atlas from multiple textures
   */
  public createAtlas(
    name: string,
    textures: { [key: string]: THREE.Texture },
    config: Partial<AtlasConfiguration> = {}
  ): THREE.Texture {
    const defaultConfig: AtlasConfiguration = {
      size: 1024,
      padding: 2,
      format: THREE.RGBAFormat,
      type: THREE.UnsignedByteType,
      generateMipmaps: true
    }

    const finalConfig = { ...defaultConfig, ...config }
    
    // Set canvas size
    this.canvas.width = finalConfig.size
    this.canvas.height = finalConfig.size
    
    // Clear canvas
    this.context.clearRect(0, 0, finalConfig.size, finalConfig.size)
    
    // Pack textures using simple bin packing
    const packedTextures = this.packTextures(textures, finalConfig)
    
    // Draw textures to canvas
    const entries = new Map<string, TextureAtlasEntry>()
    
    Object.entries(packedTextures).forEach(([key, packed]) => {
      const { texture, x, y, width, height } = packed
      
      // Convert texture to image data
      const imageData = this.textureToImageData(texture)
      if (imageData) {
        this.context.putImageData(imageData, x, y)
      }
      
      // Store atlas entry
      entries.set(key, {
        name: key,
        texture,
        uvOffset: new THREE.Vector2(x / finalConfig.size, y / finalConfig.size),
        uvScale: new THREE.Vector2(width / finalConfig.size, height / finalConfig.size),
        originalSize: new THREE.Vector2(width, height)
      })
    })
    
    // Create Three.js texture from canvas
    const atlasTexture = new THREE.CanvasTexture(this.canvas)
    atlasTexture.format = finalConfig.format
    atlasTexture.type = finalConfig.type
    atlasTexture.generateMipmaps = finalConfig.generateMipmaps
    atlasTexture.wrapS = THREE.ClampToEdgeWrapping
    atlasTexture.wrapT = THREE.ClampToEdgeWrapping
    atlasTexture.needsUpdate = true
    
    // Store atlas and entries
    this.atlases.set(name, atlasTexture)
    this.atlasEntries.set(name, entries)
    
    return atlasTexture
  }

  /**
   * Get UV coordinates for a texture in an atlas
   */
  public getUVMapping(atlasName: string, textureName: string): TextureAtlasEntry | null {
    const entries = this.atlasEntries.get(atlasName)
    return entries?.get(textureName) || null
  }

  /**
   * Update geometry UVs to use atlas coordinates
   */
  public updateGeometryUVs(
    geometry: THREE.BufferGeometry,
    atlasName: string,
    textureName: string
  ): THREE.BufferGeometry {
    const entry = this.getUVMapping(atlasName, textureName)
    if (!entry) return geometry

    const uvAttribute = geometry.getAttribute('uv') as THREE.BufferAttribute
    if (!uvAttribute) return geometry

    const uvArray = uvAttribute.array as Float32Array
    const newUvArray = new Float32Array(uvArray.length)

    // Transform UVs to atlas coordinates
    for (let i = 0; i < uvArray.length; i += 2) {
      const u = uvArray[i]
      const v = uvArray[i + 1]
      
      newUvArray[i] = entry.uvOffset.x + u * entry.uvScale.x
      newUvArray[i + 1] = entry.uvOffset.y + v * entry.uvScale.y
    }

    geometry.setAttribute('uv', new THREE.BufferAttribute(newUvArray, 2))
    return geometry
  }

  /**
   * Create material that uses atlas texture
   */
  public createAtlasMaterial(
    atlasName: string,
    materialType: 'basic' | 'standard' | 'physical' = 'standard'
  ): THREE.Material | null {
    const atlas = this.atlases.get(atlasName)
    if (!atlas) return null

    switch (materialType) {
      case 'basic':
        return new THREE.MeshBasicMaterial({ map: atlas })
      case 'standard':
        return new THREE.MeshStandardMaterial({ map: atlas })
      case 'physical':
        return new THREE.MeshPhysicalMaterial({ map: atlas })
      default:
        return new THREE.MeshStandardMaterial({ map: atlas })
    }
  }

  /**
   * Simple bin packing algorithm
   */
  private packTextures(
    textures: { [key: string]: THREE.Texture },
    config: AtlasConfiguration
  ): { [key: string]: { texture: THREE.Texture, x: number, y: number, width: number, height: number } } {
    const packed: { [key: string]: { texture: THREE.Texture, x: number, y: number, width: number, height: number } } = {}
    
    // Sort textures by size (largest first)
    const sortedTextures = Object.entries(textures).sort(([, a], [, b]) => {
      const aSize = (a.image?.width || 256) * (a.image?.height || 256)
      const bSize = (b.image?.width || 256) * (b.image?.height || 256)
      return bSize - aSize
    })

    let currentX = config.padding
    let currentY = config.padding
    let rowHeight = 0

    sortedTextures.forEach(([key, texture]) => {
      const width = texture.image?.width || 256
      const height = texture.image?.height || 256

      // Check if texture fits in current row
      if (currentX + width + config.padding > config.size) {
        // Move to next row
        currentX = config.padding
        currentY += rowHeight + config.padding
        rowHeight = 0
      }

      // Check if texture fits in atlas
      if (currentY + height + config.padding <= config.size) {
        packed[key] = {
          texture,
          x: currentX,
          y: currentY,
          width,
          height
        }

        currentX += width + config.padding
        rowHeight = Math.max(rowHeight, height)
      } else {
        console.warn(`Texture ${key} doesn't fit in atlas`)
      }
    })

    return packed
  }

  /**
   * Convert Three.js texture to ImageData
   */
  private textureToImageData(texture: THREE.Texture): ImageData | null {
    if (!texture.image) return null

    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')!
    
    canvas.width = texture.image.width
    canvas.height = texture.image.height
    
    context.drawImage(texture.image, 0, 0)
    
    return context.getImageData(0, 0, canvas.width, canvas.height)
  }

  /**
   * Get atlas texture
   */
  public getAtlas(name: string): THREE.Texture | null {
    return this.atlases.get(name) || null
  }

  /**
   * Dispose of atlas resources
   */
  public dispose(): void {
    this.atlases.forEach(atlas => atlas.dispose())
    this.atlases.clear()
    this.atlasEntries.clear()
  }
}

/**
 * Block texture atlas for NanoWorld
 */
export class BlockTextureAtlas {
  private static instance: BlockTextureAtlas
  private atlasManager: TextureAtlasManager
  private blockAtlas: THREE.Texture | null = null

  private constructor() {
    this.atlasManager = new TextureAtlasManager()
  }

  public static getInstance(): BlockTextureAtlas {
    if (!BlockTextureAtlas.instance) {
      BlockTextureAtlas.instance = new BlockTextureAtlas()
    }
    return BlockTextureAtlas.instance
  }

  /**
   * Create block texture atlas
   */
  public createBlockAtlas(): THREE.Texture {
    if (this.blockAtlas) return this.blockAtlas

    // Create simple colored textures for different block types
    const blockTextures = {
      grass: this.createColorTexture('#228B22'),
      dirt: this.createColorTexture('#8B4513'),
      stone: this.createColorTexture('#696969'),
      neural: this.createColorTexture('#22D3EE'),
      quantum: this.createColorTexture('#8B5CF6'),
      crystal: this.createColorTexture('#E6E6FA')
    }

    this.blockAtlas = this.atlasManager.createAtlas('blocks', blockTextures, {
      size: 512,
      padding: 1
    })

    return this.blockAtlas
  }

  /**
   * Get UV mapping for block type
   */
  public getBlockUVMapping(blockType: string): TextureAtlasEntry | null {
    return this.atlasManager.getUVMapping('blocks', blockType)
  }

  /**
   * Create atlas material for blocks
   */
  public createBlockMaterial(): THREE.Material {
    if (!this.blockAtlas) {
      this.createBlockAtlas()
    }
    
    return new THREE.MeshBasicMaterial({
      map: this.blockAtlas,
      vertexColors: false // Use texture instead of vertex colors
    })
  }

  /**
   * Create simple colored texture
   */
  private createColorTexture(color: string): THREE.Texture {
    const canvas = document.createElement('canvas')
    canvas.width = 64
    canvas.height = 64
    
    const context = canvas.getContext('2d')!
    context.fillStyle = color
    context.fillRect(0, 0, 64, 64)
    
    return new THREE.CanvasTexture(canvas)
  }

  public dispose(): void {
    this.atlasManager.dispose()
    this.blockAtlas?.dispose()
    this.blockAtlas = null
  }
}

// Global texture atlas instance
export const blockTextureAtlas = BlockTextureAtlas.getInstance()
