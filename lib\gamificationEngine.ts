// Advanced Gamification Engine for QuantumLink Educational Features
import { CommunicationQuest } from '@/types/chat'
import { LearningMetrics } from './learningAnalytics'

export interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary'
  category: 'communication' | 'kindness' | 'learning' | 'leadership' | 'special'
  requirements: AchievementRequirement[]
  rewards: {
    ce: number
    cubits: number
    title?: string
    avatarUpgrade?: string
    specialAbility?: string
  }
  unlockedAt?: Date
  progress?: number
  maxProgress?: number
}

export interface AchievementRequirement {
  type: 'message_count' | 'kindness_streak' | 'help_count' | 'quest_complete' | 'skill_level' | 'special_event'
  value: number
  timeframe?: 'day' | 'week' | 'month' | 'all_time'
  condition?: string
}

export interface StreakTracker {
  type: 'kindness' | 'daily_login' | 'help_others' | 'quest_completion' | 'reflection'
  currentStreak: number
  longestStreak: number
  lastActivity: Date
  streakBonuses: StreakBonus[]
}

export interface StreakBonus {
  streakLength: number
  multiplier: number
  description: string
  specialReward?: {
    ce: number
    cubits: number
    badge?: string
  }
}

export interface SpecialEvent {
  id: string
  name: string
  description: string
  type: 'community' | 'seasonal' | 'milestone' | 'challenge'
  startDate: Date
  endDate: Date
  isActive: boolean
  participants: string[]
  goals: EventGoal[]
  rewards: EventReward[]
  progress: number
  maxProgress: number
}

export interface EventGoal {
  id: string
  description: string
  target: number
  current: number
  type: 'collective' | 'individual'
}

export interface EventReward {
  tier: 'participation' | 'bronze' | 'silver' | 'gold' | 'platinum'
  requirements: number
  rewards: {
    ce: number
    cubits: number
    badge?: string
    title?: string
    avatarUpgrade?: string
    specialItem?: string
  }
}

// Define comprehensive achievement system
export const ACHIEVEMENTS: Achievement[] = [
  // Communication Achievements
  {
    id: 'first_message',
    name: 'First Words',
    description: 'Send your first message in QuantumLink',
    icon: '💬',
    rarity: 'common',
    category: 'communication',
    requirements: [{ type: 'message_count', value: 1 }],
    rewards: { ce: 10, cubits: 5, title: 'Newcomer' }
  },
  {
    id: 'chatterbox',
    name: 'Chatterbox',
    description: 'Send 100 messages',
    icon: '🗣️',
    rarity: 'uncommon',
    category: 'communication',
    requirements: [{ type: 'message_count', value: 100 }],
    rewards: { ce: 50, cubits: 15, avatarUpgrade: 'voice_amplifier' }
  },
  {
    id: 'master_communicator',
    name: 'Master Communicator',
    description: 'Send 1000 high-quality messages',
    icon: '🎭',
    rarity: 'epic',
    category: 'communication',
    requirements: [{ type: 'message_count', value: 1000, condition: 'high_quality' }],
    rewards: { ce: 200, cubits: 50, title: 'Communication Master', specialAbility: 'message_boost' }
  },

  // Kindness Achievements
  {
    id: 'kind_soul',
    name: 'Kind Soul',
    description: 'Maintain a 7-day kindness streak',
    icon: '💝',
    rarity: 'uncommon',
    category: 'kindness',
    requirements: [{ type: 'kindness_streak', value: 7 }],
    rewards: { ce: 40, cubits: 12, avatarUpgrade: 'kindness_aura' }
  },
  {
    id: 'beacon_of_light',
    name: 'Beacon of Light',
    description: 'Maintain a 30-day kindness streak',
    icon: '🌟',
    rarity: 'legendary',
    category: 'kindness',
    requirements: [{ type: 'kindness_streak', value: 30 }],
    rewards: { ce: 300, cubits: 75, title: 'Beacon of Light', specialAbility: 'inspire_others' }
  },

  // Learning Achievements
  {
    id: 'helpful_hand',
    name: 'Helpful Hand',
    description: 'Help 10 other users',
    icon: '🤝',
    rarity: 'common',
    category: 'learning',
    requirements: [{ type: 'help_count', value: 10 }],
    rewards: { ce: 30, cubits: 8 }
  },
  {
    id: 'mentor',
    name: 'Mentor',
    description: 'Help 100 other users',
    icon: '🎓',
    rarity: 'rare',
    category: 'learning',
    requirements: [{ type: 'help_count', value: 100 }],
    rewards: { ce: 150, cubits: 35, title: 'Mentor', avatarUpgrade: 'wisdom_crown' }
  },

  // Leadership Achievements
  {
    id: 'quest_master',
    name: 'Quest Master',
    description: 'Complete 50 communication quests',
    icon: '🏆',
    rarity: 'rare',
    category: 'leadership',
    requirements: [{ type: 'quest_complete', value: 50 }],
    rewards: { ce: 120, cubits: 30, title: 'Quest Master' }
  },

  // Special Achievements
  {
    id: 'harmony_builder',
    name: 'Harmony Builder',
    description: 'Contribute 1000 points to timeline harmony',
    icon: '🌈',
    rarity: 'epic',
    category: 'special',
    requirements: [{ type: 'special_event', value: 1000, condition: 'harmony_contribution' }],
    rewards: { ce: 250, cubits: 60, title: 'Harmony Builder', specialAbility: 'harmony_boost' }
  }
]

// Streak bonus configurations
export const STREAK_BONUSES: Record<string, StreakBonus[]> = {
  kindness: [
    { streakLength: 3, multiplier: 1.2, description: '+20% kindness rewards' },
    { streakLength: 7, multiplier: 1.5, description: '+50% kindness rewards', specialReward: { ce: 25, cubits: 5 } },
    { streakLength: 14, multiplier: 2.0, description: '+100% kindness rewards', specialReward: { ce: 50, cubits: 10, badge: 'kindness_champion' } },
    { streakLength: 30, multiplier: 3.0, description: '+200% kindness rewards', specialReward: { ce: 100, cubits: 25, badge: 'kindness_legend' } }
  ],
  daily_login: [
    { streakLength: 7, multiplier: 1.1, description: '+10% daily rewards' },
    { streakLength: 30, multiplier: 1.3, description: '+30% daily rewards', specialReward: { ce: 40, cubits: 8 } }
  ],
  help_others: [
    { streakLength: 5, multiplier: 1.3, description: '+30% help rewards' },
    { streakLength: 10, multiplier: 1.6, description: '+60% help rewards', specialReward: { ce: 35, cubits: 7 } }
  ]
}

// Special events system
export function createSpecialEvent(
  type: 'community_kindness' | 'knowledge_sharing' | 'harmony_surge' | 'mentor_week',
  duration: number = 7 // days
): SpecialEvent {
  const now = new Date()
  const endDate = new Date(now.getTime() + duration * 24 * 60 * 60 * 1000)

  const eventConfigs = {
    community_kindness: {
      name: 'Community Kindness Week',
      description: 'Spread kindness throughout the timeline! Extra rewards for kind messages.',
      goals: [
        { id: 'kind_messages', description: 'Send 1000 kind messages as a community', target: 1000, current: 0, type: 'collective' as const },
        { id: 'individual_kindness', description: 'Send 10 kind messages', target: 10, current: 0, type: 'individual' as const }
      ],
      rewards: [
        { tier: 'participation' as const, requirements: 1, rewards: { ce: 20, cubits: 5 } },
        { tier: 'bronze' as const, requirements: 5, rewards: { ce: 50, cubits: 12, badge: 'kindness_participant' } },
        { tier: 'silver' as const, requirements: 10, rewards: { ce: 80, cubits: 20, badge: 'kindness_champion' } },
        { tier: 'gold' as const, requirements: 20, rewards: { ce: 150, cubits: 35, title: 'Kindness Ambassador' } }
      ]
    },
    knowledge_sharing: {
      name: 'Knowledge Sharing Festival',
      description: 'Share your knowledge and help others learn! Bonus XP for teaching.',
      goals: [
        { id: 'explanations_given', description: 'Provide 500 helpful explanations', target: 500, current: 0, type: 'collective' as const },
        { id: 'individual_teaching', description: 'Help 5 people learn something new', target: 5, current: 0, type: 'individual' as const }
      ],
      rewards: [
        { tier: 'participation' as const, requirements: 1, rewards: { ce: 25, cubits: 6 } },
        { tier: 'bronze' as const, requirements: 3, rewards: { ce: 60, cubits: 15, badge: 'knowledge_sharer' } },
        { tier: 'silver' as const, requirements: 5, rewards: { ce: 100, cubits: 25, avatarUpgrade: 'teacher_glow' } },
        { tier: 'gold' as const, requirements: 10, rewards: { ce: 200, cubits: 50, title: 'Master Teacher' } }
      ]
    },
    harmony_surge: {
      name: 'Harmony Surge',
      description: 'Unite the community through collaborative activities! Work together for greater rewards.',
      goals: [
        { id: 'collaborative_projects', description: 'Complete 100 collaborative projects', target: 100, current: 0, type: 'collective' as const },
        { id: 'individual_collaboration', description: 'Participate in 3 collaborative projects', target: 3, current: 0, type: 'individual' as const }
      ],
      rewards: [
        { tier: 'participation' as const, requirements: 1, rewards: { ce: 30, cubits: 8 } },
        { tier: 'bronze' as const, requirements: 2, rewards: { ce: 70, cubits: 18, badge: 'harmony_builder' } },
        { tier: 'silver' as const, requirements: 3, rewards: { ce: 120, cubits: 30, avatarUpgrade: 'harmony_aura' } },
        { tier: 'gold' as const, requirements: 5, rewards: { ce: 250, cubits: 60, title: 'Harmony Master' } }
      ]
    },
    mentor_week: {
      name: 'Mentor Week',
      description: 'Become a mentor and guide others on their learning journey! Special rewards for mentoring.',
      goals: [
        { id: 'mentoring_sessions', description: 'Complete 200 mentoring sessions', target: 200, current: 0, type: 'collective' as const },
        { id: 'individual_mentoring', description: 'Mentor 2 new learners', target: 2, current: 0, type: 'individual' as const }
      ],
      rewards: [
        { tier: 'participation' as const, requirements: 1, rewards: { ce: 40, cubits: 10 } },
        { tier: 'bronze' as const, requirements: 1, rewards: { ce: 80, cubits: 20, badge: 'mentor_guide' } },
        { tier: 'silver' as const, requirements: 2, rewards: { ce: 150, cubits: 35, avatarUpgrade: 'mentor_crown' } },
        { tier: 'gold' as const, requirements: 3, rewards: { ce: 300, cubits: 75, title: 'Grand Mentor' } }
      ]
    }
  }

  const config = eventConfigs[type]
  
  return {
    id: `${type}_${Date.now()}`,
    name: config.name,
    description: config.description,
    type: 'community',
    startDate: now,
    endDate,
    isActive: true,
    participants: [],
    goals: config.goals,
    rewards: config.rewards,
    progress: 0,
    maxProgress: config.goals.find((g: any) => g.type === 'collective')?.target || 100
  }
}

// Achievement checking system
export function checkAchievements(
  userStats: {
    messageCount: number
    helpCount: number
    questsCompleted: number
    kindnessStreak: number
    harmonyContribution: number
  },
  currentAchievements: string[]
): Achievement[] {
  const newAchievements: Achievement[] = []

  for (const achievement of ACHIEVEMENTS) {
    if (currentAchievements.includes(achievement.id)) continue

    let requirementsMet = true
    for (const req of achievement.requirements) {
      switch (req.type) {
        case 'message_count':
          if (userStats.messageCount < req.value) requirementsMet = false
          break
        case 'help_count':
          if (userStats.helpCount < req.value) requirementsMet = false
          break
        case 'quest_complete':
          if (userStats.questsCompleted < req.value) requirementsMet = false
          break
        case 'kindness_streak':
          if (userStats.kindnessStreak < req.value) requirementsMet = false
          break
        case 'special_event':
          if (req.condition === 'harmony_contribution' && userStats.harmonyContribution < req.value) {
            requirementsMet = false
          }
          break
      }
    }

    if (requirementsMet) {
      newAchievements.push({
        ...achievement,
        unlockedAt: new Date()
      })
    }
  }

  return newAchievements
}

// Streak management
export function updateStreak(
  streakTracker: StreakTracker,
  activityPerformed: boolean,
  currentDate: Date = new Date()
): StreakTracker {
  const lastActivityDate = new Date(streakTracker.lastActivity)
  const daysDifference = Math.floor((currentDate.getTime() - lastActivityDate.getTime()) / (1000 * 60 * 60 * 24))

  if (activityPerformed) {
    if (daysDifference === 1) {
      // Continue streak
      streakTracker.currentStreak += 1
    } else if (daysDifference === 0) {
      // Same day, no change to streak
    } else {
      // Streak broken, reset
      streakTracker.currentStreak = 1
    }
    
    streakTracker.lastActivity = currentDate
    streakTracker.longestStreak = Math.max(streakTracker.longestStreak, streakTracker.currentStreak)
  } else if (daysDifference > 1) {
    // Streak broken due to inactivity
    streakTracker.currentStreak = 0
  }

  return streakTracker
}

// Calculate multipliers from active streaks
export function calculateStreakMultipliers(streaks: Record<string, StreakTracker>): Record<string, number> {
  const multipliers: Record<string, number> = {}

  Object.entries(streaks).forEach(([streakType, tracker]) => {
    const bonuses = STREAK_BONUSES[streakType] || []
    let multiplier = 1.0

    for (const bonus of bonuses) {
      if (tracker.currentStreak >= bonus.streakLength) {
        multiplier = bonus.multiplier
      }
    }

    multipliers[streakType] = multiplier
  })

  return multipliers
}

// Dynamic quest generation based on user behavior
export function generatePersonalizedQuests(
  userMetrics: LearningMetrics,
  userLevel: number,
  _recentActivity: string[]
): CommunicationQuest[] {
  const quests: CommunicationQuest[] = []

  // Adaptive difficulty based on user performance
  const difficultyMultiplier = Math.max(0.5, Math.min(2.0, userLevel / 5))

  // Generate quests based on weak areas
  if (userMetrics.empathyScore < 0.6) {
    quests.push({
      id: `empathy_quest_${Date.now()}`,
      title: 'Empathy Builder',
      description: `Show empathy to ${Math.ceil(3 * difficultyMultiplier)} different users`,
      type: 'show_kindness',
      target: Math.ceil(3 * difficultyMultiplier),
      progress: 0,
      reward: {
        ce: Math.floor(30 * difficultyMultiplier),
        cubits: Math.floor(8 * difficultyMultiplier),
        badge: 'empathy_builder'
      },
      isDaily: true
    })
  }

  if (userMetrics.collaborationRating < 0.5) {
    quests.push({
      id: `collaboration_quest_${Date.now()}`,
      title: 'Team Player',
      description: `Successfully collaborate on ${Math.ceil(2 * difficultyMultiplier)} group activities`,
      type: 'help_someone',
      target: Math.ceil(2 * difficultyMultiplier),
      progress: 0,
      reward: {
        ce: Math.floor(45 * difficultyMultiplier),
        cubits: Math.floor(12 * difficultyMultiplier),
        badge: 'team_player'
      },
      isDaily: false
    })
  }

  return quests.slice(0, 3) // Return up to 3 personalized quests
}
