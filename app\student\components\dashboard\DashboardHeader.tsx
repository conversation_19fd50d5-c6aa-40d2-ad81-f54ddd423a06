'use client'

import { motion } from 'framer-motion'
import { Dna } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { DashboardHeaderProps } from './types'

export default function DashboardHeader({ 
  evolutionTheme, 
  playerData,  
}: DashboardHeaderProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="relative"
    >
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <motion.div
            className="relative p-4 rounded-xl bg-gradient-to-br from-space-dark/80 to-space-dark/60 border-2"
            style={{
              borderColor: evolutionTheme.primary + '40',
              boxShadow: `0 0 20px ${evolutionTheme.glow}`
            }}
            animate={{
              boxShadow: [
                `0 0 20px ${evolutionTheme.glow}`,
                `0 0 30px ${evolutionTheme.glow}`,
                `0 0 20px ${evolutionTheme.glow}`
              ]
            }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <Dna className="w-8 h-8" style={{ color: evolutionTheme.primary }} />

          </motion.div>
          <div>
            <h1 className="text-3xl font-bold text-white font-orbitron">NanoCore</h1>
            <p className="text-white/60">NanoArchitect Dashboard</p>
          </div>
        </div>

        <div className="text-right space-y-2">
          <div className="text-sm text-white/60">Evolution Stage</div>
          <div className="text-2xl font-bold" style={{ color: evolutionTheme.primary }}>
            {playerData?.level || 1}/20
          </div>
          <Badge className="bg-gradient-to-r from-cyan-500/20 to-purple-500/20 text-cyan-400 border-cyan-500/30">
            NanoArchitect
          </Badge>
        </div>
      </div>
    </motion.div>
  )
}
