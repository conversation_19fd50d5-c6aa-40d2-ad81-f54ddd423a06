"use client"

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'
import {
  ChatChannel,
  ChatMessage,
  ChatUser,
  ChatState,
  PresetPhrase,
  ReflectionPrompt,
  ModerationFlag
} from '@/types/chat'
import { moderateMessage, calculateHarmonyImpact } from '@/lib/moderation'
import { PRESET_PHRASES, getPhrasesForUser } from '@/data/presetPhrases'
import { checkQuestProgress, isQuestComplete } from '@/data/communicationQuests'

interface ChatContextType extends ChatState {
  // Channel actions
  sendMessage: (content: string, channelId?: string, dmId?: string, isPresetPhrase?: boolean, presetPhraseId?: string) => Promise<void>
  sendPresetPhrase: (phrase: PresetPhrase, channelId?: string, dmId?: string) => Promise<void>
  editMessage: (messageId: string, newContent: string) => Promise<void>
  deleteMessage: (messageId: string) => Promise<void>
  reactToMessage: (messageId: string, emoji: string) => Promise<void>
  flagMessage: (messageId: string, reason: string, description?: string) => Promise<void>

  // Channel management
  joinChannel: (channelId: string) => Promise<void>
  leaveChannel: (channelId: string) => Promise<void>
  createChannel: (name: string, description?: string, isPrivate?: boolean) => Promise<void>

  // Direct messages
  startDirectMessage: (userId: string) => Promise<string>

  // UI state
  setActiveChannel: (channelId: string | null) => void
  setActiveDM: (dmId: string | null) => void
  markAsRead: (channelId?: string, dmId?: string) => Promise<void>

  // Typing indicators
  startTyping: (channelId?: string, dmId?: string) => void
  stopTyping: () => void

  // Notifications
  markNotificationAsRead: (notificationId: string) => Promise<void>
  clearAllNotifications: () => Promise<void>

  // QuantumLink features
  getAvailablePresetPhrases: () => PresetPhrase[]
  submitReflection: (prompt: ReflectionPrompt, responseIndex: number, response: string) => Promise<void>
  claimQuestReward: (questId: string) => Promise<void>
  updateUserExpression: (expression: 'happy' | 'focused' | 'curious' | 'helpful' | 'neutral') => void
  contributeToHarmony: (amount: number) => Promise<void>

  // Moderation
  reportUser: (userId: string, reason: string) => Promise<void>
  blockUser: (userId: string) => Promise<void>
  unblockUser: (userId: string) => Promise<void>
}

const ChatContext = createContext<ChatContextType | undefined>(undefined)

export function useChat() {
  const context = useContext(ChatContext)
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider')
  }
  return context
}

export function ChatProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth()

  // Generate mock messages for demo
  const generateMockMessages = (channelId: string, channelType: string): ChatMessage[] => {
    const now = new Date()
    const messages: ChatMessage[] = []

    const mockUsers = [
      { id: 'user1', name: 'QuantumExplorer', avatar: '🌟' },
      { id: 'user2', name: 'NanoMentor', avatar: '🧠' },
      { id: 'user3', name: 'CodeWizard', avatar: '⚡' },
      { id: 'user4', name: 'ScienceSeeker', avatar: '🔬' },
      { id: 'user5', name: 'KindnessBot', avatar: '💝' }
    ]

    const messageTemplates = {
      world: [
        "Welcome to the QuantumLink! Let's learn together! 🌟",
        "Has anyone tried the new DNA evolution challenge?",
        "I just completed my first quantum quest! So exciting!",
        "Remember to be kind and help each other grow 💝",
        "The harmony meter is looking great today!"
      ],
      team: [
        "Great job on that collaboration quest, team!",
        "Who wants to work on the timeline project together?",
        "I found a really cool pattern in the quantum data",
        "Let's help each other with today's challenges",
        "Our team harmony is at an all-time high! 🎉"
      ],
      node: [
        "This learning node has amazing content!",
        "Can someone explain the quantum entanglement concept?",
        "I love how this connects to what we learned yesterday",
        "The 3D visualization here is incredible",
        "Anyone want to explore the next section together?"
      ]
    }

    const templates = messageTemplates[channelType as keyof typeof messageTemplates] || messageTemplates.world

    // Generate 5-8 recent messages
    for (let i = 0; i < Math.floor(Math.random() * 4) + 5; i++) {
      const user = mockUsers[Math.floor(Math.random() * mockUsers.length)]
      const template = templates[Math.floor(Math.random() * templates.length)]

      messages.push({
        id: `msg-${channelId}-${i}`,
        content: template,
        senderId: user.id,
        senderName: user.name,
        senderAvatar: user.avatar,
        timestamp: new Date(now.getTime() - (i * 300000)), // 5 minutes apart
        type: 'text',
        reactions: [],
        moderationStatus: 'approved',
        emotionTags: ['positive', 'helpful'],
        kindnessScore: 0.8,
        helpfulnessScore: 0.7,
        quantumParticleEffect: 'sparkle',
        ceReward: 5,
        harmonyContribution: 10
      })
    }

    return messages.reverse() // Oldest first
  }

  const [chatState, setChatState] = useState<ChatState>({
    channels: [],
    directMessages: [],
    messages: {},
    activeChannelId: null,
    activeDMId: null,
    onlineUsers: [],
    typingIndicators: [],
    notifications: [],
    isConnected: false,
    isLoading: true,
    // QuantumLink additions
    presetPhrases: PRESET_PHRASES,
    activeQuests: [],
    pendingReflections: [],
    timelineHarmony: null,
    moderationFlags: [],
    userCubits: 150, // Updated starting amount
    userCE: 1250,
    communicationLevel: 5,
    dailyInteractionCount: 0,
    kindnessStreak: 0,
    parentalMode: false
  })

  // Create default QuantumLink channels
  const createDefaultChannels = (): ChatChannel[] => {
    const now = new Date()

    return [
      {
        id: 'world-chat',
        name: 'World Chat',
        description: 'Global communication hub for all timeline participants',
        type: 'world',
        channelType: 'world',
        participants: ['all'], // Special identifier for all users
        createdBy: 'system',
        createdAt: now,
        lastActivity: now,
        unreadCount: 0,
        harmonyMeter: 75,
        activeQuests: [],
        moderationLevel: 'moderate',
        parentalOversight: true,
        settings: {
          allowFiles: false,
          allowImages: true,
          maxMessageLength: 500,
          slowMode: 3,
          requiresApproval: false,
          aiModerationEnabled: true,
          emotionTaggingEnabled: true,
          reflectionPromptsEnabled: true
        }
      },
      {
        id: 'team-chat',
        name: 'Team Chat',
        description: 'Collaborate with your learning team',
        type: 'team',
        channelType: 'team',
        participants: [user?.id || ''],
        createdBy: 'system',
        createdAt: now,
        lastActivity: now,
        unreadCount: 0,
        teamId: `team-${Math.floor(Date.now() / 1000000)}`, // Simple team assignment
        maxParticipants: 8,
        harmonyMeter: 85,
        activeQuests: ['team_collaboration', 'knowledge_sharing'],
        moderationLevel: 'relaxed',
        parentalOversight: true,
        settings: {
          allowFiles: true,
          allowImages: true,
          maxMessageLength: 1000,
          requiresApproval: false,
          aiModerationEnabled: true,
          emotionTaggingEnabled: true,
          reflectionPromptsEnabled: false
        }
      },
      {
        id: 'node-chat',
        name: 'Node Chat',
        description: 'Local chat for your current learning node',
        type: 'node',
        channelType: 'node',
        participants: [user?.id || ''],
        createdBy: 'system',
        createdAt: now,
        lastActivity: now,
        unreadCount: 0,
        nodeId: 'current-node',
        maxParticipants: 15,
        harmonyMeter: 90,
        activeQuests: ['node_exploration', 'peer_support'],
        moderationLevel: 'moderate',
        parentalOversight: true,
        settings: {
          allowFiles: false,
          allowImages: true,
          maxMessageLength: 300,
          requiresApproval: false,
          aiModerationEnabled: true,
          emotionTaggingEnabled: true,
          reflectionPromptsEnabled: true,
          cubitsRequired: 5
        }
      }
    ]
  }

  // Initialize chat data
  useEffect(() => {
    if (!user) return

    const initializeChat = async () => {
      try {
        // For demo purposes, use default channels
        // In production, this would load from Supabase
        const defaultChannels = createDefaultChannels()

        // Generate mock messages for each channel
        const messagesData: Record<string, ChatMessage[]> = {}

        defaultChannels.forEach(channel => {
          messagesData[channel.id] = generateMockMessages(channel.id, channel.channelType)
        })

        // Generate mock online users
        const mockOnlineUsers: ChatUser[] = [
          {
            id: 'user1',
            username: 'QuantumExplorer',
            displayName: 'Quantum Explorer',
            avatar_url: '🌟',
            status: 'online',
            lastSeen: new Date(),
            timelineId: 'timeline-1',
            currentNodeId: 'node-learning',
            avatarExpression: 'curious',
            quantumHarmonyScore: 85,
            communicationLevel: 4,
            badges: ['first_message', 'kindness_champion'],
            parentalControlsEnabled: true,
            canUseDMs: false
          },
          {
            id: 'user2',
            username: 'NanoMentor',
            displayName: 'Nano Mentor',
            avatar_url: '🧠',
            status: 'online',
            lastSeen: new Date(),
            timelineId: 'timeline-1',
            currentNodeId: 'node-advanced',
            avatarExpression: 'helpful',
            quantumHarmonyScore: 95,
            communicationLevel: 8,
            badges: ['mentor', 'wisdom_keeper', 'harmony_master'],
            parentalControlsEnabled: false,
            canUseDMs: true
          },
          {
            id: 'user3',
            username: 'CodeWizard',
            displayName: 'Code Wizard',
            avatar_url: '⚡',
            status: 'away',
            lastSeen: new Date(Date.now() - 600000),
            timelineId: 'timeline-2',
            currentNodeId: 'node-coding',
            avatarExpression: 'focused',
            quantumHarmonyScore: 78,
            communicationLevel: 6,
            badges: ['code_master', 'problem_solver'],
            parentalControlsEnabled: true,
            canUseDMs: false
          },
          {
            id: 'user4',
            username: 'ScienceSeeker',
            displayName: 'Science Seeker',
            avatar_url: '🔬',
            status: 'online',
            lastSeen: new Date(),
            timelineId: 'timeline-1',
            currentNodeId: 'node-science',
            avatarExpression: 'curious',
            quantumHarmonyScore: 82,
            communicationLevel: 5,
            badges: ['explorer', 'question_master'],
            parentalControlsEnabled: true,
            canUseDMs: false
          }
        ]

        setChatState(prev => ({
          ...prev,
          channels: defaultChannels,
          directMessages: [],
          messages: messagesData,
          onlineUsers: mockOnlineUsers,
          isConnected: true,
          isLoading: false,
          activeChannelId: 'world-chat' // Set default active channel
        }))

      } catch (error) {
        console.error('Error initializing chat:', error)
        setChatState(prev => ({ ...prev, isLoading: false }))
      }
    }

    initializeChat()
  }, [user])

  // Real-time subscriptions
  useEffect(() => {
    if (!user) return

    // Subscribe to new messages
    const messageSubscription = supabase
      .channel('chat_messages')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'chat_messages'
      }, (payload) => {
        const newMessage = payload.new as ChatMessage
        setChatState(prev => ({
          ...prev,
          messages: {
            ...prev.messages,
            [newMessage.channelId || newMessage.dmId || '']: [
              ...(prev.messages[newMessage.channelId || newMessage.dmId || ''] || []),
              newMessage
            ]
          }
        }))
      })
      .subscribe()

    // Subscribe to typing indicators
    const typingSubscription = supabase
      .channel('typing_indicators')
      .on('presence', { event: 'sync' }, () => {
        // Handle typing indicators
      })
      .subscribe()

    return () => {
      messageSubscription.unsubscribe()
      typingSubscription.unsubscribe()
    }
  }, [user])

  // Enhanced chat actions with QuantumLink features
  const sendMessage = useCallback(async (
    content: string,
    channelId?: string,
    dmId?: string,
    isPresetPhrase?: boolean,
    presetPhraseId?: string
  ) => {
    if (!user || !content.trim()) return

    try {
      // AI Moderation
      const moderationResult = await moderateMessage(content, 12) // Default age for now

      if (!moderationResult.isApproved) {
        // Handle rejected message
        if (moderationResult.suggestedAction === 'suggest_preset') {
          // Show preset phrase picker
          console.log('Suggest preset phrases')
        }
        throw new Error(moderationResult.explanation || 'Message not approved')
      }

      const messageData = {
        content: content.trim(),
        sender_id: user.id,
        sender_name: user.user_metadata?.full_name || user.email || 'User',
        sender_avatar: user.user_metadata?.avatar_url,
        timestamp: new Date().toISOString(),
        type: isPresetPhrase ? 'preset_phrase' : 'text',
        channel_id: channelId,
        dm_id: dmId,
        // QuantumLink fields
        is_preset_phrase: isPresetPhrase || false,
        preset_phrase_id: presetPhraseId,
        emotion_tags: moderationResult.emotionTags,
        moderation_score: moderationResult.confidence,
        kindness_score: moderationResult.kindnessScore,
        helpfulness_score: moderationResult.helpfulnessScore,
        moderation_status: 'approved'
      }

      const { error } = await supabase
        .from('chat_messages')
        .insert([messageData])

      if (error) throw error

      // Update quest progress
      setChatState(prev => {
        const updatedQuests = prev.activeQuests.map(quest => {
          if (moderationResult.helpfulnessScore > 0.7) {
            return checkQuestProgress(quest, 'help_someone')
          }
          if (moderationResult.kindnessScore > 0.7) {
            return checkQuestProgress(quest, 'give_compliment')
          }
          return quest
        })

        return {
          ...prev,
          activeQuests: updatedQuests,
          dailyInteractionCount: prev.dailyInteractionCount + 1
        }
      })

      // Contribute to timeline harmony
      const harmonyImpact = calculateHarmonyImpact(moderationResult)
      if (harmonyImpact > 0) {
        await contributeToHarmony(harmonyImpact)
      }

    } catch (error) {
      console.error('Error sending message:', error)
      throw error
    }
  }, [user])

  const setActiveChannel = useCallback((channelId: string | null) => {
    setChatState(prev => ({
      ...prev,
      activeChannelId: channelId,
      activeDMId: null
    }))
  }, [])

  const setActiveDM = useCallback((dmId: string | null) => {
    setChatState(prev => ({
      ...prev,
      activeDMId: dmId,
      activeChannelId: null
    }))
  }, [])

  const startTyping = useCallback((channelId?: string, dmId?: string) => {
    // Implement typing indicator logic
  }, [])

  const stopTyping = useCallback(() => {
    // Implement stop typing logic
  }, [])

  // QuantumLink-specific functions
  const sendPresetPhrase = useCallback(async (phrase: PresetPhrase, channelId?: string, dmId?: string) => {
    if (chatState.userCubits < phrase.cubitsRequired) {
      throw new Error('Not enough Cubits')
    }

    // Deduct Cubits
    setChatState(prev => ({
      ...prev,
      userCubits: prev.userCubits - phrase.cubitsRequired
    }))

    await sendMessage(phrase.text, channelId, dmId, true, phrase.id)
  }, [sendMessage, chatState.userCubits])

  const getAvailablePresetPhrases = useCallback(() => {
    return getPhrasesForUser(chatState.communicationLevel, '10-13', chatState.userCubits) // Default age group
  }, [chatState.communicationLevel, chatState.userCubits])

  const submitReflection = useCallback(async (prompt: ReflectionPrompt, responseIndex: number, response: string) => {
    // Calculate rewards based on response
    const impact = {
      ceBonus: 10,
      cubitsBonus: 2,
      harmonyBonus: 5,
      kindnessImpact: 0.1
    }

    setChatState(prev => ({
      ...prev,
      userCE: prev.userCE + impact.ceBonus,
      userCubits: prev.userCubits + impact.cubitsBonus,
      pendingReflections: prev.pendingReflections.filter(p => p.id !== prompt.id)
    }))

    await contributeToHarmony(impact.harmonyBonus)
  }, [])

  const claimQuestReward = useCallback(async (questId: string) => {
    const quest = chatState.activeQuests.find(q => q.id === questId)
    if (!quest || !isQuestComplete(quest)) return

    setChatState(prev => ({
      ...prev,
      userCE: prev.userCE + quest.reward.ce,
      userCubits: prev.userCubits + quest.reward.cubits,
      activeQuests: prev.activeQuests.filter(q => q.id !== questId)
    }))
  }, [chatState.activeQuests])

  const updateUserExpression = useCallback((expression: 'happy' | 'focused' | 'curious' | 'helpful' | 'neutral') => {
    // Update user expression in the context
    setChatState(prev => ({
      ...prev,
      onlineUsers: prev.onlineUsers.map(u =>
        u.id === user?.id ? { ...u, avatarExpression: expression } : u
      )
    }))
  }, [user?.id])

  const contributeToHarmony = useCallback(async (amount: number) => {
    if (!chatState.timelineHarmony) return

    setChatState(prev => ({
      ...prev,
      timelineHarmony: prev.timelineHarmony ? {
        ...prev.timelineHarmony,
        totalContributions: prev.timelineHarmony.totalContributions + amount,
        recentActivity: prev.timelineHarmony.recentActivity + amount,
        lastUpdated: new Date()
      } : null
    }))
  }, [chatState.timelineHarmony])

  const flagMessage = useCallback(async (messageId: string, reason: string, description?: string) => {
    const flag: ModerationFlag = {
      id: `flag_${Date.now()}`,
      messageId,
      reporterId: user?.id || '',
      reason: reason as any,
      description,
      status: 'pending',
      timestamp: new Date()
    }

    setChatState(prev => ({
      ...prev,
      moderationFlags: [...prev.moderationFlags, flag]
    }))

    // In production, this would send to moderation queue
    console.log('Message flagged:', flag)
  }, [user?.id])

  // Placeholder implementations for other methods
  const editMessage = async (messageId: string, newContent: string) => {}
  const deleteMessage = async (messageId: string) => {}
  const reactToMessage = async (messageId: string, emoji: string) => {}
  const joinChannel = async (channelId: string) => {}
  const leaveChannel = async (channelId: string) => {}
  const createChannel = async (name: string, description?: string, isPrivate?: boolean) => {}
  const startDirectMessage = async (userId: string): Promise<string> => { return '' }
  const markAsRead = async (channelId?: string, dmId?: string) => {}
  const markNotificationAsRead = async (notificationId: string) => {}
  const clearAllNotifications = async () => {}
  const reportUser = async (userId: string, reason: string) => {}
  const blockUser = async (userId: string) => {}
  const unblockUser = async (userId: string) => {}

  const contextValue: ChatContextType = {
    ...chatState,
    sendMessage,
    sendPresetPhrase,
    editMessage,
    deleteMessage,
    reactToMessage,
    flagMessage,
    joinChannel,
    leaveChannel,
    createChannel,
    startDirectMessage,
    setActiveChannel,
    setActiveDM,
    markAsRead,
    startTyping,
    stopTyping,
    markNotificationAsRead,
    clearAllNotifications,
    // QuantumLink features
    getAvailablePresetPhrases,
    submitReflection,
    claimQuestReward,
    updateUserExpression,
    contributeToHarmony,
    // Moderation
    reportUser,
    blockUser,
    unblockUser
  }

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  )
}
