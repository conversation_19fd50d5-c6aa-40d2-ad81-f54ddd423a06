"use client"

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { supabase } from '@/lib/supabase'
import { motion } from 'framer-motion'
import { Loader2, CheckCircle, AlertCircle } from 'lucide-react'

// Deterministic particle positions to avoid hydration mismatch
const PARTICLE_POSITIONS = [
  { x: 10, y: 15 }, { x: 25, y: 35 }, { x: 40, y: 55 }, { x: 55, y: 75 },
  { x: 70, y: 25 }, { x: 85, y: 45 }, { x: 15, y: 65 }, { x: 30, y: 85 },
  { x: 45, y: 20 }, { x: 60, y: 40 }, { x: 75, y: 60 }, { x: 90, y: 80 },
  { x: 20, y: 30 }, { x: 35, y: 50 }, { x: 50, y: 70 }, { x: 65, y: 90 },
  { x: 80, y: 10 }, { x: 95, y: 95 }, { x: 5, y: 85 }, { x: 12, y: 42 }
]

export default function AuthCallback() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [message, setMessage] = useState('Processing authentication...')

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Get the session from the URL hash
        const { data, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Auth callback error:', error)
          setStatus('error')
          setMessage('Authentication failed. Please try again.')
          setTimeout(() => {
            router.push('/login?error=auth_failed')
          }, 3000)
          return
        }

        if (data.session) {
          const user = data.session.user
          const accountType = searchParams.get('type') || 'student'
          
          // Update user profile with account type if it's a new user
          const { data: profile, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single()

          if (profile && !profile.account_type) {
            // Update the profile with the selected account type
            await supabase
              .from('profiles')
              .update({ 
                account_type: accountType,
                is_new_user: false 
              })
              .eq('id', user.id)
          }

          setStatus('success')
          setMessage('Authentication successful! Redirecting...')

          // Redirect based on account type
          setTimeout(() => {
            switch (accountType) {
              case 'parent':
                router.push('/parent')
                break
              case 'student':
              default:
                router.push('/student')
                break
            }
          }, 2000)
        } else {
          setStatus('error')
          setMessage('No session found. Please try signing in again.')
          setTimeout(() => {
            router.push('/login?error=unexpected')
          }, 3000)
        }
      } catch (error) {
        console.error('Unexpected error in auth callback:', error)
        setStatus('error')
        setMessage('An unexpected error occurred. Please try again.')
        setTimeout(() => {
          router.push('/login?error=unexpected')
        }, 3000)
      }
    }

    handleAuthCallback()
  }, [router, searchParams])

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="w-8 h-8 animate-spin text-cyan-400" />
      case 'success':
        return <CheckCircle className="w-8 h-8 text-green-400" />
      case 'error':
        return <AlertCircle className="w-8 h-8 text-red-400" />
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case 'loading':
        return 'from-cyan-500 to-blue-500'
      case 'success':
        return 'from-green-500 to-emerald-500'
      case 'error':
        return 'from-red-500 to-pink-500'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 relative overflow-hidden">
      {/* Quantum Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-blue-500/5 rounded-full blur-2xl animate-pulse delay-500" />
      </div>

      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden">
        {PARTICLE_POSITIONS.map((position, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-cyan-400/30 rounded-full"
            initial={{
              x: `${position.x}%`,
              y: `${position.y}%`,
            }}
            animate={{
              y: [`${position.y}%`, `${position.y - 10}%`],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 3 + (i % 3),
              repeat: Infinity,
              delay: i * 0.1,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
          className="text-center"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
            className={`mx-auto w-20 h-20 bg-gradient-to-r ${getStatusColor()} rounded-full flex items-center justify-center mb-6`}
          >
            {getStatusIcon()}
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="text-2xl font-bold text-white font-space-grotesk mb-4"
          >
            {status === 'loading' && 'Authenticating...'}
            {status === 'success' && 'Welcome to NanoHero!'}
            {status === 'error' && 'Authentication Error'}
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="text-gray-400 font-inter max-w-md mx-auto"
          >
            {message}
          </motion.p>

          {status === 'loading' && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6 }}
              className="mt-8"
            >
              <div className="w-64 h-2 bg-gray-800 rounded-full mx-auto overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-cyan-500 to-blue-500"
                  initial={{ width: 0 }}
                  animate={{ width: '100%' }}
                  transition={{ duration: 3, ease: "easeInOut" }}
                />
              </div>
            </motion.div>
          )}
        </motion.div>
      </div>
    </div>
  )
}
