'use client'

import React, { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCorners
} from '@dnd-kit/core'
import { arrayMove } from '@dnd-kit/sortable'
import {
  BookOpen
} from 'lucide-react'

import {
  WeeklyKanbanPlan,
  KanbanDayColumn,
  KanbanLessonCard,
  WeekDay,
  ChildProfile
} from '../types/curriculum'
import { LessonLibrarySidebar } from './LessonLibrarySidebar'
import { KanbanDayColumn as DayColumnComponent } from './KanbanDayColumn'
import { WeekNavigator } from './WeekNavigator'
import { KanbanPlannerControls } from './KanbanPlannerControls'
import { StudentAnalyticsDashboard } from './StudentAnalyticsDashboard'

interface KanbanWeeklyPlannerProps {
  selectedChild: ChildProfile | null
  onChildChange: (child: ChildProfile) => void
}

export function KanbanWeeklyPlanner({ selectedChild, onChildChange: _onChildChange }: KanbanWeeklyPlannerProps) {
  const [currentPlan, setCurrentPlan] = useState<WeeklyKanbanPlan | null>(null)
  const [draggedLesson, setDraggedLesson] = useState<KanbanLessonCard | null>(null)
  const [showSidebar, setShowSidebar] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // DnD Kit sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3,
      },
    })
  )

  // Initialize default week plan
  const initializeWeekPlan = useCallback(() => {
    if (!selectedChild) return null

    const today = new Date()
    const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay() + 1)) // Monday
    const endOfWeek = new Date(startOfWeek)
    endOfWeek.setDate(startOfWeek.getDate() + 6) // Sunday

    const weekDays: WeekDay[] = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

    const columns: KanbanDayColumn[] = weekDays.map((day, index) => ({
      day,
      displayName: dayNames[index],
      lessons: [],
      totalTime: 0,
      maxTime: selectedChild.preferences.dailyTimeLimit || 60,
      isWeekend: day === 'saturday' || day === 'sunday'
    }))

    return {
      id: `plan-${selectedChild.id}-${startOfWeek.toISOString().split('T')[0]}`,
      childId: selectedChild.id,
      parentId: 'current-parent', // TODO: Get from auth context
      weekStartDate: startOfWeek,
      weekEndDate: endOfWeek,
      columns,
      totalWeeklyTime: 0,
      weeklyTimeLimit: selectedChild.preferences.dailyTimeLimit * 7 || 420, // 7 hours default
      isTemplate: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      status: 'draft' as const
    }
  }, [selectedChild])

  // Initialize plan when child changes
  React.useEffect(() => {
    if (selectedChild && !currentPlan) {
      const newPlan = initializeWeekPlan()
      setCurrentPlan(newPlan)
    }
  }, [selectedChild, currentPlan, initializeWeekPlan])

  // Handle lesson drop from sidebar to day column
  const handleLessonDrop = useCallback((lessonCard: KanbanLessonCard, targetDay: WeekDay) => {
    if (!currentPlan) return

    const updatedColumns = currentPlan.columns.map(column => {
      if (column.day === targetDay) {
        const newLessons = [...column.lessons, lessonCard]
        const newTotalTime = newLessons.reduce((sum, lesson) => sum + lesson.duration, 0)
        
        return {
          ...column,
          lessons: newLessons,
          totalTime: newTotalTime
        }
      }
      return column
    })

    const newTotalWeeklyTime = updatedColumns.reduce((sum, col) => sum + col.totalTime, 0)

    setCurrentPlan({
      ...currentPlan,
      columns: updatedColumns,
      totalWeeklyTime: newTotalWeeklyTime,
      updatedAt: new Date()
    })
    
    setHasUnsavedChanges(true)
  }, [currentPlan])

  // Handle lesson removal from day column
  const handleLessonRemove = useCallback((lessonId: string, fromDay: WeekDay) => {
    if (!currentPlan) return

    const updatedColumns = currentPlan.columns.map(column => {
      if (column.day === fromDay) {
        const newLessons = column.lessons.filter(lesson => lesson.id !== lessonId)
        const newTotalTime = newLessons.reduce((sum, lesson) => sum + lesson.duration, 0)
        
        return {
          ...column,
          lessons: newLessons,
          totalTime: newTotalTime
        }
      }
      return column
    })

    const newTotalWeeklyTime = updatedColumns.reduce((sum, col) => sum + col.totalTime, 0)

    setCurrentPlan({
      ...currentPlan,
      columns: updatedColumns,
      totalWeeklyTime: newTotalWeeklyTime,
      updatedAt: new Date()
    })
    
    setHasUnsavedChanges(true)
  }, [currentPlan])

  // DnD Kit handlers
  const handleDragStart = useCallback((event: DragStartEvent) => {
    console.log('Drag start:', event)
    const { active } = event
    const activeData = active.data.current

    if (activeData?.type === 'lesson-card') {
      console.log('Dragging lesson card:', activeData.lesson)
      setDraggedLesson(activeData.lesson)
    } else if (activeData?.type === 'library-lesson') {
      console.log('Dragging library lesson:', activeData.lesson)
      setDraggedLesson(activeData.lesson)
    }
  }, [])

  const handleDragOver = useCallback((event: DragOverEvent) => {
    const { active, over } = event

    if (!over || !currentPlan) return

    const activeData = active.data.current
    const overData = over.data.current

    // Handle dragging lesson cards over different day columns
    if ((activeData?.type === 'lesson-card' || activeData?.type === 'library-lesson') && overData?.type === 'day-column') {
      const overDay = overData.day as WeekDay
      const lesson = activeData.lesson as KanbanLessonCard

      // Handle library lessons (no source day) or lessons from different days
      if (activeData?.type === 'library-lesson' || (activeData.day && activeData.day !== overDay)) {
        const activeDay = activeData.day as WeekDay | undefined

        const updatedColumns = currentPlan.columns.map(column => {
          if (activeDay && column.day === activeDay) {
            // Remove from source column (only if there's a source day)
            const newLessons = column.lessons.filter(l => l.id !== lesson.id)
            const newTotalTime = newLessons.reduce((sum, l) => sum + l.duration, 0)
            return {
              ...column,
              lessons: newLessons,
              totalTime: newTotalTime
            }
          } else if (column.day === overDay) {
            // Add to target column
            const newLessons = [...column.lessons, lesson]
            const newTotalTime = newLessons.reduce((sum, l) => sum + l.duration, 0)
            return {
              ...column,
              lessons: newLessons,
              totalTime: newTotalTime
            }
          }
          return column
        })

        const newTotalWeeklyTime = updatedColumns.reduce((sum, col) => sum + col.totalTime, 0)

        setCurrentPlan({
          ...currentPlan,
          columns: updatedColumns,
          totalWeeklyTime: newTotalWeeklyTime,
          updatedAt: new Date()
        })

        setHasUnsavedChanges(true)
      }
    }
  }, [currentPlan])

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event

    if (!over || !currentPlan) {
      setDraggedLesson(null)
      return
    }

    const activeData = active.data.current
    const overData = over.data.current

    // Handle lesson card being dropped
    if (activeData?.type === 'lesson-card' || activeData?.type === 'library-lesson') {
      const lesson = activeData.lesson as KanbanLessonCard
      const sourceDay = activeData.day as WeekDay | undefined

      // Dropped on another lesson card (reorder within column)
      if (overData?.type === 'lesson-card') {
        const targetDay = overData.day as WeekDay
        const targetLesson = overData.lesson as KanbanLessonCard

        if (sourceDay && sourceDay === targetDay && lesson.id !== targetLesson.id) {
          // Reorder within same column
          const sourceColumn = currentPlan.columns.find(col => col.day === sourceDay)
          if (sourceColumn) {
            const oldIndex = sourceColumn.lessons.findIndex(l => l.id === lesson.id)
            const newIndex = sourceColumn.lessons.findIndex(l => l.id === targetLesson.id)

            if (oldIndex !== newIndex) {
              const updatedColumns = currentPlan.columns.map(column => {
                if (column.day === sourceDay) {
                  const newLessons = arrayMove(column.lessons, oldIndex, newIndex)
                  return {
                    ...column,
                    lessons: newLessons
                  }
                }
                return column
              })

              setCurrentPlan({
                ...currentPlan,
                columns: updatedColumns,
                updatedAt: new Date()
              })

              setHasUnsavedChanges(true)
            }
          }
        }
      }
      // Dropped on a day column
      else if (overData?.type === 'day-column') {
        const targetDay = overData.day as WeekDay

        if (!sourceDay || sourceDay !== targetDay) {
          // Move from library or between columns - this was already handled in handleDragOver
          // Just ensure the final state is correct
          console.log(`Moving lesson from ${sourceDay || 'library'} to ${targetDay}`)
        }
      }
    }

    setDraggedLesson(null)
  }, [currentPlan])

  // Handle lesson reorder within or between columns
  const handleLessonReorder = useCallback((
    lessonId: string,
    fromDay: WeekDay,
    toDay: WeekDay,
    newIndex: number
  ) => {
    if (!currentPlan) return

    // Find the lesson to move
    const sourceColumn = currentPlan.columns.find(col => col.day === fromDay)
    const lessonToMove = sourceColumn?.lessons.find(lesson => lesson.id === lessonId)

    if (!lessonToMove) return

    const updatedColumns = currentPlan.columns.map(column => {
      if (column.day === fromDay) {
        // Remove lesson from source column
        const newLessons = column.lessons.filter(lesson => lesson.id !== lessonId)
        const newTotalTime = newLessons.reduce((sum, lesson) => sum + lesson.duration, 0)

        return {
          ...column,
          lessons: newLessons,
          totalTime: newTotalTime
        }
      } else if (column.day === toDay) {
        // Add lesson to target column at specific index
        const newLessons = [...column.lessons]
        newLessons.splice(newIndex, 0, lessonToMove)
        const newTotalTime = newLessons.reduce((sum, lesson) => sum + lesson.duration, 0)

        return {
          ...column,
          lessons: newLessons,
          totalTime: newTotalTime
        }
      }
      return column
    })

    const newTotalWeeklyTime = updatedColumns.reduce((sum, col) => sum + col.totalTime, 0)

    setCurrentPlan({
      ...currentPlan,
      columns: updatedColumns,
      totalWeeklyTime: newTotalWeeklyTime,
      updatedAt: new Date()
    })

    setHasUnsavedChanges(true)
  }, [currentPlan])

  // Handle quick add from library
  const handleQuickAdd = useCallback((lesson: any, targetDay?: WeekDay) => {
    if (!currentPlan) return

    const lessonCard: KanbanLessonCard = {
      id: `card-${lesson.id}-${Date.now()}`,
      lessonId: lesson.id,
      title: lesson.title,
      duration: lesson.duration,
      difficulty: lesson.difficulty,
      subject: lesson.subject,
      category: lesson.category,
      status: 'not-started',
      priority: 'medium'
    }

    const day = targetDay || 'monday'
    handleLessonDrop(lessonCard, day)
  }, [currentPlan, handleLessonDrop])

  // Save plan
  const handleSavePlan = useCallback(async () => {
    if (!currentPlan) return

    setIsLoading(true)
    try {
      // TODO: Implement actual save to backend
      console.log('Saving plan:', currentPlan)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setHasUnsavedChanges(false)
      
      // Update plan status to active
      setCurrentPlan({
        ...currentPlan,
        status: 'active',
        updatedAt: new Date()
      })
      
    } catch (error) {
      console.error('Failed to save plan:', error)
    } finally {
      setIsLoading(false)
    }
  }, [currentPlan])

  // Calculate weekly stats
  const weeklyStats = React.useMemo(() => {
    if (!currentPlan) return { totalLessons: 0, totalTime: 0, completedLessons: 0, timeUtilization: 0 }

    const totalLessons = currentPlan.columns.reduce((sum, col) => sum + col.lessons.length, 0)
    const completedLessons = currentPlan.columns.reduce(
      (sum, col) => sum + col.lessons.filter(lesson => lesson.status === 'completed').length, 
      0
    )
    const timeUtilization = currentPlan.weeklyTimeLimit > 0 
      ? (currentPlan.totalWeeklyTime / currentPlan.weeklyTimeLimit) * 100 
      : 0

    return {
      totalLessons,
      totalTime: currentPlan.totalWeeklyTime,
      completedLessons,
      timeUtilization: Math.min(timeUtilization, 100)
    }
  }, [currentPlan])

  if (!selectedChild) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <BookOpen className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-400 mb-2">Select a Child</h3>
          <p className="text-gray-500">Choose a child to start planning their weekly curriculum</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h2 className="text-2xl font-bold text-white font-space-grotesk">
            Weekly Kanban Planner
          </h2>
          <p className="text-gray-400 mt-1">
            Drag lessons from the library to plan {selectedChild.name}&apos;s week
          </p>
        </div>

        <KanbanPlannerControls
          hasUnsavedChanges={hasUnsavedChanges}
          isLoading={isLoading}
          onSave={handleSavePlan}
          onToggleSidebar={() => setShowSidebar(!showSidebar)}
          showSidebar={showSidebar}
        />
      </motion.div>

      {/* Student Analytics Dashboard */}
      <StudentAnalyticsDashboard
        selectedChild={selectedChild}
        weeklyPlan={currentPlan}
      />

      {/* Week Navigator and Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Week Navigation */}
              <div>
                <WeekNavigator
                  currentWeek={currentPlan?.weekStartDate || new Date()}
                  onWeekChange={(date) => {
                    // TODO: Load plan for selected week
                    console.log('Week changed to:', date)
                  }}
                />
              </div>

              {/* Weekly Stats */}
              <div className="space-y-4">
                <div className="grid grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-cyan-400">{weeklyStats.totalLessons}</div>
                    <div className="text-xs text-gray-400">Lessons</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-400">{weeklyStats.completedLessons}</div>
                    <div className="text-xs text-gray-400">Completed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-400">{Math.round(weeklyStats.totalTime / 60)}h</div>
                    <div className="text-xs text-gray-400">Total Time</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-400">{Math.round(weeklyStats.timeUtilization)}%</div>
                    <div className="text-xs text-gray-400">Utilization</div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Weekly Progress</span>
                    <span className="text-gray-300">{weeklyStats.totalTime} / {currentPlan?.weeklyTimeLimit || 0} min</span>
                  </div>
                  <Progress
                    value={weeklyStats.timeUtilization}
                    className="h-2 bg-gray-800"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Main Kanban Board with DnD Context */}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCorners}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="flex gap-6"
        >
          {/* Lesson Library Sidebar */}
          <AnimatePresence>
            {showSidebar && (
              <motion.div
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: 320, opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="flex-shrink-0"
              >
                <LessonLibrarySidebar
                  selectedChild={selectedChild}
                  onLessonDrag={setDraggedLesson}
                  onLessonDrop={handleLessonDrop}
                  onQuickAdd={handleQuickAdd}
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Kanban Columns */}
          <div className="flex-1 grid grid-cols-7 gap-4 min-h-[600px]">
            {currentPlan?.columns.map((column, index) => (
              <DayColumnComponent
                key={column.day}
                column={column}
                onLessonDrop={handleLessonDrop}
                onLessonRemove={handleLessonRemove}
                onLessonReorder={handleLessonReorder}
                draggedLesson={draggedLesson}
                index={index}
              />
            ))}
          </div>
        </motion.div>
      </DndContext>
    </div>
  )
}
