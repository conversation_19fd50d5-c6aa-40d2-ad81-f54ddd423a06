// Integration between QuantumLink chat and NanoHero game systems
import { ModerationResult } from './moderation'
import { CommunicationQuest } from '@/types/chat'

export interface RewardCalculation {
  ce: number
  cubits: number
  xpBonus: number
  harmonyContribution: number
  badges: string[]
  avatarUpgrades: string[]
  specialEffects: string[]
}

export interface GameIntegration {
  updatePlayerStats: (userId: string, rewards: RewardCalculation) => Promise<void>
  checkAvatarUnlocks: (communicationLevel: number) => string[]
  getTimelineMultiplier: (harmonyLevel: number) => number
  triggerSpecialEvent: (eventType: string, participants: string[]) => Promise<void>
}

// Calculate rewards based on message quality and impact
export function calculateMessageRewards(
  moderationResult: ModerationResult,
  isHelpful: boolean = false,
  isEncouraging: boolean = false,
  isCreative: boolean = false
): RewardCalculation {
  let ce = 0
  let cubits = 0
  let xpBonus = 0
  let harmonyContribution = 0
  const badges: string[] = []
  const avatarUpgrades: string[] = []
  const specialEffects: string[] = []

  // Base rewards for approved messages
  if (moderationResult.isApproved) {
    ce += 5
    cubits += 1
    xpBonus += 10
  }

  // Kindness bonuses
  if (moderationResult.kindnessScore > 0.8) {
    ce += 10
    cubits += 2
    harmonyContribution += 15
    specialEffects.push('kindness_aura')
  } else if (moderationResult.kindnessScore > 0.6) {
    ce += 5
    harmonyContribution += 8
  }

  // Helpfulness bonuses
  if (moderationResult.helpfulnessScore > 0.8) {
    ce += 15
    cubits += 3
    harmonyContribution += 20
    if (isHelpful) {
      badges.push('quantum_helper')
      specialEffects.push('wisdom_glow')
    }
  } else if (moderationResult.helpfulnessScore > 0.6) {
    ce += 8
    harmonyContribution += 10
  }

  // Emotional intelligence bonuses
  if (moderationResult.emotionTags.includes('encouraging')) {
    ce += 12
    cubits += 2
    harmonyContribution += 12
    if (isEncouraging) {
      badges.push('inspiration_spark')
    }
  }

  if (moderationResult.emotionTags.includes('creative')) {
    ce += 8
    cubits += 2
    if (isCreative) {
      badges.push('creative_mind')
      avatarUpgrades.push('creative_aura')
    }
  }

  // Penalty for negative content
  if (moderationResult.flags.length > 0) {
    ce = Math.max(0, ce - 20)
    cubits = Math.max(0, cubits - 5)
    harmonyContribution = Math.min(0, harmonyContribution - 30)
  }

  return {
    ce,
    cubits,
    xpBonus,
    harmonyContribution,
    badges,
    avatarUpgrades,
    specialEffects
  }
}

// Calculate quest completion rewards
export function calculateQuestRewards(quest: CommunicationQuest): RewardCalculation {
  const baseReward = quest.reward
  
  return {
    ce: baseReward.ce,
    cubits: baseReward.cubits,
    xpBonus: baseReward.ce * 2, // XP is double CE
    harmonyContribution: Math.floor(baseReward.ce / 2),
    badges: baseReward.badge ? [baseReward.badge] : [],
    avatarUpgrades: baseReward.avatarUpgrade ? [baseReward.avatarUpgrade] : [],
    specialEffects: ['quest_complete_burst']
  }
}

// Calculate timeline harmony level effects
export function getHarmonyLevelEffects(harmonyLevel: number): {
  xpMultiplier: number
  ceMultiplier: number
  cubitsMultiplier: number
  unlockedFeatures: string[]
  specialAbilities: string[]
} {
  const effects = {
    xpMultiplier: 1,
    ceMultiplier: 1,
    cubitsMultiplier: 1,
    unlockedFeatures: [] as string[],
    specialAbilities: [] as string[]
  }

  if (harmonyLevel >= 2) {
    effects.xpMultiplier = 1.2
    effects.unlockedFeatures.push('enhanced_learning_mode')
  }

  if (harmonyLevel >= 3) {
    effects.ceMultiplier = 1.3
    effects.unlockedFeatures.push('quantum_events')
    effects.specialAbilities.push('harmony_boost')
  }

  if (harmonyLevel >= 4) {
    effects.cubitsMultiplier = 1.4
    effects.unlockedFeatures.push('advanced_collaboration')
  }

  if (harmonyLevel >= 5) {
    effects.xpMultiplier = 1.5
    effects.ceMultiplier = 1.5
    effects.unlockedFeatures.push('timeline_mastery')
    effects.specialAbilities.push('wisdom_sharing')
  }

  if (harmonyLevel >= 7) {
    effects.xpMultiplier = 2.0
    effects.ceMultiplier = 2.0
    effects.cubitsMultiplier = 2.0
    effects.unlockedFeatures.push('cosmic_consciousness')
    effects.specialAbilities.push('reality_shaping')
  }

  return effects
}

// Avatar progression based on communication skills
export function getAvatarProgressionUnlocks(
  communicationLevel: number,
  kindnessStreak: number,
  helpfulnessRating: number
): {
  availableUpgrades: string[]
  expressions: string[]
  accessories: string[]
  auras: string[]
} {
  const unlocks = {
    availableUpgrades: [] as string[],
    expressions: ['neutral', 'happy'] as string[],
    accessories: [] as string[],
    auras: [] as string[]
  }

  // Level-based unlocks
  if (communicationLevel >= 2) {
    unlocks.expressions.push('curious', 'focused')
    unlocks.accessories.push('wisdom_crystal')
  }

  if (communicationLevel >= 3) {
    unlocks.expressions.push('helpful')
    unlocks.auras.push('learning_glow')
  }

  if (communicationLevel >= 5) {
    unlocks.accessories.push('harmony_crown')
    unlocks.auras.push('kindness_aura')
  }

  if (communicationLevel >= 7) {
    unlocks.availableUpgrades.push('quantum_form')
    unlocks.auras.push('wisdom_radiance')
  }

  // Kindness streak unlocks
  if (kindnessStreak >= 7) {
    unlocks.accessories.push('compassion_badge')
  }

  if (kindnessStreak >= 14) {
    unlocks.auras.push('empathy_field')
  }

  if (kindnessStreak >= 30) {
    unlocks.availableUpgrades.push('heart_of_gold')
  }

  // Helpfulness rating unlocks
  if (helpfulnessRating >= 0.8) {
    unlocks.accessories.push('mentor_staff')
  }

  if (helpfulnessRating >= 0.9) {
    unlocks.auras.push('teacher_light')
  }

  return unlocks
}

// Special event triggers
export function checkSpecialEventTriggers(
  timelineHarmony: number,
  activeUsers: number,
  recentActivity: number
): {
  eventType: string | null
  description: string
  rewards: RewardCalculation
  duration: number // minutes
} | null {
  // Harmony Surge Event
  if (timelineHarmony > 1000 && activeUsers >= 10) {
    return {
      eventType: 'harmony_surge',
      description: 'The timeline is experiencing a harmony surge! All communication rewards are doubled!',
      rewards: {
        ce: 0,
        cubits: 0,
        xpBonus: 0,
        harmonyContribution: 0,
        badges: [],
        avatarUpgrades: [],
        specialEffects: ['harmony_surge_multiplier']
      },
      duration: 30
    }
  }

  // Wisdom Circle Event
  if (recentActivity > 50 && activeUsers >= 5) {
    return {
      eventType: 'wisdom_circle',
      description: 'A wisdom circle has formed! Share knowledge to unlock special rewards!',
      rewards: {
        ce: 25,
        cubits: 10,
        xpBonus: 50,
        harmonyContribution: 30,
        badges: ['wisdom_circle_participant'],
        avatarUpgrades: [],
        specialEffects: ['wisdom_sharing_boost']
      },
      duration: 20
    }
  }

  // Kindness Wave Event
  if (timelineHarmony > 500 && recentActivity > 30) {
    return {
      eventType: 'kindness_wave',
      description: 'A wave of kindness is spreading through the timeline! Spread positivity!',
      rewards: {
        ce: 15,
        cubits: 5,
        xpBonus: 30,
        harmonyContribution: 25,
        badges: ['kindness_wave_rider'],
        avatarUpgrades: [],
        specialEffects: ['kindness_amplifier']
      },
      duration: 15
    }
  }

  return null
}

// Integration with NanoHero progression system
export function integrateWithNanoHeroProgression(
  userId: string,
  communicationLevel: number,
  _totalCE: number,
  _totalCubits: number
): {
  nanoHeroLevel: number
  unlockedAreas: string[]
  availableQuests: string[]
  specialAbilities: string[]
} {
  // Calculate NanoHero level contribution from communication
  const communicationContribution = Math.floor(communicationLevel * 0.3)
  
  const unlocks = {
    nanoHeroLevel: communicationContribution,
    unlockedAreas: [] as string[],
    availableQuests: [] as string[],
    specialAbilities: [] as string[]
  }

  // Communication-based area unlocks
  if (communicationLevel >= 3) {
    unlocks.unlockedAreas.push('social_learning_lab')
  }

  if (communicationLevel >= 5) {
    unlocks.unlockedAreas.push('collaboration_chamber')
    unlocks.availableQuests.push('team_harmony_quest')
  }

  if (communicationLevel >= 7) {
    unlocks.unlockedAreas.push('wisdom_sanctuary')
    unlocks.specialAbilities.push('empathic_resonance')
  }

  if (communicationLevel >= 10) {
    unlocks.unlockedAreas.push('consciousness_nexus')
    unlocks.specialAbilities.push('collective_intelligence')
  }

  return unlocks
}

// Daily/Weekly communication challenges
export function generateCommunicationChallenges(
  userLevel: number,
  _recentActivity: any[]
): CommunicationQuest[] {
  const challenges: CommunicationQuest[] = []

  // Adaptive difficulty based on user level
  const baseTarget = Math.max(1, Math.floor(userLevel / 2))

  challenges.push({
    id: `daily_helper_${Date.now()}`,
    title: 'Daily Helper',
    description: `Help ${baseTarget * 2} fellow NanoArchitects today`,
    type: 'help_someone',
    target: baseTarget * 2,
    progress: 0,
    reward: {
      ce: 30 + (userLevel * 5),
      cubits: 8 + (userLevel * 2),
      badge: userLevel >= 5 ? 'advanced_helper' : 'helpful_hand'
    },
    isDaily: true
  })

  challenges.push({
    id: `daily_kindness_${Date.now()}`,
    title: 'Kindness Catalyst',
    description: `Spread kindness ${baseTarget * 3} times today`,
    type: 'show_kindness',
    target: baseTarget * 3,
    progress: 0,
    reward: {
      ce: 25 + (userLevel * 4),
      cubits: 6 + (userLevel * 1.5),
      badge: userLevel >= 7 ? 'kindness_master' : 'kindness_spark'
    },
    isDaily: true
  })

  return challenges
}
