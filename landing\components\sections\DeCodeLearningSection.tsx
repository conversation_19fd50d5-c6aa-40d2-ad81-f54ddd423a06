"use client"

import React, { useState, useRef, Suspense } from 'react'
import { motion } from 'framer-motion'
import { Canvas, useFrame } from '@react-three/fiber'
import { Float, OrbitControls } from '@react-three/drei'
import * as THREE from 'three'
import { 
  BookOpen, 
  Shield, 
  Gamepad2, 
  Play, 
  ChevronRight,
  Code,
  Zap
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'

interface DeCodeLearningSectionProps {
  onSignupClick: () => void
}

// Quantum 3D Floating Hexagon Component
function QuantumFloatingHexagon({ position, color, isActive, onClick }: {
  position: [number, number, number]
  color: string
  isActive: boolean
  onClick: () => void
}) {
  const meshRef = useRef<THREE.Mesh>(null)
  const glowRef = useRef<THREE.Mesh>(null)

  useFrame((state) => {
    if (!meshRef.current || !glowRef.current) return

    const time = state.clock.getElapsedTime()

    // Quantum rotation and floating
    meshRef.current.rotation.y = time * 0.5
    meshRef.current.rotation.x = Math.sin(time * 0.3) * 0.1
    meshRef.current.position.y = position[1] + Math.sin(time * 2 + position[0]) * 0.3

    // Quantum pulsing effect
    if (isActive) {
      const pulse = 1.2 + Math.sin(time * 6) * 0.15
      meshRef.current.scale.setScalar(pulse)
      glowRef.current.scale.setScalar(pulse * 1.5)
    } else {
      const breathe = 1 + Math.sin(time * 2) * 0.05
      meshRef.current.scale.setScalar(breathe)
      glowRef.current.scale.setScalar(breathe * 1.2)
    }

    // Quantum glow rotation
    glowRef.current.rotation.y = -time * 0.3
    glowRef.current.rotation.z = time * 0.2
  })

  return (
    <Float speed={2} rotationIntensity={0.5} floatIntensity={0.5}>
      <group onClick={onClick}>
        {/* Quantum glow effect */}
        <mesh ref={glowRef}>
          <cylinderGeometry args={[1.5, 1.5, 0.1, 6]} />
          <meshStandardMaterial
            color={color}
            emissive={color}
            emissiveIntensity={isActive ? 0.6 : 0.2}
            transparent
            opacity={0.3}
          />
        </mesh>

        {/* Main hexagon */}
        <mesh ref={meshRef}>
          <cylinderGeometry args={[1, 1, 0.3, 6]} />
          <meshStandardMaterial
            color={color}
            emissive={color}
            emissiveIntensity={isActive ? 1.0 : 0.4}
            transparent
            opacity={0.9}
            metalness={0.8}
            roughness={0.2}
          />
        </mesh>

        {/* Quantum core */}
        <mesh>
          <sphereGeometry args={[0.3, 16, 16]} />
          <meshStandardMaterial
            color="#ffffff"
            emissive={color}
            emissiveIntensity={isActive ? 1.5 : 0.8}
            transparent
            opacity={0.8}
          />
        </mesh>
      </group>
    </Float>
  )
}

// Quantum 3D Scene Component
function QuantumHexagonScene({ activeModule, onModuleClick }: {
  activeModule: string | null
  onModuleClick: (module: string) => void
}) {
  const modules = [
    { id: 'learn', position: [-3, 0, 0] as [number, number, number], color: '#22d3ee' }, // Neural cyan
    { id: 'hack', position: [0, 0, 0] as [number, number, number], color: '#8b5cf6' }, // Quantum purple
    { id: 'create', position: [3, 0, 0] as [number, number, number], color: '#fbbf24' }, // Quantum gold
  ]

  return (
    <>
      <ambientLight intensity={0.3} />
      <pointLight position={[10, 10, 10]} intensity={1.2} color="#22d3ee" />
      <pointLight position={[-10, -10, -10]} intensity={0.8} color="#8b5cf6" />
      <pointLight position={[0, 10, -10]} intensity={0.6} color="#fbbf24" />

      {modules.map((module) => (
        <QuantumFloatingHexagon
          key={module.id}
          position={module.position}
          color={module.color}
          isActive={activeModule === module.id}
          onClick={() => onModuleClick(module.id)}
        />
      ))}

      <OrbitControls enableZoom={false} enablePan={false} autoRotate autoRotateSpeed={0.8} />
    </>
  )
}

export function DeCodeLearningSection({ onSignupClick }: DeCodeLearningSectionProps) {
  const [activeModule, setActiveModule] = useState<string | null>(null)
  const [showModal, setShowModal] = useState(false)
  const [selectedModule, setSelectedModule] = useState<string | null>(null)

  type Module = {
    id: string
    title: string
    icon: React.ComponentType<{ className?: string }>
    color: string
    quantumColor: string
    description: string
    features: string[]
    preview: string
    cta: string
  }

  const modules: Module[] = [
    {
      id: 'learn',
      title: 'Neural Learning',
      icon: BookOpen,
      color: 'from-neural-cyan to-cyan-400',
      quantumColor: '#22d3ee',
      description: 'Master quantum coding fundamentals through neural-enhanced tutorials and consciousness-driven learning',
      features: [
        'Quantum Python & JavaScript protocols',
        'Neural-link peer learning system',
        'Consciousness tracking with Quantum XP',
        'Community-driven knowledge synthesis'
      ],
      preview: 'Initialize your first quantum algorithm and build neural web apps in 30 minutes',
      cta: 'Begin Neural Learning'
    },
    {
      id: 'hack',
      title: 'Quantum Security',
      icon: Shield,
      color: 'from-quantum-purple to-purple-400',
      quantumColor: '#8b5cf6',
      description: 'Explore quantum cryptography and consciousness security in controlled quantum environments',
      features: [
        'Quantum Capture The Flag challenges',
        'Neural network security simulations',
        'Quantum cryptography protocols',
        'Consciousness bounty programs'
      ],
      preview: 'Decrypt your first quantum cipher and discover hidden consciousness patterns',
      cta: 'Enter Quantum Lab'
    },
    {
      id: 'create',
      title: 'Reality Forge',
      icon: Gamepad2,
      color: 'from-quantum-gold to-yellow-400',
      quantumColor: '#fbbf24',
      description: 'Build quantum games, consciousness apps, and neural art while sharing your creations across dimensions',
      features: [
        'Quantum game development protocols',
        'Neural app creation tools',
        'Consciousness art and animation studio',
        'Interdimensional showcase platform'
      ],
      preview: 'Design your first quantum reality game and share it across the NanoVerse',
      cta: 'Start Creating'
    }
  ]

  const handleModuleClick = (moduleId: string) => {
    setActiveModule(moduleId)
    setSelectedModule(moduleId)
    setShowModal(true)
  }

  const getModuleData = (id: string) => {
    return modules.find(m => m.id === id)
  }

  return (
    <section className="relative px-4 sm:px-6 py-16 sm:py-20 lg:py-24 bg-space-gradient overflow-hidden">
      {/* Quantum background overlay */}
      <div className="absolute inset-0 consciousness-wave opacity-20" />

      <div className="relative max-w-7xl mx-auto">
        {/* Quantum Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-12 sm:mb-16 lg:mb-20"
        >
          {/* Quantum glow effect behind title */}
          <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-neural-cyan/10 rounded-full blur-3xl opacity-50" />

          <motion.h2
            className="relative text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-orbitron font-bold mb-4 sm:mb-6 bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold bg-clip-text text-transparent neural-glow"
            animate={{
              textShadow: [
                '0 0 20px rgba(34, 211, 238, 0.5)',
                '0 0 40px rgba(34, 211, 238, 0.8)',
                '0 0 20px rgba(34, 211, 238, 0.5)'
              ]
            }}
            transition={{ duration: 3, repeat: Infinity }}
          >
            DeCode Your Quantum Learning
          </motion.h2>

          <motion.p
            className="text-base sm:text-lg lg:text-xl text-white/80 max-w-4xl mx-auto font-space-grotesk leading-relaxed px-4"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            Three quantum learning domains designed to transform consciousness into expertise.
            <br className="hidden sm:block" />
            <span className="text-neural-cyan font-semibold">Click on each floating quantum island to explore your neural path.</span>
          </motion.p>
        </motion.div>

        {/* Quantum 3D Interactive Hexagons */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 1 }}
          className="h-80 sm:h-96 lg:h-[28rem] mb-12 sm:mb-16 relative"
        >
          {/* Quantum field background */}
          <div className="absolute inset-0 rounded-3xl border border-neural-cyan/20 quantum-glass">
            <div className="absolute inset-0 bg-gradient-to-r from-neural-cyan/5 via-quantum-purple/5 to-quantum-gold/5 rounded-3xl" />
          </div>

          <Canvas
            camera={{ position: [0, 0, 8], fov: 75 }}
            gl={{ antialias: true, alpha: true, powerPreference: 'high-performance' }}
            style={{ borderRadius: '1.5rem' }}
          >
            <Suspense fallback={null}>
              <QuantumHexagonScene
                activeModule={activeModule}
                onModuleClick={handleModuleClick}
              />
            </Suspense>
          </Canvas>

          {/* Quantum interaction hint */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 2 }}
            className="absolute bottom-4 left-1/2 transform -translate-x-1/2"
          >
            <div className="quantum-glass rounded-full px-4 py-2 border border-neural-cyan/30">
              <p className="text-neural-cyan text-xs sm:text-sm font-space-grotesk font-medium neural-glow flex items-center gap-2">
                <motion.span
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                >
                  ✨
                </motion.span>
                <span className="hidden sm:inline">Click on the quantum islands to explore</span>
                <span className="sm:hidden">Tap to explore</span>
              </p>
            </div>
          </motion.div>
        </motion.div>

        {/* Quantum Module Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
          {modules.map((module, index) => (
            <motion.div
              key={module.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.2 }}
              whileHover={{ scale: 1.02, y: -8 }}
              whileTap={{ scale: 0.98 }}
              className="group cursor-pointer"
              onClick={() => handleModuleClick(module.id)}
            >
              <Card className="relative quantum-glass border-2 hover:border-opacity-80 transition-all duration-500 h-full overflow-hidden quantum-border"
                style={{
                  borderColor: `${module.quantumColor}40`,
                  background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)',
                  boxShadow: `0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px ${module.quantumColor}20, inset 0 1px 0 rgba(255, 255, 255, 0.1)`
                }}
              >
                {/* Quantum glow effect */}
                <div
                  className="absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-500"
                  style={{
                    background: `linear-gradient(135deg, ${module.quantumColor}30, ${module.quantumColor}10)`,
                    boxShadow: `0 0 40px ${module.quantumColor}40`
                  }}
                />

                {/* Quantum sweep effect */}
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                  <div
                    className="absolute inset-0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"
                    style={{
                      background: `linear-gradient(90deg, transparent, ${module.quantumColor}30, transparent)`
                    }}
                  />
                </div>

                <CardContent className="p-6 lg:p-8 relative z-10">
                  <div className="flex items-center gap-4 mb-6">
                    <motion.div
                      className="relative p-4 rounded-xl border-2 group-hover:scale-110 transition-transform duration-300"
                      style={{
                        background: `linear-gradient(135deg, ${module.quantumColor}20, ${module.quantumColor}10)`,
                        borderColor: module.quantumColor,
                        boxShadow: `0 0 20px ${module.quantumColor}40`
                      }}
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.8 }}
                    >
                      <module.icon className="w-6 h-6 lg:w-7 lg:h-7 text-white" />

                      {/* Icon quantum glow */}
                      <div
                        className="absolute inset-0 rounded-xl blur-lg opacity-50"
                        style={{ backgroundColor: module.quantumColor }}
                      />
                    </motion.div>

                    <div>
                      <h3 className="text-xl lg:text-2xl font-orbitron font-bold text-white group-hover:neural-glow transition-all duration-300"
                        style={{ color: `${module.quantumColor}` }}
                      >
                        {module.title}
                      </h3>
                    </div>
                  </div>

                  <p className="text-white/70 mb-6 leading-relaxed font-space-grotesk">
                    {module.description}
                  </p>

                  <div className="space-y-3 mb-6">
                    {module.features.slice(0, 2).map((feature, i) => (
                      <div key={i} className="flex items-center gap-3 text-sm text-white/60">
                        <div
                          className="w-2 h-2 rounded-full quantum-pulse"
                          style={{ backgroundColor: module.quantumColor }}
                        />
                        <span className="font-space-grotesk">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <motion.div
                    className="flex items-center group-hover:text-white transition-colors duration-300"
                    style={{ color: module.quantumColor }}
                    whileHover={{ x: 8 }}
                  >
                    <span className="text-sm font-space-grotesk font-semibold">Explore {module.title}</span>
                    <ChevronRight className="w-4 h-4 ml-2 group-hover:translate-x-2 transition-transform duration-300" />
                  </motion.div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Quantum CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mt-16 lg:mt-20"
        >
          <div className="relative quantum-glass rounded-3xl p-8 lg:p-12 border-2 border-neural-cyan/30 overflow-hidden"
            style={{
              background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)',
              boxShadow: '0 20px 40px rgba(0, 0, 0, 0.4), 0 0 40px rgba(34, 211, 238, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
            }}
          >
            {/* Quantum background effects */}
            <div className="absolute inset-0 consciousness-wave opacity-30" />
            <div className="absolute top-0 left-1/4 w-32 h-32 bg-neural-cyan/20 rounded-full blur-3xl" />
            <div className="absolute bottom-0 right-1/4 w-40 h-40 bg-quantum-purple/20 rounded-full blur-3xl" />

            <div className="relative z-10">
              <motion.h3
                className="text-2xl sm:text-3xl lg:text-4xl font-orbitron font-bold text-white mb-4 lg:mb-6"
                animate={{
                  textShadow: [
                    '0 0 20px rgba(34, 211, 238, 0.5)',
                    '0 0 40px rgba(34, 211, 238, 0.8)',
                    '0 0 20px rgba(34, 211, 238, 0.5)'
                  ]
                }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                <motion.span
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
                  className="inline-block mr-3"
                >
                  🚀
                </motion.span>
                Ready to Choose Your Quantum Path?
              </motion.h3>

              <p className="text-base sm:text-lg lg:text-xl text-white/80 mb-8 lg:mb-10 max-w-3xl mx-auto font-space-grotesk leading-relaxed">
                Every <span className="text-neural-cyan font-semibold">NanoHero</span> starts somewhere.
                Pick your first quantum domain and begin building real consciousness skills through hands-on neural challenges!
              </p>

              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="relative group"
              >
                {/* Enhanced quantum glow effect */}
                <div className="absolute -inset-2 bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold rounded-2xl blur-xl opacity-50 group-hover:opacity-80 transition-opacity duration-500 quantum-pulse" />

                <Button
                  onClick={onSignupClick}
                  className="relative px-8 lg:px-12 py-4 lg:py-5 text-base lg:text-lg font-orbitron font-bold text-white border-2 border-neural-cyan/50 hover:border-neural-cyan transition-all duration-300 neural-glow"
                  style={{
                    background: 'linear-gradient(135deg, rgba(34, 211, 238, 0.2), rgba(139, 92, 246, 0.2))',
                    boxShadow: '0 0 30px rgba(34, 211, 238, 0.3)'
                  }}
                >
                  <motion.div
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                    className="inline-block mr-3"
                  >
                    <Code className="w-5 h-5 lg:w-6 lg:h-6" />
                  </motion.div>
                  Begin Your Quantum Journey
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="inline-block ml-3"
                  >
                    <Zap className="w-4 h-4 lg:w-5 lg:h-5" />
                  </motion.div>

                  {/* Quantum sweep */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700 rounded-xl" />
                </Button>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Quantum Module Detail Modal */}
      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent className="quantum-glass border-2 border-neural-cyan/30 text-white max-w-3xl"
          style={{
            background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(10, 15, 28, 0.95) 50%, rgba(0, 0, 0, 0.95) 100%)',
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.5), 0 0 40px rgba(34, 211, 238, 0.2)'
          }}
        >
          {selectedModule && (
            <>
              <DialogHeader>
                <DialogTitle className="flex items-center gap-4 text-2xl lg:text-3xl">
                  {(() => {
                    const moduleData = getModuleData(selectedModule)
                    if (!moduleData) return null
                    return (
                      <>
                        <motion.div
                          className="relative p-4 rounded-xl border-2"
                          style={{
                            background: `linear-gradient(135deg, ${moduleData.quantumColor}30, ${moduleData.quantumColor}10)`,
                            borderColor: moduleData.quantumColor,
                            boxShadow: `0 0 30px ${moduleData.quantumColor}40`
                          }}
                          animate={{ rotate: [0, 360] }}
                          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                        >
                          <moduleData.icon className="w-7 h-7 lg:w-8 lg:h-8 text-white" />

                          {/* Quantum glow */}
                          <div
                            className="absolute inset-0 rounded-xl blur-lg opacity-50"
                            style={{ backgroundColor: moduleData.quantumColor }}
                          />
                        </motion.div>

                        <span className="font-orbitron font-bold neural-glow" style={{ color: moduleData.quantumColor }}>
                          {moduleData.title}
                        </span>
                      </>
                    )
                  })()}
                </DialogTitle>
              </DialogHeader>

              {(() => {
                const moduleData = getModuleData(selectedModule)
                if (!moduleData) return null

                return (
                  <div className="space-y-6 lg:space-y-8">
                    <p className="text-white/80 text-lg lg:text-xl leading-relaxed font-space-grotesk">
                      {moduleData.description}
                    </p>

                    <div className="quantum-glass rounded-2xl p-6 border border-neural-cyan/20"
                      style={{
                        background: 'linear-gradient(135deg, rgba(34, 211, 238, 0.05), rgba(139, 92, 246, 0.05))'
                      }}
                    >
                      <h4 className="font-orbitron font-bold text-neural-cyan mb-4 text-lg neural-glow">
                        Quantum Learning Protocols:
                      </h4>
                      <div className="space-y-3">
                        {moduleData.features.map((feature, i) => (
                          <motion.div
                            key={i}
                            className="flex items-center gap-3"
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: i * 0.1 }}
                          >
                            <motion.div
                              animate={{ rotate: [0, 360] }}
                              transition={{ duration: 3, repeat: Infinity, ease: "linear", delay: i * 0.2 }}
                            >
                              <Zap className="w-4 h-4" style={{ color: moduleData.quantumColor }} />
                            </motion.div>
                            <span className="text-white/80 font-space-grotesk">{feature}</span>
                          </motion.div>
                        ))}
                      </div>
                    </div>

                    <div className="quantum-glass rounded-2xl p-6 border-2"
                      style={{
                        borderColor: `${moduleData.quantumColor}40`,
                        background: `linear-gradient(135deg, ${moduleData.quantumColor}10, ${moduleData.quantumColor}05)`
                      }}
                    >
                      <h4 className="font-orbitron font-bold text-white mb-3 neural-glow">Quantum Preview:</h4>
                      <p className="text-white/80 italic font-space-grotesk text-lg">&quot;{moduleData.preview}&quot;</p>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-4">
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="flex-1"
                      >
                        <Button
                          onClick={onSignupClick}
                          className="w-full py-4 text-lg font-orbitron font-bold text-white border-2 transition-all duration-300 relative overflow-hidden group"
                          style={{
                            background: `linear-gradient(135deg, ${moduleData.quantumColor}30, ${moduleData.quantumColor}20)`,
                            borderColor: moduleData.quantumColor,
                            boxShadow: `0 0 30px ${moduleData.quantumColor}40`
                          }}
                        >
                          <motion.div
                            animate={{ rotate: [0, 360] }}
                            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                            className="inline-block mr-3"
                          >
                            <Play className="w-5 h-5" />
                          </motion.div>
                          {moduleData.cta}

                          {/* Quantum sweep */}
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
                        </Button>
                      </motion.div>

                      <Button
                        variant="outline"
                        onClick={() => setShowModal(false)}
                        className="border-2 border-neural-cyan/40 text-neural-cyan hover:bg-neural-cyan/10 font-space-grotesk font-semibold py-4"
                      >
                        Explore Others
                      </Button>
                    </div>
                  </div>
                )
              })()}
            </>
          )}
        </DialogContent>
      </Dialog>
    </section>
  )
}
